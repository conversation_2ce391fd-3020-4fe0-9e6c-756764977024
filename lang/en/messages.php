<?php

return [

    /*
    |--------------------------------------------------------------------------
    | All Titles and static string in blade files - English Language
    |--------------------------------------------------------------------------
    |
    */
    //menu.blade keys

    'pdf' => [
        'date' => 'Date',
        'number' => 'Number',
        'payment_status' => 'Payment Status',
        'customer_info' => 'Customer Info',
        'name' => 'Name',
        'phone' => 'Phone',
        'address' => 'Address',
        'email' => 'Email',
        'company_info' => 'Company Info',
        'product' => 'Product',
        'unit_price' => 'UNIT PRICE',
        'quantity' => 'QUANTITY',
        'discount' => 'Discount',
        'tax' => 'TAX',
        'total' => 'Total',
        'order_tax' => 'Order Tax',
        'shipping' => 'Shipping',
        'paid_amount' => 'Paid Amount',
        'signature' => 'Signature',
        'status' => 'Status',
        'supplier_info' => 'Supplier Info',
        'unit_cost' => 'UNIT COST',
        'customer' => 'customer',
        'total_sales' => 'Total Sales',
        'total_amount' => 'Total Amount',
        'total_paid' => 'Total Paid',
        'total_sale_due' => 'Total Sale Due',
        'reference' => 'Reference',
        'due_amount' => 'Due Amount',
        'customer_pdf' => 'Customer Pdf',
        'customer_sales_pdf' => 'Customer Sales Pdf',
        'sale_list' => 'Sale List',
        'customer_name' => 'Customer Name',
        'customer_quotations_pdf' => 'Customer Quotations Pdf',
        'quotation_list' => 'Quotation List',
        'customer_returns_pdf' => 'Customer Returns Pdf',
        'return_list' => 'Return List',
        'customer_payments_pdf' => 'Customer Payments Pdf',
        'payment_list' => 'Payment List',
        'received_amount' => 'Received Amount',
        'payment_type' => 'Payment Type',
        'sale_reference' => 'Sale Reference',
        'top_customers_list' => 'Top Customers List',
        'top_customers_pdf' => 'Top Customers Pdf',
        'supplier' => 'Supplier',
        'warehouse' => 'warehouse',
        'amount' => 'Amount',
        'category' => 'Category',
        'code' => 'Code',
        'brand' => 'Brand',
        'price' => 'Price',
        'product_unit' => 'Product unit',
        'in_stock' => 'In stock',
        'created_on' => 'Created on',
        'client' => 'Client',
        'sub_total' => 'Sub Total',
        'paid' => 'Paid',
        'due' => 'Due',
        'cost' => 'Cost',
        'current_stock' => 'Current Stock',
        'product_code' => 'Product Code',
        'product_name' => 'Product Name',
    ],

    'error' => [
        'product_cant_deleted' => 'Product can\'t be deleted',
        'code_taken' => 'The code has already been taken.',
        'default_warehouse_cant_delete' => 'Default warehouse can\'t be deleted.',
        'warehouse_cant_delete' => 'Warehouse can\'t be deleted.',
        'default_user_cant_delete' => 'Default user can\'t be deleted.',
        'this_action_is_not_allowed_for_default_record' => 'This action is not allowed for default record.',
        'coupon_code_used_by_user' => 'This Coupon Code is used by user.',
        'currency_used_somewhere_else' => 'This currency is used somewhere else.',
        'default_language_cant_change' => 'Default Language can\'t be change.',
        'default_language_cant_delete' => 'Default Language can\'t be deleted.',
        'user_language_cant_delete' => 'User Language can\'t be deleted.',
        'json_file_not_found'       => 'Json File not found.',
        'directory_not_found'       => 'Directory not found.',
        'payment_failed'       => 'Unable to process the payment at the moment. Try again later.',
        'invalid_coupon_code'       => 'Invalid coupon code',
        'coupon_code_is_not_available'       => 'This coupon code is not available',
        'current_password_invalid'       => 'Current password is invalid',
        'confirm_password_mismatch'       => 'The confirm password and new password must match.'
    ],

    'success' => [
        'cache_clear_successfully' => 'Cache cleared Successfully',
        'coupon_code_deleted' => 'Coupon Code deleted successfully',
        'currency_deleted' => 'Currency deleted successfully',
        'language_deleted' => 'Language deleted successfully',
        'link_deleted' => 'Link deleted successfully',
        'project_deleted' => 'Project deleted successfully',
        'qr_code_deleted' => 'QR Code deleted successfully',
        'admin_data_retrieved' => 'admin dashboard data retrieved successfully',
        'language_updated' => 'Language updated successfully',
        'payment_status_updated' => 'Payment status updated successfully.',
        'default_plan_changed' => 'Default Plan Changed Successfully.',
        'message_send_successfully' => 'Message send successfully.',
        'message_view_successfully' => 'Message view successfully.',
        'message_deleted' => 'Message deleted successfully.',
        'front_cms_updated' => 'Front CMS updated successfully.',
        'sub_feature_updated' => 'Sub Features updated successfully.',
        'feature_updated' => 'Feature updated successfully.',
        'password_updated' => 'Password updated successfully',
        'plan_subscribed' => 'Subscription plan has been subscribed successfully',
        'subscribed_successfully' => 'Subscribed Successfully.',
        'subscriber_deleted' => 'Subscriber deleted successfully.',
        'user_deleted' => 'User deleted successfully',
        'wait_for_admin_approval' => 'Your payment is done and your subscription will be activated once the admin approve your transaction.',
        'subscription_date_updated' => 'Subscription date updated successfully.',
        'subscription_date_retrieved' => 'Subscription data retrieved successfully.',
        'email_verified' => 'Email verified successfully',
        'user_status_updated' => 'User status updated successfully',
        'account_deleted' => 'Your Account has been deleted successfully',
        'main_setting_retrieved' => 'Mail settings data retrieved.',
        'main_setting_updated' => 'Main settings updated successfully.',
        'paypal_data_retrieved' => 'Paypal settings data retrieved.',
        'paypal_data_updated' => 'Paypal settings updated successfully.',
        'stripe_data_retrieved' => 'Stripe settings data retrieved.',
        'stripe_data_updated' => 'Stripe settings updated successfully.',
        'razorpay_setting_retrieved' => 'Razorpay settings data retrieved.',
        'razorpay_setting_updated' => 'Razorpay settings updated successfully.',
        'captcha_data_retrieved' => 'Captcha settings data retrieved.',
        'captcha_data_updated' => 'Captcha settings updated successfully.',
        'ads_data_retrieved' => 'Ads settings data retrieved.',
        'ads_data_updated' => 'Ads settings updated successfully.',
        'social_setting_data_retrieved' => 'Social settings data retrieved.',
        'social_setting_data_updated' => 'Social settings updated successfully.',
        'custom_style_setting_retrieved' => 'Custom Style settings data retrieved.',
        'custom_style_setting_updated' => 'Custom Style settings updated successfully.',
        'announcement_setting_retrieved' => 'Announcement settings data retrieved.',
        'announcement_setting_updated' => 'Announcement settings updated successfully.',
        'email_notification_retrieved' => 'Email notification settings data retrieved.',
        'email_notification_updated' => 'Email notification settings updated successfully.',
        'setting_value_retrieved' => 'Setting value retrieved successfully.',
        'google_login_setting_retrieved' => 'Google Login settings data retrieved.',
        'google_login_setting_updated' => 'Google Login settings updated successfully.',
        'config_retrieved' => 'Config retrieved successfully.',
    ],

    'heading_discount' => 'DISCOUNT',
    'heading_total' => 'TOTAL',
    'sale_pdf' => 'Sale Pdf',
    'purchase_pdf' => 'Purchase PDF',
    'Sale_return_pdf' => 'Sale Return PDF',
    'purchase_return_pdf' => 'Purchase Return PDF',
    'quotation_pdf' => 'Quotation PDF',

    'template' => [
        'share_my_template'  => 'Share My Template',
        'email'              => 'Email',
        'phone_number'       => 'Mobile Number',
        'qr_code'            => 'QR Code',
        'download_template'  => 'Download Template',
        'download_qr_code'   => 'Download QR Code'
    ],
];
