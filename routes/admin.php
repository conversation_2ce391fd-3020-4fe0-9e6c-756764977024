<?php

use App\Http\Controllers\API\Admin\CouponCodeAPIController;
use App\Http\Controllers\API\Admin\CurrencyAPIController;
use App\Http\Controllers\API\Admin\DashboardAPIController;
use App\Http\Controllers\API\Admin\DigitalBusinessCardAPIController;
use App\Http\Controllers\API\Admin\LanguageAPIController;
use App\Http\Controllers\API\Admin\LinkAPIController;
use App\Http\Controllers\API\Admin\PageAPIController;
use App\Http\Controllers\API\Admin\PlanAPIController;
use App\Http\Controllers\API\Admin\ProjectAPIController;
use App\Http\Controllers\API\Admin\QrCodeAPIController;
use App\Http\Controllers\API\Admin\QrCodeTypeAPIController;
use App\Http\Controllers\API\Admin\SettingAPIController;
use App\Http\Controllers\API\Admin\TransactionAPIController;
use App\Http\Controllers\API\ContactUsAPIController;
use App\Http\Controllers\API\FrontCmsAPIController;
use App\Http\Controllers\API\SubscriberAPIController;
use App\Http\Controllers\API\SubscriptionAPIController;
use App\Http\Controllers\API\UserAPIController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'admin', 'middleware' => ['auth:sanctum', 'role:admin']], function () {
    Route::middleware('xss')->group(function () {
        // Dashboard Route
        Route::get('dashboard', [DashboardAPIController::class, 'index']);

        // Users Route
        Route::get('get-users', [UserAPIController::class, 'getAllUsers']);
        Route::get('verify-email/{id}', [UserAPIController::class, 'emailVerified']);
        Route::get('update-status/{id}', [UserAPIController::class, 'updateStatus']);
        Route::get('user-qrcodes/{id}', [UserAPIController::class, 'getQrCodes']);
        Route::get('user-links/{id}', [UserAPIController::class, 'getLinks']);

        // Plans Route
        Route::post('make-plan-default/{id}', [PlanAPIController::class, 'makePlanDefault']);

        // Qr Codes Route
        Route::post('project-list', [QrCodeAPIController::class, 'projectList']);
        Route::post('qrcode-types', [QrCodeAPIController::class, 'qrCodeTypes']);

        // Links Route
        Route::resource('links', LinkAPIController::class);

        // Clear Cache Route
        Route::get('cache-clear', [SettingAPIController::class, 'clearCache'])->name('cache.clear');

        // Website Settings Route
        Route::get('paypal-settings', [SettingAPIController::class, 'paypalSetting'])->name('paypal.settings');
        Route::post('paypal-settings', [SettingAPIController::class, 'updatePaypalSetting'])->name('update.paypal.settings');

        Route::get('stripe-settings', [SettingAPIController::class, 'stripeSetting'])->name('stripe.settings');
        Route::post('stripe-settings', [SettingAPIController::class, 'updateStripeSetting'])->name('update.stripe.settings');

        Route::get('razorpay-settings', [SettingAPIController::class, 'razorpaySetting'])->name('razorpay.settings');
        Route::post('razorpay-settings', [SettingAPIController::class, 'updateRazorpaySetting'])->name('update.razorpay.settings');

        Route::get('captcha-settings', [SettingAPIController::class, 'captchaSetting'])->name('captcha.settings');
        Route::post('captcha-settings', [SettingAPIController::class, 'updateCaptchaSetting'])->name('update.captcha.settings');

        Route::get('social-settings', [SettingAPIController::class, 'socialSetting'])->name('social.settings');
        Route::post('social-settings', [SettingAPIController::class, 'updateSocialSetting'])->name('update.social.settings');

        Route::get('mail-settings', [SettingAPIController::class, 'mailSetting'])->name('mail.settings');
        Route::post('mail-settings', [SettingAPIController::class, 'updateMailSetting'])->name('update.mail.settings');

        Route::get('email-notification-settings',
            [SettingAPIController::class, 'emailNotificationSetting'])->name('email.notification.settings');
        Route::post('email-notification-settings', [
            SettingAPIController::class, 'updateEmailNotificationSetting'])->name('update.email.notification.settings');

        Route::get('google-login', [SettingAPIController::class, 'googleLoginSetting'])->name('google.login');
        Route::post('google-login', [SettingAPIController::class, 'updateGoogleLoginSetting'])->name('update.google.login');

        // Subscriptions Route
        Route::get('cash-payments', [TransactionAPIController::class, 'cashPayments'])->name('cash.payments');
        Route::get('cash-payments/{id}', [TransactionAPIController::class, 'cashPaymentDetails']);
        Route::get('transactions', [TransactionAPIController::class, 'index'])->name('transactions');
        Route::get('transaction/{id}', [TransactionAPIController::class, 'show'])->name('transactions.show');
        Route::get('subscribed-user-plans', [SubscriptionAPIController::class, 'subscribedUserPlans'])->name('subscribed.user.plans');
        Route::post('change-payment-status', [TransactionAPIController::class, 'changePaymentStatus'])->name('change.payment.status');
        Route::post('update-subscription-date',
            [SubscriptionAPIController::class, 'updateSubscriptionEndDate'])->name('update.subscription.date');

        // change subscribed user plan status
        Route::get('change-status/{id}/{userId}', [SubscriptionAPIController::class, 'changeStatus'])->name('change.status');

        // Subscribers
        Route::resource('subscribers', SubscriberAPIController::class);

        // Pages Route
        Route::resource('pages', PageAPIController::class)->except(['store','update']);
        Route::post('update-page/{id}', [PageAPIController::class, 'update']);

        // Most Visited Link
        Route::get('most-visited-link', [LinkAPIController::class, 'mostVisitedLink']);
    });

    // Users Route
    Route::resource('users', UserAPIController::class);
    Route::post('update-user/{id}', [UserAPIController::class, 'update']);

    // Plans Route
    Route::resource('plans', PlanAPIController::class);
    Route::post('update-plan/{id}', [PlanAPIController::class, 'update']);

    // Projects Route
    Route::resource('projects', ProjectAPIController::class)->except(['store', 'show', 'update']);

    // Projects Route
    Route::post('projects', [ProjectAPIController::class, 'store'])->withoutMiddleware('xss');

    // Qr Codes Route
    Route::resource('qr-codes', QrCodeAPIController::class);

    // Coupon Codes Route
    Route::resource('coupon-codes', CouponCodeAPIController::class);
    Route::post('update-coupon-code/{id}', [CouponCodeAPIController::class, 'update']);

    // Currencies Route
    Route::resource('currencies', CurrencyAPIController::class);
    Route::post('update-currency/{id}', [CurrencyAPIController::class, 'update']);

    // Website Settings Route
    Route::get('main-settings', [SettingAPIController::class, 'mainSetting'])->name('main.settings');
    Route::post('main-settings', [SettingAPIController::class, 'updateMainSetting'])->name('update.main.settings');

    Route::get('custom-style-settings', [SettingAPIController::class, 'customStyleSetting'])->name('custom.style.settings');
    Route::post('custom-style-settings', [SettingAPIController::class, 'updateCustomStyleSetting'])->name('update.custom.style.settings');

    Route::get('ads-settings', [SettingAPIController::class, 'adsSetting'])->name('ads.settings');
    Route::post('ads-settings', [SettingAPIController::class, 'updateAdsSetting'])->name('update.ads.settings');

    Route::get('announcement-settings', [SettingAPIController::class, 'announcementSetting'])->name('announcement.settings');
    Route::post('announcement-settings', [SettingAPIController::class, 'updateAnnouncementSetting'])->name('update.announcement.settings');

    // Front CMS
    Route::post('update-front-cms', [FrontCmsAPIController::class, 'updateFrontCMS']);
    Route::post('update-features', [FrontCmsAPIController::class, 'updateFeatures']);
    Route::post('update-sub-features', [FrontCmsAPIController::class, 'updateSubFeatures']);

    // Term & Privacy
    Route::post('term-conditions', [FrontCmsAPIController::class, 'UpdateTermConditions']);

    // Contact Us
    Route::get('contacts', [ContactUsAPIController::class, 'index']);
    Route::post('delete-contact', [ContactUsAPIController::class, 'destroy']);
    Route::get('contact/{id}', [ContactUsAPIController::class, 'show']);

    // Earnings
    Route::get('get-week-earnings', [TransactionAPIController::class, 'getWeekEarnings']);

    // Digital business card API route
    Route::get('digital-business-cards', [DigitalBusinessCardAPIController::class, 'index']);
    Route::post('digital-business-cards', [DigitalBusinessCardAPIController::class, 'store']);
    Route::get('digital-business-cards/{id}', [DigitalBusinessCardAPIController::class, 'show']);
    Route::delete('digital-business-cards/{id}', [DigitalBusinessCardAPIController::class, 'destroy']);
    Route::post('business-card-social-link/{id}', [DigitalBusinessCardAPIController::class, 'update']);
    Route::post('digital-business-cards/change-status/{id}', [DigitalBusinessCardAPIController::class, 'changeStatus']);

    // Pages store and update Route
    Route::post('pages', [PageAPIController::class,'store'])->name('pages.store');
    Route::post('update-page/{id}', [PageAPIController::class,'update'])->name('pages.update');
});

// Front CMS
Route::get('front-cms', [FrontCmsAPIController::class, 'index']);
Route::get('features', [FrontCmsAPIController::class, 'features']);
Route::get('sub-features', [FrontCmsAPIController::class, 'subFeatures']);

//Languages Route
Route::resource('languages', LanguageAPIController::class);
Route::get('languages/translation/{language}', [LanguageAPIController::class, 'showTranslation']);
Route::post('languages/translation/{language}/update', [LanguageAPIController::class, 'updateTranslation']);

// QR Code Types API route
Route::get('qr-code-types', [QrCodeTypeAPIController::class, 'index']);
Route::post('qr-code-types', [QrCodeTypeAPIController::class, 'update']);
