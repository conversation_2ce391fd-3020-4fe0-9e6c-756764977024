<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\LinkAPIController;
use App\Http\Controllers\API\DigitalBusinessCardAPIController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

Route::get('/', function () {
    return view('welcome');
});

Route::get('{alias}', [LinkAPIController::class, 'showPublicAlias'])->middleware(['analytics', 'templateLanguage']);

// download template route
Route::get('download-template/{id}', [DigitalBusinessCardAPIController::class, 'downloadTemplate']);

// change template language route
Route::get('language/{languageName}/{alias}', [DigitalBusinessCardAPIController::class, 'changeLanguage']);

Route::get('/run-migration', function() {

});
