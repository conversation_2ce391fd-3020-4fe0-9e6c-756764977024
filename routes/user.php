<?php

use App\Http\Controllers\API\DashboardAPIController;
use App\Http\Controllers\API\DigitalBusinessCardAPIController;
use App\Http\Controllers\API\LinkAPIController;
use App\Http\Controllers\API\PaypalApIController;
use App\Http\Controllers\API\ProjectAPIController;
use App\Http\Controllers\API\QrCodeAPIController;
use App\Http\Controllers\API\RazorpayAPIController;
use App\Http\Controllers\API\StripeAPIController;
use App\Http\Controllers\API\SubscriptionAPIController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'user', 'middleware' => ['auth:sanctum', 'role:user', 'checkUserStatus', 'verified', 'xss']],
    function () {

        Route::middleware('check_subscription')->group(function () {
            // Dashboard Route
            Route::get('dashboard', [DashboardAPIController::class, 'index']);

            // Projects Route
            Route::resource('projects', ProjectAPIController::class)->withoutMiddleware('xss');
            Route::post('update-project/{id}', [ProjectAPIController::class, 'update'])->name('update.project')->withoutMiddleware('xss');

            // QR Codes Route
            Route::resource('qr-codes', QrCodeAPIController::class)->withoutMiddleware('xss');
            Route::post('update-qrcode/{id}', [QrCodeAPIController::class, 'update'])->withoutMiddleware('xss');
            Route::get('project-list', [QrCodeAPIController::class, 'projectList']);
            Route::get('qrcode-types', [QrCodeAPIController::class, 'qrCodeTypes']);

            // Links Route
            Route::resource('links', LinkAPIController::class);
            Route::post('update-link/{id}', [LinkAPIController::class, 'update'])->name('update.link');

            // Most Visited Link
            Route::get('most-visited-link', [LinkAPIController::class, 'mostVisitedLink']);

            // Digital Business Card API Route
            Route::resource('digital-business-cards', DigitalBusinessCardAPIController::class)->except(['store', 'update']);
            Route::post('digital-business-cards', [DigitalBusinessCardAPIController::class, 'store'])->withoutMiddleware('xss');
            Route::post('digital-business-cards/{digital_business_card}', [DigitalBusinessCardAPIController::class, 'update'])->withoutMiddleware('xss');

            // business card social links API route
            Route::get('social-links/{id}', [DigitalBusinessCardAPIController::class, 'getSocialLinks']);
            Route::post('business-card-social-links', [DigitalBusinessCardAPIController::class, 'storeSocialLinks']);
            Route::delete('business-card-social-links/{businessCardId}/{socialLinkId}', [DigitalBusinessCardAPIController::class, 'deleteSocialLink']);
        });

        // Subscriptions Route
        // Manual Payment
        Route::get('subscription-plans', [SubscriptionAPIController::class, 'index'])->name('subscription.plans.index');
        Route::post('choose-payment-type',
            [SubscriptionAPIController::class, 'choosePaymentType'])->name('choose.payment.type');
        Route::post('purchase-plan', [SubscriptionAPIController::class, 'pay'])->name('purchase.plan');
        Route::get('user-subscriptions', [SubscriptionAPIController::class, 'subscriptions'])->name('subscriptions');

        // Razor Pay Routes
        Route::post('razorpay-onboard',
            [RazorpayAPIController::class, 'onBoard'])->name('razorpay.onboard');

        // PayPal Payment Gateway
        Route::post('paypal-onboard', [PaypalApIController::class, 'onBoard'])->name('paypal.onboard');

        // Stripe Payment Route
        Route::post('stripe-onboard',
            [StripeAPIController::class, 'onBoard'])->name('stripe.onboard');

        // Remaining Subscription Days
        Route::get('remaining-subscription',
            [SubscriptionAPIController::class, 'remainingSubscriptionDays'])->name('remaining.subscription.days');

        // Announcements
        Route::get('announcements', [
            \App\Http\Controllers\API\Admin\SettingAPIController::class, 'announcementSetting',
        ])->name('announcements');

    });

Route::post('razorpay-payment-success', [RazorpayAPIController::class, 'success'])
    ->name('razorpay.success');
Route::get('razorpay-payment-failed', [RazorpayAPIController::class, 'failed'])
    ->name('razorpay.failed');
Route::get('paypal-payment-success', [PaypalApIController::class, 'success'])->name('paypal.success');
Route::get('paypal-payment-failed/{id}', [PaypalApIController::class, 'failed'])->name('paypal.failed');
Route::get('stripe-payment-success', [StripeAPIController::class, 'success'])->name('stripe-payment-success');
Route::get('stripe-payment-failed/{id}', [StripeAPIController::class, 'failed'])->name('stripe.failed');
