<?php

use App\Http\Controllers\API\Admin\SettingAPIController;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\ContactUsAPIController;
use App\Http\Controllers\API\FrontCmsAPIController;
use App\Http\Controllers\API\LinkAPIController;
use App\Http\Controllers\API\SocialAuthController;
use App\Http\Controllers\API\SubscriberAPIController;
use App\Http\Controllers\API\UserAPIController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\SubscriptionAPIController;
use App\Http\Controllers\API\Admin\PageAPIController;
use App\Models\Transaction;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('login', [AuthController::class, 'login'])->name('login');
Route::post('login/{provider}', [SocialAuthController::class, 'socialLogin'])->name('social.login');

// Forgot Password 
Route::post('forgot-password',
    [AuthController::class, 'sendPasswordResetLinkEmail']);
Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('password.reset');

// Registration Route
Route::post('registration',
    [UserAPIController::class, 'store'])->name('registration')->middleware('checkAllowUserRegistration');

Route::middleware('auth:sanctum')->group(static function () {
    Route::middleware('xss')->group(static function () {
        // Profile Routes
        Route::get('edit-profile', [UserAPIController::class, 'editProfile']);
        Route::post('update-profile', [UserAPIController::class, 'updateProfile']);
    });
    // Profile Routes
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('change-password', [UserAPIController::class, 'changePassword']);

    // Link Analysis
    Route::get('link/{id}/analytics', [LinkAPIController::class, 'analytics']);
    Route::post('link/chart-data', [LinkAPIController::class, 'chartData']);

    // Digital business card Analysis
    Route::get('business-card/{id}/analytics', [LinkAPIController::class, 'analytics']);
    Route::post('business-card/chart-data', [LinkAPIController::class, 'chartData']);

    // Delete Account
    Route::post('delete-account',
        [UserAPIController::class, 'deleteAccount'])->name('delete.account');

    // Change Password On First Time Login
    Route::post('change-login-password',
        [UserAPIController::class, 'changeLoginPassword'])->name('change.login.password');

    //Language Change
    Route::post('change-language', [UserAPIController::class, 'updateLanguage']);
});
// Front setting
Route::get('front-setting', [SettingAPIController::class, 'getFrontSettingsValue'])->name('front-settings');

Route::get('public-subscription-plans', [SubscriptionAPIController::class, 'publicSubscriptionPlans']);
Route::post('subscribe', [SubscriberAPIController::class, 'store']);
Route::post('contact', [ContactUsAPIController::class, 'store']);
Route::get('config', [SettingAPIController::class, 'config']);

// Term & Privacy
Route::get('term-conditions', [FrontCmsAPIController::class, 'termConditions']);

// Front Pages
Route::get('pages', [PageAPIController::class, 'getPages']);
Route::get('get-page/{slug}', [PageAPIController::class, 'getPage']);

require __DIR__.'/user.php';
require __DIR__.'/admin.php';

