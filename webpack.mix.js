const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.options({
    postCss: [
        require('autoprefixer'),
    ],
});

mix.setPublicPath('public');
mix.webpackConfig({
    resolve: {
        extensions: ['.js', '.jsx'],
        alias: {
            '@': __dirname + 'resources'
        }
    },
    output: {
        chunkFilename: 'js/chunks/[name].js',
    },
}).react();

mix.js('resources/js/app.js', 'public/js');

mix.js('resources/qr-builder/src/index.js', 'public/js/app.js').version();

mix.copyDirectory('resources/images', 'public/assets/images')
mix.copyDirectory('resources/theme/webfonts', 'public/assets/webfonts')
mix.copyDirectory('resources/theme/fonts', 'public/assets/fonts')

// third-party css
mix.styles([
    'resources/theme/css/third-party.css',
    'node_modules/toastr/build/toastr.min.css'
], 'public/assets/css/third-party.css')

// light theme css
mix.styles('resources/theme/css/style.css', 'public/assets/css/style.css');
mix.styles('resources/theme/css/plugins.css', 'public/css/plugins.css');

mix.sass('resources/assets/scss/template1.scss', 'public/assets/css/template1.css').
    sass('resources/assets/scss/template2.scss', 'public/assets/css/template2.css').
    sass('resources/assets/scss/template3.scss', 'public/assets/css/template3.css').
    sass('resources/assets/scss/template4.scss', 'public/assets/css/template4.css').
    sass('resources/assets/scss/template5.scss', 'public/assets/css/template5.css').
    version()

mix.sass('resources/assets/scss/custom-template.scss', 'public/assets/css/custom-template.css').version()

mix.copy('node_modules/lightbox2/dist/css/lightbox.min.css', 'public/assets/css/lightbox.css')
mix.babel('node_modules/lightbox2/dist/js/lightbox.min.js', 'public/assets/js/lightbox.js')
mix.copyDirectory('node_modules/lightbox2/dist/images', 'public/assets/images')

mix.scripts([
    'resources/theme/js/vendor.js',
    'resources/theme/js/plugins.js',
], 'public/assets/js/front-third-party.js').version()


mix.copy('node_modules/bootstrap/dist/css/bootstrap.min.css', 'public/assets/css/bootstrap.min.css');
mix.copy('node_modules/toastr/build/toastr.min.css', 'public/assets/css/toastr.min.css');

mix.babel('node_modules/bootstrap/dist/js/bootstrap.bundle.min.js', 'public/assets/js/bootstrap.bundle.min.js');
mix.babel('node_modules/toastr/build/toastr.min.js', 'public/assets/js/toastr.min.js');

mix.js('resources/assets/js/templates/template-view.js',
    'public/assets/js/templates/template-view.js').version();

// fronted css
// mix.sass([
//     'resources/pos/src/frontend/assets/sass/bootstrap.scss',
//     'resources/pos/src/frontend/assets/sass/pos.scss'
// ], 'public/css/app.css').version();
