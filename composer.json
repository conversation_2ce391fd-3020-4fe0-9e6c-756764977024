{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "version": "1.0.0", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "brotzka/laravel-dotenv-editor": "^2.1", "guzzlehttp/guzzle": "^7.2", "jenssegers/agent": "^2.6", "laravel/framework": "^9.19", "laravel/sanctum": "^3.0", "laravel/socialite": "^5.6", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.10", "mews/purifier": "^3.3", "opcodesio/log-viewer": "^1.9", "prettus/l5-repository": "^2.8", "razorpay/razorpay": "^2.8", "simplesoftwareio/simple-qrcode": "~4", "spatie/laravel-medialibrary": "^10.0.0", "spatie/laravel-permission": "^5.7", "spatie/laravel-query-builder": "^5.1", "srmklive/paypal": "^3.0", "stancl/tenancy": "^3.6", "stevebauman/location": "^6.5", "stripe/stripe-php": "^10.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "barryvdh/laravel-ide-helper": "^2.12", "beyondcode/laravel-query-detector": "^1.6", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}