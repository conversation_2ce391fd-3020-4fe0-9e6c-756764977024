import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Card, Table } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../shared/tab-title/TabTitle';
import { placeholderText } from '../../shared/sharedMethod';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";
import { fetchLink } from '../../store/action/linkAction';
import HeaderTitle from '../header/HeaderTitle';
import RoundLoader from '../../shared/components/loaders/RoundLoader';

const LinkDetails = (props) => {
    const { isLoading, links, fetchLink } = props;
    const { id } = useParams();

    useEffect(() => {
        fetchLink(id);
    }, []);

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('links.title')} />
            <HeaderTitle title={getFormattedMessage('link.details.title')} />
            {<>
                <div className='pt-5'>
                    <Card>
                        {/* <Card.Header as='h5'>{getFormattedMessage('link.details.title')}</Card.Header> */}
                        <Card.Body className=''>
                            {isLoading ? <RoundLoader /> : <Table responsive>
                                <tbody>
                                    <tr>
                                        <td>
                                            <img src={links[0]?.attributes?.qrcode_image} className="w-25 h-25 " />
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('react-data-table.name.column.title')}</td>
                                        <td className='py-4'>{links[0]?.attributes.url_alias}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('destination.url.title')}</td>
                                        <td className='py-4'>
                                            <span>
                                                <a href={links[0]?.attributes.destination_url} target={"_blank"} className='text-decoration-none text-muted'>
                                                    {links[0]?.attributes.destination_url}
                                                </a>
                                            </span>

                                        </td>
                                    </tr>
                                </tbody>
                            </Table>}
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </MasterLayout>
    )

};


const mapStateToProps = state => {
    const { isLoading, links } = state;
    return { isLoading, links }
};


export default connect(mapStateToProps, { fetchLink })(LinkDetails);
