import React, { useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../shared/table/ReactDataTable';
import { fetchLinks } from '../../store/action/linkAction';
import DeleteLink from './DeleteLink';
import TabTitle from '../../shared/tab-title/TabTitle';
import { getFormattedDate } from '../../shared/sharedMethod';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { placeholderText } from '../../shared/sharedMethod';
import ActionButton from '../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";
import { environment } from "../../config/environment";
import {
    faChartLine,
    faExternalLink,
    faPaste
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { addToast } from '../../store/action/toastAction';
import { toastType } from '../../constants';
import { saveAs } from 'file-saver';
import ImageModal from '../../shared/imageModal/ImageModal';

const Links = ( props ) => {
    const { links, fetchLinks, totalRecord, isLoading } = props;
    const [ deleteModel, setDeleteModel ] = useState( false );
    const [ isDelete, setIsDelete ] = useState( null );
    const DarkMod = localStorage.getItem( 'isDarkMod' );
    const [ showImageSlider, setShowImageSlider ] = useState( {
        display: false,
        src: '',
        name: ""
    } )

    const dispatch = useDispatch()

    const onClickDeleteModel = ( isDelete = null ) => {
        setDeleteModel( !deleteModel );
        setIsDelete( isDelete );
    };

    const closeImageSlider = () => {
        setShowImageSlider( {
            display: false,
            src: '',
            name: ""
        } )
    }

    const onDownloadClick = ( e, url, name, ext ) => {
        e.preventDefault();
        saveAs( url, `${name}.${ext}` );
    }


    const itemsValue = links.length >= 0 && links.map( link => ( {
        date: getFormattedDate( link.attributes.created_at, "d-m-y" ),
        time: moment( link.attributes.created_at ).format( 'LT' ),
        name: link.attributes.url_alias,
        to: link.attributes.destination_url,
        link_name: link.attributes.name,
        url: `${environment.URL}/${link.attributes.url_alias}`,
        qrcode_image: link.attributes.qrcode_image,
        id: link.id
    } ) );


    const onChange = ( filter ) => {
        fetchLinks( filter, true );
    };

    const goToEdit = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/minify-link/edit/' + id;
    };


    const goToDetails = ( id ) => {
        window.location.href = '#/app/minify-link/detail/' + id;
    };

    const copyClickBoard = ( data ) => {
        const unsecuredCopyToClipboard = () => {
            const textArea = document.createElement( "textarea" );
            textArea.value = environment.URL + "/" + data.name;
            document.body.appendChild( textArea );
            textArea.focus();
            textArea.select();
            try {
                document.execCommand( 'copy' );
                dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
            } catch ( err ) {
                dispatch( addToast( { text: `Failed to copy text to clipboard: ${err}`, type: toastType.ERROR } ) )
            }
            document.body.removeChild( textArea );
        }

        if ( window.isSecureContext && navigator.clipboard ) {
            navigator.clipboard.writeText( environment.URL + "/" + data.name );
            dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
        } else {
            unsecuredCopyToClipboard();
        }
    }

    const goToAnalytics = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/minify-link/' + id + '/analytics';
    };

    const columns = [
        {
            name: getFormattedMessage( 'globally.input.name.label' ),
            selector: row => row.link_name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        {row.link_name}
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'globally.input.url.lable' ),
            selector: row => row.url_alias,
            sortField: 'destination_url',
            sortable: true,
            cell: row => {
                return <><div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/minify-link/detail/${row.id}`} className={`${DarkMod === 'true' ? 'text-muted' : 'text-dark'} text-decoration-none`}>
                            {row.to}
                        </Link>
                        <div className='text-primary pe-3'>
                            <FontAwesomeIcon className={"me-1"} width={10} icon={faExternalLink} />
                            <a href={row.url} target={"_blank"} className='text-decoration-none text-primary'>{row.url}</a>
                            <span className='text-blue ms-2 fs-3 cursor-pointer' title={placeholderText( 'globally.copy-link.tool-tip.message' )} onClick={( e ) => { copyClickBoard( row ) }}>
                                <FontAwesomeIcon icon={faPaste} className='text-primary' />
                            </span>
                        </div>
                    </div>
                </div></>
            }
        },
        // {
        //     name: getFormattedMessage('globally.react-table.column.stats.label'),
        //     selector: row => row.name,
        //     sortField: 'stats',
        //     sortable: false,
        //     cell: row => {
        //         return <div className='d-flex cursor-pointer'>
        //             <div className='text-info'><FontAwesomeIcon icon={faChartLine} className="fs-2" onClick={() => goToAnalytics(row)} /></div>
        //         </div>
        //     }
        // },
        {
            name: getFormattedMessage( "qrcode.lable" ),
            selector: row => row.qrcode_image,
            sortField: 'qrcode_image',
            sortable: false,
            cell: row => {
                return <div className='me-2' >
                    <img src={row?.qrcode_image} height='50' width='50' alt='QR Code Image'
                        className='image image-mini image-effect cursor-pointer' onClick={() => setShowImageSlider( {
                            display: true,
                            src: row?.qrcode_image,
                            name: row?.link_name
                        } )} />
                </div>
            }
        },
        {
            name: getFormattedMessage( 'globally.react-table.column.created-date.label' ),
            selector: row => row.date,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div className='mb-1'>{row.time}</div>
                        <div>{row.date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage( 'react-data-table.action.column.label' ),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row =>
                <ActionButton isCopyBtn={false} isAnalytics={true} goToAnalytics={goToAnalytics} onCopybtn={copyClickBoard} item={row} goToEditProduct={goToEdit} isEditMode={true}
                    onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( 'links.title' )} />
            <div className='link-table-columns'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                    ButtonValue={getFormattedMessage( 'links.create.title' )}
                    to='#/app/minify-link/create' totalRows={totalRecord} isLoading={isLoading} />
            </div>
            <DeleteLink onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
            <ImageModal display={showImageSlider} closeSlider={closeImageSlider} onDownloadClick={onDownloadClick} />
        </MasterLayout>
    )
};

const mapStateToProps = ( state ) => {
    const { links, totalRecord, isLoading } = state;
    return { links, totalRecord, isLoading }
};
export default connect( mapStateToProps, { fetchLinks } )( Links );
