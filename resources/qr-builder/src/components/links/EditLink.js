import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import LinkForm from './LinkForm';
import { fetchLink } from '../../store/action/linkAction';
import { useParams } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../components/header/HeaderTitle';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import TopProgressBar from '../../shared/components/loaders/TopProgressBar';
import TabTitle from "../../shared/tab-title/TabTitle";

const EditLink = ( props ) => {
    const { fetchLink, links } = props;
    const { id } = useParams();
    const [ isEdit, setIsEdit ] = useState( false );

    useEffect( () => {
        fetchLink( id );
        setIsEdit( true );
    }, [] );

    const itemsValue = links && links.length === 1 && links.map( link => ( {
        name: link.attributes.name,
        destination_url: link.attributes.destination_url,
        url_alias: link.attributes.url_alias,
        id: link.id
    } ) );

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( 'link.edit.title' )} />
            <HeaderTitle title={getFormattedMessage( 'link.edit.title' )} to='/app/minify-link' />
            {links.length && <LinkForm singleLink={itemsValue} id={id} isEdit={isEdit} />}
        </MasterLayout>
    );
}

const mapStateToProps = ( state ) => {
    const { links } = state;
    return { links }
};

export default connect( mapStateToProps, { fetchLink } )( EditLink );
