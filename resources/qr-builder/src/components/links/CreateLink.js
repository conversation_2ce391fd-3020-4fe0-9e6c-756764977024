import React from 'react';
import { connect, useSelector } from 'react-redux';
import LinkForm from './LinkForm';
import { addLink } from '../../store/action/linkAction';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../components/header/HeaderTitle';
import { Filters } from '../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import { Helmet } from 'react-helmet';

const CreateLink = ( props ) => {
    const { addLink } = props;
    const navigate = useNavigate();
    const { frontSettings } = useSelector( state => state )
    const addLinkData = ( formValue ) => {
        addLink( formValue, navigate, Filters.OBJ );
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText( 'link.create.title' ) + ' | ' + frontSettings?.title} />
            <HeaderTitle title={getFormattedMessage( 'link.create.title' )} to='/app/minify-link' />
            <LinkForm addLinkData={addLinkData} />
        </MasterLayout>
    );
}

export default connect( null, { addLink } )( CreateLink );
