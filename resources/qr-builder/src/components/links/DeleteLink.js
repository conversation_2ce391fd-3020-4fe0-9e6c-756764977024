import React from 'react';
import {connect} from 'react-redux';
import { deleteLink } from '../../store/action/linkAction';
import DeleteModel from '../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../shared/sharedMethod';

const DeleteLink = (props) => {
    const {deleteLink, onDelete, deleteModel, onClickDeleteModel} = props;

    const deleteUserClick = () => {
        deleteLink(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                                         deleteUserClick={deleteUserClick} name={getFormattedMessage('links.title')}/>}
        </div>
    )
};

export default connect(null, {deleteLink})(DeleteLink);
