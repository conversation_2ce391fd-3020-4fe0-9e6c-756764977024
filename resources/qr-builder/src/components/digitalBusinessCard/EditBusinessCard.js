import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../components/header/HeaderTitle';
import { getFormattedMessage } from '../../shared/sharedMethod';
import TopProgressBar from '../../shared/components/loaders/TopProgressBar';
import { fetchBusinessCard, fetchBusinessCardLink } from '../../store/action/digitalBusinessCardsAction';
import BussinessCardTabs from './BussinessCardTabs';
import user from '../../assets/images/avatar.png';
import { environment } from '../../config/environment';

const EditBusinessCard = (props) => {
    const { fetchBusinessCard, businessCard, fetchBusinessCardLink, socialLink } = props;
    const { id } = useParams();
    const [isEdit, setIsEdit] = useState(false);
    const [updateDatas, setUpdateDatas] = useState([])
    const [linkValues, setLinkValues] = useState([
        { icone: environment.URL + '/default-images/socialimages/web.svg', url: '', pivImage: user, name: 'globally.input.website.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/twitter.svg', url: '', pivImage: user, name: 'globally.input.twitter.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/facebook.svg', url: '', pivImage: user, name: 'globally.input.facebook.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/insta.svg', url: '', pivImage: user, name: 'globally.input.instagram.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/reditt.svg', url: '', pivImage: user, name: 'globally.input.raddit.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/tumbir.svg', url: '', pivImage: user, name: 'globally.input.tumblr.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/youtube.svg', url: '', pivImage: user, name: 'globally.input.youtube.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/linkedin.svg', url: '', pivImage: user, name: 'globally.input.linkedin.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/whatsapp.svg', url: '', pivImage: user, name: 'globally.input.whatsapp.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/pinteresht.svg', url: '', pivImage: user, name: 'globally.input.pinterest.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/tiktok.svg', url: '', pivImage: user, name: 'globally.input.tiktok.url.placeholder' }
    ])
    useEffect(() => {
        fetchBusinessCard(id);
        fetchBusinessCardLink(id)
        setIsEdit(true);
    }, []);

    const itemsValue = businessCard && businessCard.length === 1 && businessCard.map(items => ({
        name: items.attributes.name,
        job_title: items.attributes.job_title,
        company: items.attributes.company,
        phone: items.attributes.phone,
        email: items.attributes.email,
        website: items.attributes.website,
        template_id: items.attributes.template_id,
        profile_image: items.attributes.profile_image,
        cover_image: items.attributes.cover_image,
        status: items?.attributes?.status === false ? false : items?.attributes?.status === 0 ? false : true,
        url_alias: items.attributes.url_alias,
        id: items.id
    }));


    return (
        <MasterLayout>
            <TopProgressBar />
            <HeaderTitle title={getFormattedMessage('digital.business.card.edit.title')} to='/app/digital-business-cards' />
            {itemsValue.length === 1 && <BussinessCardTabs singleBusinessCard={itemsValue} ids={id} isEdit={isEdit} updateDatas={updateDatas} setUpdateDatas={setUpdateDatas} socialLink={socialLink} linkValues={linkValues} setLinkValues={setLinkValues} />}
        </MasterLayout>
    );
}

const mapStateToProps = (state) => {
    const { businessCard, socialLink } = state;
    return { businessCard, socialLink }
};

export default connect(mapStateToProps, { fetchBusinessCard, fetchBusinessCardLink })(EditBusinessCard);
