import React, { useEffect, useState } from 'react'
import user from '../../assets/images/avatar.png';
import { getAvatarName, getFormattedMessage, isValidHttpUrl, placeholderText } from '../../shared/sharedMethod';
import SocialLinkPicker from '../../shared/image-picker/SocialLinkPicker';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import ModelFooter from '../../shared/components/modelFooter';
import { useNavigate, useParams } from 'react-router';
import { editBusinessCardLink } from '../../store/action/digitalBusinessCardsAction';
import { useDispatch } from 'react-redux';
import { environment } from '../../config/environment';
import TabTitle from '../../shared/tab-title/TabTitle';

const BusinessCradLinks = (props) => {
    const { singleBusinessCard, updateDatas, socialLink, linkValues } = props
    const { id } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [updateData, setUpdateData] = useState([])
    const [linkValue, setLinkValue] = useState([
        { icone: environment.URL + '/default-images/socialimages/web.svg', url: '', pivImage: user, name: 'globally.input.website.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/twitter.svg', url: '', pivImage: user, name: 'globally.input.twitter.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/facebook.svg', url: '', pivImage: user, name: 'globally.input.facebook.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/insta.svg', url: '', pivImage: user, name: 'globally.input.instagram.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/reditt.svg', url: '', pivImage: user, name: 'globally.input.raddit.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/tumbir.svg', url: '', pivImage: user, name: 'globally.input.tumblr.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/youtube.svg', url: '', pivImage: user, name: 'globally.input.youtube.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/linkedin.svg', url: '', pivImage: user, name: 'globally.input.linkedin.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/whatsapp.svg', url: '', pivImage: user, name: 'globally.input.whatsapp.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/pinteresht.svg', url: '', pivImage: user, name: 'globally.input.pinterest.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/tiktok.svg', url: '', pivImage: user, name: 'globally.input.tiktok.url.placeholder' }
    ])

    const [disabled, setDisabled] = useState(true);

    useEffect(() => {
        setUpdateData(updateDatas)
        setLinkValue(linkValues)
    }, [])

    useEffect(() => {
        if (socialLink && socialLink.length >= 1) {
            setUpdateData(socialLink)
        }
    }, [socialLink])

    useEffect(() => {
        if (socialLink && updateData && updateData.length >= 1) {
            let data = []
            linkValue.map((items, index) => {
                const getData = updateData.filter((item) => item.default_link === index)
                if (getData.length === 1) {
                    data.push({ icone: items.icone, url: getData[0].link, pivImage: items.pivImage, name: items.name, id: getData[0].id })
                } else {
                    data.push({ icone: items.icone, url: items.url, pivImage: items.pivImage, name: items.name })
                }
            })
            let getData = data.filter((item, index) => index <= 10)
            updateData.map((items) => {
                if (data.length >= 1) {
                    if (items.default_link >= 11) {
                        getData.push({ icone: items.icon, url: items.link, pivImage: items.icon, name: '', id: items.id })
                    }
                }
            })
            setLinkValue(getData)
        }
    }, [updateData])

    const onChangeInput = (e, index) => {
        e.preventDefault();
        const { name, value } = e.target;
        if (e.target.name?.includes("socialURL")) {
            let data = []
            linkValue.map((items, indexs) => {
                if (indexs === index) {
                    data.push({
                        icone: items.icone,
                        url: value?.replace(/\s+/g, '-'),
                        pivImage: items.pivImage,
                        id: items.id
                    })
                } else {
                    data.push(items)
                }
            })
            setLinkValue(data)
            setDisabled(false)
        }
        setErrors({});
    };

    const handleProfileImageChanges = (e, index) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === 'image/jpeg' || file.type === 'image/png') {
                let data = []
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    linkValue.map((items, indexs) => {
                        if (indexs === index) {
                            data.push({
                                icone: fileReader.result,
                                url: items.url,
                                pivImage: file,
                                id: items.id,
                            })
                        } else {
                            data.push(items)
                        }
                    })
                    setLinkValue(data)
                    setDisabled(false)
                };
                fileReader.readAsDataURL(file);
                setErrors('');
            }
        }
    };

    const addSocialURL = (e) => {
        e.preventDefault();
        let data = []
        linkValue.map((items, indexs) => {
            data.push(items)
        })
        data.push({ icone: user, url: '', pivImage: user, class: 'text-primary' })
        setLinkValue(data)
        setDisabled(false)
    }

    const onClickDelete = (e, index) => {
        e.preventDefault();
        const data = linkValue.filter((items, indexs) => indexs !== index)
        setLinkValue(data)
        setDisabled(false)
    }

    const prepareFormData = (data) => {
        let prepareValue = []
        data?.map((items, index) => {
            prepareValue?.push({ icon: items?.icone?.includes("base64") ? items?.icone : items?.icone?.includes('uploads') ? items?.icone : '', link: items?.url, default_link: index, id: items.id ? items.id : null })

        })
        const fromValue = {
            business_card_id: id,
            social_links: prepareValue.filter((items) => items.link !== "")
        }
        return fromValue
    }

    const [errors, setErrors] = useState([]);
    const validator = () => {
        let isValid = false;
        let errorFind = []
        linkValue.map((items, index) => {
            if (items.url.length >= 1) {
                if (isValidHttpUrl(items.url) === false) {
                    errorFind.push(index)
                }
            }
        })
        if (errorFind.length >= 1) {
            isValid = false
        } else {
            isValid = true
        }
        setErrors(errorFind)
        return isValid
    }

    const onSubmit = (event) => {
        event.preventDefault();
        const getValid = validator()
        if (getValid) {
            dispatch(editBusinessCardLink(prepareFormData(linkValue), navigate));
        }
    };

    return (
        <div className='card'>
            {singleBusinessCard && <TabTitle title={placeholderText("digital.business.card.url.title")} />}
            <div className='card-body'>
                <div className='d-flex justify-content-end align-items-center mb-4'>
                    <button className='btn btn-primary' onClick={e => addSocialURL(e)}>
                        {getFormattedMessage("globally.add-btn")}
                    </button>
                </div>
                <div className='row align-items-center'>
                    {linkValue && linkValue.map((items, index) => {
                        const profileAvtarName = getAvatarName(items && items.url === '' && items.url);
                        return (
                            <div className='col-lg-6 col-12 row align-items-center' key={index * 2}>
                                <div className='col-2 mb-3 d-flex justify-content-end'>
                                    {index + 1 > 11 ? <SocialLinkPicker imagePreviewUrl={items.icone}
                                        user={user} isEdite={index + 1 > 11} index={index}
                                        avtarName={profileAvtarName} handleImageChange={handleProfileImageChanges} />
                                        :
                                        <img src={items.icone} className="w-28px" />
                                    }
                                </div>
                                <div className='col-9 mb-3' key={index * 1}>
                                    <input type='url' name={`socialURL${index + 1}`} value={items?.url?.replace(/\s+/g, '-')}
                                        placeholder={items.name ? placeholderText(items.name) : ''}
                                        className='form-control' autoFocus={false}
                                        onChange={(e) => onChangeInput(e, index)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{
                                            errors?.length >= 1 && errors.includes(index) ? getFormattedMessage("globally.input.URL.valid.validate.label") : null}</span>
                                </div>
                                {index + 1 > 11 && <div className='col-1 mb-3' key={index * 3}>
                                    <button title={placeholderText('globally.delete.tooltip.label')}
                                        className='btn px-1 pe-0 text-danger fs-3 border-0'
                                        onClick={(e) => { onClickDelete(e, index) }}>
                                        <FontAwesomeIcon icon={faTrash} />
                                    </button>
                                </div>}
                            </div>
                        )
                    })}

                </div>
                <ModelFooter onEditRecord={singleBusinessCard} onSubmit={onSubmit} editDisabled={disabled}
                    link='/app/digital-business-cards' addDisabled={disabled} />
            </div>
        </div>
    )
}

export default BusinessCradLinks
