import React, { useEffect, useState } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../shared/table/ReactDataTable';
import DeleteBusinessCard from './DeleteBusinessCard';
import TabTitle from '../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedDate } from '../../shared/sharedMethod';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { placeholderText } from '../../shared/sharedMethod';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";
import { environment } from "../../config/environment";
import { addToast } from '../../store/action/toastAction';
import { toastType } from '../../constants';
import { saveAs } from 'file-saver';
import ImageModal from '../../shared/imageModal/ImageModal';
import { getBusinessCards, updateStatusBusinessCard } from '../../store/action/digitalBusinessCardsAction';
import ActionButton from '../../shared/action-buttons/ActionButton';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPaste } from '@fortawesome/free-solid-svg-icons';
import { Link, useNavigate } from "react-router-dom";

const DigitalBusinessCard = ( props ) => {
    const { totalRecord, isLoading, updateStatusBusinessCard, frontSettings } = props;
    const [ deleteModel, setDeleteModel ] = useState( false );
    const [ isDelete, setIsDelete ] = useState( null );
    const DarkMod = localStorage.getItem( 'isDarkMod' );
    const [ isStatus, setIsStatus ] = useState( false );
    const navigate = useNavigate()
    const [ showImageSlider, setShowImageSlider ] = useState( {
        display: false,
        src: '',
        name: ""
    } )
    const dispatch = useDispatch()
    const { businessCard } = useSelector( state => state )

    useEffect( () => {
        if ( frontSettings.allow_create_business_card === false ) {
            navigate( "/app/dashboard" )
        }
    } )

    const onClickDeleteModel = ( items ) => {
        setDeleteModel( !deleteModel );
        setIsDelete( items );
    };

    const closeImageSlider = () => {
        setShowImageSlider( {
            display: false,
            src: '',
            name: ""
        } )
    }

    const onDownloadClick = ( e, url, name, ext ) => {
        e.preventDefault();
        saveAs( url, `${name}.${ext}` );
    }


    const itemsValue = businessCard.length >= 0 && businessCard.map( card => ( {
        profile_image: card?.attributes?.profile_image,
        name: card?.attributes?.name,
        email: card?.attributes?.email,
        website: `${environment.URL}/${card?.attributes?.url_alias}`,
        date: getFormattedDate( card?.attributes?.created_at, "d-m-y" ),
        status: card?.attributes?.status === false ? false : card?.attributes?.status === 0 ? false : true,
        id: card?.id,
        url_alias: card?.attributes?.url_alias,
        job_title: card?.attributes?.job_title,
        company: card?.attributes?.company,
        phone: card?.attributes?.phone,
        template_id: card?.attributes?.template_id
    } ) );

    // const itemsValue = []
    const onChange = ( filter ) => {
        dispatch( getBusinessCards( filter ) )
    };

    const goToEdit = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/digital-business-cards/edit/' + id;
    };

    const goToDetails = ( id ) => {
        window.location.href = '#/app/minify-link/detail/' + id;
    };

    const copyClickBoard = ( data ) => {
        const unsecuredCopyToClipboard = () => {
            const textArea = document.createElement( "textarea" );
            textArea.value = environment.URL + '/' + data.url_alias;
            document.body.appendChild( textArea );
            textArea.focus();
            textArea.select();
            try {
                document.execCommand( 'copy' );
                dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
            } catch ( err ) {
                dispatch( addToast( { text: `Failed to copy text to clipboard: ${err}`, type: toastType.ERROR } ) )
            }
            document.body.removeChild( textArea );
        }

        if ( window.isSecureContext && navigator.clipboard ) {
            navigator.clipboard.writeText( environment.URL + '/' + data.url_alias );
            dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
        } else {
            unsecuredCopyToClipboard();
        }
    }

    const goToAnalytics = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/digital-business-cards/' + id + '/analytics';
    };

    const prepareFormData = ( items ) => {
        const formData = new FormData();
        formData.append( 'name', items.name );
        formData.append( 'url_alias', items.url_alias );
        formData.append( 'website', items.website );
        formData.append( 'job_title', items.job_title );
        formData.append( 'company', items.company );
        formData.append( 'phone', items.phone );
        formData.append( 'email', items.email );
        formData.append( 'template_id', items.template_id );
        formData.append( 'status', items.status === true ? 0 : 1 );
        return formData;
    };

    const onCheckedStatus = ( e, id, items ) => {
        setIsStatus( true )
        e.preventDefault();
        updateStatusBusinessCard( id, prepareFormData( items ), setIsStatus )
    };

    const goToDetailScreen = ( id ) => {
        window.location.href = '#/app/digital-business-cards/detail/' + id;
    }

    const columns = [
        {
            name: getFormattedMessage( 'react-data-table.name.column.title' ),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/users/detail/${row.id}`}>
                            {row.profile_image ?
                                <img src={row.profile_image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName( row.name )}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row.id}`} className='text-decoration-none'>{row.name}</Link>
                        <span>{row.email}</span>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'alias-url.input.placeholder.label' ),
            selector: row => row.website,
            sortField: 'url_alias',
            sortable: false,
            cell: row => {
                return <div className='text-primary pe-3 cursor-pointer'>
                    <a href={row.website} target={"_blank"} className={"text-decoration-none"}>{row.website}</a>
                    <span className='text-blue ms-2 fs-3 cursor-pointer' title={placeholderText( 'globally.copy-link.tool-tip.message' )} onClick={( e ) => { copyClickBoard( row ) }}>
                        <FontAwesomeIcon icon={faPaste} className='text-primary' />
                    </span>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'globally.react-table.column.status.label' ),
            selector: row => row.status,
            cell: row => {
                return (
                    <div className="col-md-4 d-flex align-items-center mt-4 ">
                        <label className="form-check form-switch form-switch-sm cursor-pointer">
                            <input autoComplete="off" name="paypal" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                checked={row.status}
                                onChange={( e ) => onCheckedStatus( e, row.id, row )}
                            />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                )
            }
        },
        {
            name: getFormattedMessage( 'globally.react-table.column.created-date.label' ),
            selector: row => row.date,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        {/* <div className='mb-1'>{row.date}</div> */}
                        <div>{row.date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage( 'react-data-table.action.column.label' ),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row =>
                <ActionButton item={row} isEditMode={true}
                    isAnalytics={true}
                    isCopyBtn={false}
                    isViewIcon={true}
                    isDeleteMode={true} goToEditProduct={goToEdit}
                    onClickDeleteModel={onClickDeleteModel}
                    goToAnalytics={goToAnalytics}
                    goToDetailScreen={goToDetailScreen}
                    onCopybtn={copyClickBoard} />

            // <ActionDropDownButton item={row} goToEditProduct={goToEdit} isEditMode={true}
            //     onClickDeleteModel={onClickDeleteModel}
            //     isAnalytics={true}
            //     isCopyBtn={true}
            //     goToAnalytics={goToAnalytics}
            //     onCopybtn={copyClickBoard}
            // />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( "digital.business.card.title" )} />
            <div className='business-card-table-user'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                    ButtonValue={getFormattedMessage( "digital.business.card.add.title" )} isStatus={isStatus}
                    to='#/app/digital-business-cards/create' totalRows={totalRecord} isLoading={isLoading} />
            </div>
            <DeleteBusinessCard onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
            <ImageModal display={showImageSlider} closeSlider={closeImageSlider} onDownloadClick={onDownloadClick} />
        </MasterLayout>
    )
};

const mapStateToProps = ( state ) => {
    const { totalRecord, isLoading, frontSettings } = state;
    return { totalRecord, isLoading, frontSettings }
};
export default connect( mapStateToProps, { updateStatusBusinessCard } )( DigitalBusinessCard );
