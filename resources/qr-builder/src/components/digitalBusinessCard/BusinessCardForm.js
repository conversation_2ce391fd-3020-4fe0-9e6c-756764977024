import React, { useEffect, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Dropdown, DropdownButton, InputGroup } from 'react-bootstrap-v5';
import * as EmailValidator from 'email-validator';
import { getFormattedMessage, placeholderText, numValidate, isValidHttpUrl, getAvatarName } from '../../shared/sharedMethod';
import ModelFooter from '../../shared/components/modelFooter';
import { environment } from "../../config/environment"
import parsePhoneNumberFromString from 'libphonenumber-js';
import countryData from 'country-telephone-data';
import ImagePicker from '../../shared/image-picker/ImagePicker';
import user from '../../assets/images/avatar.png';
import { editBusinessCard } from '../../store/action/digitalBusinessCardsAction';
import CountryList from 'country-list-with-dial-code-and-flag'
import TabTitle from '../../shared/tab-title/TabTitle';

const BusinessCardForm = (props) => {
    const { addBusinessCardData, id, singleBusinessCard, editBusinessCard } = props;
    const navigate = useNavigate();
    const flagData = CountryList.getAll()

    const [businessCardValue, setBusinessCardValue] = useState({
        name: '',
        url_alias: '',
        website: '',
        job: '',
        company: '',
        phone: '',
        email: '',
        template_id: 1,
        status: false,
        profile_image: '',
        cover_image: '',
    });
    const [countryCode, setCountryCode] = useState("+91")
    const [phoneNumber, setPhoneNumber] = useState("")
    const [errors, setErrors] = useState({
        name: '',
        url_alias: '',
        job: '',
        company: '',
        phone: '',
        email: '',
        website: '',
        template_id: '',
        status: ''
    });

    const coverAvtarName = getAvatarName(singleBusinessCard && singleBusinessCard[0]?.cover_image === '' && singleBusinessCard[0].name);
    const coverNewImg = singleBusinessCard && singleBusinessCard[0]?.cover_image === null && coverAvtarName;
    const profileAvtarName = getAvatarName(singleBusinessCard && singleBusinessCard[0]?.profile_image === '' && singleBusinessCard[0].name);
    const profileNewImg = singleBusinessCard && singleBusinessCard[0]?.profile_image === null && profileAvtarName;
    const [profileImagePreviewUrl, setProfileImagePreviewUrl] = useState(profileNewImg && profileNewImg);
    const [selectProfileImg, setProfileSelectImg] = useState(null);
    const [imagePreviewUrl, setImagePreviewUrl] = useState(coverNewImg && coverNewImg);
    const [selectImg, setSelectImg] = useState(null);

    const countryFlag = flagData?.filter(d => d?.dial_code === countryCode)

    useEffect(() => {
        if (singleBusinessCard?.length === 1) {
            setBusinessCardValue({
                name: singleBusinessCard ? singleBusinessCard[0]?.name : '',
                url_alias: singleBusinessCard ? singleBusinessCard[0]?.url_alias : '',
                website: singleBusinessCard ? singleBusinessCard[0]?.website : '',
                job: singleBusinessCard ? singleBusinessCard[0]?.job_title : '',
                company: singleBusinessCard ? singleBusinessCard[0]?.company : '',
                phone: singleBusinessCard ? singleBusinessCard[0]?.phone : '',
                email: singleBusinessCard ? singleBusinessCard[0]?.email : '',
                template_id: singleBusinessCard ? singleBusinessCard[0]?.template_id : 1,
                status: singleBusinessCard ? singleBusinessCard[0]?.status : false,
                profile_image: singleBusinessCard ? singleBusinessCard[0]?.cover_image : '',
                cover_image: singleBusinessCard ? singleBusinessCard[0]?.profile_image : '',
            })

            setCountryCode(singleBusinessCard[0]?.phone ? singleBusinessCard[0]?.phone?.split(" ")[0] :
                "+91")
            setPhoneNumber(singleBusinessCard[0]?.phone ? singleBusinessCard[0]?.phone?.split(" ")[1] : "")
            setImagePreviewUrl(singleBusinessCard ? singleBusinessCard[0]?.cover_image : user);
            setProfileImagePreviewUrl(singleBusinessCard ? singleBusinessCard[0]?.profile_image : user);
        }
    }, [])

    const disabled = selectImg ? false : selectProfileImg ? false : singleBusinessCard && singleBusinessCard[0].name === businessCardValue.name
        && singleBusinessCard[0].company === businessCardValue.company
        && singleBusinessCard[0].email === businessCardValue.email
        && singleBusinessCard[0].job_title === businessCardValue.job
        && singleBusinessCard[0].phone === businessCardValue.phone
        && singleBusinessCard[0].status === businessCardValue.status
        && singleBusinessCard[0].template_id === businessCardValue.template_id
        && singleBusinessCard[0].url_alias?.replace(/ /g, '') === businessCardValue.url_alias.replace(/ /g, '')
        && singleBusinessCard[0].website?.replace(/ /g, '') === businessCardValue.website?.replace(/ /g, '')

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        const countryName = countryData.allCountries.find(c => `+${c?.dialCode}` === countryCode);
        const parsedNumber = parsePhoneNumberFromString(businessCardValue?.phone?.split(' ')?.join(''), countryName?.iso2)?.isValid()
        if (!businessCardValue['url_alias'] || businessCardValue['url_alias']?.trim()?.length === 0) {
            errorss['url_alias'] = getFormattedMessage('alias.input.validate.label');
        } else if (!businessCardValue['website'] || businessCardValue['website']?.trim()?.length === 0) {
            errorss['website'] = getFormattedMessage("website.input.validate.label");
        } else if (isValidHttpUrl(businessCardValue.website) === false) {
            errorss['website'] = getFormattedMessage("website.input.valid.validate.label");
        } else if (!businessCardValue['name'] || businessCardValue['name']?.trim()?.length === 0) {
            errorss['name'] = getFormattedMessage('user.input.first-name.validate.label');
        } else if (parsedNumber !== true) {
            errorss.phone = getFormattedMessage("globally.input.phonenumber.validate.label")
        } else if (businessCardValue.email?.trim()?.length === 0) {
            errorss.email = getFormattedMessage("globally.input.email.validate.label")
        } else if (!EmailValidator.validate(businessCardValue.email?.trim())) {
            errorss.email = getFormattedMessage("user.input.email.valid.validate.label")
        } else if (!businessCardValue['job'] || businessCardValue['job']?.trim()?.length === 0) {
            errorss['job'] = getFormattedMessage('job-title.input.validate.label');
        } else if (!businessCardValue['company'] || businessCardValue['company']?.trim()?.length === 0) {
            errorss['company'] = getFormattedMessage('company.input.validate.label');
        } else if (!businessCardValue['template_id']) {
            errorss['template_id'] = getFormattedMessage('user.input.first-name.validate.label');
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };
    const onChangeCountryCode = (code, name) => {
        setCountryCode(code)
        setErrors('')
        setBusinessCardValue(inputs => ({
            ...inputs,
            [name]: `${code} ${phoneNumber.replace(/ /g, "")}`
        }))
    }
    const onChangeInput = (e) => {
        e.preventDefault();
        setBusinessCardValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    };


    const onChangeAliasInput = (e) => {
        e.preventDefault();
        setBusinessCardValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    }

    const onPasswordProtection = (e) => {
        const check = e.target.checked
        if (check) {
            setBusinessCardValue(inputs => ({ ...inputs, status: true }))
        } else {
            setBusinessCardValue(inputs => ({ ...inputs, status: false }))
        }
        setErrors('')
    };


    const handleImageChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === 'image/jpeg' || file.type === 'image/png') {
                setSelectImg(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setImagePreviewUrl(fileReader.result);
                };
                fileReader.readAsDataURL(file);
                setErrors('');
            }
        }
    };

    const handleProfileImageChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === 'image/jpeg' || file.type === 'image/png') {
                setProfileSelectImg(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setProfileImagePreviewUrl(fileReader.result);
                };
                fileReader.readAsDataURL(file);
                setErrors('');
            }
        }
    };

    const prepareFormData = (data) => {
        const formData = new FormData();
        formData.append('name', data.name);
        formData.append('url_alias', data.url_alias?.replace(/\s/g, ''));
        formData.append('website', data.website?.replace(/\s+/g, '-'));
        formData.append('job_title', data.job);
        formData.append('company', data.company);
        formData.append('phone', "+" + data.phone.split("+").pop());
        formData.append('email', data.email);
        formData.append('status', data.status === false ? 0 : 1);
        formData.append('template_id', data.template_id);
        if (selectProfileImg) {
            formData.append('profile_image', data.profile_image);
        }
        if (selectImg) {
            formData.append('cover_image', data.cover_image);
        }
        return formData;
    };


    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation();
        businessCardValue.profile_image = selectProfileImg;
        businessCardValue.cover_image = selectImg;
        if (singleBusinessCard && valid) {
            if (!disabled) {
                businessCardValue.profile_image = selectProfileImg;
                businessCardValue.cover_image = selectImg;
                editBusinessCard(id, prepareFormData(businessCardValue), navigate);
            }
        } else {
            if (valid) {
                businessCardValue.profile_image = selectProfileImg;
                businessCardValue.cover_image = selectImg;
                setBusinessCardValue(businessCardValue)
                addBusinessCardData(prepareFormData(businessCardValue));
            }
        }
    };

    return (
        <div className='card'>
            <div className='card-body'>
                {singleBusinessCard && <TabTitle title={placeholderText("digital.business.card.from.title")} />}
                <Form>
                    <div className='row'>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("URL.alias.title")}:<span className="required" />
                            </label>
                            <InputGroup>
                                <InputGroup.Text>{environment.URL}</InputGroup.Text>
                                <input type='text' name='url_alias' value={businessCardValue?.url_alias?.replace(/\s/g, '')}
                                    placeholder={placeholderText("alias-url.input.placeholder.label")}
                                    className='form-control' autoFocus={true}
                                    onChange={(e) => onChangeAliasInput(e)} /><br />
                            </InputGroup>
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['url_alias'] ? errors['url_alias'] : null}</span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("qrcode.website-url.title")}:<span className="required" />
                            </label>
                            <input type='url' name='website' value={businessCardValue.website?.replace(/\s+/g, '-')}
                                placeholder={placeholderText("qrcode.website-url.title")}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['website'] ? errors['website'] : null}</span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("globally.input.name.label")}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={businessCardValue.name || ""}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText("user.input.name.placeholder.label")}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['name'] ? errors['name'] : null}
                            </span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label className="form-label">
                                {getFormattedMessage("globally.input.phone.lable")}:<span className="required" />
                            </label>
                            <InputGroup className="country_code_dropdown">
                                <DropdownButton
                                    variant="primary p-3"
                                    title={<span>
                                        <img className="thumbnail-image"
                                            src={`https://flagcdn.com/16x12/${countryFlag[0]?.data?.code?.toLowerCase()}.png`}
                                            srcSet={`https://flagcdn.com/32x24/${countryFlag[0]?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${countryFlag[0]?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12}
                                            alt={countryFlag[0]?.data?.code}
                                        /> {countryCode}
                                    </span>}
                                    id="input-group-dropdown-1"
                                >
                                    {
                                        flagData.map((d, i) => {
                                            return <Dropdown.Item key={i + 1} onClick={() => onChangeCountryCode(d.data.dial_code, "phone")} value={d.data.dial_code}><img src={`https://flagcdn.com/16x12/${d?.data?.code?.toLowerCase()}.png`} srcSet={`https://flagcdn.com/32x24/${d?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${d?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12} alt="South Africa"
                                            /> {d.data.dial_code}</Dropdown.Item>
                                        })
                                    }
                                </DropdownButton>
                                <Form.Control
                                    type='text' name='phone' value={phoneNumber}
                                    pattern="^(?:\(\d{3}\)|\d{3})[- ]?\d{3}[- ]?\d{4}$"
                                    className='form-control'
                                    placeholder={placeholderText("globally.input.phone-number.placeholder.label")}
                                    onKeyPress={(event) => numValidate(event)}
                                    onChange={(e) => {
                                        setPhoneNumber(e.target.value.replace(/ /g, ""))
                                        setBusinessCardValue(inputs => ({
                                            ...inputs,
                                            [e.target.name]: `${countryCode} ${e.target.value.replace(/ /g, "")}`
                                        }))
                                    }}
                                />
                            </InputGroup>
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['phone'] ? errors['phone'] : null}</span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("globally.input.email.label")}:<span className="required" />
                            </label>
                            <input
                                type='text'
                                name='email'
                                value={businessCardValue.email}
                                placeholder={placeholderText("globally.input.email.placeholder")}
                                className='form-control'
                                autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                            />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['email'] ? errors['email'] : null}
                            </span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label className="form-label">
                                {getFormattedMessage("globally.input.job-title.label")}:<span className="required" />
                            </label>
                            <input type='text' name='job' value={businessCardValue.job || ""}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText("globally.input.job-title.label")}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['job'] ? errors['job'] : null}
                            </span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label className="form-label">
                                {getFormattedMessage("globally.input.company.label")}:<span className="required" />
                            </label>
                            <input type='text' name='company' value={businessCardValue.company || ""}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText("globally.input.company.label")}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['company'] ? errors['company'] : null}
                            </span>
                        </div>
                        <div className="col-md-6 d-flex align-items-center mb-3">
                            <label className="form-check form-switch form-switch-sm cursor-pointer pt-md-5 pt-0">
                                <input autoComplete="off" name="password_protection" data-id="704" className="form-check-input admin-status cursor-pointer" checked={businessCardValue.status} type="checkbox" value={businessCardValue.status}
                                    onChange={(e) => onPasswordProtection(e)} />
                                {getFormattedMessage("input.template.status.label")}
                                <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                            </label>
                        </div>
                        {/* image sec  */}
                        <div className='col-md-3 mb-3'>
                            <div className='mb-4'>
                                <ImagePicker imageTitle={'header.profile-menu.profile.label'} imagePreviewUrl={profileImagePreviewUrl}
                                    user={user}
                                    avtarName={profileAvtarName} handleImageChange={handleProfileImageChanges} />
                            </div>
                        </div>
                        <div className='col-md-3 mb-3'>
                            <div className='mb-4'>
                                <ImagePicker imageTitle={"globally.input.cover-image.lable"} imagePreviewUrl={imagePreviewUrl}
                                    user={user}
                                    avtarName={coverAvtarName} handleImageChange={handleImageChanges} />
                            </div>
                        </div>
                        <div className='col-md-12 mb-3 template-hover-sec'>
                            <label className="form-label">
                                {getFormattedMessage("input.template.label")}:<span className="required" />
                            </label>
                            <div className='row'>
                                <div className='col-md-2 col-6 mb-3'>
                                    <div className={`image-wrap ${businessCardValue.template_id === 1 ? 'active' : ''}`} onClick={(e) => { setBusinessCardValue(inputs => ({ ...inputs, template_id: 1 })) }}>
                                        <img src={environment.URL + '/default-images/templates/1.png'} />
                                    </div>
                                </div>
                                <div className='col-md-2 col-6 mb-3'>
                                    <div className={`image-wrap ${businessCardValue.template_id === 2 ? 'active' : ''}`} onClick={(e) => { setBusinessCardValue(inputs => ({ ...inputs, template_id: 2 })) }}>
                                        <img src={environment.URL + '/default-images/templates/2.png'} />
                                    </div>
                                </div>
                                <div className='col-md-2 col-6 mb-3'>
                                    <div className={`image-wrap ${businessCardValue.template_id === 3 ? 'active' : ''}`} onClick={(e) => { setBusinessCardValue(inputs => ({ ...inputs, template_id: 3 })) }}>
                                        <img src={environment.URL + '/default-images/templates/3.png'} />
                                    </div>
                                </div>
                                <div className='col-md-2 col-6 mb-3'>
                                    <div className={`image-wrap ${businessCardValue.template_id === 4 ? 'active' : ''}`} onClick={(e) => { setBusinessCardValue(inputs => ({ ...inputs, template_id: 4 })) }}>
                                        <img src={environment.URL + '/default-images/templates/4.png'} />
                                    </div>
                                </div>
                                <div className='col-md-2 col-6 mb-3'>
                                    <div className={`image-wrap ${businessCardValue.template_id === 5 ? 'active' : ''}`} onClick={(e) => { setBusinessCardValue(inputs => ({ ...inputs, template_id: 5 })) }}>
                                        <img src={environment.URL + '/default-images/templates/5.png'} />
                                    </div>
                                </div>
                            </div>
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['template_id'] ? errors['template_id'] : null}
                            </span>
                        </div>
                        <ModelFooter onEditRecord={singleBusinessCard} onSubmit={onSubmit} editDisabled={disabled}
                            link='/app/digital-business-cards' addDisabled={!businessCardValue.url_alias} />
                    </div>
                </Form>
            </div>
        </div>
    )
};

const mapStateToProps = (state) => {
    const { roles } = state;
    return { roles }
};

export default connect(mapStateToProps, { editBusinessCard })(BusinessCardForm);

