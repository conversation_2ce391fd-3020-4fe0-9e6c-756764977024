import React, { useState } from 'react'
import { Tab, Tabs } from 'react-bootstrap'
import { connect } from 'react-redux';
import { getFormattedMessage } from '../../shared/sharedMethod';
import BusinessCardForm from './BusinessCardForm';
import BusinessCradLinks from './BusinessCradLinks';
import { useParams } from 'react-router';

const WebsiteSettings = (props) => {
    const { singleBusinessCard, ids, isEdit, updateDatas, setUpdateDatas, socialLink, linkValues, setLinkValues } = props
    const [key, setKey] = useState("form");
    const { id } = useParams();

    return (
        <>
            <Tabs defaultActiveKey='form' id='uncontrolled-tab-example' onSelect={(k) => setKey(k)}
                className='mt-7 mb-0'>
                <Tab eventKey='form' title={getFormattedMessage("digital.business.card.from.title")}
                    tabClassName='position-relative m-0 mb-4 me-3'>
                    <div className='w-100 mx-auto'>
                        {key === 'form' && <BusinessCardForm singleBusinessCard={singleBusinessCard} id={ids} isEdit={isEdit}
                            setUpdateDatas={setUpdateDatas} updateDatas={updateDatas}
                            setLinkValues={setLinkValues} linkValues={linkValues}
                        />}
                    </div>
                </Tab>
                <Tab eventKey='links' title={getFormattedMessage("digital.business.card.url.title")}
                    tabClassName='position-relative m-0 mb-4 me-3'>
                    <div className='w-100 mx-auto'>
                        {key === 'links' && <BusinessCradLinks singleBusinessCard={singleBusinessCard} id={id} isEdit={isEdit}

                            updateDatas={updateDatas} setUpdateDatas={setUpdateDatas} socialLink={socialLink} linkValues={linkValues} setLinkValues={setLinkValues}
                        />}
                    </div>
                </Tab>
            </Tabs>

        </>
    )
}
const mapStateToProps = (state) => {
    const { allConfigData } = state;
    return { allConfigData }
};

export default connect(mapStateToProps, {})(WebsiteSettings);
