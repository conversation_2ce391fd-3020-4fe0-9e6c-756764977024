import React from 'react';
import { connect, useSelector } from 'react-redux';
import BusinessCardForm from './BusinessCardForm';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../components/header/HeaderTitle';
import { Filters } from '../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import { Helmet } from 'react-helmet';
import { addBusinessCard } from '../../store/action/digitalBusinessCardsAction';

const CreateBusinessCard = (props) => {
    const { addBusinessCard } = props;
    const navigate = useNavigate();
    const { frontSettings } = useSelector(state => state)
    const addBusinessCardData = (formValue) => {
        addBusinessCard(formValue, navigate, Filters.OBJ);
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText('digital.business.card.add.title') + ' | ' + frontSettings?.title} />
            <HeaderTitle title={getFormattedMessage('digital.business.card.add.title')} to='/app/digital-business-cards' />
            <BusinessCardForm addBusinessCardData={addBusinessCardData} />
        </MasterLayout>
    );
}

export default connect(null, { addBusinessCard })(CreateBusinessCard);
