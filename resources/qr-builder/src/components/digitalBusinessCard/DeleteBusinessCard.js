import React from 'react';
import { connect } from 'react-redux';
import DeleteModel from '../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { deleteBusinessCard } from '../../store/action/digitalBusinessCardsAction';

const DeleteBusinessCard = (props) => {
    const { deleteBusinessCard, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteBusinessCardClick = () => {
        deleteBusinessCard(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteBusinessCardClick} name={getFormattedMessage("single.digital.business.card.title")} />}
        </div>
    )
};

export default connect(null, { deleteBusinessCard })(DeleteBusinessCard);
