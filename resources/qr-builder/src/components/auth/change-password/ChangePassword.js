import React, { useState } from "react";
import { Form, Modal } from "react-bootstrap-v5";
import { connect, useSelector } from "react-redux";
import { onChangePassword } from "../../../store/action/changePasswordAction";
import {
    getFormattedMessage,
    placeholderText,
} from "../../../shared/sharedMethod";
import { useNavigate } from "react-router";
import { deleteAccountAction } from "../../../store/action/authAction";
import PasswordHandling from "../../../shared/password-handler/PasswordHandling";
import ConfrimPasswordHandling from "../../../shared/password-handler/ConfrimPasswordHandling";

const ChangePassword = (props) => {
    const {
        deleteModel,
        onClickDeleteModel,
        onChangePassword,
        isDeleteAccount,
        onDeleteAccount,
        deleteAccountAction,
    } = props;

    const navigate = useNavigate();

    const [passwordInputs, setPasswordInputs] = useState({
        current_password: "",
        new_password: "",
        confirm_password: "",
    });

    const [errors, setErrors] = useState({
        current_password: "",
        new_password: "",
        confirm_password: "",
    });

    const handleChangePassword = (e) => {
        setPasswordInputs((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value.trim(),
        }));
        setErrors("");
    };

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (
            !passwordInputs.current_password ||
            passwordInputs["current_password"]?.trim() === 0
        ) {
            errorss["current_password"] = getFormattedMessage(
                "change-password.input.current.validate.label"
            );
        } else if (
            (!passwordInputs["new_password"] ||
                passwordInputs.new_password.trim() === 0) &&
            !isDeleteAccount
        ) {
            errorss["new_password"] = getFormattedMessage(
                "change-password.input.new.validate.label"
            );
        } else if (
            (!passwordInputs["confirm_password"] ||
                passwordInputs.confirm_password.trim() === 0) &&
            !isDeleteAccount
        ) {
            errorss["confirm_password"] = getFormattedMessage(
                "change-password.input.confirm.validate.label"
            );
        } else if (
            passwordInputs["confirm_password"] !==
                passwordInputs["new_password"] &&
            !isDeleteAccount
        ) {
            errorss["confirm_password"] = getFormattedMessage(
                "change-password.input.confirm.valid.validate.label"
            );
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const onSubmit = (e) => {
        e.preventDefault();
        const valid = handleValidation();
        if (isDeleteAccount) {
            if (valid) {
                deleteAccountAction(
                    { password: passwordInputs.current_password },
                    onDeleteAccount,
                    navigate
                );
            }
        } else {
            if (valid) {
                onChangePassword(passwordInputs, onClickDeleteModel);
                setPasswordInputs(passwordInputs);
            }
        }
    };

    return (
        <Modal
            show={deleteModel}
            onHide={() => onClickDeleteModel(false)}
            keyboard={true}
        >
            <Form
                onKeyPress={(e) => {
                    if (e.key === "Enter") {
                        onSubmit(e);
                    }
                }}
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        {isDeleteAccount
                            ? getFormattedMessage("header.delete-account.label")
                            : getFormattedMessage(
                                  "header.profile-menu.change-password.label"
                              )}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="row">
                        {isDeleteAccount ? (
                            <>
                                <div className="col-md-12 mb-5">
                                    <PasswordHandling
                                        IsRequired={true}
                                        current_password={true}
                                        onChangeInput={(e) =>
                                            handleChangePassword(e)
                                        }
                                        passwordValue={
                                            passwordInputs.current_password
                                        }
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["current_password"]
                                            ? errors["current_password"]
                                            : null}
                                    </span>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="col-md-12 mb-5">
                                    <PasswordHandling
                                        IsRequired={true}
                                        current_password={true}
                                        onChangeInput={(e) =>
                                            handleChangePassword(e)
                                        }
                                        passwordValue={
                                            passwordInputs.current_password
                                        }
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["current_password"]
                                            ? errors["current_password"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-md-12 mb-5">
                                    <PasswordHandling
                                        IsRequired={true}
                                        new_password={true}
                                        onChangeInput={(e) =>
                                            handleChangePassword(e)
                                        }
                                        passwordValue={
                                            passwordInputs.new_password
                                        }
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["new_password"]
                                            ? errors["new_password"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-md-12">
                                    <ConfrimPasswordHandling
                                        IsRequired={true}
                                        onChangeInput={(e) =>
                                            handleChangePassword(e)
                                        }
                                        passwordValue={
                                            passwordInputs.confirm_password
                                        }
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["confirm_password"]
                                            ? errors["confirm_password"]
                                            : null}
                                    </span>
                                </div>
                            </>
                        )}
                    </div>
                </Modal.Body>
            </Form>
            <Modal.Footer children="justify-content-start" className="pt-0">
                <button
                    type="button"
                    className="btn btn-primary m-0"
                    onClick={(event) => onSubmit(event)}
                >
                    {isDeleteAccount
                        ? placeholderText("globally.confirm-btn")
                        : placeholderText("globally.save-btn")}
                </button>
                <button
                    type="button"
                    className="btn btn-secondary my-0 ms-5 me-0"
                    data-bs-dismiss="modal"
                    onClick={() => {
                        isDeleteAccount
                            ? onDeleteAccount()
                            : onClickDeleteModel(false);
                    }}
                >
                    {getFormattedMessage("globally.cancel-btn")}
                </button>
            </Modal.Footer>
        </Modal>
    );
};

export default connect(null, { onChangePassword, deleteAccountAction })(
    ChangePassword
);
