import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import TabTitle from "../../shared/tab-title/TabTitle";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { Image } from "react-bootstrap-v5";
import { resetPassword } from "../../store/action/authAction";
import { cssHandling } from "../../cssHandling/cssHandling";
import { Tokens } from "../../constants";
import Logo from "../../assets/media/logos/logo.png";

const ResetPassword = (props) => {
    const { frontSettings, resetPassword } = props;
    const navigate = useNavigate();
    const { token, email } = useParams();
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const [resetValue, setResetValue] = useState({
        password: "",
        password_confirmation: "",
        email: email,
        token: token,
    });
    const [disabled, setDisabled] = useState(false);

    const [errors, setErrors] = useState({
        password: "",
        password_confirmation: "",
    });

    useEffect(() => {
        cssHandling(updatedLanguage);

        return () => cssHandling(updatedLanguage);
    }, []);

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (!resetValue["password"] || resetValue["password"].trim() === 0) {
            errorss["password"] = getFormattedMessage(
                "user.input.password.validate.label"
            );
        } else if (
            !resetValue["password_confirmation"] ||
            resetValue["password_confirmation"]?.trim()?.length === 0
        ) {
            errorss["password_confirmation"] = getFormattedMessage(
                "user.input.confirm-password.validate.label"
            );
        } else if (
            resetValue["password"] !== resetValue["password_confirmation"]
        ) {
            errorss["password_confirmation"] = getFormattedMessage(
                "reset-password.password.validate.label"
            );
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const prepareFormData = (data) => {
        const formData = new FormData();
        formData.append("password", data.password);
        formData.append("password_confirmation", data.password_confirmation);
        formData.append("email", data.email);
        formData.append("token", data.token);
        return formData;
    };

    const handleChange = (e) => {
        e.persist();
        setResetValue((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value,
        }));
        setErrors("");
    };

    const onSubmit = (e) => {
        e.preventDefault();
        const Valid = handleValidation();
        if (Valid) {
            resetPassword(prepareFormData(resetValue), navigate, setDisabled);
            setDisabled(true);
        }
    };

    return (
        <div className="d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20 vh-100 auth_pages">
            <TabTitle title="Reset Password" />
            <div className="col-12 text-center align-items-center justify-content-center">
                <a href="#" className="image">
                    <Image
                        className="logo-height image login-company-logo mb-7 mb-sm-10 "
                        src={
                            frontSettings && frontSettings.logo
                                ? frontSettings.logo
                                : Logo
                        }
                    />
                </a>
            </div>
            <div className="rounded-15 shadow-md width-540 px-5 px-sm-7 py-10 mx-auto">
                <form className="form w-100">
                    <div className="text-center mb-10">
                        <h1 className="text-dark mb-3">
                            {getFormattedMessage("reset-password.title")}
                        </h1>
                    </div>
                    <div className="mb-5">
                        <label className="form-label">
                            {getFormattedMessage("user.input.password.label")} :
                        </label>
                        <span className="required" />
                        <input
                            type="password"
                            className="form-control"
                            placeholder={placeholderText(
                                "user.input.password.placeholder.label"
                            )}
                            name="password"
                            value={resetValue.password}
                            required
                            onChange={(e) => handleChange(e)}
                        />
                        <span className="text-danger d-block fw-400 fs-small mt-2">
                            {errors["password"] ? errors["password"] : null}
                        </span>
                    </div>

                    <div className="mb-10">
                        <label className="form-label">
                            {getFormattedMessage(
                                "change-password.input.confirm.label"
                            )}
                        </label>
                        <span className="required" />
                        <input
                            type="password"
                            className="form-control"
                            placeholder={placeholderText(
                                "change-password.input.confirm.placeholder.label"
                            )}
                            name="password_confirmation"
                            value={resetValue.password_confirmation}
                            required
                            onChange={(e) => handleChange(e)}
                        />
                        <span className="text-danger d-block fw-400 fs-small mt-2">
                            {errors["password_confirmation"]
                                ? errors["password_confirmation"]
                                : null}
                        </span>
                    </div>

                    <div className="d-flex justify-content-center pb-lg-0">
                        <button
                            type="submit"
                            className="btn btn-primary me-4"
                            onClick={(e) => onSubmit(e)}
                            disabled={disabled}
                        >
                            {
                                <span>
                                    {getFormattedMessage(
                                        "reset-password.title"
                                    )}
                                </span>
                            }
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

const mapStateToProps = (state) => {
    const { frontSettings } = state;
    return { frontSettings };
};

export default connect(mapStateToProps, { resetPassword })(ResetPassword);
