import React, { useEffect, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Image } from "react-bootstrap-v5";
import * as EmailValidator from "email-validator";
import {
    loginAction,
    loginViaGoogleAction,
} from "../../store/action/authAction";
import TabTitle from "../../shared/tab-title/TabTitle";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { toastType, Tokens } from "../../constants";
import Logo from "../../assets/media/logos/logo.png";
import PasswordHandling from "../../shared/password-handler/PasswordHandling";
import { fetchFrontSetting } from "../../store/action/frontSettingAction";
import axios from "axios";
import { isMobile, isDesktop, isTablet, osName } from "react-device-detect";
import { LoginSocialGoogle } from "reactjs-social-login";
import { GoogleLoginButton } from "react-social-login-buttons";
import { cssHandling } from "../../cssHandling/cssHandling";
import { addToast } from "../../store/action/toastAction";
import ReCAPTCHA from "react-google-recaptcha";

const Login = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { frontSettings } = useSelector((state) => state);
    const [loading, setLoading] = useState(false);
    const token = localStorage.getItem(Tokens.ADMIN);
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const captchaRef = useRef(null);
    const DarkMod = localStorage.getItem("isDarkMod");
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );

    const logo = frontSettings?.favicon ? frontSettings?.favicon : "";
    let link = document.querySelector("link[rel~='icon']");
    if (!link) {
        link = document.createElement("link");
        link.rel = "icon";
        document.getElementsByTagName("head")[0].appendChild(link);
    }
    link.href = logo;

    const [loginInputs, setLoginInputs] = useState({
        email: "",
        password: "",
        country_code: "",
        device: "",
        os: osName || "",
    });

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, []);

    useEffect(() => {
        cssHandling(updatedLanguage);

        return () => cssHandling(updatedLanguage);
    }, []);

    useEffect(() => {
        dispatch(fetchFrontSetting());
    }, []);

    const getDeviceType = () => {
        if (isMobile) {
            setLoginInputs((inputs) => ({ ...inputs, device: "Mobile" }));
        } else if (isDesktop) {
            setLoginInputs((inputs) => ({ ...inputs, device: "Desktop" }));
        } else if (isTablet) {
            setLoginInputs((inputs) => ({ ...inputs, device: "Tablet" }));
        }
    };

    useEffect(() => {
        if (token) {
            dispatch(fetchFrontSetting());
            navigate("/");
        }

        axios
            .get("https://ipapi.co/json/")
            .then((response) => {
                let data = response.data;
                setLoginInputs((inputs) => ({
                    ...inputs,
                    country_code: data.country_calling_code,
                }));
            })
            .catch((error) => {
                // console.log(error);
            });

        setLoginInputs((inputs) => ({ ...inputs, os: osName }));
        getDeviceType();
    }, []);

    const [errors, setErrors] = useState({
        email: "",
        password: "",
    });

    const handleValidation = () => {
        let error = {};
        let isValid = false;
        if (!EmailValidator.validate(loginInputs["email"])) {
            if (!loginInputs["email"]) {
                error["email"] = getFormattedMessage(
                    "globally.input.email.validate.label"
                );
            } else {
                error["email"] = getFormattedMessage(
                    "globally.input.email.valid.validate.label"
                );
            }
        } else if (
            !loginInputs["password"] ||
            loginInputs["password"]?.trim() === 0
        ) {
            error["password"] = getFormattedMessage(
                "user.input.password.validate.label"
            );
        } else {
            isValid = true;
        }
        setErrors(error);
        setLoading(false);
        return isValid;
    };

    const prepareFormData = () => {
        const formData = new FormData();
        formData.append("email", loginInputs.email);
        formData.append("password", loginInputs.password);
        formData.append("os", loginInputs.os);
        formData.append("device", loginInputs.device);
        formData.append("country_code", loginInputs.country_code);
        return formData;
    };

    const prepareFormDataWithToken = (token) => {
        const formData = new FormData();
        formData.append("email", loginInputs.email);
        formData.append("password", loginInputs.password);
        formData.append("os", loginInputs.os);
        formData.append("device", loginInputs.device);
        formData.append("country_code", loginInputs.country_code);
        formData.append("token", token);
        return formData;
    };

    const onLogin = async (e) => {
        e.preventDefault();
        const valid = handleValidation();
        let token =
            frontSettings &&
            frontSettings?.captcha_on_register_login === "1" &&
            frontSettings?.captcha_site_key &&
            captchaRef.current.getValue();
        if (valid) {
            setLoading(true);
            if (token) {
                dispatch(
                    loginAction(
                        prepareFormDataWithToken(token),
                        navigate,
                        setLoading,
                        captchaRef
                    )
                );
                setLoginInputs({
                    email: "",
                    password: "",
                });
            } else {
                if (
                    frontSettings &&
                    frontSettings?.captcha_on_register_login === "0"
                ) {
                    setLoading(true);
                    dispatch(
                        loginAction(
                            prepareFormData(),
                            navigate,
                            setLoading,
                            captchaRef
                        )
                    );
                    setLoginInputs({
                        email: "",
                        password: "",
                    });
                }
                if (
                    frontSettings &&
                    frontSettings?.captcha_on_register_login === "1"
                ) {
                    dispatch(
                        addToast({
                            text: getFormattedMessage(
                                "Please.verify.the.captcha.validate.label"
                            ),
                            type: toastType.ERROR,
                        })
                    );
                    setLoading(false);
                }
            }
        }
    };

    const handleChange = (e) => {
        e.persist();
        setLoginInputs((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value,
        }));
        setErrors("");
    };

    const handlePasswordChange = (e) => {
        e.persist();
        setLoginInputs((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value.trim(),
        }));
        setErrors("");
    };

    return (
        <div className="content d-flex flex-column flex-column-fluid auth_pages">
            <div className="d-flex flex-wrap flex-column-fluid">
                <div className="d-flex flex-column flex-column-fluid align-items-center justify-content-center p-4">
                    <TabTitle
                        title={placeholderText("login-form.login-btn.label")}
                    />
                    <div className="col-12 text-center align-items-center justify-content-center">
                        <a href="#" className="image">
                            <Image
                                className="logo-height image login-company-logo mb-7 mb-sm-10 w-100"
                                src={
                                    frontSettings && frontSettings.logo
                                        ? frontSettings.logo
                                        : Logo
                                }
                            />
                        </a>
                    </div>
                    <div className="bg-theme-white rounded-15 shadow-md width-540 px-5 px-sm-7 py-10 mx-auto">
                        <h1 className="text-dark text-center mb-7">
                            {placeholderText("login-form.title")}
                        </h1>
                        <form>
                            <div className="mb-sm-7 mb-4">
                                <label className="form-label">
                                    {getFormattedMessage(
                                        "globally.input.email.label"
                                    )}
                                    :
                                </label>
                                <span className="required" />
                                <input
                                    placeholder={placeholderText(
                                        "user.input.email.label"
                                    )}
                                    required
                                    value={loginInputs.email}
                                    className="form-control"
                                    type="text"
                                    name="email"
                                    autoComplete="off"
                                    onChange={(e) => handleChange(e)}
                                />
                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                    {errors["email"] ? errors["email"] : null}
                                </span>
                            </div>
                            <div className="mb-sm-7 mb-4">
                                <PasswordHandling
                                    onChangeInput={handlePasswordChange}
                                    passwordValue={loginInputs.password}
                                    showForgotPassword={true}
                                    IsRequired={true}
                                    forgotPasswordURL={"/forgot-password"}
                                />
                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                    {errors["password"]
                                        ? errors["password"]
                                        : null}
                                </span>
                            </div>
                            {frontSettings &&
                                frontSettings?.captcha_on_register_login ===
                                    "1" &&
                                frontSettings?.captcha_site_key && (
                                    <div className="mb-4 d-flex justify-content-center">
                                        <ReCAPTCHA
                                            sitekey={
                                                frontSettings?.captcha_site_key
                                            }
                                            ref={captchaRef}
                                        />
                                    </div>
                                )}
                            <div className="text-center">
                                <button
                                    type="submit"
                                    className="btn btn-primary w-100"
                                    onClick={(e) => onLogin(e)}
                                >
                                    {loading ? (
                                        <span className="d-block">
                                            {getFormattedMessage(
                                                "globally.loading.label"
                                            )}
                                        </span>
                                    ) : (
                                        <span>
                                            {getFormattedMessage(
                                                "login-form.login-btn.label"
                                            )}
                                        </span>
                                    )}
                                </button>
                            </div>
                            <div
                                className={`mt-2 ${
                                    DarkMod === "true" ? "text-muted" : ""
                                }`}
                            >
                                {getFormattedMessage("not-registered.title")}{" "}
                                <Link
                                    to={"/register"}
                                    className={
                                        "text-decoration-none text-secondary"
                                    }
                                >
                                    {getFormattedMessage("register.here.title")}
                                </Link>
                            </div>
                        </form>
                        <div className="text-center mt-6">
                            {frontSettings &&
                            parseInt(frontSettings?.enable_google_login) ===
                                1 ? (
                                <LoginSocialGoogle
                                    client_id={
                                        frontSettings?.google_client_id !== null
                                            ? frontSettings?.google_client_id
                                            : ""
                                    }
                                    fetch_basic_profile={true}
                                    scope="https://www.googleapis.com/auth/userinfo.profile"
                                    onResolve={(response) => {
                                        dispatch(
                                            loginViaGoogleAction(
                                                response,
                                                navigate
                                            )
                                        );
                                    }}
                                    onReject={(error) => {
                                        // console.log("error", error)
                                    }}
                                >
                                    <GoogleLoginButton
                                        className="bg-danger text-white w-100 p-2 h-100 fs-6 mt-3 d-flex align-items-center justify-content-center"
                                        text={getFormattedMessage(
                                            "login-via-google.btn.label"
                                        )}
                                    />
                                </LoginSocialGoogle>
                            ) : null}
                            {/* {
                                frontSettings && parseInt(frontSettings?.enable_facebook_login) === 1 ?
                                    <LoginSocialFacebook
                                        appId={frontSettings?.facebook_app_id !== null ? frontSettings?.facebook_app_id : ""}
                                        onResolve={(response) => {
                                            dispatch(loginViaFacebookAction(response, navigate))
                                        }}
                                        onReject={(error) => {
                                            // console.log("error", error)
                                        }}
                                    >
                                        <FacebookLoginButton
                                            className='bg-info w-100 p-2 h-100 fs-6 mt-3 d-flex align-items-center justify-content-center'
                                            text={getFormattedMessage('login-via-facebook.btn.label')} />
                                    </LoginSocialFacebook>
                                    : null
                            } */}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;
