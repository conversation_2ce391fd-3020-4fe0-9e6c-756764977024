import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import TabTitle from "../../shared/tab-title/TabTitle";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { Image } from "react-bootstrap-v5";
import { resetLoginPassword } from "../../store/action/authAction";
import { cssHandling } from "../../cssHandling/cssHandling";
import { Tokens } from "../../constants";
import Logo from "../../assets/media/logos/logo.png";

const ResetLoginPassword = (props) => {
    const { frontSettings, resetLoginPassword } = props;
    const navigate = useNavigate();
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );
    const [resetValue, setResetValue] = useState({
        password: "",
    });

    const [errors, setErrors] = useState({
        password: "",
    });

    useEffect(() => {
        cssHandling(updatedLanguage);

        return () => cssHandling(updatedLanguage);
    }, []);

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, []);

    const logo = frontSettings?.favicon ? frontSettings?.favicon : "";
    let link = document.querySelector("link[rel~='icon']");
    if (!link) {
        link = document.createElement("link");
        link.rel = "icon";
        document.getElementsByTagName("head")[0].appendChild(link);
    }
    link.href = logo;

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (
            !resetValue["password"] ||
            resetValue["password"]?.trim()?.length === 0
        ) {
            errorss["password"] = getFormattedMessage(
                "user.input.password.validate.label"
            );
        } else if (resetValue["password"].length <= 5) {
            errorss["password"] = getFormattedMessage(
                "change-password.input.characters.valid.validate.label"
            );
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const prepareFormData = (data) => {
        const formData = new FormData();
        formData.append("password", data.password);
        return formData;
    };

    const handleChange = (e) => {
        e.persist();
        setResetValue((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value.trim(),
        }));
        setErrors("");
    };

    const onSubmit = (e) => {
        e.preventDefault();
        const Valid = handleValidation();
        if (Valid) {
            resetLoginPassword(prepareFormData(resetValue), navigate);
        }
    };

    return (
        <div className="d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20 vh-100 auth_pages">
            <TabTitle title="Reset Password" />
            <div className="mb-12 col-12 text-center">
                <Image
                    className="logo-height"
                    src={
                        frontSettings && frontSettings.logo
                            ? frontSettings.logo
                            : Logo
                    }
                />
            </div>
            <div className="bg-theme-white rounded-15 shadow-md width-540 px-5 px-sm-7 py-10 mx-auto">
                <form className="form w-100">
                    <div className="text-center mb-10">
                        <h1 className="text-dark mb-3">
                            {getFormattedMessage("reset-password.title")}
                        </h1>
                    </div>
                    <div className="mb-5">
                        <label className="form-label">
                            {getFormattedMessage("user.input.password.label")} :
                        </label>
                        <span className="required" />
                        <input
                            type="password"
                            className="form-control"
                            placeholder={placeholderText(
                                "user.input.password.placeholder.label"
                            )}
                            name="password"
                            value={resetValue.password}
                            required
                            onChange={(e) => handleChange(e)}
                        />
                        <span className="text-danger d-block fw-400 fs-small mt-2">
                            {errors["password"] ? errors["password"] : null}
                        </span>
                    </div>

                    <div className="d-flex justify-content-center pb-lg-0">
                        <button
                            type="submit"
                            className="btn btn-primary me-4"
                            onClick={(e) => onSubmit(e)}
                        >
                            {
                                <span>
                                    {getFormattedMessage(
                                        "reset-password.title"
                                    )}
                                </span>
                            }
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

const mapStateToProps = (state) => {
    const { frontSettings } = state;
    return { frontSettings };
};

export default connect(mapStateToProps, { resetLoginPassword })(
    ResetLoginPassword
);
