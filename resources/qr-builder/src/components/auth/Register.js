import React, { useEffect, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Image } from "react-bootstrap-v5";
import * as EmailValidator from "email-validator";
import { userRegister } from "../../store/action/authAction";
import TabTitle from "../../shared/tab-title/TabTitle";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { toastType, Tokens } from "../../constants";
import { createBrowserHistory } from "history";
import Logo from "../../assets/media/logos/logo.png";
import PasswordHandling from "../../shared/password-handler/PasswordHandling";
import ConfrimPasswordHandling from "../../shared/password-handler/ConfrimPasswordHandling";
import { cssHandling } from "../../cssHandling/cssHandling";
import ReCAPTCHA from "react-google-recaptcha";
import { addToast } from "../../store/action/toastAction";

const Register = () => {
    const captchaRef = useRef(null);
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const history = createBrowserHistory();
    const { frontSettings } = useSelector((state) => state);
    const [loading, setLoading] = useState(false);
    const token = localStorage.getItem(Tokens.ADMIN);
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const DarkMod = localStorage.getItem("isDarkMod");
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );
    const [registerInputs, setRegisterInputs] = useState({
        name: "",
        email: "",
        password: "",
        confirm_password: "",
    });

    useEffect(() => {
        if (token) {
            history.push(window.location.pathname);
        }
    }, []);

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, []);

    useEffect(() => {
        cssHandling(updatedLanguage);

        return () => cssHandling(updatedLanguage);
    }, []);

    const [errors, setErrors] = useState({
        name: "",
        email: "",
        password: "",
        confirm_password: "",
    });

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        const nameReg = /^[a-zA-Z ]{2,30}$/;
        if (
            !registerInputs.name ||
            registerInputs["name"].trim().length === 0
        ) {
            errorss["name"] = getFormattedMessage(
                "globally.input.name.validate.label"
            );
        } else if (!EmailValidator.validate(registerInputs["email"])) {
            if (!registerInputs["email"]) {
                errorss["email"] = getFormattedMessage(
                    "globally.input.email.validate.label"
                );
            } else {
                errorss["email"] = getFormattedMessage(
                    "globally.input.email.valid.validate.label"
                );
            }
        } else if (registerInputs["password"].length <= 5) {
            errorss["password"] = getFormattedMessage(
                "change-password.input.characters.valid.validate.label"
            );
        } else if (
            !registerInputs["password"] ||
            registerInputs["password"].trim().length === 0
        ) {
            errorss["password"] = getFormattedMessage(
                "user.input.password.validate.label"
            );
        } else if (
            registerInputs["password"] !== registerInputs["confirm_password"]
        ) {
            errorss["confirm_password"] = getFormattedMessage(
                "change-password.input.confirm.valid.validate.label"
            );
        } else {
            isValid = true;
        }
        setErrors(errorss);
        setLoading(false);
        return isValid;
    };

    const prepareFormData = (loginInputs) => {
        const formData = new FormData();
        formData.append("name", loginInputs.name);
        formData.append("email", loginInputs.email);
        formData.append("password", loginInputs.password);
        formData.append("confirm_password", loginInputs.confirm_password);
        return formData;
    };

    const prepareFormDataWithToken = (loginInputs, token) => {
        const formData = new FormData();
        formData.append("name", loginInputs.name);
        formData.append("email", loginInputs.email);
        formData.append("password", loginInputs.password);
        formData.append("confirm_password", loginInputs.confirm_password);
        formData.append("token", token);
        return formData;
    };

    const onRegister = async (e) => {
        e.preventDefault();
        const valid = handleValidation();
        let token =
            frontSettings &&
            frontSettings?.captcha_on_register_login === "1" &&
            frontSettings?.captcha_site_key &&
            captchaRef.current.getValue();
        if (valid) {
            setLoading(true);
            if (token) {
                dispatch(
                    userRegister(
                        prepareFormDataWithToken(registerInputs, token),
                        navigate,
                        setLoading,
                        captchaRef
                    )
                );
                setRegisterInputs({
                    name: "",
                    email: "",
                    password: "",
                    confirm_password: "",
                });
            } else {
                if (
                    frontSettings &&
                    frontSettings?.captcha_on_register_login === "0"
                ) {
                    setLoading(true);
                    dispatch(
                        userRegister(
                            prepareFormData(registerInputs),
                            navigate,
                            setLoading,
                            captchaRef
                        )
                    );
                    setRegisterInputs({
                        name: "",
                        email: "",
                        password: "",
                        confirm_password: "",
                    });
                }
                if (
                    frontSettings &&
                    frontSettings?.captcha_on_register_login === "1"
                ) {
                    dispatch(
                        addToast({
                            text: getFormattedMessage(
                                "Please.verify.the.captcha.validate.label"
                            ),
                            type: toastType.ERROR,
                        })
                    );
                    setLoading(false);
                }
            }
        }
    };

    const handleChange = (e) => {
        e.persist();
        setRegisterInputs((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value,
        }));
        setErrors("");
    };

    return (
        <div className="content d-flex flex-column flex-column-fluid auth_pages">
            <div className="d-flex flex-wrap flex-column-fluid">
                <div className="d-flex flex-column flex-column-fluid align-items-center justify-content-center p-4">
                    <TabTitle
                        title={placeholderText(
                            "register-form.register-btn.label"
                        )}
                    />
                    <div className="col-12 text-center align-items-center justify-content-center">
                        <a href="#" className="image">
                            <Image
                                className="logo-height image mb-7 mb-sm-10"
                                src={
                                    frontSettings && frontSettings.logo
                                        ? frontSettings.logo
                                        : Logo
                                }
                            />
                        </a>
                    </div>
                    <div className="bg-theme-white rounded-15 shadow-md width-540 px-5 px-sm-7 py-10 mx-auto">
                        <h1 className="text-dark text-center mb-7">
                            {getFormattedMessage(
                                "register-form.register-btn.label"
                            )}
                        </h1>
                        <form>
                            <div className="mb-sm-7 mb-3">
                                <label className="form-label">
                                    {getFormattedMessage(
                                        "react-data-table.name.column.title"
                                    )}
                                    :
                                </label>
                                <span className="required" />
                                <input
                                    placeholder={placeholderText(
                                        "globally.input.name.placeholder.label"
                                    )}
                                    required
                                    value={registerInputs.name}
                                    className="form-control"
                                    type="text"
                                    name="name"
                                    autoComplete="off"
                                    onChange={(e) => handleChange(e)}
                                />
                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                    {errors["name"] ? errors["name"] : null}
                                </span>
                            </div>
                            <div className="mb-sm-7 mb-3">
                                <label className="form-label">
                                    {getFormattedMessage(
                                        "globally.input.email.label"
                                    )}
                                    :
                                </label>
                                <span className="required" />
                                <input
                                    placeholder={placeholderText(
                                        "globally.input.email.placeholder.label"
                                    )}
                                    required
                                    value={registerInputs.email}
                                    className="form-control"
                                    type="text"
                                    name="email"
                                    autoComplete="off"
                                    onChange={(e) => handleChange(e)}
                                />
                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                    {errors["email"] ? errors["email"] : null}
                                </span>
                            </div>

                            <div className="mb-sm-7 mb-3">
                                <PasswordHandling
                                    IsRequired={true}
                                    onChangeInput={handleChange}
                                    passwordValue={registerInputs.password}
                                />
                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                    {errors["password"]
                                        ? errors["password"]
                                        : null}
                                </span>
                            </div>
                            <div className="mb-sm-7 mb-3">
                                <ConfrimPasswordHandling
                                    IsRequired={true}
                                    onChangeInput={handleChange}
                                    passwordValue={
                                        registerInputs.confirm_password
                                    }
                                />
                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                    {errors["confirm_password"]
                                        ? errors["confirm_password"]
                                        : null}
                                </span>
                            </div>
                            {frontSettings &&
                                frontSettings?.captcha_on_register_login ===
                                    "1" &&
                                frontSettings?.captcha_site_key && (
                                    <div className="mb-4 d-flex justify-content-center">
                                        <ReCAPTCHA
                                            sitekey={
                                                frontSettings?.captcha_site_key
                                            }
                                            ref={captchaRef}
                                        />
                                    </div>
                                )}
                            <div className="text-center">
                                <button
                                    type="submit"
                                    className="btn btn-primary w-100"
                                    onClick={(e) => onRegister(e)}
                                >
                                    {loading ? (
                                        <span className="d-block">
                                            {getFormattedMessage(
                                                "globally.loading.label"
                                            )}
                                        </span>
                                    ) : (
                                        <span>
                                            {getFormattedMessage(
                                                "register-form.register-btn.label"
                                            )}
                                        </span>
                                    )}
                                </button>
                            </div>
                            <div
                                className={`mt-3 text-decoration-none ${
                                    DarkMod === "true" ? "text-muted" : ""
                                }`}
                            >
                                {getFormattedMessage("already.register.title")}{" "}
                                <Link
                                    to={"/login"}
                                    className={
                                        "text-decoration-none text-secondary"
                                    }
                                >
                                    {getFormattedMessage(
                                        "login-form.login-btn.label"
                                    )}
                                </Link>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Register;
