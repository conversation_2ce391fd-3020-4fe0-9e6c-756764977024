import React, { useEffect, useState } from "react";
import Form from "react-bootstrap/Form";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
    getFormattedMessage,
    getFormattedOptions,
    numValidate,
    placeholderText,
} from "../../shared/sharedMethod";
import ReactSelect from "../../shared/select/reactSelect";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faPalette,
    faQuestionCircle,
    faWrench,
} from "@fortawesome/free-solid-svg-icons";
import { Button, InputGroup } from "react-bootstrap-v5";
import ReactDatePicker from "../../shared/datepicker/ReactDatePicker";
import QrCodeStyle from "../qr-codes/qrCodeStyle/QrCodeStyle";
import TooltipComponent from "../../shared/tooltip/TooltipComponent";
import {
    backgroundTransparencyArr,
    cryptoCoinOptions,
    currencyCodeOptions,
    customeEyeColorOptions,
    encryptionOptions,
    errorCorrectionArr,
    forgroundGradientStyleOptions,
    payPalTypesOptions,
    toastType,
    Tokens,
    typeDynamicOptions,
    typeOptions,
    wifiHiddenOptions,
} from "../../constants";
import ReactColorPicker from "../../shared/colorpocker/ReactColorPicker";
import { addHomeQrcodeAction } from "../../store/action/homeQrcodeAction";
import { fetchQrcodeType } from "../../store/action/qrCodeAction";
import { cssHandling } from "../../cssHandling/cssHandling";
import TabTitle from "../../shared/tab-title/TabTitle";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { addToast } from "../../store/action/toastAction";
import HomeFooter from "../../mainLayout/frontend/HomeFooter";
import HomeHeader from "../../mainLayout/frontend/HomeHeader";

const Home = (props) => {
    const { qrCodeTypes, addHomeQrcodeAction, fetchQrcodeType, addToast } =
        props;
    const navigate = useNavigate();
    const [showColor, setShowColor] = useState(false);
    const [showOptions, setShowOptions] = useState(false);
    const [gradientColor1, setGradientColor1] = useState("");
    const [gradientColor2, setGradientColor2] = useState("");
    const [backgroundColor, setBackgroundColor] = useState("#ffffff");
    const [isTooltipActive, setIsTooltipActive] = useState(false);
    const [token, setToken] = useState(localStorage.getItem(Tokens.ADMIN));
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const [getTitle, setTitel] = useState("globally.product.code.title");
    const [disable, setDisable] = useState(true);

    const [qrDetails, setQrdetails] = useState({
        name: "",
        project_id: { label: getFormattedMessage("none.title"), value: null },
        type: { label: "Text", value: 1 },
        style: 1,
        foreground_type: 1,
        foreground_color1: "",
        foreground_color2: "",
        background_color: "#ffffff",
        background_transparency: 0,
        custom_eye_color: {
            label: getFormattedMessage("globally.input.no.lable"),
            value: 2,
        },
        eye_inner_color: "",
        eye_outer_color: "",
        error_correction: {
            label: getFormattedMessage(
                "qrcode.error-correction-capability.low.label"
            ),
            value: 1,
        },
        size: 400,
        margin_size: 10,
        foreground_color: "",
        color: "",
        gradient: {
            type: 1,
            colorStops: [
                { offset: 0, color: gradientColor1 },
                { offset: 1, color: gradientColor2 },
            ],
        },
        foreground_gradient_style: {
            label: getFormattedMessage("qrcode.horizontal.lable"),
            value: 1,
        },
        birth_date: new Date(),
        text_content: "",
        url: "",
        phone_number: "",
        prefilled_message: "",
        email: "",
        prefilled_subject: "",
        wp_phone_number: "",
        phone_or_email: "",
        latitude: "",
        longitude: "",
        wifi_name: "",
        encryption: { label: "WEP", value: 1 },
        password: "",
        wifi_is_hidden: {
            label: getFormattedMessage("globally.input.yes.lable"),
            value: 1,
        },
        event_name: "",
        geo_location: "",
        event_url: "",
        notes: "",
        starts_on: new Date(),
        ends_on: new Date(),
        timezone: { value: 1, label: "Africa/Abidjan" },
        coin: { label: getFormattedMessage("qrcode.bitcoin.title"), value: 1 },
        address: "",
        amount: "",
        first_name: "",
        last_name: "",
        website_url: "",
        company: "",
        job_title: "",
        birthday: new Date(),
        street_address: "",
        city: "",
        zip: "",
        region: "",
        country: "",
        paypal_type: {
            label: getFormattedMessage("qrcode.buy-now.lable"),
            value: 1,
        },
        paypal_email: "",
        product_title: "",
        currency_code: { label: "USD", value: 1 },
        price: "",
        thanks_url: "",
        cancel_url: "",
    });

    const [errors, setErrors] = useState({
        name: "",
        text_content: "",
    });

    useEffect(() => {
        token && fetchQrcodeType();
        cssHandling(updatedLanguage);

        return () => cssHandling(updatedLanguage);
    }, []);

    const qrTypeOptions = getFormattedOptions(typeOptions);
    const qrTypeDefaultValue = qrTypeOptions.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    //  Qr-code Demo Styling Hendling
    useEffect(() => {
        if (token === null) {
            let timeOut = setInterval(() => {
                let qrCodeDemo = document.getElementsByClassName("qrCodeDemo");
                if (qrCodeDemo.length === 0) {
                    document.getElementById("QrCodeStyleModel").remove();
                    clearInterval(timeOut);
                } else {
                    if (
                        document.getElementById("qrCodeOverlay")?.style
                            ?.backdropFilter !== "blur(15px)"
                    ) {
                        document.getElementById("QrCodeStyleModel").remove();
                        clearInterval(timeOut);
                    } else if (
                        document.getElementById("qrCodeOverlay")?.style
                            ?.display === "none"
                    ) {
                        document.getElementById("QrCodeStyleModel").remove();
                        clearInterval(timeOut);
                    }
                }
            }, 100);
            return () => clearInterval(timeOut);
        }
    }, []);

    useEffect(() => {
        const qrTypes =
            localStorage.getItem("generatQRType") !== null
                ? localStorage.getItem("generatQRType") == "undefined"
                    ? null
                    : JSON.parse(localStorage.getItem("generatQRType"))
                : null;

        if (
            token === null &&
            qrTypeDefaultValue.length > 0 &&
            qrTypes !== null &&
            qrTypes !== undefined &&
            qrTypes !== false
        ) {
            setQrdetails((inputs) => ({
                ...inputs,
                type: qrTypeDefaultValue.filter((d) => d.value === qrTypes)[0],
            }));
            localStorage.removeItem("generatQRType");
        } else if (qrTypeDefaultValue.length > 0 && qrCodeTypes.length > 0) {
            if (
                qrTypes !== null &&
                qrTypes !== undefined &&
                qrTypes !== false
            ) {
                const notavailable = qrCodeTypes.filter(
                    (d) =>
                        d ===
                        qrTypeDefaultValue.filter(
                            (dt) => dt.value === qrTypes
                        )[0]?.label
                );
                if (notavailable.length === 0) {
                    const firstType = qrTypeDefaultValue.filter(
                        (d) => d?.label === qrCodeTypes[0]
                    );
                    addToast({
                        text:
                            qrTypeDefaultValue.filter(
                                (d) => d.value === qrTypes
                            )[0]?.label +
                            " type of QR Code is not available in your plan",
                        type: toastType.ERROR,
                    });
                    if (firstType.length > 0) {
                        setQrdetails((inputs) => ({
                            ...inputs,
                            type: firstType[0],
                        }));
                    }
                    localStorage.removeItem("generatQRType");
                } else {
                    setQrdetails((inputs) => ({
                        ...inputs,
                        type: qrTypeDefaultValue.filter(
                            (d) => d.value === qrTypes
                        )[0],
                    }));
                    localStorage.removeItem("generatQRType");
                }
            } else {
                if (qrTypeDefaultValue.length > 0 && qrCodeTypes.length > 0) {
                    const firstType = qrTypeDefaultValue.filter(
                        (d) => d?.label === qrCodeTypes[0]
                    );
                    if (firstType.length > 0) {
                        if (!qrCodeTypes.includes(qrDetails.type?.label)) {
                            if (firstType[0].value !== qrDetails.type.value) {
                                setQrdetails((inputs) => ({
                                    ...inputs,
                                    type: firstType[0],
                                }));
                            }
                        }
                    }
                }
            }
        }
    }, [qrCodeTypes, qrTypeDefaultValue]);

    const onChangeInput = (e) => {
        setQrdetails((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value,
        }));
        setErrors("");
        setDisable(false);
    };

    const onSubmit = (event) => {
        event.preventDefault();
        if (token) {
            addHomeQrcodeAction(qrDetails);
            navigate("/app/qrcode/create");
        } else {
            navigate("/register");
            localStorage.setItem(
                Tokens.QRCODE_DETAILS,
                JSON.stringify(qrDetails)
            );
        }
    };

    const encryptionTypeOptions = getFormattedOptions(encryptionOptions);
    const encryptionTypeDefaultValue = encryptionTypeOptions.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    const wifiHiddenTypeOptions = getFormattedOptions(wifiHiddenOptions);
    const wifiHiddenTypeDefaultValue = wifiHiddenTypeOptions.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    const cryptoCoinTypeOptions = getFormattedOptions(cryptoCoinOptions);
    const cryptoCoinTypeDefaultValue = cryptoCoinTypeOptions.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    const payPalTypeOptions = getFormattedOptions(payPalTypesOptions);
    const payPalTypeDefaultValue = payPalTypeOptions.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    const currencyCodeTypeOptions = getFormattedOptions(currencyCodeOptions);
    const currencyCodeTypeDefaultValue = payPalTypeOptions.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    const forgroundGradientStyleTypeOptions = getFormattedOptions(
        forgroundGradientStyleOptions
    );
    const forgroundGradientStyleTypeDefaultValue =
        forgroundGradientStyleTypeOptions.map((option) => {
            return {
                value: option.id,
                label: option.name,
            };
        });

    const customeEyeColorTypeOptions = getFormattedOptions(
        customeEyeColorOptions
    );
    const customeEyeColorTypeDefaultValue = customeEyeColorTypeOptions.map(
        (option) => {
            return {
                value: option.id,
                label: option.name,
            };
        }
    );

    const errorCorrectionTypeOptions = getFormattedOptions(errorCorrectionArr);
    const errorCorrectionTypeDefaultValue = errorCorrectionTypeOptions.map(
        (option) => {
            return {
                value: option.id,
                label: option.name,
            };
        }
    );

    const onEncChange = (e) => {
        setQrdetails((inputs) => ({ ...inputs, encryption: e }));
        setDisable(false);
    };

    const onCryptoChange = (e) => {
        setQrdetails((inputs) => ({ ...inputs, coin: e }));
        setDisable(false);
    };

    const onPayPalTypeChange = (e) => {
        setTitel(
            e.value === 1
                ? "globally.product.code.title"
                : "globally.product.name.title"
        );
        setQrdetails((inputs) => ({ ...inputs, paypal_type: e }));
    };

    const onCurrencyCodeTypeChange = (e) => {
        setQrdetails((inputs) => ({ ...inputs, currency_code: e }));
        setDisable(false);
    };

    const onForgroundGradientStyleChange = (e) => {
        setQrdetails((inputs) => ({
            ...inputs,
            gradient: {
                ...inputs.gradient,
                type: e.value,
            },
            foreground_gradient_style: e,
        }));
    };

    const onChangeCustomeEyeColor = (e) => {
        setQrdetails((inputs) => ({
            ...inputs,
            custom_eye_color: e,
        }));
    };

    const onErrorCorrectionChange = (e) => {
        setQrdetails((inputs) => ({
            ...inputs,
            error_correction: e,
        }));
    };

    const onWifiHiddenChange = (e) => {
        setQrdetails((inputs) => ({ ...inputs, wifi_is_hidden: e }));
    };

    const onTimeZoneChange = (e) => {
        setQrdetails((inputs) => ({ ...inputs, timezone: e }));
    };

    let aryIannaTimeZones = Intl.supportedValuesOf("timeZone");

    let timeZonArr = aryIannaTimeZones.map((timeZone, i) => {
        return {
            value: i + 1,
            label: timeZone,
        };
    });

    const onChangeStartDate = (date) => {
        setQrdetails((inputs) => ({ ...inputs, starts_on: date }));
    };

    const onChangeEndDate = (date) => {
        setQrdetails((inputs) => ({ ...inputs, ends_on: date }));
    };

    const onChangeDOB = (date) => {
        setQrdetails((inputs) => ({ ...inputs, birthday: date }));
    };

    const handleBGTransparencyChange = (e) => {
        const transparency = backgroundTransparencyArr.filter(
            (t) => t.id.toString() === e.target.value.toString()
        );
        if (backgroundColor.length > 7) {
            const bg = backgroundColor.slice(0, -2);
            setQrdetails((inputs) => ({
                ...inputs,
                background_color: bg + transparency[0]?.name,
            }));
        } else {
            setQrdetails((inputs) => ({
                ...inputs,
                background_color: backgroundColor + transparency[0]?.name,
            }));
        }
        setQrdetails((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value / 100,
        }));
    };

    const handleForgroundColor = (color) => {
        setQrdetails((inputs) => ({ ...inputs, foreground_color: color.hex }));
    };

    const eyeInnerColorChnage = (c) => {
        setQrdetails((inputs) => ({
            ...inputs,
            eye_inner_color: c.hex,
        }));
    };

    const eyeOuterColorChange = (c) => {
        setQrdetails((inputs) => ({
            ...inputs,
            eye_outer_color: c.hex,
        }));
    };

    const handleForgroundColor1 = (color) => {
        setGradientColor1(color.hex);
        setQrdetails((inputs) => ({
            ...inputs,
            gradient: {
                ...inputs.gradient,
                colorStops: [
                    { offset: 0, color: color.hex },
                    { offset: 1, color: gradientColor2 },
                ],
            },
            foreground_color1: color.hex,
        }));
    };

    const handleForgroundColor2 = (color) => {
        setGradientColor2(color.hex);
        setQrdetails((inputs) => ({
            ...inputs,
            gradient: {
                ...inputs.gradient,
                colorStops: [
                    { offset: 0, color: gradientColor1 },
                    { offset: 1, color: color.hex },
                ],
            },
            foreground_color2: color.hex,
        }));
    };

    const handleBackgroundColorChange = (color) => {
        setQrdetails((inputs) => ({
            ...inputs,
            background_color: color.hex,
        }));
        setBackgroundColor(color.hex);
    };

    return (
        <>
            <TabTitle title={"Generate QR Code"} />
            {/* <!-- start header section --> */}
            <HomeHeader />
            {/* <!-- end header section --> */}

            <div className="bg-white py-5 home">
                <div className={"text-center mt-5 "}>
                    <h1 className={"fw-bolder"} style={{ fontSize: "50px" }}>
                        {getFormattedMessage("globally.generate-qr-code.title")}
                    </h1>
                    <h5 style={{ fontSize: "25px" }}>
                        {getFormattedMessage(
                            "globally.generate-qr-code.sub.title"
                        )}
                    </h5>
                </div>
                <div className={"text-center my-5"}>
                    {qrTypeDefaultValue.length > 0
                        ? qrTypeDefaultValue.map((data) => {
                              let match = false;
                              const exist = typeDynamicOptions.filter((d) => {
                                  if (qrCodeTypes.includes(d.name)) {
                                      return d;
                                  }
                              });

                              exist.length > 0 &&
                                  exist.map((d) => {
                                      if (d.id === data.value) {
                                          match = true;
                                      }
                                  });
                              return (
                                  <button
                                      key={data.value}
                                      onClick={() => {
                                          setQrdetails((inputs) => ({
                                              ...inputs,
                                              type: {
                                                  value: data.value,
                                                  label: data.label,
                                              },
                                          }));
                                          setDisable(true);
                                          setShowColor(false);
                                          setShowOptions(false);
                                      }}
                                      className={`btn ${
                                          data.label === qrDetails.type?.label
                                              ? "btn-primary"
                                              : "btn-outline-primary"
                                      }  mx-1 my-1`}
                                      disabled={
                                          token ? (match ? false : true) : false
                                      }
                                  >
                                      {data.label}
                                  </button>
                              );
                          })
                        : null}
                </div>
                <div className="d-flex justify-content-center flex-md-row flex-column align-items-center;">
                    <div className="col-md-5 col-12 card mx-2">
                        <div className="card-body">
                            <Form>
                                <div className="row">
                                    {qrDetails.type.value === 1 && (
                                        <div className="col-12 mb-3">
                                            <label
                                                htmlFor="exampleInputEmail1"
                                                className="form-label"
                                            >
                                                {getFormattedMessage(
                                                    "globally.input.text-content.lable"
                                                )}
                                                :
                                            </label>
                                            <textarea
                                                type="text"
                                                name="text_content"
                                                rows={5}
                                                value={qrDetails.text_content}
                                                className="form-control"
                                                autoFocus={true}
                                                placeholder={placeholderText(
                                                    "globally.input.text-content.placeholder"
                                                )}
                                                onChange={(e) =>
                                                    onChangeInput(e)
                                                }
                                            />
                                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                                {errors["text_content"]
                                                    ? errors["text_content"]
                                                    : null}
                                            </span>
                                        </div>
                                    )}
                                    {qrDetails.type.value === 2 && (
                                        <div className="col-12 mb-3">
                                            <label
                                                htmlFor="exampleInputEmail1"
                                                className="form-label"
                                            >
                                                {getFormattedMessage(
                                                    "globally.input.url.lable"
                                                )}
                                                :
                                            </label>
                                            <input
                                                type="text"
                                                name="url"
                                                value={qrDetails.url}
                                                className="form-control"
                                                autoFocus={true}
                                                placeholder={placeholderText(
                                                    "globally.input.url.placeholder"
                                                )}
                                                onChange={(e) =>
                                                    onChangeInput(e)
                                                }
                                            />
                                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                                {errors["name"]
                                                    ? errors["name"]
                                                    : null}
                                            </span>
                                        </div>
                                    )}
                                    {qrDetails.type.value === 3 ? (
                                        <div className="col-12 mb-3">
                                            <label
                                                htmlFor="exampleInputEmail1"
                                                className="form-label"
                                            >
                                                {getFormattedMessage(
                                                    "globally.input.phone-number.lable"
                                                )}
                                                :
                                            </label>
                                            <input
                                                type="text"
                                                name="phone_number"
                                                value={qrDetails.phone_number}
                                                className="form-control"
                                                autoFocus={true}
                                                placeholder={placeholderText(
                                                    "globally.input.phone-number.placeholder.label"
                                                )}
                                                onChange={(e) =>
                                                    onChangeInput(e)
                                                }
                                            />
                                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                                {errors["name"]
                                                    ? errors["name"]
                                                    : null}
                                            </span>
                                        </div>
                                    ) : null}
                                    {qrDetails.type.value === 4 && (
                                        <>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.phone-number.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="phone_number"
                                                    value={
                                                        qrDetails.phone_number
                                                    }
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "globally.input.phone-number.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.prefilled-message.lable"
                                                    )}
                                                    :
                                                </label>
                                                <textarea
                                                    type="text"
                                                    name="prefilled_message"
                                                    value={
                                                        qrDetails.prefilled_message
                                                    }
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.prefilled-message.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                        </>
                                    )}
                                    {qrDetails.type.value === 5 && (
                                        <>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.email.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="email"
                                                    name="email"
                                                    value={qrDetails.email}
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "globally.input.email.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.prefilled-subject.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="email"
                                                    name="prefilled_subject"
                                                    value={
                                                        qrDetails.prefilled_subject
                                                    }
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.prefilled-subject.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.prefilled-message.lable"
                                                    )}
                                                    :
                                                </label>
                                                <textarea
                                                    type="text"
                                                    name="prefilled_message"
                                                    value={
                                                        qrDetails.prefilled_message
                                                    }
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.prefilled-message.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                        </>
                                    )}

                                    {qrDetails.type.value === 6 && (
                                        <>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.phone-number.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="wp_phone_number"
                                                    value={
                                                        qrDetails.wp_phone_number
                                                    }
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "globally.input.phone-number.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.prefilled-message.lable"
                                                    )}
                                                    :
                                                </label>
                                                <textarea
                                                    type="text"
                                                    name="prefilled_message"
                                                    value={
                                                        qrDetails.prefilled_message
                                                    }
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.prefilled-message.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                        </>
                                    )}

                                    {qrDetails.type.value === 7 && (
                                        <div className="col-12 mb-3">
                                            <label
                                                htmlFor="exampleInputEmail1"
                                                className="form-label"
                                            >
                                                {getFormattedMessage(
                                                    "globally.input.phone-number.label"
                                                )}{" "}
                                                or{" "}
                                                {getFormattedMessage(
                                                    "globally.input.email.label"
                                                )}
                                                :
                                            </label>
                                            <input
                                                type="text"
                                                name="phone_or_email"
                                                value={qrDetails.phone_or_email}
                                                className="form-control"
                                                autoFocus={true}
                                                placeholder={placeholderText(
                                                    "globally.input.phone-or-email.placeholder"
                                                )}
                                                onChange={(e) =>
                                                    onChangeInput(e)
                                                }
                                            />
                                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                                {errors["name"]
                                                    ? errors["name"]
                                                    : null}
                                            </span>
                                        </div>
                                    )}

                                    {qrDetails.type.value === 8 && (
                                        <>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.latitude.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="number"
                                                    name="latitude"
                                                    value={qrDetails.latitude}
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "globally.input.latitude.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.longitude.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="number"
                                                    name="longitude"
                                                    value={qrDetails.longitude}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.longitude.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                        </>
                                    )}

                                    {qrDetails.type.value === 9 && (
                                        <>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.wifi-ssid.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="wifi_name"
                                                    value={qrDetails.wifi_name}
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "globally.input.wifi-name.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <ReactSelect
                                                title={getFormattedMessage(
                                                    "globally.input.encryption.lable"
                                                )}
                                                name="encryption"
                                                value={qrDetails.encryption}
                                                errors={errors["type"]}
                                                isRequired={true}
                                                defaultValue={
                                                    encryptionTypeDefaultValue[0]
                                                }
                                                multiLanguageOption={
                                                    encryptionTypeOptions
                                                }
                                                onChange={onEncChange}
                                            />
                                            <div className="col-12 my-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.password.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="password"
                                                    name="password"
                                                    value={qrDetails.password}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "user.input.password.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <ReactSelect
                                                title={getFormattedMessage(
                                                    "globally.input.wifi-hidden.lable"
                                                )}
                                                value={qrDetails.wifi_is_hidden}
                                                errors={errors["payment_type"]}
                                                defaultValue={
                                                    wifiHiddenTypeDefaultValue[0]
                                                }
                                                multiLanguageOption={
                                                    wifiHiddenTypeOptions
                                                }
                                                isRequired={true}
                                                onChange={onWifiHiddenChange}
                                            />
                                        </>
                                    )}

                                    {qrDetails.type.value === 10 && (
                                        <>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.event-name.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="event_name"
                                                    value={qrDetails.event_name}
                                                    placeholder={placeholderText(
                                                        "globally.input.event-name.placeholder"
                                                    )}
                                                    className="form-control"
                                                    autoFocus={true}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.geo-location.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="geo_location"
                                                    value={
                                                        qrDetails.geo_location
                                                    }
                                                    placeholder={placeholderText(
                                                        "globally.input.deo-location-name.placeholder"
                                                    )}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.event-url.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="event_url"
                                                    value={qrDetails.event_url}
                                                    placeholder={placeholderText(
                                                        "globally.input.event-url.placeholder"
                                                    )}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.notes.label"
                                                    )}
                                                    :
                                                </label>
                                                <textarea
                                                    type="text"
                                                    name="notes"
                                                    value={qrDetails.notes}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.notes.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="position-relative col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.start-date.lable"
                                                    )}
                                                    :
                                                </label>
                                                <ReactDatePicker
                                                    onChangeDate={
                                                        onChangeStartDate
                                                    }
                                                    newStartDate={
                                                        qrDetails.starts_on
                                                    }
                                                    isShowTimeSelect={true}
                                                    minDate={new Date()}
                                                />
                                            </div>
                                            <div className="position-relative col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.ends-date.lable"
                                                    )}
                                                    :
                                                </label>
                                                <ReactDatePicker
                                                    onChangeDate={
                                                        onChangeEndDate
                                                    }
                                                    newStartDate={
                                                        qrDetails.ends_on
                                                    }
                                                    isShowTimeSelect={true}
                                                    minDate={new Date()}
                                                />
                                            </div>

                                            {/* <ReactSelect
                                                title={getFormattedMessage('globally.input.timezone.lable')}
                                                defaultValue={qrDetails.timezone}
                                                errors={errors['brand_id']}
                                                isRequired={true}
                                                data={timeZonArr}
                                                onChange={onTimeZoneChange}
                                                lassName='position-relative'
                                                value={qrDetails.timezone}
                                            /> */}
                                        </>
                                    )}

                                    {qrDetails.type.value === 11 && (
                                        <>
                                            <ReactSelect
                                                title={getFormattedMessage(
                                                    "globally.input.coin.lable"
                                                )}
                                                value={qrDetails.coin}
                                                errors={errors["payment_type"]}
                                                defaultValue={
                                                    cryptoCoinTypeDefaultValue[0]
                                                }
                                                multiLanguageOption={
                                                    cryptoCoinTypeOptions
                                                }
                                                isRequired={true}
                                                onChange={onCryptoChange}
                                            />
                                            <div className="col-12 my-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.currency.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="address"
                                                    value={qrDetails.address}
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "globally.input.crypto-currency-address.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>

                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.amount.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="amount"
                                                    value={qrDetails.amount}
                                                    placeholder={placeholderText(
                                                        "globally.input.amount.placeholder"
                                                    )}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                        </>
                                    )}

                                    {qrDetails.type.value === 12 && (
                                        <>
                                            <div className="col-md-6 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.first-name.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="first_name"
                                                    value={qrDetails.first_name}
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "user.input.first-name.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-md-6 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.last-name.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="last_name"
                                                    value={qrDetails.last_name}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "user.input.last-name.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.email.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="email"
                                                    name="email"
                                                    value={qrDetails.email}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.email.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.website-url.title"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="website_url"
                                                    value={
                                                        qrDetails.website_url
                                                    }
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.placeholder.website-url.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.company.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="company"
                                                    value={qrDetails.company}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.company.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.job-title.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="job_title"
                                                    value={qrDetails.job_title}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.job.title.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.birth-date.label"
                                                    )}
                                                    :
                                                </label>
                                                <ReactDatePicker
                                                    onChangeDate={onChangeDOB}
                                                    newStartDate={
                                                        qrDetails.birthday
                                                    }
                                                    maxDate={new Date()}
                                                />
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.paypal.vcard.title"
                                                    )}{" "}
                                                    {getFormattedMessage(
                                                        "globally.input.address.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="street_address"
                                                    value={
                                                        qrDetails.street_address
                                                    }
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.street.address.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.city.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="city"
                                                    value={qrDetails.city}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.city.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.zip.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="zip"
                                                    value={qrDetails.zip}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.zip.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.region.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="region"
                                                    value={qrDetails.region}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.region.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.country.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="country"
                                                    value={qrDetails.country}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.country.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.notes.label"
                                                    )}
                                                    :
                                                </label>
                                                <textarea
                                                    type="text"
                                                    name="notes"
                                                    value={qrDetails.notes}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.notes.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                        </>
                                    )}

                                    {qrDetails.type.value === 13 && (
                                        <>
                                            <ReactSelect
                                                title={getFormattedMessage(
                                                    "globally.type.label"
                                                )}
                                                value={qrDetails.paypal_type}
                                                errors={errors["payment_type"]}
                                                defaultValue={
                                                    payPalTypeDefaultValue[0]
                                                }
                                                isRequired={true}
                                                multiLanguageOption={
                                                    payPalTypeOptions
                                                }
                                                onChange={onPayPalTypeChange}
                                            />
                                            <div className="col-12 my-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.paypal.email.title"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="email"
                                                    name="paypal_email"
                                                    value={
                                                        qrDetails.paypal_email
                                                    }
                                                    className="form-control"
                                                    autoFocus={true}
                                                    placeholder={placeholderText(
                                                        "globally.input.paypal.email.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        getTitle
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="product_title"
                                                    value={
                                                        qrDetails.product_title
                                                    }
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={
                                                        getTitle ===
                                                        "globally.product.code.title"
                                                            ? placeholderText(
                                                                  "globally.input.product.code.placeholder"
                                                              )
                                                            : placeholderText(
                                                                  "globally.input.product.name.placeholder"
                                                              )
                                                    }
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <ReactSelect
                                                    title={getFormattedMessage(
                                                        "globally.input.currency.code.label"
                                                    )}
                                                    value={
                                                        qrDetails.currency_code
                                                    }
                                                    errors={
                                                        errors["currency_code"]
                                                    }
                                                    defaultValue={
                                                        currencyCodeTypeDefaultValue[0]
                                                    }
                                                    isRequired={true}
                                                    multiLanguageOption={
                                                        currencyCodeTypeOptions
                                                    }
                                                    onChange={
                                                        onCurrencyCodeTypeChange
                                                    }
                                                />
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.price.label"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="price"
                                                    value={qrDetails.price}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "user.input.price.placeholder.label"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.paypal.thank-you-url.title"
                                                    )}{" "}
                                                    {getFormattedMessage(
                                                        "globally.input.url.lable"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="thanks_url"
                                                    value={qrDetails.thanks_url}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.thank-url.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                            <div className="col-12 mb-3">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label"
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.cancel-you-url.title"
                                                    )}
                                                    :
                                                </label>
                                                <input
                                                    type="text"
                                                    name="cancel_url"
                                                    value={qrDetails.cancel_url}
                                                    className="form-control"
                                                    autoFocus={false}
                                                    placeholder={placeholderText(
                                                        "globally.input.cancel-url.placeholder"
                                                    )}
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                                    {errors["name"]
                                                        ? errors["name"]
                                                        : null}
                                                </span>
                                            </div>
                                        </>
                                    )}

                                    {/* color  */}
                                    <div className="col-12 mt-3">
                                        <Button
                                            variant="outline-primary"
                                            className="w-100 mb-3 qr_color_btn"
                                            onClick={() =>
                                                setShowColor(!showColor)
                                            }
                                        >
                                            <FontAwesomeIcon icon={faPalette} />{" "}
                                            {getFormattedMessage(
                                                "globally.input.color.lable"
                                            )}
                                        </Button>
                                        <div
                                            className={
                                                showColor
                                                    ? "d-flex justify-content-center flex-wrap"
                                                    : "d-none"
                                            }
                                        >
                                            {/* style */}
                                            <div className="d-flex justify-content-between flex-wrap w-100 qr_style_btn">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label col-12"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.heading.style.title"
                                                    )}
                                                </label>
                                                <Button
                                                    variant="outline-primary"
                                                    className={`mb-3 col-5 ${
                                                        qrDetails.style === 1 &&
                                                        "active"
                                                    }`}
                                                    onClick={() => {
                                                        setQrdetails(
                                                            (inputs) => ({
                                                                ...inputs,
                                                                style: 1,
                                                            })
                                                        );
                                                    }}
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.btn.square.label"
                                                    )}
                                                </Button>
                                                <Button
                                                    variant="outline-primary"
                                                    className={`mb-3 col-5 ${
                                                        qrDetails.style === 2 &&
                                                        "active"
                                                    }`}
                                                    onClick={() => {
                                                        setQrdetails(
                                                            (inputs) => ({
                                                                ...inputs,
                                                                style: 2,
                                                            })
                                                        );
                                                    }}
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.btn.dot.label"
                                                    )}
                                                </Button>
                                                <Button
                                                    variant="outline-primary"
                                                    className={`mb-3 col-5 ${
                                                        qrDetails.style === 3 &&
                                                        "active"
                                                    }`}
                                                    onClick={() => {
                                                        setQrdetails(
                                                            (inputs) => ({
                                                                ...inputs,
                                                                style: 3,
                                                            })
                                                        );
                                                    }}
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.btn.round.label"
                                                    )}
                                                </Button>
                                            </div>
                                            {/* forground type */}
                                            <div className="d-flex mt-3 justify-content-between flex-wrap w-100 qr_style_btn">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label col-12"
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.btn.foreground.label"
                                                    )}{" "}
                                                    {getFormattedMessage(
                                                        "globally.input.type.lable"
                                                    )}
                                                </label>
                                                <Button
                                                    variant="outline-primary"
                                                    className={`mb-3 col-5 ${
                                                        qrDetails.foreground_type ===
                                                            1 && "active"
                                                    }`}
                                                    onClick={() => {
                                                        setQrdetails(
                                                            (inputs) => ({
                                                                ...inputs,
                                                                foreground_type: 1,
                                                            })
                                                        );
                                                    }}
                                                >
                                                    Color
                                                </Button>
                                                <Button
                                                    variant="outline-primary"
                                                    className={`mb-3 col-5 ${
                                                        qrDetails.foreground_type ===
                                                            2 && "active"
                                                    }`}
                                                    onClick={() => {
                                                        setQrdetails(
                                                            (inputs) => ({
                                                                ...inputs,
                                                                foreground_type: 2,
                                                            })
                                                        );
                                                        setQrdetails(
                                                            (inputs) => ({
                                                                ...inputs,
                                                                gradient: {
                                                                    ...inputs.gradient,
                                                                    colorStops:
                                                                        [
                                                                            {
                                                                                offset: 0,
                                                                                color: gradientColor1,
                                                                            },
                                                                            {
                                                                                offset: 1,
                                                                                color: gradientColor2,
                                                                            },
                                                                        ],
                                                                },
                                                            })
                                                        );
                                                    }}
                                                >
                                                    {getFormattedMessage(
                                                        "globally.btn.gradient.label"
                                                    )}
                                                </Button>
                                                <div
                                                    className={`w-100 form-label ${
                                                        qrDetails.foreground_type ===
                                                        1
                                                            ? ""
                                                            : "d-none"
                                                    }`}
                                                >
                                                    {getFormattedMessage(
                                                        "qrcode.btn.foreground.label"
                                                    )}{" "}
                                                    {getFormattedMessage(
                                                        "globally.input.color.lable"
                                                    )}
                                                    <ReactColorPicker
                                                        onChangeColor={(c) =>
                                                            handleForgroundColor(
                                                                c
                                                            )
                                                        }
                                                        selectedColor={
                                                            qrDetails.foreground_color
                                                        }
                                                    />
                                                </div>
                                                <div
                                                    className={`w-100 form-label ${
                                                        qrDetails.foreground_type ===
                                                        2
                                                            ? ""
                                                            : "d-none"
                                                    }`}
                                                >
                                                    <ReactSelect
                                                        title={getFormattedMessage(
                                                            "globally.type.label"
                                                        )}
                                                        value={
                                                            qrDetails.foreground_gradient_style
                                                        }
                                                        errors={
                                                            errors[
                                                                "payment_type"
                                                            ]
                                                        }
                                                        isRequired={true}
                                                        defaultValue={
                                                            forgroundGradientStyleTypeDefaultValue[0]
                                                        }
                                                        multiLanguageOption={
                                                            forgroundGradientStyleTypeOptions
                                                        }
                                                        onChange={
                                                            onForgroundGradientStyleChange
                                                        }
                                                    />
                                                    <label className="mt-2 form-label">
                                                        {getFormattedMessage(
                                                            "qrcode.foreground-first.label"
                                                        )}{" "}
                                                        {getFormattedMessage(
                                                            "globally.input.color.lable"
                                                        )}
                                                    </label>
                                                    <ReactColorPicker
                                                        onChangeColor={(c) =>
                                                            handleForgroundColor1(
                                                                c
                                                            )
                                                        }
                                                        selectedColor={
                                                            qrDetails.foreground_color1
                                                        }
                                                    />
                                                    <label className="mt-2 form-label">
                                                        {getFormattedMessage(
                                                            "qrcode.foreground-second.label"
                                                        )}{" "}
                                                        {getFormattedMessage(
                                                            "globally.input.color.lable"
                                                        )}
                                                    </label>
                                                    <ReactColorPicker
                                                        onChangeColor={(c) =>
                                                            handleForgroundColor2(
                                                                c
                                                            )
                                                        }
                                                        selectedColor={
                                                            qrDetails.foreground_color2
                                                        }
                                                    />
                                                </div>
                                            </div>
                                            {/* background color */}
                                            <div className="w-100 form-label mt-3">
                                                {getFormattedMessage(
                                                    "qrcode.btn.background.label"
                                                )}{" "}
                                                {getFormattedMessage(
                                                    "globally.input.color.lable"
                                                )}
                                                <ReactColorPicker
                                                    onChangeColor={
                                                        handleBackgroundColorChange
                                                    }
                                                    selectedColor={
                                                        qrDetails.background_color
                                                    }
                                                />
                                            </div>
                                            {/* transparency */}
                                            <div className="d-flex mt-3 justify-content-between flex-wrap w-100 qr_style_btn ">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label col-12"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.background-color-transparency.label"
                                                    )}
                                                </label>

                                                <Form.Range
                                                    id="range"
                                                    className="w-100"
                                                    name="background_transparency"
                                                    value={
                                                        qrDetails.background_transparency *
                                                        100
                                                    }
                                                    onMouseEnter={() =>
                                                        setIsTooltipActive(true)
                                                    }
                                                    onMouseLeave={() =>
                                                        setIsTooltipActive(
                                                            false
                                                        )
                                                    }
                                                    onChange={(e) =>
                                                        handleBGTransparencyChange(
                                                            e
                                                        )
                                                    }
                                                />
                                                <TooltipComponent
                                                    active={isTooltipActive}
                                                    parent="#range"
                                                    className="w-100"
                                                >
                                                    {Math.round(
                                                        qrDetails.background_transparency *
                                                            100
                                                    )}
                                                </TooltipComponent>
                                            </div>
                                            {/* custom_eye_color */}
                                            <div className="d-flex mt-3 justify-content-between flex-wrap w-100 qr_style_btn ">
                                                <ReactSelect
                                                    title={getFormattedMessage(
                                                        "globally.input.custom-eyes-color.label"
                                                    )}
                                                    value={
                                                        qrDetails.custom_eye_color
                                                    }
                                                    errors={
                                                        errors["payment_type"]
                                                    }
                                                    defaultValue={
                                                        customeEyeColorTypeDefaultValue[0]
                                                    }
                                                    isRequired={true}
                                                    multiLanguageOption={
                                                        customeEyeColorTypeOptions
                                                    }
                                                    onChange={
                                                        onChangeCustomeEyeColor
                                                    }
                                                />
                                                <div
                                                    className={`w-100 ${
                                                        qrDetails
                                                            .custom_eye_color
                                                            .value === 1
                                                            ? "d-block"
                                                            : "d-none"
                                                    }`}
                                                >
                                                    <label className="mt-2">
                                                        {getFormattedMessage(
                                                            "globally.input.eyes-inner-color.label"
                                                        )}
                                                    </label>
                                                    <ReactColorPicker
                                                        onChangeColor={(c) =>
                                                            eyeInnerColorChnage(
                                                                c
                                                            )
                                                        }
                                                        selectedColor={
                                                            qrDetails.eye_inner_color
                                                        }
                                                    />
                                                    <label className="mt-2">
                                                        {getFormattedMessage(
                                                            "globally.input.eyes-outer-color.label"
                                                        )}
                                                    </label>
                                                    <ReactColorPicker
                                                        onChangeColor={(c) =>
                                                            eyeOuterColorChange(
                                                                c
                                                            )
                                                        }
                                                        selectedColor={
                                                            qrDetails.eye_outer_color
                                                        }
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Branding */}
                                    <div className="col-12 mt-3">
                                        <Button
                                            variant="outline-primary"
                                            className="w-100 qr_color_btn"
                                            onClick={() =>
                                                setShowOptions(!showOptions)
                                            }
                                        >
                                            <FontAwesomeIcon icon={faWrench} />{" "}
                                            {getFormattedMessage(
                                                "option.label"
                                            )}
                                        </Button>
                                        <div
                                            className={
                                                showOptions
                                                    ? "d-flex justify-content-center flex-wrap"
                                                    : "d-none"
                                            }
                                        >
                                            {/* Size */}
                                            <div className="col-12 mb-0">
                                                <InputGroup className="flex-nowrap mb-0 dropdown-side-btn">
                                                    <div className="col-12 pt-3 mb-0 position-relative">
                                                        <label
                                                            htmlFor="exampleInputEmail1"
                                                            className="form-label"
                                                        >
                                                            {getFormattedMessage(
                                                                "globally.input.size.lable"
                                                            )}
                                                            :
                                                        </label>{" "}
                                                        <span
                                                            id="my-element1"
                                                            className="form-label"
                                                            data-tooltip-content={placeholderText(
                                                                "Minimum.100px.and.Maximum.400px"
                                                            )}
                                                        >
                                                            <FontAwesomeIcon
                                                                icon={
                                                                    faQuestionCircle
                                                                }
                                                            />
                                                        </span>
                                                        <ReactTooltip anchorId="my-element1" />
                                                        <div className="col-12 d-flex justify-content-center align-items-center">
                                                            <div className="col-10">
                                                                <input
                                                                    type="text"
                                                                    name="size"
                                                                    value={
                                                                        qrDetails.size
                                                                    }
                                                                    className="form-control py-2"
                                                                    autoFocus={
                                                                        true
                                                                    }
                                                                    placeholder={placeholderText(
                                                                        "globally.placeholder.size.label"
                                                                    )}
                                                                    onKeyPress={(
                                                                        event
                                                                    ) =>
                                                                        numValidate(
                                                                            event
                                                                        )
                                                                    }
                                                                    onChange={(
                                                                        e
                                                                    ) =>
                                                                        onChangeInput(
                                                                            e
                                                                        )
                                                                    }
                                                                />
                                                            </div>
                                                            <div className="col-2 py-2 text-white home px-display">
                                                                {getFormattedMessage(
                                                                    "qrcode.btn.px.label"
                                                                )}
                                                            </div>
                                                        </div>
                                                        <span className="text-danger d-block fw-400 fs-small">
                                                            {errors["size"]
                                                                ? errors["size"]
                                                                : null}
                                                        </span>
                                                    </div>
                                                </InputGroup>
                                            </div>

                                            {/* Margin size */}
                                            <div className="d-flex mb-3 pt-3 justify-content-between flex-wrap w-100 qr_style_btn position-relative">
                                                <label
                                                    htmlFor="exampleInputEmail1"
                                                    className="form-label col-12"
                                                >
                                                    {getFormattedMessage(
                                                        "globally.input.margin-size.lable"
                                                    )}
                                                    :{" "}
                                                    <span
                                                        id="my-element"
                                                        className="form-label"
                                                        data-tooltip-content={placeholderText(
                                                            "Minimum.10px.and.Maximum.50px"
                                                        )}
                                                    >
                                                        <FontAwesomeIcon
                                                            icon={
                                                                faQuestionCircle
                                                            }
                                                        />
                                                    </span>
                                                </label>
                                                <ReactTooltip anchorId="my-element" />
                                                <input
                                                    type="text"
                                                    name="margin_size"
                                                    value={
                                                        qrDetails.margin_size
                                                    }
                                                    className="form-control"
                                                    placeholder={placeholderText(
                                                        "globally.placeholder.margin-size.label"
                                                    )}
                                                    onKeyPress={(event) =>
                                                        numValidate(event)
                                                    }
                                                    onChange={(e) =>
                                                        onChangeInput(e)
                                                    }
                                                />
                                            </div>
                                            {/* Error correction capability */}
                                            <div className="d-flex justify-content-between flex-wrap mb-3 w-100 qr_style_btn">
                                                <ReactSelect
                                                    title={getFormattedMessage(
                                                        "globally.input.error-correction-capability.label"
                                                    )}
                                                    value={
                                                        qrDetails.error_correction
                                                    }
                                                    errors={
                                                        errors["payment_type"]
                                                    }
                                                    defaultValue={
                                                        errorCorrectionTypeDefaultValue[0]
                                                    }
                                                    multiLanguageOption={
                                                        errorCorrectionTypeOptions
                                                    }
                                                    onChange={
                                                        onErrorCorrectionChange
                                                    }
                                                    isRequired={true}
                                                />
                                            </div>
                                        </div>
                                        {token ? (
                                            <button
                                                onClick={(event) =>
                                                    onSubmit(event)
                                                }
                                                className="btn btn-primary w-100 mt-3"
                                                type="submit"
                                                disabled={disable}
                                            >
                                                {placeholderText(
                                                    "globally.save-btn"
                                                )}
                                            </button>
                                        ) : (
                                            <button
                                                onClick={(event) =>
                                                    onSubmit(event)
                                                }
                                                className="btn btn-primary w-100 mt-3"
                                                type="submit"
                                            >
                                                {placeholderText(
                                                    "home.singup-to-save.btn.title"
                                                )}
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </Form>
                        </div>
                    </div>
                    <div
                        id="QrCodeStyleModel"
                        className="col-md-6 col-12 mt-md-0  mt-3 p-2 qr_code-card position-sticky top card mx-2"
                    >
                        <QrCodeStyle
                            dotsOptionsType={qrDetails.style}
                            qrUrl={qrDetails}
                            data={qrDetails}
                        />
                    </div>
                </div>
            </div>

            {/* <!-- start footer-section --> */}
            <HomeFooter />
            {/* <!-- end footer-section --> */}
        </>
    );
};

const mapStateToProps = (state) => {
    const { roles, qrCodeTypes } = state;
    return { roles, qrCodeTypes };
};

export default connect(mapStateToProps, {
    addHomeQrcodeAction,
    fetchQrcodeType,
    addToast,
})(Home);
