import React, { useEffect, useRef, useState } from "react";
import QRCodeStyling from "qr-code-styling";
import { Tokens, downloadExtentionOptions } from "../../../constants";
import { getFormattedMessage, getFormattedOptions } from "../../../shared/sharedMethod";
import Dropdown from 'react-bootstrap/Dropdown';
import { environment } from "../../../config/environment";
import QrcodeLoder from "../../../shared/components/loaders/QrcodeLoder";
import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAngleDown } from "@fortawesome/free-solid-svg-icons";

const qrCode = new QRCodeStyling({
    width: 400,
    height: 400,
    dotsOptions: {
        color: "#A561FF",
        type: "square",
    },
    imageOptions: {
        crossOrigin: "anonymous",
        margin: 20
    },
    margin: 10
});

const QrCodeStyle = (props) => {
    const { dotsOptionsType, qrUrl, data, isLoading } = props
    const [url, setUrl] = useState(environment.URL);
    const [fileExt, setFileExt] = useState({ value: 0, label: "Download" });
    const ref = useRef(null);
    const [backGroundColor, setBackgroundColor] = useState("")
    const [tokens, setTokens] = useState(localStorage.getItem(Tokens.ADMIN))

    useEffect(() => {
        qrCode?.append(ref.current);
    }, [isLoading]);

    useEffect(() => {
        if (data.size > 99 && data.size < 401) {
            qrCode?.update({
                height: data.size,
                width: data.size,
                margin: (data.margin_size > 10 && data.margin_size < 50) ? data.margin_size : 10
            })
        }
    }, [data.size, data.error_correction])

    useEffect(() => {
        if (data.size > 99 && data.size < 401) {
            qrCode?.update({
                height: data.size,
                width: data.size,
                margin: (data.margin_size > 10 && data.margin_size < 50) ? data.margin_size : 10
            })
        }
    }, [data.margin_size])

    if (data.error_correction?.value === 1) {
        qrCode?.update({
            qrOptions: {
                errorCorrectionLevel: "L"
            }
        })
    } else if (data.error_correction?.value === 2) {
        qrCode?.update({
            qrOptions: {
                errorCorrectionLevel: "M"
            }
        })
    } else if (data.error_correction?.value === 3) {
        qrCode?.update({
            qrOptions: {
                errorCorrectionLevel: "Q"
            }
        })
    } else if (data.error_correction?.value === 4) {
        qrCode?.update({
            qrOptions: {
                errorCorrectionLevel: "H"
            }
        })
    } else {
        qrCode?.update({
            qrOptions: {
                errorCorrectionLevel: "Q"
            }
        })
    }

    useEffect(() => {
        if (data.custom_eye_color?.value === 1) {
            qrCode?.update({
                cornersSquareOptions: {
                    color: data.eye_outer_color === null || data.eye_outer_color === "" ? "#A561FF" : data.eye_outer_color
                },
                cornersDotOptions: {
                    color: data.eye_inner_color === null || data.eye_inner_color === "" ? "#A561FF" : data.eye_inner_color
                }
            })
        } else {
            qrCode?.update({
                cornersDotOptions: {
                    color: ""
                }
            })
        }
    }, [data.eye_inner_color, data.custom_eye_color])

    useEffect(() => {
        if (data.custom_eye_color?.value === 1) {
            qrCode?.update({
                cornersSquareOptions: {
                    color: data.eye_outer_color === null || data.eye_outer_color === "" ? "#A561FF" : data.eye_outer_color
                },
                cornersDotOptions: {
                    color: data.eye_inner_color === null || data.eye_inner_color === "" ? "#A561FF" : data.eye_inner_color
                }
            })
        } else {
            qrCode?.update({
                cornersSquareOptions: {
                    color: ""
                }
            })
        }
    }, [data.eye_outer_color, data.custom_eye_color])

    useEffect(() => {
        setBackgroundColor(data.background_color)
    }, [data.background_color])

    qrCode?.update({
        backgroundOptions: {
            color: backGroundColor
        }
    })

    useEffect(() => {
        if (data.foreground_type === 2) {
            qrCode?.update({
                dotsOptions: {
                    gradient: {
                        type: data.gradient.type === 1 ? "linear" : "radial",
                        colorStops: data.gradient.colorStops
                    }
                }
            })
        }
    }, [data.gradient.colorStops[0].color, data.gradient.colorStops[1].color, data.gradient.type, data.foreground_type])

    useEffect(() => {
        if (data.foreground_type === 1) {
            qrCode?.update({
                dotsOptions: {
                    color: data.foreground_color,
                    gradient: null
                }
            })
        }
    }, [data.foreground_color, data.foreground_type])

    useEffect(() => {
        if (dotsOptionsType === 1) {
            qrCode?.update({
                dotsOptions: {
                    type: "square"
                }
            })
        } else if (dotsOptionsType === 2) {
            qrCode?.update({
                dotsOptions: {
                    type: "dots"
                }
            })
        } else if (dotsOptionsType === 3) {
            qrCode?.update({
                dotsOptions: {
                    type: "rounded"
                }
            })
        }
    }, [dotsOptionsType])

    useEffect(() => {
        qrCode?.update({
            data: url
        });
    }, [url]);

    useEffect(() => {
        if (qrUrl.type?.value === 1) {
            if (qrUrl.text_content !== "") {
                setUrl(qrUrl.text_content)
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 2) {
            if (qrUrl.url !== "") {
                setUrl(qrUrl.url)
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 3) {
            if (qrUrl.phone_number !== "") {
                setUrl("tel:" + qrUrl.phone_number)
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 4) {
            if (qrUrl.phone_number !== "") {
                setUrl("SMSTO:" + qrUrl.phone_number.split(" ").join("") + ":" + qrUrl.prefilled_message)
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 5) {
            if (qrUrl.email !== "") {
                setUrl("MATMSG:TO:" + qrUrl.email + ";SUB:" + qrUrl.prefilled_subject + ";BODY:" + qrUrl.prefilled_message + ";; ")
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 6) {
            if (qrUrl.wp_phone_number !== "") {
                setUrl("https://wa.me/" + qrUrl.wp_phone_number.split(" ").join("") + "?text=" + qrUrl.prefilled_message || "")
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 7) {
            if (qrUrl.phone_or_email !== "") {
                setUrl("facetime:" + qrUrl.phone_or_email)
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 8) {
            if (qrUrl.latitude !== "" && qrUrl.longitude !== "") {
                setUrl("https://www.google.ca/maps/@" + qrUrl.latitude + "," + qrUrl.longitude + ",14.78z")
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 9) {
            const hidden = qrUrl.wifi_is_hidden?.value === 1 ? "true" : "false"
            if (qrUrl.wifi_name !== "") {
                if (qrUrl.encryption?.value === 3) {
                    setUrl("WIFI:S:" + qrUrl.wifi_name + ";T:nopass;;")
                } else if (qrUrl.encryption?.value === 1 && qrUrl.password !== "") {
                    setUrl("WIFI:S:" + qrUrl.wifi_name + ";T:WEP;P:" + qrUrl.password + ";H:" + hidden + ";;")
                } else if (qrUrl.encryption?.value === 2 && qrUrl.password !== "") {
                    setUrl("WIFI:S:" + qrUrl.wifi_name + ";T:WPA;P:" + qrUrl.password + ";H:" + hidden + ";;")
                }
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 10) {

            if (qrUrl.event_name !== "") {
                const startDate = new Date(qrUrl.starts_on).toISOString().toString().replace(/-/g, "").replace(/:/g, "").slice(0, 15) + "Z"
                const endDate = new Date(qrUrl.ends_on).toISOString().toString().replace(/-/g, "").replace(/:/g, "").slice(0, 15) + "Z"

                let beginEvent = "BEGIN:VEVENT\r\n";
                let endEvent = "END:VEVENT";
                let eventDesc = "SUMMARY:" + qrUrl.event_name + "\r\n";
                let eventStart = "DTSTART:" + startDate + "\r\n";
                let eventEnd = "DTEND:" + endDate + "\r\n";
                let location = "LOCATION:" + qrUrl.geo_location + "\r\n";
                let desc = "DESCRIPTION:" + qrUrl.notes + "\r\n" + qrUrl.event_url + "\r\n";

                let generatedEvent = beginEvent + eventDesc + desc + eventStart + eventEnd + location + endEvent;
                setUrl(generatedEvent)
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 11) {
            const hidden = qrUrl.wifi_is_hidden?.value === 1 ? "true" : "false"
            if (qrUrl.address !== "" && qrUrl.amount !== "") {
                if (qrUrl.coin?.value === 3) {
                    setUrl("alrond:" + qrUrl.address + "/delegations?value=" + qrUrl.amount)
                } else if (qrUrl.coin?.value === 1) {
                    setUrl("bitcoin:" + qrUrl.address + "?amount=" + qrUrl.amount)
                } else if (qrUrl.coin?.value === 2) {
                    setUrl("ethereum:" + qrUrl.address + "?amount=" + qrUrl.amount)
                }
            } else {
                setUrl(environment.URL)
            }
        }
        if (qrUrl.type?.value === 12) {
            if (qrUrl.first_name !== "" && qrUrl.last_name !== "" && qrUrl.email !== "" && qrUrl.job_title !== "") {

                const birth_date = new Date(qrUrl.birthday)
                var month = (parseInt(birth_date.getUTCMonth()) + 1) < 10 ? "0" + (birth_date.getUTCMonth() + 1) : birth_date.getUTCMonth() + 1;
                var day = parseInt(birth_date.getUTCDate()) < 10 ? "0" + birth_date.getUTCDate() : birth_date.getUTCDate();
                var year = parseInt(birth_date.getUTCFullYear());
                let startnewdate = year + "" + month + "" + day;
                // qrUrl.notes + " " +
                const vcardBegin = "BEGIN:VCARD\r\n";
                const fullname = "N:" + qrUrl.first_name + " " + qrUrl.last_name + "\r\n"
                const jobTitle = "TITLE:" + qrUrl.job_title + "\r\n"
                const companyName = "ORG:" + qrUrl.company + "\r\n"
                const bday = "BIRTHDAY:" + startnewdate + "\r\n"
                const email = "EMAIL;TYPE=internet,work:" + qrUrl.email + "\r\n"
                const vcardUrl = "URL:" + qrUrl.website_url + "\r\n"
                const vcardNotes = "NOTE:" + qrUrl.notes + "\r\n"
                const address = "ADR;TYPE=WORK,PREF:;" + qrUrl.street_address + "," + qrUrl.city + "," + qrUrl.region + "," + qrUrl.country + "," + qrUrl.zip + "\r\n"
                const version = "VERSION:3.0\r\n"
                const vcardEnd = "END:VCARD"

                const vCardString = vcardBegin + fullname + bday + jobTitle + companyName + email + vcardUrl + vcardNotes + address + version + vcardEnd

                setUrl(vCardString)
            } else {
                setUrl(environment.URL)
            }
        }

        if (qrUrl.type?.value === 13) {
            if (qrUrl.paypal_email !== "") {
                if (qrUrl.paypal_type.value === 1) {
                    const paypalUrl = "https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=" + qrUrl.paypal_email + "&item_name=Hello+Ji&item_number=" + qrUrl.product_title + '&amount=' + qrUrl.price + '&currency_code=' + qrUrl.currency_code.label
                    // const paypalUrl = "https://www.paypal.com/cgi-bin/webscr?business=" + qrUrl.paypal_email + "&cmd=" + "_xclick" + "&currency_code=" + qrUrl.currency_code + "&amount=" + qrUrl.price + "&item_name=" + qrUrl.product_title + "&return="
                    //     + qrUrl.thanks_url + "&cancel_return=" + qrUrl.cancel_url
                    setUrl(paypalUrl)
                } else if (qrUrl.paypal_type.value === 2) {
                    const paypalUrl = "https://www.paypal.com/cgi-bin/webscr?business=" + qrUrl.paypal_email + "&cmd=" + "_cart" + "&currency_code=" + qrUrl.currency_code.label + "&amount=" + qrUrl.price + "&item_name=" + qrUrl.product_title + "&button_subtype=products&add=1&return=" + qrUrl.thanks_url + "&cancel_return=" + qrUrl.cancel_url
                    setUrl(paypalUrl)
                } else if (qrUrl.paypal_type.value === 3) {
                    const paypalUrl = "https://www.paypal.com/cgi-bin/webscr?business=" + qrUrl.paypal_email + "&cmd=" + "_donations" + "&currency_code=" + qrUrl.currency_code.label + "&amount=" + qrUrl.price + "&item_name=" + qrUrl.product_title + "&return="
                        + qrUrl.thanks_url + "&cancel_return=" + qrUrl.cancel_url
                    setUrl(paypalUrl)
                }

            }
        }
    }, [qrUrl])

    useEffect(() => {
        if (fileExt?.value !== 0) {
            qrCode?.download({
                name: data.name || "infy-qr-code",
                extension: fileExt.label
            });
        }
    }, [fileExt])

    const onDownloadClick = (ext) => {
        qrCode?.download({
            name: data.name || "infy-qr-code",
            extension: ext
        });
    };

    const downloadExtentionTypeOptions = getFormattedOptions(downloadExtentionOptions)

    return (
        <>
            <div className="qrcode  overflow-visible">
                {isLoading ? <QrcodeLoder size={data.spinnerSize} /> :
                    tokens ? <div ref={ref} id="canvas" /> :
                        <div className="qrShow">
                            <div ref={ref} id="canvas" />
                            <div id="qrCodeOverlay" className="qrCodeDemo shadow d-flex justify-content-center align-items-center fs-1" style={{ backdropFilter: 'blur(15px)' }}>
                                <Link to={"/register"} type="button" className="btn btn-primary shadow">
                                    {getFormattedMessage("globally.sign-up.label")}
                                </Link>
                            </div>
                        </div>}
                {tokens ?
                    <Dropdown className="table-dropdown download-qr-code">
                        <Dropdown.Toggle disabled={tokens ? false : true} className={`w-100 overflow-visible Qr-Code-Download d-flex align-items-center justify-content-center hide-arrow`} id="dropdown-basic">
                            {getFormattedMessage("react-data-table.download.placeholder")} <span className="ms-2"><FontAwesomeIcon icon={faAngleDown} /></span>
                        </Dropdown.Toggle>
                        <Dropdown.Menu className="w-100">
                            {
                                downloadExtentionTypeOptions?.map((d) => {
                                    return <Dropdown.Item key={d.id} onClick={() => onDownloadClick(d.name)}>{d.name.toUpperCase()}</Dropdown.Item>
                                })
                            }
                        </Dropdown.Menu>
                    </Dropdown> : ''
                    // <Button variant="primary" disabled={true} className='w-100 overflow-visible'>
                    // Download</Button>
                }
            </div>
        </>
    );
}

export default QrCodeStyle
