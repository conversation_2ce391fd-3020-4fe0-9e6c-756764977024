import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import { getAvatarName, getFormattedDate, getFormattedMessage, getFormattedOptions, placeholderText } from '../../shared/sharedMethod';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar"
import TabTitle from "../../shared/tab-title/TabTitle"
import ReactDataTable from "../../shared/table/ReactDataTable"
import DeleteQrCode from "./DeleteQrCode"
import { fetchQrcodes } from '../../store/action/qrCodeAction';
import { downloadExtentionOptions, typeOptions } from '../../constants';
import ActionDropDownButton from "../../shared/action-buttons/ActionDropDownButton"
import Dropdown from "react-bootstrap/Dropdown";
import { faDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { saveAs } from 'file-saver';
import ImageModal from '../../shared/imageModal/ImageModal';

const QrCode = (props) => {
    const { fetchQrcodes, qrcode, totalRecord, isLoading } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);
    const [showImageSlider, setShowImageSlider] = useState({
        display: false,
        src: '',
        name: ""
    })

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    const closeImageSlider = () => {
        setShowImageSlider({
            display: false,
            src: '',
            name: ""
        })
    }

    const itemsValue = qrcode.length >= 0 && qrcode.map(qrcode => ({
        date: getFormattedDate(qrcode?.attributes?.created_at, "d-m-y"),
        time: moment(qrcode?.attributes?.created_at).format('LT'),
        name: qrcode?.attributes?.name,
        project_name: qrcode?.attributes?.project_name,
        type: qrcode?.attributes?.type,
        type_name: placeholderText(typeOptions.filter(d => d.id === qrcode?.attributes?.type)[0]?.name),
        id: qrcode.id,
        image: qrcode?.attributes?.image
    }));

    const onChange = (filter) => {
        fetchQrcodes(filter, true)
    };


    const goToEdit = (item) => {
        const id = item.id;
        window.location.href = '#/app/qrcode/edit/' + id;
    };

    const downloadExtentionTypeOptions = getFormattedOptions(downloadExtentionOptions)

    const onDownloadClick = (e, url, name, ext) => {
        e.preventDefault();
        saveAs(url, `${name}.${ext}`);
    }

    const columns = [
        {
            name: getFormattedMessage('react-data-table.name.column.title'),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                const imageUrl = row.image ? row.image : null;
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/qrcode/detail/${row.id}`}>
                            {imageUrl ?
                                <img src={imageUrl} height='50' width='50' alt='User Image'
                                    className='image image-mini image-effect' /> :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName(row.name)}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/qrcode/detail/${row.id}`} className='text-decoration-none'>{row.name}</Link>
                        <span>{row.email}</span>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage("react-data-table.qr-code-types.label"),
            selector: row => row.type_name,
            sortField: 'type_name',
            sortable: false,
        },
        {
            name: getFormattedMessage("globally.input.project.lable"),
            selector: row => row.project_name,
            sortField: 'project_name',
            sortable: false,
            cell: row => {
                return (
                    <span>
                        {row.project_name || "N/A"}
                    </span>
                )
            }
        },
        // {
        //     name: getFormattedMessage('globally.react-table.column.created-date.label'),
        //     selector: row => row.date,
        //     sortField: 'created_at',
        //     sortable: true,
        //     cell: row => {
        //         return (
        //             <span className='badge bg-light-info'>
        //                 <div className='mb-1'>{row.time}</div>
        //                 <div>{row.date}</div>
        //             </span>
        //         )
        //     }
        // },
        {
            name: getFormattedMessage('react-data-table.download.placeholder'),
            cell: row => {
                return (
                    <div className='d-flex justify-content-center align-items-center'>
                        <div className='me-2'>
                            <img src={row?.image} height='50' width='50' alt='QR Code Image'
                                className='image image-mini image-effect cursor-pointer' onClick={() => setShowImageSlider({
                                    display: true,
                                    src: row?.image,
                                    name: row?.name
                                })} />
                        </div>
                        <Dropdown className={`table-dropdown`}>
                            <Dropdown.Toggle className='text-primary hide-arrow bg-transparent border-0 p-0' id="dropdown-basic">
                                <FontAwesomeIcon icon={faDownload} className={'fs-3'} />
                            </Dropdown.Toggle>
                            <Dropdown.Menu className="w-100">
                                {
                                    downloadExtentionTypeOptions?.map((d) => {
                                        return <Dropdown.Item key={d.id}
                                            onClick={(e) => onDownloadClick(e, row.image, d.name, d.name)}>{d.name.toUpperCase()}
                                        </Dropdown.Item>
                                    })
                                }
                            </Dropdown.Menu>
                        </Dropdown>
                    </div>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionDropDownButton
                item={row}
                goToEditProduct={goToEdit}
                isEditMode={true}
                onClickDeleteModel={onClickDeleteModel}
            />

        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('qrcode.title')} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                ButtonValue={getFormattedMessage('qrcode.create.title')}
                to='#/app/qrcode/create' totalRows={totalRecord} isLoading={isLoading} />
            <DeleteQrCode onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
            <ImageModal display={showImageSlider} closeSlider={closeImageSlider} onDownloadClick={onDownloadClick} />
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { qrcode, totalRecord, isLoading } = state;
    return { qrcode, totalRecord, isLoading }
};
export default connect(mapStateToProps, { fetchQrcodes })(QrCode);
