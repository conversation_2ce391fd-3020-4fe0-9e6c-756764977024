import React, { useEffect, useState } from 'react';
import { Tab, Tabs } from 'react-bootstrap';
import { connect, useDispatch } from 'react-redux';
import QrcodeType from '../QrcodeType';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import { fetchProjects } from "../../../store/action/projectAction";
import { backgroundTransparencyArr, cryptoCoinOptions, currencyCodeOptions, customeEyeColorOptions, encryptionOptions, errorCorrectionArr, Filters, forgroundGradientStyleOptions, payPalTypesOptions, toastType, Tokens, typeDynamicOptions, typeOptions, wifiHiddenOptions } from "../../../constants";
import { addQrcode, editQrcode, fetchQrcodeType } from "../../../store/action/qrCodeAction";
import * as EmailValidator from 'email-validator';
import { addToast } from "../../../store/action/toastAction";
import { validate } from 'bitcoin-address-validation';
import { isAddress } from 'ethereum-address';
import QrCodeBasicForm from '../QrCodeBasicForm';
import QrCodeDesign from '../QrCodeDesign';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import countryData from 'country-telephone-data';

const QrcodeFrom = ( props ) => {
    const [ key, setKey ] = useState( 'Type' );
    const { homeQrcodeDetails, project, fetchProjects, singleQrCode, isEdit, fetchQrcodeType, qrCodeTypes, selectedQrType } = props;

    const navigate = useNavigate();
    const [ showColor, setShowColor ] = useState( false )
    const [ showOptions, setShowOptions ] = useState( false )
    const [ gradientColor1, setGradientColor1 ] = useState( singleQrCode ? singleQrCode[ 0 ].foreground_color1 : '' )
    const [ gradientColor2, setGradientColor2 ] = useState( singleQrCode ? singleQrCode[ 0 ].foreground_color2 : '' )
    const [ backgroundColor, setBackgroundColor ] = useState( "#ffffff" )
    const [ isTooltipActive, setIsTooltipActive ] = useState( false )
    const [ modalShowProject, setModalShowProject ] = useState( false );
    const [ defultArray, setDefultArray ] = useState( [] );
    const [ projectLength, setProjectLength ] = useState( 0 )
    const [ countryCode, setCountryCode ] = useState( "+91" )
    const [ phoneNumber, setPhoneNumber ] = useState( "" )
    const [ wPhoneNumber, setWPhoneNumber ] = useState( "" )
    const [ phone, setPhone ] = useState( "" )
    const [ isLoading, setIsLoading ] = useState( false )
    const [ getTitle, setTitel ] = useState( "globally.product.code.title" )
    const [ enableEyesColor, setEnableEyesColor ] = useState( false )
    const [ enableColorHorizontal, setEnableColorHorizontal ] = useState( true )
    const [ enableColorRadial, setEnableColorRadial ] = useState( false )
    const [ defaulytColorType, setDefaultColorType ] = useState( 1 )
    const [ isDark, setDark ] = useState( false )

    setInterval( () => {
        const dark = localStorage.getItem( 'isDarkMod' )
        if ( dark === 'true' ) {
            setDark( true )
        } else (
            setDark( false )
        )
    }, 50 );


    const dispatch = useDispatch()

    const [ qrDetails, setQrdetails ] = useState( {
        name: singleQrCode ? singleQrCode[ 0 ].name : '',
        project_id: singleQrCode ? singleQrCode[ 0 ]?.project_id === null ? { label: getFormattedMessage( "none.title" ), value: null } : singleQrCode[ 0 ]?.project_id : { label: getFormattedMessage( "none.title" ), value: null },
        type: singleQrCode ? singleQrCode[ 0 ].type : { label: placeholderText( "globally.input.text.lable" ), value: 1 },
        style: singleQrCode ? singleQrCode[ 0 ].style : 1,
        foreground_type: singleQrCode ? singleQrCode[ 0 ].foreground_type : 1,
        foreground_color1: singleQrCode ? singleQrCode[ 0 ].foreground_color1 : '#A561FF ',
        foreground_color2: singleQrCode ? singleQrCode[ 0 ].foreground_color2 : '#A561FF ',
        background_color: singleQrCode ? singleQrCode[ 0 ].background_color : "#ffffff",
        background_transparency: singleQrCode ? singleQrCode[ 0 ].background_transparency : 0,
        custom_eye_color: singleQrCode ? {
            label: singleQrCode[ 0 ].custom_eye_color.label,
            value: singleQrCode[ 0 ].custom_eye_color.value
        } : { label: placeholderText( 'globally.input.no.lable' ), value: 2 },
        eye_inner_color: singleQrCode ? singleQrCode[ 0 ].eye_inner_color : '#A561FF ',
        eye_outer_color: singleQrCode ? singleQrCode[ 0 ].eye_outer_color : '#A561FF',
        error_correction: singleQrCode ? {
            label: singleQrCode[ 0 ].error_correction.label,
            value: singleQrCode[ 0 ].error_correction.value
        } : { label: placeholderText( 'qrcode.error-correction-capability.low.label' ), value: 1 },
        size: singleQrCode ? singleQrCode[ 0 ].size : 400,
        margin_size: singleQrCode ? singleQrCode[ 0 ].margin_size : 10,
        foreground_color: singleQrCode ? singleQrCode[ 0 ].foreground_color : '#A561FF',
        color: "",
        gradient: {
            type: 1,
            colorStops: [ { offset: 0, color: gradientColor1 }, { offset: 1, color: gradientColor2 } ]
        },
        foreground_gradient_style: singleQrCode ? singleQrCode[ 0 ].foreground_gradient_style : { label: placeholderText( "qrcode.horizontal.lable" ), value: 1 },

        birth_date: singleQrCode ? singleQrCode[ 0 ].birth_date : new Date,
        text_content: singleQrCode ? singleQrCode[ 0 ].text_content : '',
        url: singleQrCode ? singleQrCode[ 0 ].url : '',
        phone_number: singleQrCode ? singleQrCode[ 0 ].phone_number : `${countryCode} ${phoneNumber}`,
        prefilled_message: singleQrCode ? singleQrCode[ 0 ].prefilled_message : '',
        email: singleQrCode ? singleQrCode[ 0 ].email : '',
        prefilled_subject: singleQrCode ? singleQrCode[ 0 ].prefilled_subject : '',
        wp_phone_number: singleQrCode ? singleQrCode[ 0 ].wp_phone_number : '',
        phone_or_email: singleQrCode ? singleQrCode[ 0 ].phone_or_email : '',
        latitude: singleQrCode ? singleQrCode[ 0 ].latitude : '',
        longitude: singleQrCode ? singleQrCode[ 0 ].longitude : '',
        wifi_name: singleQrCode ? singleQrCode[ 0 ].wifi_name : '',
        encryption: singleQrCode ? singleQrCode[ 0 ].encryption?.value === undefined ? { label: placeholderText( "qrcode.wep.label" ), value: 1 } : singleQrCode[ 0 ].encryption : { label: placeholderText( "qrcode.wep.label" ), value: 1 },
        password: singleQrCode ? singleQrCode[ 0 ].password : '',
        wifi_is_hidden: singleQrCode ? singleQrCode[ 0 ].wifi_is_hidden?.value === undefined ? { label: placeholderText( "globally.input.yes.lable" ), value: 1 } : singleQrCode[ 0 ].wifi_is_hidden : { label: placeholderText( 'globally.input.yes.lable' ), value: 1 },
        event_name: singleQrCode ? singleQrCode[ 0 ].event_name : '',
        geo_location: singleQrCode ? singleQrCode[ 0 ].geo_location : '',
        event_url: singleQrCode ? singleQrCode[ 0 ].event_url : '',
        notes: singleQrCode ? singleQrCode[ 0 ].notes : '',
        starts_on: singleQrCode ? singleQrCode[ 0 ].starts_on : new Date,
        ends_on: singleQrCode ? singleQrCode[ 0 ].ends_on : new Date,
        timezone: singleQrCode ? singleQrCode[ 0 ].timezone : { value: 1, label: 'Africa/Abidjan' },
        coin: singleQrCode ? singleQrCode[ 0 ].coin : { label: placeholderText( "qrcode.bitcoin.title" ), value: 1 },
        address: singleQrCode ? singleQrCode[ 0 ].address : '',
        amount: singleQrCode ? singleQrCode[ 0 ].amount : '',
        first_name: singleQrCode ? singleQrCode[ 0 ].first_name : '',
        last_name: singleQrCode ? singleQrCode[ 0 ].last_name : '',
        website_url: singleQrCode ? singleQrCode[ 0 ].website_url : '',
        company: singleQrCode ? singleQrCode[ 0 ].company : '',
        job_title: singleQrCode ? singleQrCode[ 0 ].job_title : '',
        birthday: singleQrCode ? singleQrCode[ 0 ].birthday : new Date,
        street_address: singleQrCode ? singleQrCode[ 0 ].street_address : '',
        city: singleQrCode ? singleQrCode[ 0 ].city : '',
        zip: singleQrCode ? singleQrCode[ 0 ].zip : '',
        region: singleQrCode ? singleQrCode[ 0 ].region : '',
        country: singleQrCode ? singleQrCode[ 0 ].country : '',
        paypal_type: singleQrCode ? singleQrCode[ 0 ].paypal_type : { label: placeholderText( "qrcode.buy-now.lable" ), value: 1 },
        paypal_email: singleQrCode ? singleQrCode[ 0 ].paypal_email : '',
        product_title: singleQrCode ? singleQrCode[ 0 ].product_title : '',
        currency_code: singleQrCode ? singleQrCode[ 0 ].currency_code : { label: "USD", value: 1 },
        price: singleQrCode ? singleQrCode[ 0 ].price : '',
        thanks_url: singleQrCode ? singleQrCode[ 0 ].thanks_url : '',
        cancel_url: singleQrCode ? singleQrCode[ 0 ].cancel_url : '',
        spinnerSize: 400,
        phone: singleQrCode ? singleQrCode[ 0 ].phone : '',
    } )

    useEffect( () => {
        setShowColor( isEdit )
        setShowOptions( isEdit )
    }, [ isEdit ] )

    useEffect( () => {
        if ( localStorage.getItem( Tokens.QRCODE_DETAILS ) && project ) {
            const data = JSON.parse( localStorage.getItem( Tokens.QRCODE_DETAILS ) )
            if ( qrCodeTypes?.includes( data?.type?.label ) ) {
                setQrdetails( JSON.parse( localStorage.getItem( Tokens.QRCODE_DETAILS ) ) )
                localStorage.removeItem( Tokens.QRCODE_DETAILS )
            } else {
                dispatch( addToast( { text: getFormattedMessage( "This.Type.of.QR.Code.is.Not.Available.in.Your.Plan" ), type: toastType.ERROR } ) );
                localStorage.removeItem( Tokens.QRCODE_DETAILS )
            }

        }
    }, [ qrCodeTypes ] )

    useEffect( () => {
        if ( homeQrcodeDetails?.type ) {
            if ( qrCodeTypes.length > 0 ) {
                setQrdetails( homeQrcodeDetails )
            }
        }
    }, [ homeQrcodeDetails, project ] )

    useEffect( () => {
        if ( enableEyesColor ) {
            setQrdetails( inputs => ( {
                ...inputs,
                custom_eye_color: { label: placeholderText( 'globally.input.no.lable' ), value: 1 }
            } ) )
        } else {
            setQrdetails( inputs => ( {
                ...inputs,
                custom_eye_color: { label: placeholderText( 'globally.input.no.lable' ), value: 2 }
            } ) )
        }
    }, [ enableEyesColor ] )

    useEffect( () => {
        if ( singleQrCode ) {
            setQrdetails( qrDetails )
            setPhoneNumber( singleQrCode[ 0 ]?.phone_number )
            setWPhoneNumber( singleQrCode[ 0 ].wp_phone_number )
            setPhone( singleQrCode[ 0 ].phone )
            setCountryCode( singleQrCode[ 0 ]?.country_code ? singleQrCode[ 0 ]?.country_code : "+91" )

            singleQrCode[ 0 ].custom_eye_color.value && singleQrCode[ 0 ].custom_eye_color.value == "1" ? setEnableEyesColor( true ) : setEnableEyesColor( false )
            singleQrCode && singleQrCode[ 0 ].foreground_gradient_style.value === 1 ?
                ( setDefaultColorType( 1 ),
                    setEnableColorHorizontal( true ),
                    setEnableColorRadial( false ) )
                : ( setDefaultColorType( 2 ),
                    setEnableColorHorizontal( false ),
                    setEnableColorRadial( true ) )
        }
    }, [ singleQrCode ] )

    const [ errors, setErrors ] = useState( {
        name: '',
        text_content: '',
        latitude: "",
        longitude: "",
        url: '',
        phone_number: '',
        email: '',
        wp_phone_number: '',
        phone_or_email: '',
        wifi_name: '',
        event_name: '',
        paypal_email: '',
        product_title: '',
        currency_code: '',
        price: '',
        address: '',
        amount: '',
        coin: '',
    } );

    useEffect( () => {
        setTimeout( () => setIsLoading( false ), 1800 );
    }, [ isLoading ] )

    useEffect( () => {
        fetchQrcodeType()
        if ( project ) {
            fetchProjects( Filters.OBJ )
        }
    }, [] )

    useEffect( () => {
        if ( project ) {
            if ( ( project?.length - projectLength === 1 ) ) {
                setQrdetails( inputs => ( { ...inputs, project_id: { label: project[ project.length - 1 ].attributes.name, value: project[ project.length - 1 ].id } } ) )
            }
            setProjectLength( project?.length )
        }
    }, [ project ] )

    const onChangeInput = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, [ e.target.name ]: e.target.value } ) )
        setIsLoading( true )
        if ( e.target.name === "size" ) {
            if ( e.target.value < 100 ) {
                setQrdetails( inputs => ( { ...inputs, spinnerSize: 100 } ) )
                setErrors( { size: getFormattedMessage( "qrcode-size.input.validate.label" ) } )
            } else if ( e.target.value > 400 ) {
                setQrdetails( inputs => ( { ...inputs, spinnerSize: 400 } ) )
                setErrors( { size: getFormattedMessage( "qrcode-size.input.validate.label" ) } )
            } else {
                setErrors( {
                    ...errors,
                    size: ""
                } )
                setQrdetails( inputs => ( { ...inputs, spinnerSize: e.target.value } ) )
            }
        } else if ( e.target.name === "margin_size" ) {
            if ( e.target.value < 10 ) {
                setErrors( { margin_size: getFormattedMessage( "qrcode-margin-size.input.validate.label" ) } )
            } else if ( e.target.value > 50 ) {
                setErrors( { margin_size: getFormattedMessage( "qrcode-margin-size.input.validate.label" ) } )
            } else {
                setErrors( {
                    ...errors,
                    margin_size: ""
                } )
            }
        }
        // else if (e.target.name === "phone_or_email") {
        //     const { value } = e.target
        //     var mailFormat = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})|([0-9]{10})+$/;
        //     if (!mailFormat.test(value)) {
        //         let errorss = {}
        //         errorss['phone_or_email'] = getFormattedMessage("please.enter.valid.phone.number.or.email.address.error.message");
        //         setErrors(errorss);
        //         return true
        //     }
        // }
        else if ( qrDetails.coin.value === 1 && e.target.name === 'address' ) {
            const data = validate( e.target.value )
            if ( data === false ) {
                let errorss = {}
                errorss[ 'address' ] = getFormattedMessage( "invalid.bitcoin.address.error.message" );
                setErrors( errorss );
                return false;
            } else {
                setErrors( '' )
            }
        } else if ( qrDetails.coin.value === 2 && e.target.name === 'address' ) {
            const data = isAddress( e.target.value )
            if ( data === false ) {
                let errorss = {}
                errorss[ 'address' ] = getFormattedMessage( "invalid.ethereum.address.error.message" );
                setErrors( errorss );
                return false;
            } else {
                setErrors( '' )
            }
        } else if ( qrDetails.coin.value === 3 && e.target.name === 'address' ) {
            const data = isAddress( e.target.value )
            if ( data === false ) {
                let errorss = {}
                errorss[ 'address' ] = getFormattedMessage( "invalid.Elrond.address.error.message" );
                setErrors( errorss );
                return false;
            } else {
                setErrors( '' )
            }
        } else {
            setErrors( '' )
        }
    };

    const prepareFormData = ( data, dataURL ) => {
        const formData = {
            name: data.name,
            project_id: data.project_id.value,
            type: data.type.value,
            style: data.style,
            foreground_type: data.foreground_type,
            foreground_gradient_style: data.foreground_type,
            foreground_color: data.foreground_color,
            foreground_color1: data.foreground_color1,
            foreground_color2: data.foreground_color2,
            background_color: data.background_color,
            background_transparency: data.background_transparency,
            custom_eye_color: data.custom_eye_color.value,
            eye_inner_color: data.eye_inner_color,
            eye_outer_color: data.eye_outer_color,
            size: data.size,
            margin_size: data.margin_size,
            error_correction: data.error_correction.value,
            text_content: data.text_content,
            url: data.url,
            phone_number: "+" + data.phone_number.split( "+" ).pop(),
            prefilled_message: data.prefilled_message,
            email: data.email,
            prefilled_subject: data.prefilled_subject,
            wp_phone_number: "+" + data.wp_phone_number.split( "+" ).pop(),
            phone_or_email: data.phone_or_email,
            latitude: data.latitude,
            longitude: data.longitude,
            wifi_name: data.wifi_name,
            encryption: data.encryption,
            password: data.password,
            wifi_is_hidden: data.wifi_is_hidden,
            event_name: data.event_name,
            geo_location: data.geo_location,
            event_url: data.event_url,
            notes: data.notes,
            starts_on: data.starts_on,
            ends_on: data.ends_on,
            timezone: data.timezone,
            coin: data.coin,
            address: data.address,
            amount: data.amount,
            first_name: data.first_name,
            last_name: data.last_name,
            website_url: data.website_url,
            company: data.company,
            job_title: data.job_title,
            birthday: data.birthday,
            street_address: data.street_address,
            city: data.city,
            zip: data.zip,
            region: data.region,
            country: data.country,
            paypal_email: data.paypal_email,
            product_title: data.product_title,
            currency_code: data.currency_code,
            price: data.price,
            thanks_url: data.thanks_url,
            cancel_url: data.cancel_url,
            paypal_type: data.paypal_type,
            image: dataURL,
            phone: "+" + data.phone.split( "+" ).pop()
        }
        return formData
    };

    useEffect( () => {
        if ( qrCodeTypes.length ) {
            setDefultArray( [] )
            qrCodeTypes.map( ( item ) => {
                typeDynamicOptions.filter( ( data ) => {
                    if ( item === data.name ) {
                        typeOptions.filter( dynamic => {
                            if ( data.id === dynamic.id ) {
                                setDefultArray( defultArray => [ ...defultArray, dynamic ] )
                            }
                        } )
                    }
                } )
            } )
        }
    }, [ qrCodeTypes ] )

    const qrTypeOptions = getFormattedOptions( defultArray )
    const qrTypeDefaultValue = qrTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    useEffect( () => {
        if ( singleQrCode ) {
            setQrdetails( inputs => ( {
                ...inputs, type: singleQrCode ? {
                    label: getFormattedMessage( singleQrCode[ 0 ].type.label ),
                    value: singleQrCode[ 0 ].type.value
                } : { value: qrTypeDefaultValue[ 0 ].value, label: qrTypeDefaultValue[ 0 ].label }
            } ) )
        } else {
            if ( qrTypeDefaultValue.length && qrTypeDefaultValue[ 0 ]?.value ) {
                setQrdetails( inputs => ( {
                    ...inputs, type: { value: qrTypeDefaultValue[ 0 ].value, label: qrTypeDefaultValue[ 0 ].label }
                } ) )
            }
        }
    }, [ qrTypeDefaultValue.length ] )

    const encryptionTypeOptions = getFormattedOptions( encryptionOptions )
    const encryptionTypeDefaultValue = encryptionTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )


    const projectOptions = project.map( ( d ) => {
        return {
            name: d.attributes.name,
            id: d.id
        }
    } )

    projectOptions?.unshift( { name: placeholderText( "none.title" ), id: null } )

    const projectTypeOptions = getFormattedOptions( projectOptions )
    const projectTypeDefaultValue = projectTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )


    const wifiHiddenTypeOptions = getFormattedOptions( wifiHiddenOptions )
    const wifiHiddenTypeDefaultValue = wifiHiddenTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    const cryptoCoinTypeOptions = getFormattedOptions( cryptoCoinOptions )
    const cryptoCoinTypeDefaultValue = cryptoCoinTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    const payPalTypeOptions = getFormattedOptions( payPalTypesOptions )
    const payPalTypeDefaultValue = payPalTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    const currencyCodeTypeOptions = getFormattedOptions( currencyCodeOptions )
    const currencyCodeTypeDefaultValue = currencyCodeTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    const forgroundGradientStyleTypeOptions = getFormattedOptions( forgroundGradientStyleOptions )
    const forgroundGradientStyleTypeDefaultValue = forgroundGradientStyleTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    const customeEyeColorTypeOptions = getFormattedOptions( customeEyeColorOptions )
    const customeEyeColorTypeDefaultValue = customeEyeColorTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    const errorCorrectionTypeOptions = getFormattedOptions( errorCorrectionArr )
    const errorCorrectionTypeDefaultValue = errorCorrectionTypeOptions.map( ( option ) => {
        return {
            value: option.id,
            label: option.name
        }
    } )

    const onProjectChange = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, project_id: e } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const refactor = ( qrCode, obj ) => {
        const state = {
            name: qrDetails.name,
            project_id: qrDetails.project_id,
            type: obj,
            style: qrDetails.style,
            foreground_type: qrDetails.foreground_type,
            foreground_color1: qrDetails.foreground_color1,
            foreground_color2: qrDetails.foreground_color2,
            background_color: qrDetails.background_color,
            background_transparency: qrDetails.background_transparency,
            custom_eye_color: qrDetails.custom_eye_color,
            eye_inner_color: qrDetails.eye_inner_color,
            eye_outer_color: qrDetails.eye_outer_color,
            error_correction: qrDetails.error_correction,
            size: qrDetails.size,
            margin_size: qrDetails.margin_size,
            foreground_color: qrDetails.foreground_color,
            color: "",
            gradient: qrDetails.gradient,
            foreground_gradient_style: qrDetails.foreground_gradient_style,

            birth_date: new Date,
            text_content: '',
            url: '',
            phone_number: `${countryCode} ${phoneNumber}`,
            prefilled_message: '',
            email: '',
            prefilled_subject: '',
            wp_phone_number: '',
            phone_or_email: '',
            latitude: '',
            longitude: '',
            wifi_name: '',
            encryption: { label: placeholderText( "qrcode.wep.label" ), value: 1 },
            password: '',
            wifi_is_hidden: { label: placeholderText( 'globally.input.yes.lable' ), value: 1 },
            event_name: '',
            geo_location: '',
            event_url: '',
            notes: '',
            starts_on: new Date,
            ends_on: new Date,
            timezone: { value: 1, label: 'Africa/Abidjan' },
            coin: { label: placeholderText( "qrcode.bitcoin.title" ), value: 1 },
            address: '',
            amount: '',
            first_name: '',
            last_name: '',
            website_url: '',
            company: '',
            job_title: '',
            birthday: new Date,
            street_address: '',
            city: '',
            zip: '',
            region: '',
            country: '',
            paypal_type: { label: placeholderText( "qrcode.buy-now.lable" ), value: 1 },
            paypal_email: '',
            product_title: '',
            currency_code: { label: "USD", value: 1 },
            price: '',
            thanks_url: '',
            cancel_url: '',
            spinnerSize: 400
        }
        setQrdetails( state )
        return state
    }

    const onTypeChange = ( e ) => {
        setPhoneNumber( '' )
        setWPhoneNumber( '' )
        setQrdetails( inputs => ( { ...inputs, type: e } ) )
        // setQrdetails(refactor(qrDetails, e))
        setIsLoading( true )
        setErrors( '' )
    }

    const onclickTyep = ( e, items ) => {
        e.preventDefault();
        setPhoneNumber( '' )
        setWPhoneNumber( '' )
        setQrdetails( inputs => ( { ...inputs, type: { value: items.id, label: getFormattedMessage( items.name ) } } ) )
        setKey( 'basic-details' )
        // setQrdetails(refactor(qrDetails, e))
        setIsLoading( true )
        setErrors( '' )
    }

    const onEncChange = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, encryption: e } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const onCryptoChange = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, coin: e } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const onPayPalTypeChange = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, paypal_type: e } ) )
        setTitel( e.value === 1 ? "globally.product.code.title" : 'globally.product.name.title' )
        setIsLoading( true )
        setErrors( '' )
    }

    const onCurrencyCodeTypeChange = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, currency_code: e } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const onForgroundGradientStyleChange = ( e ) => {
        setQrdetails( inputs => ( {
            ...inputs, gradient: {
                ...inputs.gradient,
                type: e.value,
            },
            foreground_gradient_style: e
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    useEffect( () => {
        setQrdetails( inputs => ( {
            ...inputs, gradient: {
                ...inputs.gradient,
                type: defaulytColorType,
            },
            foreground_gradient_style: defaulytColorType === 1 ? { label: placeholderText( "qrcode.horizontal.lable" ), value: 1 } : { label: placeholderText( "qrcode.radial.lable" ), value: 2 }
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }, [ defaulytColorType ] )

    useEffect( () => {
        setDefaultColorType( enableColorHorizontal === true ? 1 : 2 )
    }, [ enableColorHorizontal ] )

    useEffect( () => {
        setDefaultColorType( enableColorRadial === true ? 2 : 1 )
    }, [ enableColorRadial ] )

    const onChangeCustomeEyeColor = ( e ) => {
        setQrdetails( inputs => ( {
            ...inputs,
            custom_eye_color: e
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const onErrorCorrectionChange = ( e ) => {
        setQrdetails( inputs => ( {
            ...inputs,
            error_correction: e
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const onWifiHiddenChange = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, wifi_is_hidden: e } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const onTimeZoneChange = ( e ) => {
        setQrdetails( inputs => ( { ...inputs, timezone: e } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    let aryIannaTimeZones = Intl.supportedValuesOf( 'timeZone' );

    let timeZonArr = aryIannaTimeZones.map( ( timeZone, i ) => {
        return {
            value: i + 1,
            label: timeZone
        }
    } );

    const onChangeStartDate = ( date ) => {
        setQrdetails( inputs => ( { ...inputs, starts_on: date } ) )
        setIsLoading( true )

        if ( qrDetails.ends_on - date <= 0 ) {
            setErrors( { ends_on: getFormattedMessage( "ends.on.date.must.be.a.greater.than.Starts.on.date.error.message" ) } )
        } else {
            setErrors( '' )
        }
    };

    const onChangeEndDate = ( date ) => {
        setQrdetails( inputs => ( { ...inputs, ends_on: date } ) )
        setIsLoading( true )

        if ( date - qrDetails.starts_on <= 0 ) {
            setErrors( { ends_on: getFormattedMessage( "ends.on.date.must.be.a.greater.than.Starts.on.date.error.message" ) } )
        } else {
            setErrors( '' )
        }

    };

    const onChangeDOB = ( date ) => {
        setQrdetails( inputs => ( { ...inputs, birthday: date } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const handleBGTransparencyChange = ( e ) => {
        if ( Number( e.target.value ) < 0 ) {
            setQrdetails( inputs => ( { ...inputs, [ e.target.name ]: '0' } ) )
        } else if ( Number( e.target.value ) > 100 ) {
            setQrdetails( inputs => ( { ...inputs, [ e.target.name ]: ( 100 / 100 )?.toString() } ) )
        } else if ( e.target.value === "00" || e.target.value === "" || e.target.value?.trim()?.length === 0 ) {
            const transparency = backgroundTransparencyArr.filter( ( t ) => t.id?.toString() === "0" )
            if ( backgroundColor.length > 7 ) {
                const bg = backgroundColor.slice( 0, -2 )
                setQrdetails( inputs => ( {
                    ...inputs,
                    background_color: bg + transparency[ 0 ]?.name
                } ) )
            } else {
                setQrdetails( inputs => ( {
                    ...inputs,
                    background_color: backgroundColor + transparency[ 0 ]?.name
                } ) )
            }
            setQrdetails( inputs => ( { ...inputs, [ e.target.name ]: ( Number( e.target.value ) / 100 )?.toString() } ) )
        } else {
            const transparency = backgroundTransparencyArr.filter( ( t ) => t.id.toString() === e.target.value?.toString() )
            if ( backgroundColor.length > 7 ) {
                const bg = backgroundColor.slice( 0, -2 )
                setQrdetails( inputs => ( {
                    ...inputs,
                    background_color: bg + transparency[ 0 ]?.name
                } ) )
            } else {
                setQrdetails( inputs => ( {
                    ...inputs,
                    background_color: backgroundColor + transparency[ 0 ]?.name
                } ) )
            }
            setQrdetails( inputs => ( { ...inputs, [ e.target.name ]: ( Number( e.target.value ) / 100 )?.toString() } ) )
        }
        setIsLoading( true )
        setErrors( '' )
    }

    const handleForgroundColor = ( color ) => {
        setQrdetails( inputs => ( { ...inputs, foreground_color: color.hex } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const eyeInnerColorChnage = ( c ) => {
        setQrdetails( inputs => ( {
            ...inputs, eye_inner_color: c.hex
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const eyeOuterColorChange = ( c ) => {
        setQrdetails( inputs => ( {
            ...inputs, eye_outer_color: c.hex
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const handleForgroundColor1 = ( color ) => {
        setGradientColor1( color.hex )
        setQrdetails( inputs => ( {
            ...inputs, gradient: {
                ...inputs.gradient,
                colorStops: [ { offset: 0, color: color.hex }, { offset: 1, color: gradientColor2 } ]
            },
            foreground_color1: color.hex
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const handleForgroundColor2 = ( color ) => {
        setGradientColor2( color.hex )
        setQrdetails( inputs => ( {
            ...inputs, gradient: {
                ...inputs.gradient,
                colorStops: [ { offset: 0, color: gradientColor1 }, { offset: 1, color: color.hex } ]
            },
            foreground_color2: color.hex
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const handleBackgroundColorChange = ( color ) => {
        setQrdetails( inputs => ( {
            ...inputs,
            background_color: color.hex,
            background_transparency: 0
        } ) )
        setIsLoading( true )
        setBackgroundColor( color.hex )
        setErrors( '' )
    }

    // create project model
    const createProjectModel = ( val ) => {
        setModalShowProject( val )
    }

    const onChangeCountryCode = ( code, name ) => {
        setCountryCode( code )
        setQrdetails( inputs => ( {
            ...inputs,
            [ name ]: `${code} ${phoneNumber.replace( / /g, "" )}`
        } ) )
        setIsLoading( true )
        setErrors( '' )
    }

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        var mailFormat = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})|([0-9]{10})+$/;
        let pattern = new RegExp( '^(https?:\\/\\/)?' +
            '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' +
            '((\\d{1,3}\\.){3}\\d{1,3}))' +
            '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' +
            '(\\?[;&a-z\\d%_.~+=-]*)?' +
            '(\\#[-a-z\\d_]*)?$', 'i' );
        const countryName = countryData.allCountries.find( c => `+${c.dialCode}` === countryCode );
        const validPhoneNumber = parsePhoneNumberFromString( qrDetails.phone_number.split( ' ' ).join( '' ), countryName?.iso2 )?.isValid()
        const validWPNumber = parsePhoneNumberFromString( qrDetails.wp_phone_number.split( ' ' ).join( '' ), countryName?.iso2 )?.isValid()
        const validNumber = parsePhoneNumberFromString( qrDetails.phone.split( ' ' ).join( '' ), countryName?.iso2 )?.isValid()
        const typeNo = qrDetails[ 'type' ].value
        if ( !qrDetails[ 'name' ] || qrDetails.name.trim().length === 0 ) {
            errorss[ 'name' ] = getFormattedMessage( "globally.input.name.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.name.validate.label' ), type: toastType.ERROR } ) );
        } else if ( qrDetails.size < 100 || qrDetails.size > 400 ) {
            errorss[ 'size' ] = getFormattedMessage( "qrcode-size.input.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'qrcode-size.input.validate.label' ), type: toastType.ERROR } ) );
        } else if ( qrDetails.margin_size < 10 || qrDetails.margin_size > 50 ) {
            errorss[ 'margin_size' ] = getFormattedMessage( "qrcode-margin-size.input.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'qrcode-margin-size.input.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 1 && ( !qrDetails[ 'text_content' ] || qrDetails.text_content.trim().length === 0 ) ) {
            errorss[ 'text_content' ] = getFormattedMessage( "globally.input.text-content.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.text-content.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 2 && !qrDetails[ 'url' ] ) {
            errorss[ 'url' ] = getFormattedMessage( "globally.input.url.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.url.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 2 && ( pattern.test( qrDetails[ 'url' ] ) === false ) ) {
            errorss[ 'url' ] = getFormattedMessage( "globally.input.url.valid.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.url.valid.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 3 && validPhoneNumber !== true ) {
            errorss.phone_number = getFormattedMessage( "globally.input.phonenumber.validate.label" )
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.phonenumber.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 4 && validPhoneNumber !== true ) {
            errorss.phone_number = getFormattedMessage( "globally.input.phonenumber.validate.label" )
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.phonenumber.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 5 && !qrDetails[ 'email' ] ) {
            errorss[ 'email' ] = getFormattedMessage( "globally.input.email.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.email.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 5 && !EmailValidator.validate( qrDetails[ 'email' ] ) ) {
            errorss[ 'email' ] = getFormattedMessage( "globally.input.email.valid.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.email.valid.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 6 && !qrDetails[ 'wp_phone_number' ] ) {
            errorss[ 'wp_phone_number' ] = getFormattedMessage( "globally.input.whatsapp-phone-number.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.whatsapp-phone-number.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 6 && validWPNumber !== true ) {
            errorss.wp_phone_number = getFormattedMessage( "globally.input.whatsapp-phonenumber.validate.label" )
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.whatsapp-phonenumber.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 7 && ( !qrDetails[ 'phone_or_email' ] || qrDetails.phone_or_email.trim().length === 0 ) ) {
            errorss[ 'phone_or_email' ] = getFormattedMessage( "globally.input.number-or-email.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.number-or-email.validate.label' ), type: toastType.ERROR } ) );
        }
        // else if (typeNo === 7 && !mailFormat.test(qrDetails.phone_or_email)) {
        //     errorss['phone_or_email'] = getFormattedMessage("please.enter.valid.phone.number.or.email.address.error.message");
        //     dispatch(addToast({ text: getFormattedMessage('please.enter.valid.phone.number.or.email.address.error.message'), type: toastType.ERROR }));
        // }
        else if ( typeNo === 8 && ( !qrDetails[ 'latitude' ] || qrDetails.latitude.trim().length === 0 ) ) {
            errorss[ 'latitude' ] = getFormattedMessage( "globally.input.latitude.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.latitude.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 8 && ( !qrDetails[ 'longitude' ] || qrDetails.longitude.trim().length === 0 ) ) {
            errorss[ 'longitude' ] = getFormattedMessage( "globally.input.longitude.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.longitude.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 9 && ( !qrDetails[ 'wifi_name' ] || qrDetails.wifi_name.trim().length === 0 ) ) {
            errorss[ 'wifi_name' ] = getFormattedMessage( "globally.input.wifi-name.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.wifi-name.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 9 && ( ( qrDetails.encryption.value === 1 || qrDetails.encryption.value === 2 ) && ( !qrDetails.password || qrDetails.password.trim().length === 0 ) ) ) {
            errorss[ "password" ] = getFormattedMessage( "user.input.password.validate.label" )
            dispatch( addToast( { text: getFormattedMessage( 'user.input.password.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 10 && ( !qrDetails[ 'event_name' ] || qrDetails.event_name.trim().length === 0 ) ) {
            errorss[ 'event_name' ] = getFormattedMessage( "globally.input.event-name.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.event-name.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 10 && ( !qrDetails[ 'geo_location' ] || qrDetails.geo_location.trim().length === 0 ) ) {
            errorss[ 'geo_location' ] = getFormattedMessage( "globally.input.geo-location.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.geo-location.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 10 && ( qrDetails.ends_on - qrDetails.starts_on <= 0 ) ) {
            errorss[ 'ends_on' ] = getFormattedMessage( "ends.on.date.must.be.a.greater.than.Starts.on.date.error.message" );
            dispatch( addToast( { text: getFormattedMessage( 'ends.on.date.must.be.a.greater.than.Starts.on.date.error.message' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 11 && ( !qrDetails[ 'address' ] || qrDetails.address.trim().length === 0 ) ) {
            errorss[ 'address' ] = getFormattedMessage( "globally.input.address.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.address.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 11 && !qrDetails[ 'amount' ] ) {
            errorss[ 'amount' ] = getFormattedMessage( "globally.input.amount.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.amount.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 11 && ( ( qrDetails.coin.value === 1 ) && ( qrDetails[ 'address' ] ) && ( validate( qrDetails.address ) === false ) ) ) {
            errorss[ 'address' ] = getFormattedMessage( "invalid.bitcoin.address.error.message" );
            dispatch( addToast( { text: getFormattedMessage( 'invalid.bitcoin.address.error.message' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 11 && ( ( qrDetails.coin.value === 2 ) && ( qrDetails[ 'address' ] ) && ( isAddress( qrDetails.address ) === false ) ) ) {
            errorss[ 'address' ] = getFormattedMessage( "invalid.ethereum.address.error.message" );
            dispatch( addToast( { text: getFormattedMessage( 'invalid.ethereum.address.error.message' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 11 && ( ( qrDetails.coin.value === 3 ) && ( qrDetails[ 'address' ] ) && ( isAddress( qrDetails.address ) === false ) ) ) {
            errorss[ 'address' ] = getFormattedMessage( "invalid.Elrond.address.error.message" );
            dispatch( addToast( { text: getFormattedMessage( 'invalid.Elrond.address.error.message' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.first_name || qrDetails.first_name.trim().length === 0 ) ) {
            errorss[ 'first_name' ] = getFormattedMessage( "globally.input.first-name.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.first-name.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.last_name || qrDetails.last_name.trim().length === 0 ) ) {
            errorss[ 'last_name' ] = getFormattedMessage( "globally.input.last-name.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.last-name.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && !qrDetails.email ) {
            errorss[ 'email' ] = getFormattedMessage( "user.input.email.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'user.input.email.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && !EmailValidator.validate( qrDetails[ 'email' ] ) ) {
            errorss[ 'email' ] = getFormattedMessage( "globally.input.email.valid.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.email.valid.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && validNumber !== true ) {
            errorss.phone = getFormattedMessage( "globally.input.phonenumber.validate.label" )
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.phonenumber.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.company || qrDetails.company.trim().length === 0 ) ) {
            errorss[ 'company' ] = getFormattedMessage( "user.input.company.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'user.input.company.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.job_title || qrDetails.job_title.trim().length === 0 ) ) {
            errorss[ 'job_title' ] = getFormattedMessage( "user.input.job-title.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'user.input.job-title.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.street_address || qrDetails.street_address.trim().length === 0 ) ) {
            errorss[ 'street_address' ] = getFormattedMessage( "globally.input.address.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.address.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.city || qrDetails.city.trim().length === 0 ) ) {
            errorss[ 'city' ] = getFormattedMessage( "globally.input.city.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.city.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.region || qrDetails.region.trim().length === 0 ) ) {
            errorss[ 'region' ] = getFormattedMessage( "globally.input.state.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.state.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.country || qrDetails.country.trim().length === 0 ) ) {
            errorss[ 'country' ] = getFormattedMessage( "globally.input.country.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.country.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && ( !qrDetails.zip || qrDetails.zip.trim().length === 0 ) ) {
            errorss[ 'zip' ] = getFormattedMessage( "globally.input.zip.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.zip.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 12 && qrDetails.zip.length !== 6 ) {
            errorss[ 'zip' ] = getFormattedMessage( "globally.input.zip-length.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.zip-length.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && !qrDetails[ 'paypal_email' ] ) {
            errorss[ 'paypal_email' ] = getFormattedMessage( "globally.input.paypal-email.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.paypal-email.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && !EmailValidator.validate( qrDetails[ 'paypal_email' ] ) ) {
            errorss[ 'paypal_email' ] = getFormattedMessage( "globally.input.email.valid.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.email.valid.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && ( !qrDetails[ 'product_title' ] || qrDetails.product_title.trim().length === 0 ) ) {
            errorss[ 'product_title' ] = getFormattedMessage( "globally.input.product-title.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.product-title.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && !qrDetails[ 'currency_code' ] ) {
            errorss[ 'currency_code' ] = getFormattedMessage( "globally.input.currency-code.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.currency-code.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && !qrDetails[ 'price' ] ) {
            errorss[ 'price' ] = getFormattedMessage( "globally.input.price.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.price.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && !qrDetails.thanks_url ) {
            errorss[ 'thanks_url' ] = getFormattedMessage( "globally.input.thank-url.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.thank-url.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && ( pattern.test( qrDetails[ 'thanks_url' ] ) === false ) ) {
            errorss[ 'thanks_url' ] = getFormattedMessage( "globally.input.thank-url.valid.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.thank-url.valid.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && !qrDetails.cancel_url ) {
            errorss[ 'cancel_url' ] = getFormattedMessage( "globally.input.cancel-url.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.cancel-url.validate.label' ), type: toastType.ERROR } ) );
        } else if ( typeNo === 13 && ( pattern.test( qrDetails[ 'cancel_url' ] ) === false ) ) {
            errorss[ 'cancel_url' ] = getFormattedMessage( "globally.input.cancel-url.valid.validate.label" );
            dispatch( addToast( { text: getFormattedMessage( 'globally.input.cancel-url.valid.validate.label' ), type: toastType.ERROR } ) );
        } else {
            isValid = true;
        }

        setErrors( errorss );
        return isValid;
    };

    const onSubmit = ( event ) => {
        event.preventDefault();
        const valid = handleValidation()
        if ( valid ) {
            if ( singleQrCode ) {
                const canvas = document.getElementById( 'canvas' );
                const dataURL = canvas?.firstElementChild?.toDataURL();
                dataURL !== undefined && dispatch( editQrcode( singleQrCode[ 0 ].id, prepareFormData( qrDetails, dataURL ), navigate ) )
            } else {
                const canvas = document.getElementById( 'canvas' );
                const dataURL = canvas?.firstElementChild?.toDataURL();
                dataURL !== undefined && dispatch( addQrcode( prepareFormData( qrDetails, dataURL ), navigate ) )
            }
        }
    };

    return (
        <div className='qr-code-form-card'>
            <Tabs defaultActiveKey={key} activeKey={key} id='uncontrolled-tab-example' onSelect={( k ) => setKey( k )}
                className='mt-7 mb-5 mt-0 tab-style'>
                <Tab eventKey='Type' title={getFormattedMessage( "globally.input.type.lable" )}
                    tabClassName='position-relative '>
                    <div className='w-100 mx-auto tab-style-button'>
                        {key === 'Type' && <QrcodeType selectedQrType={selectedQrType} qrTypeOptions={qrTypeOptions} qrDetails={qrDetails} type={qrDetails.type} setIsLoading={setIsLoading} onclickTyep={onclickTyep} />}
                    </div>
                </Tab>
                <Tab eventKey='basic-details' title={getFormattedMessage( "globally.basic.details.label" )}
                    tabClassName='position-relative tab-style-button'>
                    <div className='w-100 mx-auto'>
                        {key === 'basic-details' && <QrCodeBasicForm qrDetails={qrDetails} onChangeInput={onChangeInput} errors={errors} setIsLoading={setIsLoading} refactor={refactor} qrCodeType={qrDetails.type}
                            projectTypeDefaultValue={projectTypeDefaultValue} projectTypeOptions={projectTypeOptions} onProjectChange={onProjectChange} createProjectModel={createProjectModel} modalShowProject={modalShowProject} setModalShowProject={setModalShowProject}
                            countryCode={countryCode} onChangeCountryCode={onChangeCountryCode} phoneNumber={phoneNumber} setPhoneNumber={setPhoneNumber} setQrdetails={setQrdetails} wPhoneNumber={wPhoneNumber} setWPhoneNumber={setWPhoneNumber} setErrors={setErrors}
                            encryptionTypeDefaultValue={encryptionTypeDefaultValue} encryptionTypeOptions={encryptionTypeOptions} onEncChange={onEncChange} wifiHiddenTypeDefaultValue={wifiHiddenTypeDefaultValue} wifiHiddenTypeOptions={wifiHiddenTypeOptions} onWifiHiddenChange={onWifiHiddenChange} setKey={setKey}
                            onChangeStartDate={onChangeStartDate} onChangeEndDate={onChangeEndDate} cryptoCoinTypeDefaultValue={cryptoCoinTypeDefaultValue} cryptoCoinTypeOptions={cryptoCoinTypeOptions} onCryptoChange={onCryptoChange}
                            phone={phone} setPhone={setPhone} onChangeDOB={onChangeDOB}
                            payPalTypeDefaultValue={payPalTypeDefaultValue} payPalTypeOptions={payPalTypeOptions} onPayPalTypeChange={onPayPalTypeChange} currencyCodeTypeDefaultValue={currencyCodeTypeDefaultValue} currencyCodeTypeOptions={currencyCodeTypeOptions} onCurrencyCodeTypeChange={onCurrencyCodeTypeChange} getTitle={getTitle}
                        />}
                    </div>
                </Tab>
                <Tab eventKey='design' title={getFormattedMessage( "globally.design.label" )}
                    tabClassName='position-relative tab-style-button'>
                    <div className='w-100 mx-auto'>
                        {key === 'design' && <QrCodeDesign qrDetails={qrDetails} gradientColor1={gradientColor1} gradientColor2={gradientColor2} errors={errors} isLoading={isLoading} setKey={setKey} setShowColor={setShowColor} showColor={showColor} setQrdetails={setQrdetails} handleForgroundColor={handleForgroundColor} forgroundGradientStyleTypeDefaultValue={forgroundGradientStyleTypeDefaultValue} forgroundGradientStyleTypeOptions={forgroundGradientStyleTypeOptions} onForgroundGradientStyleChange={onForgroundGradientStyleChange} handleForgroundColor1={handleForgroundColor1} handleForgroundColor2={handleForgroundColor2} handleBackgroundColorChange={handleBackgroundColorChange} setIsTooltipActive={setIsTooltipActive} handleBGTransparencyChange={handleBGTransparencyChange} isTooltipActive={isTooltipActive} customeEyeColorTypeDefaultValue={customeEyeColorTypeDefaultValue} customeEyeColorTypeOptions={customeEyeColorTypeOptions} onChangeCustomeEyeColor={onChangeCustomeEyeColor} eyeInnerColorChnage={eyeInnerColorChnage} eyeOuterColorChange={eyeOuterColorChange} setShowOptions={setShowOptions} showOptions={showOptions} onChangeInput={onChangeInput} errorCorrectionTypeDefaultValue={errorCorrectionTypeDefaultValue} errorCorrectionTypeOptions={errorCorrectionTypeOptions} onErrorCorrectionChange={onErrorCorrectionChange} isDark={isDark}
                            setIsLoading={setIsLoading} enableEyesColor={enableEyesColor} setEnableEyesColor={setEnableEyesColor} onSubmit={onSubmit}

                            enableColorHorizontal={enableColorHorizontal} setEnableColorHorizontal={setEnableColorHorizontal}
                            enableColorRadial={enableColorRadial} setEnableColorRadial={setEnableColorRadial}
                            defaulytColorType={defaulytColorType} setDefaultColorType={setDefaultColorType}

                        />}
                    </div>
                </Tab>
            </Tabs>
        </div>
    )
}

const mapStateToProps = ( state ) => {
    const { project, qrCodeTypes, homeQrcodeDetails } = state;
    return { project, qrCodeTypes, homeQrcodeDetails }
};

export default connect( mapStateToProps, { fetchProjects, fetchQrcodeType } )( QrcodeFrom );
