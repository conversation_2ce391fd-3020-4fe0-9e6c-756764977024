import React, { useEffect } from "react";
import { getFormattedMessage, numValidate } from "../../shared/sharedMethod";
import { Button } from "react-bootstrap-v5";
import ReactColorPicker from "../../shared/colorpocker/ReactColorPicker";

const QrCodeColorOption = (props) => {
    const {
        qrDetails,
        setEnableColorType,
        enableColorType,
        setShowColor,
        setKey,
        showColor,
        setQrdetails,
        gradientColor1,
        gradientColor2,
        handleForgroundColor,
        forgroundGradientStyleTypeDefaultValue,
        forgroundGradientStyleTypeOptions,
        onForgroundGradientStyleChange,
        handleForgroundColor1,
        handleForgroundColor2,
        handleBackgroundColorChange,
        setIsTooltipActive,
        handleBGTransparencyChange,
        isTooltipActive,
        customeEyeColorTypeDefaultValue,
        customeEyeColorTypeOptions,
        onChangeCustomeEyeColor,
        eyeInnerColorChnage,
        eyeOuterColorChange,
        errors,
        setIsLoading,
        enableEyesColor,
        setEnableEyesColor,
        onSubmit,
        enableColorHorizontal,
        setEnableColorHorizontal,
        enableColorRadial,
        setEnableColorRadial,
        defaulytColorType,
        setDefaultColorType,
    } = props;

    useEffect(() => {
        setIsLoading(true);
    }, []);

    return (
        <div className="card ms-3">
            <div className="card-body">
                {/* color  */}
                <div className="col-12 mt-3">
                    <div className={"d-flex justify-content-center flex-wrap"}>
                        {/* forground type */}
                        <div className="d-flex mt-3 justify-content-between flex-wrap w-100 qr_style_btn">
                            <label
                                htmlFor="exampleInputEmail1"
                                className="form-label col-12"
                            >
                                {getFormattedMessage(
                                    "qrcode.btn.foreground.label"
                                )}{" "}
                                {getFormattedMessage(
                                    "globally.input.type.lable"
                                )}
                                :
                            </label>
                            <Button
                                variant="light"
                                className={`mb-3 col-5 ${
                                    qrDetails.foreground_type === 1 && "active"
                                }`}
                                onClick={() => {
                                    setQrdetails((inputs) => ({
                                        ...inputs,
                                        foreground_type: 1,
                                    }));
                                }}
                            >
                                {getFormattedMessage(
                                    "globally.input.solid-color.title"
                                )}
                            </Button>
                            <Button
                                variant="light"
                                className={`mb-3 col-5 ${
                                    qrDetails.foreground_type === 2 && "active"
                                }`}
                                onClick={() => {
                                    setQrdetails((inputs) => ({
                                        ...inputs,
                                        foreground_type: 2,
                                    }));
                                    setQrdetails((inputs) => ({
                                        ...inputs,
                                        gradient: {
                                            ...inputs.gradient,
                                            colorStops: [
                                                {
                                                    offset: 0,
                                                    color: gradientColor1,
                                                },
                                                {
                                                    offset: 1,
                                                    color: gradientColor2,
                                                },
                                            ],
                                        },
                                    }));
                                }}
                            >
                                {getFormattedMessage(
                                    "globally.btn.gradient.label"
                                )}
                            </Button>
                        </div>
                        <div
                            className={`${
                                qrDetails.foreground_type === 2
                                    ? "d-flex w-100 justify-content-start"
                                    : "d-none"
                            }`}
                        >
                            <div className="mt-2 qr_style_btn">
                                <label className="form-check form-check-custom form-check-solid form-check-inline d-flex align-items-center my-3 cursor-pointer custom-label">
                                    <input
                                        type="checkbox"
                                        name="show_version_on_footer"
                                        value={enableColorHorizontal}
                                        checked={enableColorHorizontal}
                                        onChange={() => {
                                            setEnableColorHorizontal(
                                                !enableColorHorizontal
                                            );
                                            setEnableColorRadial(false);
                                        }}
                                        className="me-3 form-check-input cursor-pointer"
                                    />
                                    <div className="control__indicator" />{" "}
                                    {getFormattedMessage(
                                        "qrcode.horizontal.lable"
                                    )}
                                </label>
                            </div>

                            <div className="mt-2 qr_style_btn">
                                <label className="form-check form-check-custom form-check-solid form-check-inline d-flex align-items-center my-3 cursor-pointer custom-label">
                                    <input
                                        type="checkbox"
                                        name="show_version_on_footer"
                                        value={enableColorRadial}
                                        checked={enableColorRadial}
                                        onChange={() => {
                                            setEnableColorRadial(
                                                !enableColorRadial
                                            );
                                            setEnableColorHorizontal(false);
                                        }}
                                        className="me-3 form-check-input cursor-pointer"
                                    />
                                    <div className="control__indicator" />{" "}
                                    {getFormattedMessage("qrcode.radial.lable")}
                                </label>
                            </div>
                        </div>
                        <div className="row justify-content-between align-items-center mt-2 w-100">
                            <div className="col-xxl-6 col-xl-12 col-lg-12 col-md-12 col-sm-6 col-12">
                                <div
                                    className={` ${
                                        qrDetails.foreground_type === 1
                                            ? ""
                                            : "d-none"
                                    }`}
                                >
                                    <label className="form-label">
                                        {getFormattedMessage(
                                            "globally.input.fill-color-1.title"
                                        )}
                                        :
                                    </label>
                                    <div className="d-flex justify-content-start align-items-center fillcolor_options_parent">
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor({
                                                    hex: "#fe3500",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor({
                                                    hex: "#200d51",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor({
                                                    hex: "#7f2aff",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor({
                                                    hex: "#003fff",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor({
                                                    hex: "#9062ed",
                                                })
                                            }
                                        ></div>
                                        <div className="qr-color-picker">
                                            <ReactColorPicker
                                                onChangeColor={(c) =>
                                                    handleForgroundColor(c)
                                                }
                                                selectedColor={
                                                    qrDetails.foreground_color
                                                }
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div
                                    className={` ${
                                        qrDetails.foreground_type === 2
                                            ? ""
                                            : "d-none"
                                    }`}
                                >
                                    <label className="mt-2 form-label">
                                        {getFormattedMessage(
                                            "qrcode.foreground-first.label"
                                        )}{" "}
                                        {getFormattedMessage(
                                            "globally.input.color.lable"
                                        )}
                                        :
                                    </label>
                                    <div className="d-flex justify-content-start align-items-center ffcolor_options_parent">
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor1({
                                                    hex: "#845EC2",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor1({
                                                    hex: "#D65DB1",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor1({
                                                    hex: "#FF6F91",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor1({
                                                    hex: "#FF9671",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor1({
                                                    hex: "#FFC75F",
                                                })
                                            }
                                        ></div>
                                        <div className="qr-color-picker">
                                            <ReactColorPicker
                                                onChangeColor={(c) =>
                                                    handleForgroundColor1(c)
                                                }
                                                selectedColor={
                                                    qrDetails.foreground_color1
                                                }
                                            />
                                        </div>
                                    </div>
                                    <label className="mt-2 form-label">
                                        {getFormattedMessage(
                                            "qrcode.foreground-second.label"
                                        )}{" "}
                                        {getFormattedMessage(
                                            "globally.input.color.lable"
                                        )}
                                        :
                                    </label>
                                    <div className="d-flex justify-content-start align-items-center fscolor_options_parent">
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor2({
                                                    hex: "#008F7A",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor2({
                                                    hex: "#008E9B",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor2({
                                                    hex: "#0089BA",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor2({
                                                    hex: "#0081CF",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleForgroundColor2({
                                                    hex: "#FF8066",
                                                })
                                            }
                                        ></div>
                                        <div className="qr-color-picker">
                                            <ReactColorPicker
                                                onChangeColor={(c) =>
                                                    handleForgroundColor2(c)
                                                }
                                                selectedColor={
                                                    qrDetails.foreground_color2
                                                }
                                            />
                                        </div>
                                    </div>
                                </div>
                                {/* background color */}
                                <div className="w-100 mt-3">
                                    <label className="form-label">
                                        {getFormattedMessage(
                                            "globally.input.fill-color-2.title"
                                        )}
                                        :
                                    </label>
                                    <div className="d-flex justify-content-start align-items-center bgcolor_options_parent">
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleBackgroundColorChange({
                                                    hex: "#FFF2CC",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleBackgroundColorChange({
                                                    hex: "#E9EDC9",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleBackgroundColorChange({
                                                    hex: "#FFACAC",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleBackgroundColorChange({
                                                    hex: "#D9ACF5",
                                                })
                                            }
                                        ></div>
                                        <div
                                            className="me-2"
                                            onClick={(e) =>
                                                handleBackgroundColorChange({
                                                    hex: "#b7c5ff",
                                                })
                                            }
                                        ></div>
                                        <div className="qr-color-picker">
                                            <ReactColorPicker
                                                onChangeColor={
                                                    handleBackgroundColorChange
                                                }
                                                selectedColor={
                                                    qrDetails.background_color
                                                }
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* transparency */}
                            <div className="col-xxl-6 col-xl-12 col-lg-12 col-md-12 col-sm-6 col-12 d-flex flex-column mt-2 text-start">
                                <label
                                    htmlFor="exampleInputEmail1"
                                    className="form-label text-start"
                                >
                                    {getFormattedMessage(
                                        "globally.input.background-color-transparency.label"
                                    )}
                                    : %
                                </label>
                                <input
                                    type="text"
                                    name="background_transparency"
                                    onKeyPress={(event) => numValidate(event)}
                                    value={Math.round(
                                        Number(
                                            qrDetails.background_transparency
                                        ) * 100
                                    )?.toString()}
                                    onChange={(e) =>
                                        handleBGTransparencyChange(e)
                                    }
                                    className="form-control w-100 ms-auto"
                                />
                            </div>
                        </div>

                        <div className="d-flex mt-3 justify-content-between flex-wrap w-100 qr_style_btn">
                            <label className="form-check form-check-custom form-check-solid form-check-inline d-flex align-items-center my-3 cursor-pointer custom-label">
                                <input
                                    type="checkbox"
                                    name="show_version_on_footer"
                                    value={enableEyesColor}
                                    checked={enableEyesColor}
                                    onChange={() =>
                                        setEnableEyesColor(!enableEyesColor)
                                    }
                                    className="me-3 form-check-input cursor-pointer"
                                />
                                <div className="control__indicator" />{" "}
                                {getFormattedMessage(
                                    "globally.input.enable-custom-eyes-color.label"
                                )}
                            </label>
                        </div>

                        {/* custom_eye_color */}
                        <div
                            className={`w-100 ${
                                enableEyesColor === true ? "d-block" : "d-none"
                            }`}
                        >
                            <label className="mt-2 form-label">
                                {getFormattedMessage(
                                    "globally.input.eyes-inner-color.label"
                                )}
                                :
                            </label>
                            <div className="d-flex justify-content-start align-items-center eicolor_options_parent">
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeInnerColorChnage({ hex: "#5a61e4" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeInnerColorChnage({ hex: "#18036b" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeInnerColorChnage({ hex: "#839192" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeInnerColorChnage({ hex: "#1F618D" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeInnerColorChnage({ hex: "#AF7AC5" })
                                    }
                                ></div>
                                <div className="qr-color-picker">
                                    <ReactColorPicker
                                        onChangeColor={(c) =>
                                            eyeInnerColorChnage(c)
                                        }
                                        selectedColor={
                                            qrDetails.eye_inner_color
                                        }
                                    />
                                </div>
                            </div>
                            <label className="mt-2 form-label">
                                {getFormattedMessage(
                                    "globally.input.eyes-outer-color.label"
                                )}
                                :
                            </label>
                            <div className="d-flex justify-content-start align-items-center eicolor_options_parent1">
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeOuterColorChange({ hex: "#d33621" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeOuterColorChange({ hex: "#950319" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeOuterColorChange({ hex: "#4072bd" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeOuterColorChange({ hex: "#891f8d" })
                                    }
                                ></div>
                                <div
                                    className="me-2"
                                    onClick={(e) =>
                                        eyeOuterColorChange({ hex: "#c57ab0" })
                                    }
                                ></div>
                                <div className="qr-color-picker">
                                    <ReactColorPicker
                                        onChangeColor={(c) =>
                                            eyeOuterColorChange(c)
                                        }
                                        selectedColor={
                                            qrDetails.eye_outer_color
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                        {/* <div className="d-flex mt-3 justify-content-between flex-wrap w-100 qr_style_btn ">
                            <ReactSelect title={getFormattedMessage('globally.input.custom-eyes-color.label')}
                                value={qrDetails.custom_eye_color} errors={errors['payment_type']}
                                defaultValue={customeEyeColorTypeDefaultValue[0]}
                                multiLanguageOption={customeEyeColorTypeOptions}
                                onChange={onChangeCustomeEyeColor}
                                isRequired
                            />

                        </div> */}
                    </div>
                </div>
                <div className="row">
                    <div className="col-12 mt-3 d-flex justify-content-between">
                        <Button
                            variant="light"
                            className="btn btn-secondary"
                            onClick={() => setKey("basic-details")}
                        >
                            {" "}
                            {getFormattedMessage("globally.back-btn")}
                        </Button>
                        <Button
                            variant="light"
                            className="btn btn-primary"
                            onClick={(event) => onSubmit(event)}
                        >
                            {" "}
                            {getFormattedMessage("globally.submit-btn")}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default QrCodeColorOption;
