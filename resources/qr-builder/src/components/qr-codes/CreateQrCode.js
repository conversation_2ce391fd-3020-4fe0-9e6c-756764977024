import React from 'react';
import { connect, useSelector } from 'react-redux';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../header/HeaderTitle';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import QrCodeForm from "./QrCodeForm"
import { Helmet } from 'react-helmet';

const CreateQrCode = () => {
    const { frontSettings } = useSelector(state => state)

    return (
        <MasterLayout>
            <Helmet title={placeholderText('qrcode.create.title') + ' | ' + frontSettings?.title} />
            <HeaderTitle title={getFormattedMessage('qrcode.create.title')} to='/app/qrcode' />
            <QrCodeForm />
        </MasterLayout>
    );
}

export default connect(null, {})(CreateQrCode);
