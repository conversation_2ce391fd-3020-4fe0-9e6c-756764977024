import React from 'react';
import { connect } from 'react-redux';
import DeleteModel from "../../shared/action-buttons/DeleteModel"
import { getFormattedMessage } from '../../shared/sharedMethod';
import { deleteQrcode } from '../../store/action/qrCodeAction';

const DeleteQrCode = (props) => {
    const { onDelete, deleteQrcode, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deleteQrcode(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage('qr.title')} />}
        </div>
    )
};

export default connect(null, { deleteQrcode })(DeleteQrCode);
