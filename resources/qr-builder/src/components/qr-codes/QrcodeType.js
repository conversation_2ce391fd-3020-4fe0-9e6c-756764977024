import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { environment } from '../../config/environment'
import RoundLoader from '../../shared/components/loaders/RoundLoader'
import { getFormattedMessage } from '../../shared/sharedMethod'

const QrcodeType = (props) => {
    const { qrTypeOptions, onclickTyep, type, setIsLoading, selectedQrType } = props

    const [qecodeType, setQrCodeType] = useState([])
    const { isLoading } = useSelector(state => state)
    useEffect(() => {
        if (qrTypeOptions.length >= 1) {
            setQrCodeType(qrTypeOptions)
        }
    }, [qrTypeOptions])

    useEffect(() => {
        setIsLoading(true)
    }, [])

    const qrCodeTypeArry = [
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/1.svg',
            name: "globally.input.text.lable",
            id: 1
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/7.svg',
            name: "globally.input.url.lable",
            id: 2
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/2.svg',
            name: "globally.input.phone.lable",
            id: 3
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/8.svg',
            name: "globally.input.sms.lable",
            id: 4
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/3.svg',
            name: "user.input.email.label",
            id: 5
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/9.svg',
            name: "globally.input.whatsapp.lable",
            id: 6
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/4.svg',
            name: "globally.input.facetime.lable",
            id: 7
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/10.svg',
            name: "globally.input.location.lable",
            id: 8
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/5.svg',
            name: "globally.input.wifi.lable",
            id: 9
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/11.svg',
            name: "globally.input.event.lable",
            id: 10
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/13.svg',
            name: "globally.input.crypto.lable",
            id: 11
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/12.svg',
            name: "globally.input.vcard.lable",
            id: 12
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/6.svg',
            name: "globally.input.paypal.lable",
            id: 13
        }
    ]

    return (
        <>
            <div className='qr-code-type-scs'>

                {isLoading ? <div className='card w-100'>
                    <div className='card-body'>
                        <RoundLoader />
                    </div>
                </div> : qecodeType && qecodeType.length >= 1 ?
                    <div className='qr-type'>
                        <div className="row justify-content-evenly  ">
                            {qecodeType.map((items, index) => {
                                const data = qecodeType && qrCodeTypeArry.filter((item) => items.id === item.id)
                                if (data && data.length === 1) {
                                    return (
                                        <div className={`image col-xxl-3 col-lg-4 col-md-4 col-sm-6 col-12 ${(type.value || selectedQrType.id) === items.id ? 'active' : ''}`} key={index + 1} onClick={(e) => onclickTyep(e, data[0])}>
                                            <img src={data[0].image} alt="" />
                                            <span>{getFormattedMessage(data[0].name)}</span>
                                        </div>
                                    )
                                }
                            })}
                        </div>
                    </div> :
                    <div className='card w-100'>
                        <div className='card-body text-center'>
                            <div className="text-center fs-3 my-5 nodata">
                                {getFormattedMessage("globally.qrcode.type.no.is.available.title")}
                            </div>
                        </div>
                    </div>}
            </div>
        </>
    )
}

export default QrcodeType
