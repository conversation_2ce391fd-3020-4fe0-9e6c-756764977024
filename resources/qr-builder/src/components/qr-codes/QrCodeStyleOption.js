import React, { useEffect } from 'react'
import { getFormattedMessage } from '../../shared/sharedMethod'
import { Button } from 'react-bootstrap-v5'
import { environment } from '../../config/environment'

const QrCodeStyleOption = (props) => {
    const { qrDetails, setShowColor, setKey, showColor, setQrdetails, handleForgroundColor, forgroundGradientStyleTypeDefaultValue, forgroundGradientStyleTypeOptions, onForgroundGradientStyleChange, handleForgroundColor1, handleForgroundColor2, handleBackgroundColorChange, setIsTooltipActive, handleBGTransparencyChange, isTooltipActive, customeEyeColorTypeDefaultValue, customeEyeColorTypeOptions, onChangeCustomeEyeColor, eyeInnerColorChnage, eyeOuterColorChange, errors, setIsLoading, onSubmit, isDark } = props

    useEffect(() => {
        setIsLoading(true)
    }, [])

    return (
        <div className='card ms-3'>
            <div className='card-body'>
                {/* color  */}
                <div className='col-12 mt-3'>
                    <div className={"d-flex justify-content-center flex-wrap"}>
                        {/* style */}
                        <span className="form-label mb-3 w-100">
                            {getFormattedMessage("globally.heading.style.title")}:
                        </span>
                        <div className='d-flex justify-content-start flex-wrap w-100 qr_style_btn'>
                            <div className='d-flex me-5 p-3 flex-column flex-wrap align-content-center align-content-center justify-content-center align-items-center'>
                                {isDark ? <img src={environment.URL + '/default-images/frontEnd-images/square.png'} height='120' width='100' alt='Square Image' onClick={() => {
                                    setQrdetails(inputs => ({ ...inputs, style: 1 }))
                                }} className={`image image-mini image-effect w-100 cursor-pointer ${qrDetails?.style === 1 ? "active" : ""}`} /> : <img src={environment.URL + '/default-images/frontEnd-images/square.png'} height='120' width='100' alt='Square Image' onClick={() => {
                                    setQrdetails(inputs => ({ ...inputs, style: 1 }))
                                }} className={`image image-mini image-effect w-100 cursor-pointer ${qrDetails?.style === 1 ? "active" : ""}`} />}
                                <span className='mt-3 form-label justify-content-center'>{getFormattedMessage("qrcode.btn.square.label")}</span>

                            </div>
                            <div className='d-flex me-5 p-3 flex-column flex-wrap align-content-center align-content-center justify-content-center align-items-center'>
                                {isDark ? <img src={environment.URL + '/default-images/frontEnd-images/dot.png'} height='120' width='100' alt='Dot Image' onClick={() => {
                                    setQrdetails(inputs => ({ ...inputs, style: 2 }))
                                }} className={`image image-mini image-effect w-100 cursor-pointer ${qrDetails?.style === 2 ? "active" : ""}`} /> : <img src={environment.URL + '/default-images/frontEnd-images/dot.png'} height='120' width='100' alt='Dot Image' onClick={() => {
                                    setQrdetails(inputs => ({ ...inputs, style: 2 }))
                                }} className={`image image-mini image-effect w-100 cursor-pointer ${qrDetails?.style === 2 ? "active" : ""}`} />}
                                <span className='mt-3 form-label justify-content-center'>{getFormattedMessage("qrcode.btn.dot.label")}</span>
                            </div>
                            <div className='d-flex me-5 p-3 flex-column flex-wrap align-content-center align-content-center justify-content-center align-items-center'>
                                {isDark ? <img src={environment.URL + '/default-images/frontEnd-images/round.png'} height='120' width='100' alt='Round Image' onClick={() => {
                                    setQrdetails(inputs => ({ ...inputs, style: 3 }))
                                }} className={`image image-mini image-effect w-100 cursor-pointer ${qrDetails?.style === 3 ? "active" : ""}`} /> : <img src={environment.URL + '/default-images/frontEnd-images/round.png'} height='120' width='100' alt='Round Image' onClick={() => {
                                    setQrdetails(inputs => ({ ...inputs, style: 3 }))
                                }} className={`image image-mini image-effect w-100 cursor-pointer ${qrDetails?.style === 3 ? "active" : ""}`} />}
                                <span className='mt-3 form-label justify-content-center'>{getFormattedMessage("qrcode.btn.round.label")}</span>
                            </div>
                            {/* <Button variant="light" className={`mb-3 col-5 ${qrDetails.style === 1 && "active"}`}
                            onClick={() => {
                                setQrdetails(inputs => ({ ...inputs, style: 1 }))
                            }}
                        >
                            {getFormattedMessage('qrcode.btn.square.label')}
                        </Button>
                        <Button variant="light" className={`mb-3 col-5 ${qrDetails.style === 2 && "active"}`}
                            onClick={() => {
                                setQrdetails(inputs => ({ ...inputs, style: 2 }))
                            }}
                        >
                            {getFormattedMessage('qrcode.btn.dot.label')}
                        </Button>
                        <Button variant="light" className={`mb-3 col-5 ${qrDetails.style === 3 && "active"}`}
                            onClick={() => {
                                setQrdetails(inputs => ({ ...inputs, style: 3 }))
                            }}
                        >
                            {getFormattedMessage('qrcode.btn.round.label')}
                        </Button> */}
                        </div>
                    </div>
                </div>
                <div className='row'>
                    <div className='col-12 mt-3 d-flex justify-content-between'>
                        <Button variant="light" className='btn btn-secondary'
                            onClick={() => setKey('basic-details')}
                        > {getFormattedMessage("globally.back-btn")}
                        </Button>
                        <Button variant="light" className='btn btn-primary'
                            onClick={(event) => onSubmit(event)}
                        > {getFormattedMessage("globally.submit-btn")}
                        </Button>
                    </div>

                </div >
            </div >
        </div >
    )
}

export default QrCodeStyleOption
