import React, { useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactColorPicker from '../../../shared/colorpocker/ReactColorPicker';
import { addProject } from '../../../store/action/projectAction';
import { Modal } from 'react-bootstrap-v5';

const ProjectModel = (props) => {
    const { addProject, singleProject, show, hide } = props;

    const [projectValue, setProjectValue] = useState({
        name: '',
        color: ''
    });
    const [errors, setErrors] = useState({
        name: '',
    });

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (!projectValue['name'] || projectValue['name']?.trim()?.length === 0) {
            errorss['name'] = getFormattedMessage("user.input.first-name.validate.label");
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const onChangeInput = (e) => {
        e.preventDefault();
        setProjectValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    };

    const handleCallback = (color) => {
        setProjectValue(previousState => {
            return { ...previousState, color: color.hex }
        });
        setErrors('');
    };


    const prepareFormData = (data) => {
        const formData = new FormData();
        formData.append('name', data.name);
        formData.append('color', data.color);
        return formData;
    };


    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation();
        if (valid) {
            setProjectValue(projectValue);
            addProject(prepareFormData(projectValue));
            hide(false)
        }
    };

    return (
        <>
            <Modal
                show={show}
                onHide={() => hide(false)}
                // backdrop="static"
                keyboard={true}
                size="md"
            >
                <Modal.Header closeButton>
                    <Modal.Title>{getFormattedMessage('project.create.title')}</Modal.Title>
                </Modal.Header>
                <Modal.Body className='p-0'>
                    <div className='card'>
                        <div className='card-body'>
                            <Form>
                                <div className='row'>
                                    <div className='col-md-9 mb-3'>
                                        <label htmlFor="exampleInputEmail1" className="form-label">
                                            {getFormattedMessage("project-name.title")}:<span className="required" />
                                        </label>
                                        <input type='text' name='name' value={projectValue.name}
                                            placeholder={placeholderText("project-name.title")}
                                            className='form-control' autoFocus={true}
                                            onChange={(e) => onChangeInput(e)} />
                                        <span
                                            className='text-danger d-block fw-400 fs-small mt-2'>{errors['name'] ? errors['name'] : null}</span>
                                    </div>
                                    <div className='col-md-1 mb-3'>
                                        <label className='form-label'>
                                            {getFormattedMessage("globally.input.color.lable")}:
                                        </label>
                                        <div style={{ "width": "40px" }}>
                                            <ReactColorPicker onChangeColor={handleCallback} className={'40px'} selectedColor={projectValue.color} />
                                        </div>
                                    </div>
                                    <ModelFooter onEditRecord={singleProject} modelhide={hide} onSubmit={onSubmit}
                                        link='/app/qrcode/create' addDisabled={!projectValue.name} />
                                </div>
                            </Form>
                        </div>
                    </div>
                </Modal.Body>
            </Modal>
        </>

    )
}


export default connect(null, { addProject })(ProjectModel);
