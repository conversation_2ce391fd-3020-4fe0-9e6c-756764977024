import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import QrCodeForm from './QrCodeForm';
import { fetchQrcode } from '../../store/action/qrCodeAction';
import { useParams } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../components/header/HeaderTitle';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import TopProgressBar from '../../shared/components/loaders/TopProgressBar';
import { backgroundTransparencyArr, cryptoCoinOptions, currencyCodeOptions, customeEyeColorOptions, encryptionOptions, errorCorrectionArr, forgroundGradientStyleOptions, payPalTypesOptions, typeOptions, wifiHiddenOptions } from '../../constants';
import TabTitle from "../../shared/tab-title/TabTitle";

const EditQrCode = (props) => {
    const { fetchQrcode, qrcode, project } = props;
    const { id } = useParams();
    const [isEdit, setIsEdit] = useState(false);

    useEffect(() => {
        fetchQrcode(id);
        setIsEdit(true);
    }, []);

    const selectedType = qrcode[0]?.attributes && qrcode[0]?.attributes.type && typeOptions.filter((item) => item.id === qrcode[0]?.attributes.type)

    const projectOptions = project.filter((d) => d.id === qrcode[0]?.attributes.project_id)

    const selectedWifiHidden = qrcode[0]?.attributes && qrcode[0]?.attributes.extra_data.wifi_is_hidden ? wifiHiddenOptions.filter((item) => item.id === qrcode[0]?.attributes.extra_data.wifi_is_hidden.value) : [wifiHiddenOptions[0]]

    const encryptionType = qrcode[0]?.attributes && qrcode[0]?.attributes.extra_data.encryption ? encryptionOptions.filter((item) => item.id === qrcode[0]?.attributes.extra_data.encryption.value) : [encryptionOptions[0]]

    const selectedCryptoCoinType = qrcode[0]?.attributes && qrcode[0]?.attributes.extra_data.coin ? cryptoCoinOptions.filter((item) => item.id === qrcode[0]?.attributes.extra_data.coin.value) : cryptoCoinOptions[0]

    const paypalType = qrcode[0]?.attributes && qrcode[0]?.attributes.extra_data.paypal_type ? payPalTypesOptions.filter((item) => item.id === qrcode[0]?.attributes.extra_data.paypal_type.value) : [payPalTypesOptions[0]]

    const currencyCodeType = qrcode[0]?.attributes && qrcode[0]?.attributes.extra_data.currency_code ? currencyCodeOptions.filter((item) => item.id === qrcode[0]?.attributes.extra_data.currency_code.value) : [currencyCodeOptions[0]]

    const forgroundGradientStyleType = qrcode[0]?.attributes && qrcode[0]?.attributes.foreground_gradient_style && forgroundGradientStyleOptions.filter((item) => item.id === qrcode[0]?.attributes.foreground_gradient_style)

    const selectedCustomeEyeColorType = qrcode[0]?.attributes && qrcode[0]?.attributes.custom_eye_color && customeEyeColorOptions.filter((item) => item.id === qrcode[0]?.attributes.custom_eye_color)

    const selectedErrorCorrectionType = qrcode[0]?.attributes && qrcode[0]?.attributes.error_correction && errorCorrectionArr.filter((item) => item.id === qrcode[0]?.attributes.error_correction)

    const selectedbackgroundTransparencyType = qrcode[0]?.attributes && qrcode[0]?.attributes.background_transparency && backgroundTransparencyArr?.filter((item) => (item.id / 100).toString() === qrcode[0]?.attributes.background_transparency)

    let aryIannaTimeZones = Intl.supportedValuesOf('timeZone');
    let timeZonArr = aryIannaTimeZones.map((timeZone, i) => {
        return {
            value: i + 1,
            label: timeZone
        }
    });

    const timeZone = qrcode[0]?.attributes && qrcode[0]?.attributes.extra_data.timezone && timeZonArr.filter((item) => item.value === qrcode[0]?.attributes.extra_data.timezone.value)

    const itemsValue = qrcode && qrcode.length === 1 && qrcode.map(qrcode => ({
        name: qrcode.attributes.name,
        project_id: projectOptions.length === 0 ? { label: placeholderText("none.title"), value: null } : {
            label: projectOptions && projectOptions[0]?.attributes.name,
            value: projectOptions && projectOptions[0]?.id
        },
        type: {
            label: selectedType && selectedType[0] && selectedType[0].name,
            value: selectedType && selectedType[0] && selectedType[0].id
        },
        wifi_is_hidden: {
            label: selectedWifiHidden && selectedWifiHidden[0] && placeholderText(selectedWifiHidden[0].name),
            value: selectedWifiHidden && selectedWifiHidden[0] && selectedWifiHidden[0].id
        },
        encryption: {
            label: encryptionType && encryptionType[0] && placeholderText(encryptionType[0].name),
            value: encryptionType && encryptionType[0] && encryptionType[0].id
        },
        paypal_type: {
            label: paypalType && paypalType[0] && placeholderText(paypalType[0].name),
            value: paypalType && paypalType[0] && paypalType[0].id
        },
        timezone: timeZone,
        coin: {
            label: selectedCryptoCoinType && selectedCryptoCoinType[0] ? placeholderText(selectedCryptoCoinType[0].name) : placeholderText("qrcode.bitcoin.title"),
            value: selectedCryptoCoinType && selectedCryptoCoinType[0] ? selectedCryptoCoinType[0].id : 1
        },
        style: qrcode.attributes.style,
        foreground_type: qrcode.attributes.foreground_type,
        foreground_gradient_style: {
            label: forgroundGradientStyleType && forgroundGradientStyleType[0] && placeholderText(forgroundGradientStyleType[0].name),
            value: forgroundGradientStyleType && forgroundGradientStyleType[0] && forgroundGradientStyleType[0].id
        },
        foreground_color1: qrcode.attributes.foreground_color1,
        foreground_color2: qrcode.attributes.foreground_color2,
        background_color: qrcode.attributes.background_color,
        background_transparency: selectedbackgroundTransparencyType[0]?.id / 100,
        custom_eye_color: {
            label: selectedCustomeEyeColorType && selectedCustomeEyeColorType[0] && placeholderText(selectedCustomeEyeColorType[0].name),
            value: selectedCustomeEyeColorType && selectedCustomeEyeColorType[0] && selectedCustomeEyeColorType[0].id
        },
        eye_inner_color: qrcode.attributes.eye_inner_color,
        eye_outer_color: qrcode.attributes.eye_outer_color,
        size: qrcode.attributes.size,
        margin_size: qrcode.attributes.margin_size,
        error_correction: {
            label: selectedErrorCorrectionType && selectedErrorCorrectionType[0] && placeholderText(selectedErrorCorrectionType[0].name),
            value: selectedErrorCorrectionType && selectedErrorCorrectionType[0] && selectedErrorCorrectionType[0].id
        },
        foreground_color: qrcode.attributes.foreground_color,
        id: qrcode.id,
        text_content: qrcode.attributes.extra_data.text_content || "",
        url: qrcode.attributes.extra_data.url || "",
        email: qrcode.attributes.extra_data.email || "",
        prefilled_message: qrcode.attributes.extra_data.prefilled_message || "",
        prefilled_subject: qrcode.attributes.extra_data.prefilled_subject || "",
        phone_number: qrcode.attributes.extra_data.phone_number ? qrcode.attributes.extra_data.phone_number.split(" ")[1] : "",
        phone: qrcode.attributes.extra_data.phone ? qrcode.attributes.extra_data.phone.split(" ")[1] : "",
        country_code: qrcode.attributes.extra_data.phone_number
            ? qrcode.attributes.extra_data.phone_number.split(" ")[0]
            : qrcode.attributes.extra_data.wp_phone_number
                ? qrcode.attributes.extra_data.wp_phone_number.split(" ")[0]
                : qrcode.attributes.extra_data.phone
                    ? qrcode.attributes.extra_data.phone.split(" ")[0]
                    : "+91",
        wp_phone_number: qrcode.attributes.extra_data.wp_phone_number ? qrcode.attributes.extra_data.wp_phone_number.split(" ")[1] : "",
        phone_or_email: qrcode.attributes.extra_data.phone_or_email || "",
        latitude: qrcode.attributes.extra_data.latitude || "",
        longitude: qrcode.attributes.extra_data.longitude || "",
        wifi_name: qrcode.attributes.extra_data.wifi_name || "",
        password: qrcode.attributes.extra_data.password || "",
        event_name: qrcode.attributes.extra_data.event_name || "",
        geo_location: qrcode.attributes.extra_data.geo_location || "",
        event_url: qrcode.attributes.extra_data.event_url || "",
        notes: qrcode.attributes.extra_data.notes || "",
        address: qrcode.attributes.extra_data.address || "",
        amount: qrcode.attributes.extra_data.amount || "",
        first_name: qrcode.attributes.extra_data.first_name || "",
        last_name: qrcode.attributes.extra_data.last_name || "",
        website_url: qrcode.attributes.extra_data.website_url || "",
        company: qrcode.attributes.extra_data.company || "",
        job_title: qrcode.attributes.extra_data.job_title || "",
        street_address: qrcode.attributes.extra_data.street_address || "",
        city: qrcode.attributes.extra_data.city || "",
        zip: qrcode.attributes.extra_data.zip || "",
        region: qrcode.attributes.extra_data.region || "",
        country: qrcode.attributes.extra_data.country || "",
        paypal_email: qrcode.attributes.extra_data.paypal_email || "",
        product_title: qrcode.attributes.extra_data.product_title || "",
        currency_code: {
            label: currencyCodeType && currencyCodeType[0] && currencyCodeType[0].name,
            value: currencyCodeType && currencyCodeType[0] && currencyCodeType[0].id
        },
        price: qrcode.attributes.extra_data.price || "",
        thanks_url: qrcode.attributes.extra_data.thanks_url || "",
        cancel_url: qrcode.attributes.extra_data.cancel_url || "",
        starts_on: qrcode.attributes.extra_data.starts_on ? new Date(qrcode.attributes.extra_data.starts_on) : new Date(),
        ends_on: qrcode.attributes.extra_data.ends_on ? new Date(qrcode.attributes.extra_data.ends_on) : new Date(),
        birthday: qrcode.attributes.extra_data.birthday ? new Date(qrcode.attributes.extra_data.birthday) : new Date(),
    }));

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('qrcode.edit.title')} />
            <HeaderTitle title={getFormattedMessage('qrcode.edit.title')} to='/app/qrcode' />
            {qrcode.length === 1 && <QrCodeForm singleQrCode={itemsValue} id={id} isEdit={isEdit} />}
        </MasterLayout>
    );
}

const mapStateToProps = (state) => {
    const { qrcode, project } = state;
    return { qrcode, project }
};

export default connect(mapStateToProps, { fetchQrcode })(EditQrCode);
