import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Card, Table } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../shared/tab-title/TabTitle';
import { getFormattedDate, placeholderText } from '../../shared/sharedMethod';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";
import { fetchQrcode } from '../../store/action/qrCodeAction';
import HeaderTitle from '../header/HeaderTitle';
import RoundLoader from '../../shared/components/loaders/RoundLoader';
import moment from 'moment';

const QrCodeDetails = (props) => {
    const { isLoading, qrcode, fetchQrcode } = props;
    const { id } = useParams();
    const [extraData, setExtraData] = useState([])

    useEffect(() => {
        fetchQrcode(id);
    }, []);

    useEffect(() => {
        const capitalizeTheFirstLetterOfEachWord = (words) => {
            let separateWord = words.toLowerCase().split(' ');
            for (let i = 0; i < separateWord.length; i++) {
                separateWord[i] = separateWord[i].charAt(0).toUpperCase() +
                    separateWord[i].substring(1);
            }
            return separateWord.join(' ');
        }

        if (qrcode?.length === 1) {
            for (let key in qrcode[0]?.attributes?.extra_data) {
                let allDatta = extraData.flat(1);
                if (allDatta.length >= 1) {
                    if (!allDatta.includes(qrcode[0]?.attributes?.extra_data[key])) {
                        setExtraData(inputs => ([...inputs, [capitalizeTheFirstLetterOfEachWord(key.replace("_", " ")), qrcode[0]?.attributes?.extra_data[key]]]))
                    }
                } else {
                    setExtraData(inputs => ([...inputs, [capitalizeTheFirstLetterOfEachWord(key.replace("_", " ")), qrcode[0]?.attributes?.extra_data[key]]]))
                }
            }
        }
    }, [qrcode])

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('qr-code.details.title')} />
            <HeaderTitle title={getFormattedMessage('qr-code.details.title')} />
            {<>
                <div className='pt-5'>
                    <Card>
                        {/* <Card.Header as='h5'>{getFormattedMessage('qr-code.details.title')}</Card.Header> */}
                        <Card.Body className=''>
                            {isLoading ? <RoundLoader /> : <Table responsive>
                                <tbody>
                                    <tr>
                                        <td>
                                            <img src={qrcode[0]?.attributes?.image} className="w-25 h-25 " />
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('react-data-table.name.column.title')}</td>
                                        <td className='py-4'>{qrcode[0]?.attributes?.name}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('react-data-table.qr-code-types.label')}</td>
                                        <td className='py-4'>{qrcode[0]?.attributes?.type_name}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('project-name.title')}</td>
                                        <td className='py-4'>{qrcode[0]?.attributes?.project_name || "N/A"}</td>
                                    </tr>
                                    {
                                        extraData.length > 0 &&
                                        extraData.map((d, i) => {
                                            return (<tr key={i + 1}>
                                                {d[0] === 'Text Content' && <td className='py-4'>{d[0] === 'Text Content' && getFormattedMessage('globally.input.text-content.lable')}</td>}
                                                {(d[0] === 'Phone Number' || d[0] === "Wp Phone_number" || d[0] === "Phone") && <td className='py-4'>{(d[0] === 'Phone Number' || d[0] === "Phone" || d[0] === "Wp Phone_number") && getFormattedMessage('globally.input.phone-number.lable')}</td>}
                                                {d[0] === 'Email' && <td className='py-4'>{d[0] === 'Email' && getFormattedMessage('globally.input.email.label')}</td>}
                                                {d[0] === 'Prefilled Subject' && <td className='py-4'>{d[0] === 'Prefilled Subject' && getFormattedMessage('globally.input.prefilled-subject.lable')}</td>}
                                                {d[0] === 'Prefilled Message' && <td className='py-4'>{d[0] === 'Prefilled Message' && getFormattedMessage('globally.input.prefilled-message.lable')}</td>}
                                                {d[0] === "Phone Or_email" && <td className='py-4'>{d[0] === "Phone Or_email" && getFormattedMessage('globally.number-or-email.label')}</td>}
                                                {d[0] === "Wifi Name" && <td className='py-4'>{d[0] === "Wifi Name" && getFormattedMessage('globally.wifi-name.label')}</td>}
                                                {d[0] === "Encryption" && <td className='py-4'>{d[0] === "Encryption" && getFormattedMessage('globally.input.encryption.lable')}</td>}
                                                {d[0] === "Password" && <td className='py-4'>{d[0] === "Password" && getFormattedMessage('globally.input.password.lable')}</td>}
                                                {d[0] === "Wifi Is_hidden" && <td className='py-4'>{d[0] === "Wifi Is_hidden" && getFormattedMessage('globally.input.wifi-hidden.lable')}</td>}
                                                {d[0] === "Coin" && <td className='py-4'>{d[0] === "Coin" && getFormattedMessage('globally.input.coin.lable')}</td>}
                                                {d[0] === "Address" && <td className='py-4'>{d[0] === "Address" && getFormattedMessage('globally.input.address.label')}</td>}
                                                {d[0] === "Amount" && <td className='py-4'>{d[0] === "Amount" && getFormattedMessage('globally.input.amount.label')}</td>}
                                                {d[0] === "Paypal Type" && <td className='py-4'>{d[0] === "Paypal Type" && getFormattedMessage('globally.paypal.type.title')}</td>}
                                                {d[0] === "Paypal Email" && <td className='py-4'>{d[0] === "Paypal Email" && getFormattedMessage('globally.paypal.email.title')}</td>}
                                                {d[0] === "Product Title" && <td className='py-4'>{d[0] === "Product Title" && getFormattedMessage('globally.product-title.label')}</td>}
                                                {d[0] === "Currency Code" && <td className='py-4'>{d[0] === "Currency Code" && getFormattedMessage('globally.input.currency.code.label')}</td>}
                                                {d[0] === "Price" && <td className='py-4'>{d[0] === "Price" && getFormattedMessage('globally.input.price.label')}</td>}
                                                {d[0] === "Thanks Url" && <td className='py-4'>{d[0] === "Thanks Url" && getFormattedMessage('qrcode.thank-you-url.title')}</td>}
                                                {d[0] === "Cancel Url" && <td className='py-4'>{d[0] === "Cancel Url" && getFormattedMessage('qrcode.cancel-you-url.title')}</td>}
                                                {d[0] === "Url" && <td className='py-4'>{d[0] === "Url" && getFormattedMessage('globally.input.url.lable')}</td>}
                                                {d[0] === "Latitude" && <td className='py-4'>{d[0] === "Latitude" && getFormattedMessage('globally.input.latitude.lable')}</td>}
                                                {d[0] === "Longitude" && <td className='py-4'>{d[0] === "Longitude" && getFormattedMessage('globally.input.longitude.lable')}</td>}
                                                {d[0] === "Event Name" && <td className='py-4'>{d[0] === "Event Name" && getFormattedMessage('globally.input.event-name.label')}</td>}
                                                {d[0] === "Geo Location" && <td className='py-4'>{d[0] === "Geo Location" && getFormattedMessage('globally.input.geo-location.lable')}</td>}
                                                {d[0] === "Event Url" && <td className='py-4'>{d[0] === "Event Url" && getFormattedMessage('globally.input.event-url.lable')}</td>}
                                                {d[0] === "Notes" && <td className='py-4'>{d[0] === "Notes" && getFormattedMessage('globally.input.notes.label')}</td>}
                                                {d[0] === "Starts On" && <td className='py-4'>{d[0] === "Starts On" && getFormattedMessage('globally.input.start-date.lable')}</td>}
                                                {d[0] === "Ends On" && <td className='py-4'>{d[0] === "Ends On" && getFormattedMessage('globally.input.ends-date.lable')}</td>}
                                                {d[0] === "Timezone" && <td className='py-4'>{d[0] === "Timezone" && getFormattedMessage('globally.input.timezone.lable')}</td>}
                                                {d[0] === "First Name" && <td className='py-4'>{d[0] === "First Name" && getFormattedMessage('globally.input.first-name.label')}</td>}
                                                {d[0] === "Last Name" && <td className='py-4'>{d[0] === "Last Name" && getFormattedMessage('globally.input.last-name.label')}</td>}
                                                {d[0] === "Website Url" && <td className='py-4'>{d[0] === "Website Url" && getFormattedMessage('qrcode.website-url.title')}</td>}
                                                {d[0] === "Company" && <td className='py-4'>{d[0] === "Company" && getFormattedMessage('globally.input.company.label')}</td>}
                                                {d[0] === "Job Title" && <td className='py-4'>{d[0] === "Job Title" && getFormattedMessage('globally.input.job-title.label')}</td>}
                                                {d[0] === "Birthday" && <td className='py-4'>{d[0] === "Birthday" && getFormattedMessage('globally.input.birth-date.label')}</td>}
                                                {d[0] === "Street Address" && <td className='py-4'>{d[0] === "Street Address" && getFormattedMessage('qrcode.paypal.vcard.title')}</td>}
                                                {d[0] === "City" && <td className='py-4'>{d[0] === "City" && getFormattedMessage('globally.input.city.label')}</td>}
                                                {d[0] === "Zip" && <td className='py-4'>{d[0] === "Zip" && getFormattedMessage('globally.input.zip.label')}</td>}
                                                {d[0] === "Region" && <td className='py-4'>{d[0] === "Region" && getFormattedMessage('globally.input.region.label')}</td>}
                                                {d[0] === "Country" && <td className='py-4'>{d[0] === "Country" && getFormattedMessage('globally.input.country.label')}</td>}
                                                <td className='py-4'>{typeof d[1] === "object" ? d[1]?.label : d[1]}</td>
                                            </tr>)
                                        })
                                    }
                                    {
                                        qrcode[0]?.attributes?.foreground_type === 1
                                            ?
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.input.foreground-color.label')}</td>
                                                <td className='py-4'>
                                                    <span className={`badge ${qrcode[0]?.attributes?.foreground_color === "#ffffff" && "bg-dark border"}`}
                                                        style={{ "backgroundColor": qrcode[0]?.attributes?.foreground_color }}>
                                                        <span>{qrcode[0]?.attributes?.foreground_color}</span>
                                                    </span>
                                                </td>
                                            </tr>
                                            :
                                            <>
                                                <tr>
                                                    <td className='py-4'>{getFormattedMessage('globally.input.foreground-color-1.label')}</td>
                                                    <td className='py-4'>
                                                        <span className={`badge ${qrcode[0]?.attributes?.foreground_color1 === "#ffffff" && "bg-dark border"}`}
                                                            style={{ "backgroundColor": qrcode[0]?.attributes?.foreground_color1 }}>
                                                            <span>{qrcode[0]?.attributes?.foreground_color}</span>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td className='py-4'>{getFormattedMessage('globally.input.foreground-color-1.label')}</td>
                                                    <td className='py-4'>
                                                        <span className={`badge ${qrcode[0]?.attributes?.foreground_color2 === "#ffffff" && "bg-dark border"}`}
                                                            style={{ "backgroundColor": qrcode[0]?.attributes?.foreground_color2 }}>
                                                            <span>{qrcode[0]?.attributes?.foreground_color}</span>
                                                        </span>
                                                    </td>
                                                </tr>
                                            </>
                                    }
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.background-color.label')}</td>
                                        <td className='py-4'>
                                            <span className={`badge ${qrcode[0]?.attributes?.background_color === "#ffffff" && "bg-dark border"}`}
                                                style={{ "backgroundColor": qrcode[0]?.attributes?.background_color }}
                                            >
                                                <span>{qrcode[0]?.attributes?.background_color}</span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.background-color-transparency.label')}</td>
                                        <td className='py-4'>{qrcode[0]?.attributes?.background_transparency}%</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.custom-eyes-color.label')}</td>
                                        <td className='py-4'>{qrcode[0]?.attributes?.custom_eye_color === 1 ? getFormattedMessage('globally.input.yes.lable') : getFormattedMessage('globally.input.no.lable')}</td>
                                    </tr>
                                    {
                                        qrcode[0]?.attributes?.custom_eye_color === 1 &&
                                        <>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.input.eyes-inner-color.label')}</td>
                                                <td className='py-4'>
                                                    <span className={`badge ${qrcode[0]?.attributes?.eye_inner_color === "#ffffff" && "bg-dark border"}`}
                                                        style={{ "backgroundColor": qrcode[0]?.attributes?.eye_inner_color === "#ffffff" ? "#000000" : qrcode[0]?.attributes?.eye_inner_color }}>
                                                        <span>{qrcode[0]?.attributes?.foreground_color}</span>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.input.eyes-outer-color.label')}</td>
                                                <td className='py-4'>
                                                    <span className={`badge ${qrcode[0]?.attributes?.eye_outer_color === "#ffffff" && "bg-dark border"}`}
                                                        style={{ "backgroundColor": qrcode[0]?.attributes?.eye_outer_color === "#ffffff" ? "#000000" : qrcode[0]?.attributes?.eye_outer_color }}>
                                                        <span>{qrcode[0]?.attributes?.foreground_color}</span>
                                                    </span>
                                                </td>
                                            </tr>
                                        </>
                                    }
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.error-correction-capability.label')}</td>
                                        <td className='py-4'>
                                            {qrcode[0]?.attributes?.error_correction === 1 && "L - low (7%)"}
                                            {qrcode[0]?.attributes?.error_correction === 2 && "M - medium (15%)"}
                                            {qrcode[0]?.attributes?.error_correction === 3 && "Q - high (25%)"}
                                            {qrcode[0]?.attributes?.error_correction === 4 && "H - best (30%)"}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.created-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(qrcode[0]?.attributes?.created_at, "d-m-y")} {moment(qrcode[0]?.attributes?.created_at).format('LT')}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.updated-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(qrcode[0]?.attributes?.updated_at, "d-m-y")} {moment(qrcode[0]?.attributes?.updated_at).format('LT')}
                                        </td>
                                    </tr>

                                </tbody>
                            </Table>}
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </MasterLayout>
    )

};


const mapStateToProps = state => {
    const { isLoading, qrcode } = state;
    return { isLoading, qrcode }
};


export default connect(mapStateToProps, { fetchQrcode })(QrCodeDetails);
