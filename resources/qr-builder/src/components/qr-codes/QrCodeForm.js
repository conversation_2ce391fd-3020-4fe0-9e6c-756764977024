import React, { useEffect, useState } from "react";
import { connect, useDispatch } from 'react-redux';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../shared/sharedMethod';
import { fetchProjects } from "../../store/action/projectAction";
import { Filters, toastType, Tokens, typeDynamicOptions, typeOptions } from "../../constants";
import { fetchQrcodeType } from "../../store/action/qrCodeAction";
import { addToast } from "../../store/action/toastAction";
// import 'react-tooltip/dist/react-tooltip.css'
import QrcodeFrom from "./QrCodeStyleFrom/QrcodeFrom";

const QrCodeForm = (props) => {
    const { homeQrcodeDetails, project, fetchProjects, singleQrCode, isEdit, fetchQrcodeType, qrCodeTypes } = props;

    const [gradientColor1, setGradientColor1] = useState(singleQrCode ? singleQrCode[0].foreground_color1 : '')
    const [gradientColor2, setGradientColor2] = useState(singleQrCode ? singleQrCode[0].foreground_color2 : '')
    const [defultArray, setDefultArray] = useState([]);
    const [projectLength, setProjectLength] = useState(0)
    const [countryCode, setCountryCode] = useState("+91")
    const [phoneNumber, setPhoneNumber] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [selectedType, setSelectedType] = useState("")

    const dispatch = useDispatch()

    useEffect(() => {
        if (singleQrCode && singleQrCode[0]) {
            typeOptions.filter(dynamic => {
                if (singleQrCode[0].type.label === dynamic.name) {
                    typeDynamicOptions.filter((data) => {
                        if (data.id === singleQrCode[0].type.value) {
                            setSelectedType(data)
                        }
                    })
                }
            })
        }
    }, [singleQrCode])

    const [qrDetails, setQrdetails] = useState({
        name: singleQrCode ? singleQrCode[0].name : '',
        project_id: singleQrCode ? singleQrCode[0]?.project_id === null ? { label: getFormattedMessage("none.title"), value: null } : singleQrCode[0]?.project_id : { label: getFormattedMessage("none.title"), value: null },
        type: singleQrCode ? singleQrCode[0].type : { label: placeholderText("globally.input.text.lable"), value: 1 },
        style: singleQrCode ? singleQrCode[0].style : 1,
        foreground_type: singleQrCode ? singleQrCode[0].foreground_type : 1,
        foreground_color1: singleQrCode ? singleQrCode[0].foreground_color1 : '',
        foreground_color2: singleQrCode ? singleQrCode[0].foreground_color2 : '',
        background_color: singleQrCode ? singleQrCode[0].background_color : "#ffffff",
        background_transparency: singleQrCode ? singleQrCode[0].background_transparency : 0,
        custom_eye_color: singleQrCode ? {
            label: singleQrCode[0].custom_eye_color.label,
            value: singleQrCode[0].custom_eye_color.value
        } : { label: placeholderText('globally.input.no.lable'), value: 2 },
        eye_inner_color: singleQrCode ? singleQrCode[0].eye_inner_color : '',
        eye_outer_color: singleQrCode ? singleQrCode[0].eye_outer_color : '',
        error_correction: singleQrCode ? {
            label: singleQrCode[0].error_correction.label,
            value: singleQrCode[0].error_correction.value
        } : { label: placeholderText('qrcode.error-correction-capability.low.label'), value: 1 },
        size: singleQrCode ? singleQrCode[0].size : 400,
        margin_size: singleQrCode ? singleQrCode[0].margin_size : 10,
        foreground_color: singleQrCode ? singleQrCode[0].foreground_color : '',
        color: "",
        gradient: {
            type: 1,
            colorStops: [{ offset: 0, color: gradientColor1 }, { offset: 1, color: gradientColor2 }]
        },
        foreground_gradient_style: singleQrCode ? singleQrCode[0].foreground_gradient_style : { label: placeholderText("qrcode.horizontal.lable"), value: 1 },

        birth_date: singleQrCode ? singleQrCode[0].birth_date : new Date,
        text_content: singleQrCode ? singleQrCode[0].text_content : '',
        url: singleQrCode ? singleQrCode[0].url : '',
        phone_number: singleQrCode ? singleQrCode[0].phone_number : `${countryCode} ${phoneNumber}`,
        prefilled_message: singleQrCode ? singleQrCode[0].prefilled_message : '',
        email: singleQrCode ? singleQrCode[0].email : '',
        prefilled_subject: singleQrCode ? singleQrCode[0].prefilled_subject : '',
        wp_phone_number: singleQrCode ? singleQrCode[0].wp_phone_number : '',
        phone_or_email: singleQrCode ? singleQrCode[0].phone_or_email : '',
        latitude: singleQrCode ? singleQrCode[0].latitude : '',
        longitude: singleQrCode ? singleQrCode[0].longitude : '',
        wifi_name: singleQrCode ? singleQrCode[0].wifi_name : '',
        encryption: singleQrCode ? singleQrCode[0].encryption?.value === undefined ? { label: placeholderText("qrcode.wep.label"), value: 1 } : singleQrCode[0].encryption : { label: placeholderText("qrcode.wep.label"), value: 1 },
        password: singleQrCode ? singleQrCode[0].password : '',
        wifi_is_hidden: singleQrCode ? singleQrCode[0].wifi_is_hidden?.value === undefined ? { label: placeholderText("globally.input.yes.lable"), value: 1 } : singleQrCode[0].wifi_is_hidden : { label: placeholderText('globally.input.yes.lable'), value: 1 },
        event_name: singleQrCode ? singleQrCode[0].event_name : '',
        geo_location: singleQrCode ? singleQrCode[0].geo_location : '',
        event_url: singleQrCode ? singleQrCode[0].event_url : '',
        notes: singleQrCode ? singleQrCode[0].notes : '',
        starts_on: singleQrCode ? singleQrCode[0].starts_on : new Date,
        ends_on: singleQrCode ? singleQrCode[0].ends_on : new Date,
        timezone: singleQrCode ? singleQrCode[0].timezone : { value: 1, label: 'Africa/Abidjan' },
        coin: singleQrCode ? singleQrCode[0].coin : { label: placeholderText("qrcode.bitcoin.title"), value: 1 },
        address: singleQrCode ? singleQrCode[0].address : '',
        amount: singleQrCode ? singleQrCode[0].amount : '',
        first_name: singleQrCode ? singleQrCode[0].first_name : '',
        last_name: singleQrCode ? singleQrCode[0].last_name : '',
        website_url: singleQrCode ? singleQrCode[0].website_url : '',
        company: singleQrCode ? singleQrCode[0].company : '',
        job_title: singleQrCode ? singleQrCode[0].job_title : '',
        birthday: singleQrCode ? singleQrCode[0].birthday : new Date,
        street_address: singleQrCode ? singleQrCode[0].street_address : '',
        city: singleQrCode ? singleQrCode[0].city : '',
        zip: singleQrCode ? singleQrCode[0].zip : '',
        region: singleQrCode ? singleQrCode[0].region : '',
        country: singleQrCode ? singleQrCode[0].country : '',
        paypal_type: singleQrCode ? singleQrCode[0].paypal_type : { label: placeholderText("qrcode.buy-now.lable"), value: 1 },
        paypal_email: singleQrCode ? singleQrCode[0].paypal_email : '',
        product_title: singleQrCode ? singleQrCode[0].product_title : '',
        currency_code: singleQrCode ? singleQrCode[0].currency_code : { label: "USD", value: 1 },
        price: singleQrCode ? singleQrCode[0].price : '',
        thanks_url: singleQrCode ? singleQrCode[0].thanks_url : '',
        cancel_url: singleQrCode ? singleQrCode[0].cancel_url : '',
        spinnerSize: 400,
        phone: singleQrCode ? singleQrCode[0].phone : '',
    })

    useEffect(() => {
        if (localStorage.getItem(Tokens.QRCODE_DETAILS) && project) {
            const data = JSON.parse(localStorage.getItem(Tokens.QRCODE_DETAILS))
            if (qrCodeTypes?.includes(data?.type?.label)) {
                setQrdetails(JSON.parse(localStorage.getItem(Tokens.QRCODE_DETAILS)))
                localStorage.removeItem(Tokens.QRCODE_DETAILS)
            } else {
                dispatch(addToast({ text: getFormattedMessage("This.Type.of.QR.Code.is.Not.Available.in.Your.Plan"), type: toastType.ERROR }));
                localStorage.removeItem(Tokens.QRCODE_DETAILS)
            }

        }
    }, [qrCodeTypes])

    useEffect(() => {
        if (homeQrcodeDetails?.type) {
            if (qrCodeTypes.length > 0) {
                setQrdetails(homeQrcodeDetails)
            }
        }
    }, [homeQrcodeDetails, project])

    useEffect(() => {
        if (singleQrCode) {
            setQrdetails(qrDetails)
            setPhoneNumber(singleQrCode[0]?.phone_number)
            setCountryCode(singleQrCode[0]?.country_code ? singleQrCode[0]?.country_code : "+91")
        }
    }, [singleQrCode])

    useEffect(() => {
        setTimeout(() => setIsLoading(false), 1800);
    }, [isLoading])

    useEffect(() => {
        fetchQrcodeType()
        if (project) {
            fetchProjects(Filters.OBJ)
        }
    }, [])

    useEffect(() => {
        if (project) {
            if ((project?.length - projectLength === 1)) {
                setQrdetails(inputs => ({ ...inputs, project_id: { label: project[project.length - 1].attributes.name, value: project[project.length - 1].id } }))
            }
            setProjectLength(project?.length)
        }
    }, [project])

    useEffect(() => {
        if (qrCodeTypes.length) {
            setDefultArray([])
            qrCodeTypes.map((item) => {
                typeDynamicOptions.filter((data) => {
                    if (item === data.name) {
                        typeOptions.filter(dynamic => {
                            if (data.id === dynamic.id) {
                                setDefultArray(defultArray => [...defultArray, dynamic])
                            }
                        })
                    }
                })
            })
        }
    }, [qrCodeTypes])

    const qrTypeOptions = getFormattedOptions(defultArray)
    const qrTypeDefaultValue = qrTypeOptions.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    useEffect(() => {
        if (singleQrCode) {
            setQrdetails(inputs => ({
                ...inputs, type: singleQrCode ? {
                    label: getFormattedMessage(singleQrCode[0].type.label),
                    value: singleQrCode[0].type.value
                } : { value: qrTypeDefaultValue[0].value, label: qrTypeDefaultValue[0].label }
            }))
        } else {
            if (qrTypeDefaultValue.length && qrTypeDefaultValue[0]?.value) {
                setQrdetails(inputs => ({
                    ...inputs, type: { value: qrTypeDefaultValue[0].value, label: qrTypeDefaultValue[0].label }
                }))
            }
        }
    }, [qrTypeDefaultValue.length])

    return (
        <>
            <QrcodeFrom selectedQrType={selectedType} isEdit={isEdit} singleQrCode={singleQrCode} />
        </>
    )
};

const mapStateToProps = (state) => {
    const { project, qrCodeTypes, homeQrcodeDetails } = state;
    return { project, qrCodeTypes, homeQrcodeDetails }
};

export default connect(mapStateToProps, { fetchProjects, fetchQrcodeType })(QrCodeForm);

