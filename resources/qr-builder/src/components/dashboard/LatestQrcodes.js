import React from 'react';
import { Card, Row, Table } from 'react-bootstrap';
import { connect } from 'react-redux';
import {
    getFormattedMessage
} from '../../shared/sharedMethod';
import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import ActionButton from "../../shared/action-buttons/ActionButton";
import DeleteLatestQrcode from './DeleteLatestQrcode';
import { typeOptions } from '../../constants';

const LatestQRCodes = (props) => {
    const { userDashboard } = props;
    const qrcode = userDashboard && userDashboard.latest_qrcodes
    const navigate = useNavigate()

    const goToEdit = (item) => {
        const id = item.id
        window.location.href = '#/app/qrcode/edit/' + id;
    };

    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    return (
        <>
            <div className=''>
                <Row className='g-4'>
                    <div className='col-xxl-12 col-12'>
                        <Card className='shadow'>
                            <Card.Header className='pb-0 px-10 '>
                                <h5 className="mb-0">{getFormattedMessage('latest-qr.title')}</h5>
                                <Link to={"/app/qrcode/create"}
                                    className={"btn btn-primary mt-md-0 mt-3"}>{getFormattedMessage('qrcode.create.title')}</Link>
                            </Card.Header>
                            <Card.Body className='pt-7 pb-2'>
                                {
                                    qrcode && qrcode?.length > 0
                                        ?
                                        <Table responsive className='text-center'>
                                            <thead>
                                                <tr>
                                                    <th className='text-start'>{getFormattedMessage('react-data-table.qr-code-name.label')}</th>
                                                    <th>{getFormattedMessage('react-data-table.qr-code-types.label')}</th>
                                                    <th>{getFormattedMessage('project.title')}</th>
                                                    {/* <th>{getFormattedMessage('globally.react-table.column.created-date.label')}</th> */}
                                                    <th>{getFormattedMessage("react-data-table.action.column.label")}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {qrcode && qrcode.map((qr, index) => {
                                                    if (index < 5) {
                                                        return <tr key={index + 1}>
                                                            <td>
                                                                <div className='d-flex align-items-center'>
                                                                    <div className='me-2'>
                                                                        <Link to={`/app/qrcode/detail/${qr.id}`}>
                                                                            {qr.image ?
                                                                                <img src={qr.image} height='50' width='50'
                                                                                    alt='User Image'
                                                                                    className='image image-mini image-effect' /> : ''
                                                                            }
                                                                        </Link>
                                                                    </div>
                                                                    <div className='d-flex flex-column'>
                                                                        <Link to={`/app/qrcode/detail/${qr.id}`}
                                                                            className='text-decoration-none'>{qr.name}</Link>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div className='d-flex flex-column'>
                                                                    <p>{getFormattedMessage(typeOptions.filter(d => d.id === qr?.type)[0].name)}</p>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <p>{qr.project_name || "N/A"}</p>
                                                            </td>
                                                            {/* <td>
                                                        <span className='badge bg-light-info'>
                                                            <div
                                                                className='mb-1'>{getFormattedDate(qr.created_at, "d-m-y")}</div>
                                                            <div>{moment(qr.created_at).format('LT')}</div>
                                                        </span>
                                                    </td> */}
                                                            <td>
                                                                <ActionButton item={qr} goToEditProduct={goToEdit}
                                                                    isEditMode={true}
                                                                    onClickDeleteModel={onClickDeleteModel} />
                                                            </td>
                                                        </tr>
                                                    }
                                                })}
                                            </tbody>
                                        </Table>
                                        :
                                        <h2 className='text-center py-5'>
                                            {getFormattedMessage("no-data-available.title")}
                                        </h2>
                                }

                            </Card.Body>
                        </Card>
                    </div>
                </Row>
            </div>
            <DeleteLatestQrcode onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </>
    )
};

const mapStateToProps = (state) => {
    const { userDashboard } = state;
    return { userDashboard }
};


export default connect(mapStateToProps, {})(LatestQRCodes);

