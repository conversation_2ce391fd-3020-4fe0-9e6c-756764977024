import React from 'react';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../shared/tab-title/TabTitle';
import { placeholderText } from '../../shared/sharedMethod';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";
import Widget from "./Widget";
import LatestQRCodes from "./LatestQrcodes";
import TopVisitedLinkOfUser from './TopVisitedLinkOfUser';

const Dashboard = () => {
    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('dashboard.title')} />
            <Widget />
            <div className='row'>
                <div className='col-xl-8 m-0 col-12'>
                    <LatestQRCodes />
                </div>
                <div className='charts_style col-xl-4 col-12 mt-3 mt-md-0'>
                    <TopVisitedLinkOfUser />
                </div>
            </div>

        </MasterLayout>
    )
}

export default Dashboard;
