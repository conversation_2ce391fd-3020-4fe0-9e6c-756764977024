import React, { useEffect } from 'react';
import { Col, Row } from 'react-bootstrap';
import { connect } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faQrcode, faDiagramProject, faExternalLink
} from '@fortawesome/free-solid-svg-icons';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { useNavigate } from 'react-router-dom';
import DashboardWidget from "../../shared/Widget/DashboardWidget";
import { fetchDashboardData } from "../../store/action/userDashboardAction";

const widget = ( props ) => {
    const { fetchDashboardData, userDashboard } = props
    const navigate = useNavigate()

    useEffect( () => {
        fetchDashboardData()
    }, [] )

    const onClick = ( redirect ) => {
        navigate( `/${redirect}` )
    }

    return (
        <Row className='g-4'>
            <Col className='col-12 mb-4'>
                <Row>
                    <DashboardWidget title={getFormattedMessage( 'qrcode.title' )}
                        onClick={() => onClick( 'app/qrcode' )}
                        className={`bg-primary cursor-pointer gradient-style1`} iconClass='widget-light-primary'
                        icon={<FontAwesomeIcon icon={faQrcode} className='fs-1-xl text-white' />}
                        value={userDashboard.qr_codes} />
                    <DashboardWidget title={getFormattedMessage( "links.title" )}
                        onClick={() => onClick( 'app/minify-link' )}
                        className='widget-bg-purple cursor-pointer gradient-style2' iconClass='bg-purple-700'
                        icon={<FontAwesomeIcon icon={faExternalLink} className='fs-1-xl text-white' />}
                        value={userDashboard.links} />

                    <DashboardWidget title={getFormattedMessage( "projects.title" )}
                        onClick={() => onClick( 'app/collections' )}
                        className='widget-bg-pink cursor-pointer gradient-style4' iconClass='bg-pink-700'
                        icon={<FontAwesomeIcon icon={faDiagramProject} className='fs-1-xl text-white' />}
                        value={userDashboard.projects} />
                </Row>
            </Col>
        </Row>
    )
};
const mapStateToProps = ( state ) => {
    const { userDashboard } = state;
    return { userDashboard }
};

export default connect( mapStateToProps, { fetchDashboardData } )( widget );
