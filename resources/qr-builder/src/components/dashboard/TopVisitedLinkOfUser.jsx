import React, { useEffect } from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    BarElement,
    RadialLinearScale,
    ArcElement
} from 'chart.js';
import { useSelector, useDispatch } from 'react-redux';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { Card } from 'react-bootstrap';
import { getTopVisitedLinkUserAction } from '../../store/action/userDashboardAction';
import ReactECharts from 'echarts-for-react';

const TopVisitedLinkOfUser = () => {

    const { theme, topVisitedLinkUser } = useSelector(state => state)
    const dispatch = useDispatch()

    ChartJS.register(
        CategoryScale,
        LinearScale,
        PointElement,
        LineElement,
        BarElement,
        RadialLinearScale,
        ArcElement,
        Title,
        Tooltip,
        Legend
    );

    const monthNames = [
        getFormattedMessage("month.name.jan.title"),
        getFormattedMessage("month.name.feb.title"),
        getFormattedMessage("month.name.mar.title"),
        getFormattedMessage("month.name.apr.title"),
        getFormattedMessage("month.name.may.title"),
        getFormattedMessage("month.name.jun.title"),
        getFormattedMessage("month.name.jul.title"),
        getFormattedMessage("month.name.aug.title"),
        getFormattedMessage("month.name.sep.title"),
        getFormattedMessage("month.name.act.title"),
        getFormattedMessage("month.name.nov.title"),
        getFormattedMessage("month.name.dec.title"),
    ];

    const chartData = topVisitedLinkUser?.length > 0 ? topVisitedLinkUser?.map(d => ({ name: d?.name, value: d?.count })) : []

    const option = {
        title: {
            show: false
        },
        tooltip: {
            trigger: 'item',
        },
        legend: {
            show: false
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: '90%',
                data: chartData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                    }
                },
                label: {
                    show: false,
                },
            }
        ]
    };

    useEffect(() => {
        dispatch(getTopVisitedLinkUserAction())
    }, [])

    return (
        <>
            <Card className='shadow'>
                <Card.Header className='pb-0 px-10'>
                    <h5 className="mb-0">{getFormattedMessage("dashboard.5mostvisitedlinksof.title")} <strong>({monthNames[new Date().getMonth()]})</strong> </h5>
                </Card.Header>
                <Card.Body>
                    {/* <Line options={options} data={data} height={100} /> */}
                    <ReactECharts
                        option={option}
                        style={{ height: 400 }}
                        theme={theme === true ? "dark" : ""}
                    />
                </Card.Body>
            </Card>
        </>
    )
}

export default TopVisitedLinkOfUser
