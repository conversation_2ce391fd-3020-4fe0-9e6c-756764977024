import React from 'react';
import { connect } from 'react-redux';
import DeleteModel from "../../shared/action-buttons/DeleteModel"
import { getFormattedMessage } from '../../shared/sharedMethod';
import { deleteUserLatestQrcode } from "../../store/action/userDashboardAction"

const DeleteLatestQrCode = (props) => {
    const { onDelete, deleteUserLatestQrcode, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deleteUserLatestQrcode(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage('qrcode.lable')} />}
        </div>
    )

};

export default connect(null, { deleteUserLatestQrcode })(DeleteLatestQrCode);
