import React, { useEffect, useState } from "react";
import { Image, Nav, Navbar } from "react-bootstrap-v5";
import { connect, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Tokens } from "../../constants/index";
import {
    deleteAccountAction,
    logoutAction,
} from "../../store/action/authAction";
import ChangePassword from "../auth/change-password/ChangePassword";
import { getAvatarName } from "../../shared/sharedMethod";
import English from "../../assets/images/gb.svg";
import { getFormattedMessage } from "../../shared/sharedMethod";
import User from "../../assets/images/avatar.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faUser,
    faLock,
    faRightFromBracket,
    faAngleDown,
    faMoneyBill,
    faUserXmark,
    faLanguage,
} from "@fortawesome/free-solid-svg-icons";
import { Dropdown } from "react-bootstrap";
import { cssHandling } from "../../cssHandling/cssHandling";
import { updateThemeAction } from "../../store/action/adminActions/themeAction";
import LanguageModel from "../../frontend/components/user-profile-admin/LanguageModel";

const Header = (props) => {
    const { logoutAction, selectedLanguage, isAdmin, deleteAccountAction } =
        props;
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const users = localStorage.getItem(Tokens.USER);
    const firstName = localStorage.getItem(Tokens.FIRST_NAME);
    const lastName = localStorage.getItem(Tokens.LAST_NAME);
    const token = localStorage.getItem(Tokens.ADMIN);
    const imageUrl = localStorage.getItem(Tokens.USER_IMAGE_URL);
    const image = localStorage.getItem(Tokens.IMAGE);
    const updatedEmail = localStorage.getItem(Tokens.UPDATED_EMAIL);
    const updatedFirstName = localStorage.getItem(Tokens.UPDATED_FIRST_NAME);
    const updatedLastName = localStorage.getItem(Tokens.UPDATED_LAST_NAME);
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const user_role = localStorage.getItem(Tokens.USER_ROLE);
    const DarkMod = localStorage.getItem("isDarkMod");
    const [deleteModel, setDeleteModel] = useState(false);
    const [languageModel, setLanguageModel] = useState(false);
    const [isDeleteAccount, setIsDeleteAccount] = useState(false);
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );

    useEffect(() => {
        if (DarkMod === "true") {
            setIsDark(true);
            dispatch(updateThemeAction(true));
        } else {
            dispatch(updateThemeAction(false));
            setIsDark(false);
        }
    }, []);

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        dispatch(updateThemeAction(isDark));
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, [isDark]);

    const onClickDeleteModel = () => {
        setDeleteModel(!deleteModel);
    };

    const onClickChangeLanguageModel = () => {
        setLanguageModel(!languageModel);
    };

    const onClickLanguageModel = () => {
        setLanguageModel(!languageModel);
    };

    const onLogOut = () => {
        logoutAction(token, navigate);
    };

    const onProfileClick = () => {
        if (isAdmin && user_role === "1") {
            window.location.href = "#/app/admin/profile/edit";
        } else {
            window.location.href = "#/app/profile/edit";
        }
    };

    const onAccountClick = () => {
        if (isAdmin && user_role === "1") {
            window.location.href = "#/app/admin/account";
        } else {
            window.location.href = "#/app/account";
        }
    };

    const onManageSub = (e) => {
        window.location.href = "#/app/manage-subscription";
    };

    const onDeleteAccount = () => {
        setIsDeleteAccount(!isDeleteAccount);
        setDeleteModel(!deleteModel);
        // deleteAccountAction(navigate)
    };

    const isDarkMod = (e) => {
        e.preventDefault();
        setIsDark(!isDark);
        cssHandling(updatedLanguage);
    };

    // const changeLanguage = (language) => {
    //     updateLanguage({language: language});
    // };

    const activeLanguage = updatedLanguage ? updatedLanguage : selectedLanguage;

    const activeFlag = () => {
        if (activeLanguage === "en") {
            return English;
        }
        // if (activeLanguage === 'fr') {
        //     return French
        // }
        // if (activeLanguage === 'ar') {
        //     return Arabic
        // }
        // if (activeLanguage === 'tr') {
        //     return Turkish
        // }
        // if (activeLanguage === 'gr') {
        //     return German
        // }
        // if (activeLanguage === 'sp') {
        //     return Spanish
        // }
        // if (activeLanguage === 'vi') {
        //     return Vietnamese
        // }
        // if (activeLanguage === 'cn') {
        //     return Chinese
        // }
    };

    return (
        <Navbar
            collapseOnSelect
            expand="lg"
            className="align-items-stretch ms-auto py-1"
        >
            <div className="d-flex align-items-stretch justify-content-center">
                <Nav className="align-items-stretch justify-content-between flex-row">
                    {/* <div className='d-flex me-3 hide-arrow bg-transparent border-0 p-0 d-flex align-items-center'>
                        <div className='me-3 cursor-pointer' onClick={(e) => isDarkMod(e)} >
                            <input type="checkbox" checked={isDark} className="toggle-checkbox" id="checkbox" onChange={e => e.preventDefault()} />
                            <label htmlFor="checkbox" className="toggle-label">
                                <i className="fas fa-moon" />
                                <i className="fas fa-sun" />
                                <div className="toggle-ball">
                                </div>
                            </label>
                        </div>
                    </div> */}

                    {/*<Dropdown className='d-flex align-items-stretch me-3'>*/}
                    {/*    <Dropdown.Toggle className='hide-arrow bg-transparent border-0 p-0 d-flex align-items-center'*/}
                    {/*                     id='dropdown-basic'>*/}
                    {/*<Image src={activeFlag()} width='30' height='20'*/}
                    {/*       className='image'/>*/}
                    {/*    </Dropdown.Toggle>*/}
                    {/*    <Dropdown.Menu>*/}
                    {/*        <div*/}
                    {/*            className='menu-icon-grid d-flex justify-content-around  align-items-center flex-wrap p-2 pb-0'>*/}
                    {/*            <div className='d-sm-flex'>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'en' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('en')}>*/}
                    {/*                        <Image src={English} className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>English</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'fr' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('fr')}>*/}
                    {/*                        <Image src={French} className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>French</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'ar' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('ar')}>*/}
                    {/*                        <Image src={Arabic} className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>Arabic</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*            </div>*/}
                    {/*            <div className='d-sm-flex'>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'tr' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('tr')}>*/}
                    {/*                        <Image src={Turkish}  className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>Turkish</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'gr' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('gr')}>*/}
                    {/*                        <Image src={German} className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>German</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'sp' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('sp')}>*/}
                    {/*                        <Image src={Spanish}  className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>Spanish</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*            </div>*/}
                    {/*            <div className='d-sm-flex'>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'vi' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('vi')}>*/}
                    {/*                        <Image src={Vietnamese} className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>Vietnamese</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*                <Dropdown.Item className='p-0 bg-white'>*/}
                    {/*                    <button*/}
                    {/*                        className={`language-btn btn mb-2 mx-1 ${activeLanguage === 'cn' ? 'language-btn-active active' : null}`}*/}
                    {/*                        onClick={() => changeLanguage('cn')}>*/}
                    {/*                        <Image src={Chinese} className='mb-1'/>*/}
                    {/*                        <span className='fs-6'>Chinese</span>*/}
                    {/*                    </button>*/}
                    {/*                </Dropdown.Item>*/}
                    {/*            </div>*/}
                    {/*        </div>*/}
                    {/*    </Dropdown.Menu>*/}
                    {/*</Dropdown>*/}
                    <Dropdown className="d-flex align-items-stretch">
                        <Dropdown.Toggle
                            className="hide-arrow bg-transparent border-0 p-0 d-flex align-items-center"
                            id="dropdown-basic"
                        >
                            <div className="d-flex align-items-center justify-content-center">
                                {imageUrl || image ? (
                                    <Image
                                        src={
                                            imageUrl ? imageUrl : image || User
                                        }
                                        className="image image-circle image-tiny"
                                        alt="user-avatar"
                                        height="50"
                                        width="50"
                                    />
                                ) : (
                                    <span className="custom-user-avatar">
                                        {/* {getAvatarName(updatedFirstName && updatedLastName ? updatedFirstName + ' ' + updatedLastName : firstName + ' ' + lastName)} */}
                                        {getAvatarName(
                                            updatedFirstName && updatedLastName
                                                ? updatedFirstName
                                                : firstName
                                        )}
                                    </span>
                                )}
                                <span className="ms-2 text-gray-600 d-none d-sm-block profilename">
                                    {/* {updatedFirstName && updatedLastName ? <>{updatedFirstName + ' ' + updatedLastName}</> : <> {firstName + ' ' + lastName}</>} */}
                                    {updatedFirstName && updatedLastName ? (
                                        <>{updatedFirstName}</>
                                    ) : (
                                        <> {firstName}</>
                                    )}
                                </span>
                            </div>
                            <FontAwesomeIcon
                                icon={faAngleDown}
                                className="text-gray-600 ms-2 d-none d-sm-block"
                            />
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                            <div className="text-center pb-1 border-bottom mb-4">
                                <div className="text-center text-decoration-none pb-5 w-100 align-items-center">
                                    <div className="image image-circle w-100 image-tiny pb-5">
                                        {imageUrl || image ? (
                                            <img
                                                src={
                                                    imageUrl ? imageUrl : image
                                                }
                                                className="img-fluid"
                                                height="50"
                                                width="50"
                                                alt="user-avatar"
                                            />
                                        ) : (
                                            <span className="user_avatar mx-auto">
                                                {getAvatarName(
                                                    updatedFirstName &&
                                                        updatedLastName
                                                        ? updatedFirstName +
                                                              " " +
                                                              updatedLastName
                                                        : firstName +
                                                              " " +
                                                              lastName
                                                )}
                                            </span>
                                        )}
                                    </div>
                                    <div className="d-flex flex-column">
                                        <h3 className="text-gray-900">
                                            {/* {updatedFirstName && updatedLastName ? <>{updatedFirstName + ' ' + updatedLastName}</> : <> {firstName + ' ' + lastName}</>} */}
                                            {updatedFirstName &&
                                            updatedLastName ? (
                                                <>{updatedFirstName}</>
                                            ) : (
                                                <> {firstName}</>
                                            )}
                                        </h3>
                                        <h4 className="mb-0 fw-400 fs-6">
                                            {updatedEmail
                                                ? updatedEmail
                                                : users}
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <Dropdown.Item
                                onClick={(e) => onProfileClick(e)}
                                className="px-5 fs-6"
                            >
                                <span className="dropdown-icon me-4 text-gray-600">
                                    <FontAwesomeIcon icon={faUser} />
                                </span>
                                {getFormattedMessage(
                                    "header.profile-menu.profile.label"
                                )}
                            </Dropdown.Item>
                            {!isAdmin && (
                                <Dropdown.Item
                                    onClick={(e) => onManageSub(e)}
                                    className="px-5 fs-6"
                                >
                                    <span className="dropdown-icon me-4 text-gray-600">
                                        <FontAwesomeIcon icon={faMoneyBill} />
                                    </span>
                                    {getFormattedMessage(
                                        "header.manage.subscription.label"
                                    )}
                                </Dropdown.Item>
                            )}

                            {/* <Dropdown.Item
                                onClick={(e) => onAccountClick(e)}
                                className='px-5 fs-6'>
                                <span className='dropdown-icon me-4 text-gray-600'>
                                    <FontAwesomeIcon icon={faTools} />
                                </span>
                                {getFormattedMessage('account.account.title')}
                            </Dropdown.Item> */}
                            {
                                <Dropdown.Item
                                    onClick={onClickDeleteModel}
                                    className="px-5 fs-6"
                                >
                                    <span className="dropdown-icon me-4 text-gray-600">
                                        <FontAwesomeIcon icon={faLock} />
                                    </span>
                                    {getFormattedMessage(
                                        "header.profile-menu.change-password.label"
                                    )}
                                </Dropdown.Item>
                            }
                            <Dropdown.Item
                                onClick={onClickChangeLanguageModel}
                                className="px-5 fs-6"
                            >
                                <span className="dropdown-icon me-4 text-gray-600">
                                    <FontAwesomeIcon icon={faLanguage} />
                                </span>
                                {getFormattedMessage(
                                    "header.profile-menu.change-language.label"
                                )}
                            </Dropdown.Item>
                            {!isAdmin && (
                                <Dropdown.Item
                                    onClick={(e) => onDeleteAccount(e)}
                                    className="px-5 fs-6"
                                >
                                    <span className="dropdown-icon me-4 text-gray-600">
                                        <FontAwesomeIcon icon={faUserXmark} />
                                    </span>
                                    {getFormattedMessage(
                                        "header.delete-account.label"
                                    )}
                                </Dropdown.Item>
                            )}
                            <Dropdown.Item
                                onClick={(e) => onLogOut(e)}
                                className="px-5 fs-6"
                            >
                                <span className="dropdown-icon me-4 text-gray-600">
                                    <FontAwesomeIcon
                                        icon={faRightFromBracket}
                                    />
                                </span>
                                {getFormattedMessage(
                                    "header.profile-menu.logout.label"
                                )}
                            </Dropdown.Item>
                        </Dropdown.Menu>
                    </Dropdown>
                </Nav>
            </div>
            {deleteModel === true && (
                <ChangePassword
                    deleteModel={deleteModel}
                    onClickDeleteModel={onClickDeleteModel}
                    isDeleteAccount={isDeleteAccount}
                    onDeleteAccount={onDeleteAccount}
                />
            )}

            {languageModel === true && (
                <LanguageModel
                    languageModel={languageModel}
                    onClickLanguageModel={onClickLanguageModel}
                />
            )}
        </Navbar>
    );
};

const mapStateToProps = (state) => {
    const { selectedLanguage } = state;
    return { selectedLanguage };
};

export default connect(mapStateToProps, { logoutAction, deleteAccountAction })(
    Header
);
