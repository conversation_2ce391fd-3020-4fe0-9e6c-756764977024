import React from 'react';
import { Navbar } from 'react-bootstrap-v5';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import logo from "../../assets/media/logos/logo.png"

const CommonNavbar = (props) => {

    return (
        <Navbar collapseOnSelect expand='lg' className='align-items-stretch ms-auto py-1'>
            <div className="container-fluid">
                <button className="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarTogglerDemo03" aria-controls="navbarTogglerDemo03"
                    aria-expanded="false" aria-label="Toggle navigation">
                    <span className="navbar-toggler-icon"></span>
                </button>
                <Link to={"/"} className="navbar-brand"><img src={logo} width={90} alt={"logo"} /></Link>
                <div>
                    <Link to={"/app/qr"} className={"text-decoration-none text-dark mx-1 btn"}>{getFormattedMessage('globally.header.generate-qr-code.label')}</Link>
                    <Link to={"/login"} className={"text-decoration-none text-dark mx-1 btn"}>Login</Link>
                    <Link to={"/register"} className={"text-decoration-none text-dark mx-1 btn"}>Register</Link>
                </div>
            </div>
        </Navbar>
    )
};



export default connect(null, {})(CommonNavbar);
