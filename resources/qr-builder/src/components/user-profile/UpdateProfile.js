import React, { useEffect, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import * as EmailValidator from 'email-validator';
import ImagePicker from '../../shared/image-picker/ImagePicker';
import { updateProfile, fetchProfile } from '../../store/action/updateProfileAction';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import user from '../../assets/images/avatar.png';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";

const UpdateProfile = () => {
    const { userProfile } = useSelector(state => state)
    const Dispatch = useDispatch()
    const navigate = useNavigate();
    const [profileValue, setProfileValue] = useState({
        name: '',
        email: '',
        image: '',
    });

    const [errors, setErrors] = useState({
        name: '',
        email: '',
    });

    const avtarName = getAvatarName(userProfile && userProfile?.image_url === '' && userProfile?.name);
    const newImg = userProfile && userProfile?.image_url === null && avtarName;
    const [imagePreviewUrl, setImagePreviewUrl] = useState(newImg && newImg);
    const [selectImg, setSelectImg] = useState(null);
    const disabled = selectImg ? false : userProfile && userProfile?.name === profileValue.name
        && userProfile?.email === profileValue.email

    useEffect(() => {
        if (userProfile) {
            setProfileValue({
                name: userProfile ? userProfile.name : '',
                email: userProfile ? userProfile.email : '',
                image: userProfile ? userProfile?.image_url : '',
            })
            setImagePreviewUrl(userProfile ? userProfile?.image_url : user);
        }
    }, [userProfile]);

    useEffect(() => {
        Dispatch(fetchProfile());
    }, []);

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (!profileValue['name'] || profileValue['name']?.trim()?.length === 0) {
            errorss['name'] = getFormattedMessage("user.input.first-name.validate.label");
        } else if (!EmailValidator.validate(profileValue['email'])) {
            if (!profileValue['email']) {
                errorss['email'] = getFormattedMessage("globally.input.email.validate.label");
            } else {
                errorss['email'] = getFormattedMessage("globally.input.email.valid.validate.label");
            }
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const onChangeInput = (e) => {
        e.preventDefault();
        setProfileValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    };

    const handleImageChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === 'image/jpeg' || file.type === 'image/png') {
                setSelectImg(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setImagePreviewUrl(fileReader.result);
                };
                fileReader.readAsDataURL(file);
                setErrors('');
            }
        }
    };

    const prepareFormData = (data) => {
        const formData = new FormData();
        formData.append('name', data.name);
        formData.append('email', data.email);
        if (selectImg) {
            formData.append('image', data.image);
        }
        return formData;
    };

    const onEdit = (event) => {
        event.preventDefault();
        const Valid = handleValidation();
        if (!disabled && Valid) {
            profileValue.image = selectImg;
            setProfileValue(profileValue)
            Dispatch(updateProfile(prepareFormData(profileValue), navigate));
        }
    };

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('update-profile.title')} />
            <div className='card'>
                <div className='card-body'>
                    <Form>
                        <div className='row'>
                            <div className='col-md-6 mb-3'>
                                <label htmlFor="exampleInputEmail1" className="form-label">
                                    {getFormattedMessage("globally.input.name.label")}:<span className="required" />
                                </label>
                                <input type='text' name='name' value={profileValue.name || ''}
                                    placeholder={placeholderText("globally.input.name.label")}
                                    className='form-control' autoFocus={true}
                                    onChange={(e) => onChangeInput(e)} />
                                <span
                                    className='text-danger d-block fw-400 fs-small mt-2'>{errors['name'] ? errors['name'] : null}</span>
                            </div>
                            <div className='col-md-6 mb-3'>
                                <label className='form-label'>
                                    {getFormattedMessage('user.input.email.label')}:
                                </label>
                                <span className='required' />
                                <input type='text' name='email'
                                    placeholder={placeholderText('user.input.email.placeholder.label')}
                                    className='form-control' value={profileValue.email || ''}
                                    onChange={(e) => onChangeInput(e)} />
                                <span
                                    className='text-danger d-block fw-400 fs-small mt-2'>{errors['email'] ? errors['email'] : null}</span>
                            </div>
                            <div className='col-12'>
                                <div className='mb-4'>
                                    <ImagePicker imageTitle={'header.profile-menu.profile.label'} imagePreviewUrl={imagePreviewUrl}
                                        user={user}
                                        avtarName={avtarName} handleImageChange={handleImageChanges} />
                                </div>
                            </div>
                            <div className='d-flex justify-content-end mt-5'>
                                {userProfile ?
                                    <div onClick={(event) => onEdit(event)}>
                                        <input className='btn btn-primary me-3' type='submit'
                                            value={placeholderText('globally.save-btn')}
                                            disabled={disabled}
                                        />
                                    </div> : null
                                }
                                <Link to='/app/dashboard' className='btn btn-secondary me-3'>
                                    {getFormattedMessage('globally.cancel-btn')}
                                </Link>
                            </div>
                        </div>
                    </Form>
                </div>
            </div>
        </MasterLayout>
    )
};

export default UpdateProfile;
