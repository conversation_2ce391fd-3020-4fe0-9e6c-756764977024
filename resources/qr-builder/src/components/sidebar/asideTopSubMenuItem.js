import React, { useEffect } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { getFormattedMessage } from "../../shared/sharedMethod";
import { Dropdown } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAngleDown } from "@fortawesome/free-solid-svg-icons";
import { useDispatch } from "react-redux";
import faviIcon from "./../../../../../public/default-images/favicon.png";
import {
    announcementNotification,
    fetchFrontSetting,
    remainingSubscriptionNotification,
} from "../../store/action/frontSettingAction";
import { Tokens } from "../../constants";
import { cssHandling } from "../../cssHandling/cssHandling";

const AsideTopSubMenuItem = (props) => {
    const { asideConfig, isMenuCollapse, frontSettings } = props;
    const location = useLocation();
    const dispatch = useDispatch();
    const website_favicon = localStorage.getItem("website_favicon");
    const id = useParams();
    const navigate = useNavigate();
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);

    useEffect(() => {
        cssHandling(updatedLanguage);

        return () => cssHandling(updatedLanguage);
    }, []);

    useEffect(() => {
        const isAdmin = JSON.parse(localStorage.getItem("isAdmin"));
        const email_verified_at = localStorage.getItem(Tokens.EMAIL_VERIFY);
        if (email_verified_at === "null") {
            localStorage.removeItem(Tokens.ADMIN);
            localStorage.removeItem(Tokens.EMAIL_VERIFY);
            navigate("/login");
        } else {
            if (!isAdmin) {
                if (frontSettings.plan_expired === true) {
                    if (
                        !window.location.href.includes("upgrade-plan") &&
                        !window.location.href.includes("/selected-plan/") &&
                        !window.location.href.includes("/profile/edit")
                    ) {
                        navigate("/app/manage-subscription");
                        dispatch(remainingSubscriptionNotification(navigate));
                    }
                } else {
                    dispatch(fetchFrontSetting());
                    dispatch(remainingSubscriptionNotification(navigate));
                    dispatch(announcementNotification(navigate));
                }
            }
        }
    }, [website_favicon]);

    const logo = frontSettings?.favicon
        ? frontSettings?.favicon
        : localStorage.getItem("website_favicon");
    let link = document.querySelector("link[rel~='icon']");
    if (!link) {
        link = document.createElement("link");
        link.rel = "icon";
        document.getElementsByTagName("head")[0].appendChild(link);
    }

    const faviLogo = logo === null ? faviIcon : logo;
    link.href = faviLogo;

    return (
        <>
            <nav
                className={`navbar navbar-expand-xl nav-sm navbar-light ${
                    isMenuCollapse === true ? "top-navbar" : ""
                } d-xl-flex align-items-stretch d-block px-5 py-5  px-xl-0 py-xl-01`}
            >
                <div className="navbar-collapse">
                    <div className="navbar-nav me-auto mb-2 mb-lg-0">
                        {location.pathname.includes("app/admin/front") ? (
                            <div className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0">
                                <Link
                                    to={"/app/admin/front-hero"}
                                    className={`${
                                        location.pathname.includes(
                                            "app/admin/front"
                                        )
                                            ? "active"
                                            : ""
                                    } nav-link p-0`}
                                >
                                    <span>
                                        {getFormattedMessage(
                                            "admin.frontcms.title"
                                        )}
                                    </span>
                                </Link>
                            </div>
                        ) : location.pathname.includes(
                              "app/admin/minify-link/"
                          ) && location.pathname.includes("/analytics") ? (
                            <div className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0">
                                <Link
                                    to={
                                        "/app/admin/minify-link/" +
                                        id.id +
                                        "/analytics"
                                    }
                                    className={`${
                                        location.pathname.includes(
                                            "app/admin/minify-link/"
                                        )
                                            ? "active"
                                            : ""
                                    } nav-link p-0`}
                                >
                                    <span>
                                        {getFormattedMessage(
                                            "globally.input.link-analytics.lable"
                                        )}
                                    </span>
                                </Link>
                            </div>
                        ) : location.pathname.includes("app/selected-plan/") ? (
                            <div className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0">
                                <Link
                                    to="/app/manage-subscription"
                                    className={`${
                                        location.pathname.includes(
                                            "/app/selected-plan/"
                                        )
                                            ? "active"
                                            : ""
                                    } nav-link p-0`}
                                >
                                    <span>
                                        {getFormattedMessage("plans.title")}
                                    </span>
                                </Link>
                            </div>
                        ) : location.pathname.includes(
                              "app/digital-business-cards"
                          ) ? (
                            <div className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0">
                                <Link
                                    to="/app/digital-business-cards"
                                    className={`${
                                        location.pathname.includes(
                                            "digital-business-cards"
                                        )
                                            ? "active"
                                            : ""
                                    } nav-link p-0`}
                                >
                                    <span>
                                        {getFormattedMessage(
                                            "digital.business.card.title"
                                        )}
                                    </span>
                                </Link>
                            </div>
                        ) : location.pathname.includes(
                              "app/manage-subscription"
                          ) ? (
                            <div className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0">
                                <Link
                                    to="/app/manage-subscription"
                                    className={`${
                                        location.pathname.includes(
                                            "/app/manage-subscription"
                                        )
                                            ? "active"
                                            : ""
                                    } nav-link p-0`}
                                >
                                    <span>
                                        {getFormattedMessage(
                                            "header.manage.subscription.label"
                                        )}
                                    </span>
                                </Link>
                            </div>
                        ) : location.pathname.includes("app/admin/setting") ||
                          location.pathname.includes("app/admin/payment") ? (
                            <div className="d-flex">
                                <div className=" nav-item position-relative mx-xl-3 mb-3 mb-xl-0`">
                                    <Link
                                        to="/app/admin/settings-main"
                                        className={`${
                                            location.pathname.includes(
                                                "/app/admin/setting"
                                            )
                                                ? "active"
                                                : ""
                                        } nav-link p-0`}
                                    >
                                        <span>
                                            {getFormattedMessage(
                                                "admin.settings.title"
                                            )}
                                        </span>
                                    </Link>
                                </div>
                                <div className="nav-item position-relative ms-2 mx-xl-3 mb-3 mb-xl-0`">
                                    <Link
                                        to="/app/admin/payment-paypal"
                                        className={`${
                                            location.pathname.includes(
                                                "/app/admin/payment"
                                            )
                                                ? "active"
                                                : ""
                                        } nav-link p-0`}
                                    >
                                        <span>
                                            {getFormattedMessage(
                                                "admin.setting.payment-configuration.title"
                                            )}
                                        </span>
                                    </Link>
                                </div>
                            </div>
                        ) : location.pathname === "/app/profile/edit" ||
                          location.pathname === "/app/admin/profile/edit" ? (
                            <div className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0">
                                <Link
                                    to="/app/profile/edit"
                                    className={`${
                                        location.pathname ===
                                            "/app/profile/edit" ||
                                        location.pathname ===
                                            "/app/admin/profile/edit"
                                            ? "active"
                                            : ""
                                    } nav-link p-0`}
                                >
                                    <span>
                                        {getFormattedMessage(
                                            "update-profile.title"
                                        )}
                                    </span>
                                </Link>
                            </div>
                        ) : (
                            asideConfig &&
                            asideConfig.map((mainItems, index) => {
                                return (
                                    <div
                                        key={index}
                                        className={`${
                                            location.pathname ===
                                                mainItems.to ||
                                            location.pathname ===
                                                mainItems.path ||
                                            location.pathname ===
                                                mainItems.stockPath ||
                                            location.pathname ===
                                                mainItems.productPath ||
                                            location.pathname ===
                                                mainItems.purchasePath ||
                                            location.pathname ===
                                                mainItems.topSellingPath ||
                                            location.pathname ===
                                                mainItems.productQuantityAlertPath ||
                                            location.pathname ===
                                                mainItems.prefixesPath ||
                                            location.pathname ===
                                                mainItems.supplierReportPath ||
                                            location.pathname ===
                                                mainItems.customerReportPath ||
                                            location.pathname ===
                                                mainItems.bestCustomerReportPath ||
                                            location.pathname ===
                                                mainItems.mailSettingsPath ||
                                            location.pathname ===
                                                mainItems.profitLossReportPath ||
                                            location.pathname.includes(
                                                mainItems.to
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath?.userSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.customerSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.suppliareSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.productsSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.categoriesSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.brandsSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath?.unitsSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.barcodeSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.purchasesSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.purchaseReturnSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath?.salesSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.salesReturnSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.expensesSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.expenseCategoriesSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.emailTemplateSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.smsTemplateSubPath
                                            ) ||
                                            location.pathname.includes(
                                                mainItems?.subPath
                                                    ?.smsApiSubPath
                                            ) ||
                                            location.pathname ===
                                                mainItems.stockDetailPath +
                                                    "/" +
                                                    id.id ||
                                            location.pathname ===
                                                mainItems.customerReportDetailsPath +
                                                    "/" +
                                                    id.id ||
                                            location.pathname ===
                                                mainItems.supplierReportDetailsPath +
                                                    "/" +
                                                    id.id
                                                ? "d-flex"
                                                : "d-none"
                                        }`}
                                    >
                                        {mainItems.items
                                            ? mainItems.items.map(
                                                  (item, index) => {
                                                      if (index <= 4) {
                                                          return (
                                                              <div
                                                                  key={index}
                                                                  className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0"
                                                              >
                                                                  <Link
                                                                      to={
                                                                          item.to
                                                                      }
                                                                      className={`nav-link p-0 ${
                                                                          location.pathname ===
                                                                              item.to ||
                                                                          (mainItems.isSamePrefix
                                                                              ? null
                                                                              : location.pathname.includes(
                                                                                    mainItems.to
                                                                                )) ||
                                                                          location.pathname ===
                                                                              item.detail +
                                                                                  "/" +
                                                                                  id.id ||
                                                                          location.pathname ===
                                                                              "/app/profile/edit" ||
                                                                          location.pathname ===
                                                                              "/app/admin/profile/edit"
                                                                              ? " active"
                                                                              : ""
                                                                      }`}
                                                                  >
                                                                      {location.pathname ===
                                                                          "/app/profile/edit" ||
                                                                      location.pathname ===
                                                                          "/app/admin/profile/edit" ? (
                                                                          <span>
                                                                              {getFormattedMessage(
                                                                                  "update-profile.title"
                                                                              )}
                                                                          </span>
                                                                      ) : (
                                                                          <span>
                                                                              {
                                                                                  item.title
                                                                              }
                                                                          </span>
                                                                      )}
                                                                  </Link>
                                                              </div>
                                                          );
                                                      }
                                                  }
                                              )
                                            : mainItems?.subMenu?.map(
                                                  (item, index) => {
                                                      return location.pathname ===
                                                          item.to ||
                                                          location.pathname.includes(
                                                              item.to
                                                          ) ? (
                                                          <div
                                                              key={index}
                                                              className="nav-item position-relative mx-xl-3 mb-3 mb-xl-0"
                                                          >
                                                              <Link
                                                                  to={item.to}
                                                                  className={`nav-link p-0 ${
                                                                      location.pathname ===
                                                                          item.to ||
                                                                      location.pathname.includes(
                                                                          item.to
                                                                      ) ||
                                                                      (mainItems.isSamePrefix
                                                                          ? null
                                                                          : location.pathname.includes(
                                                                                mainItems.to
                                                                            )) ||
                                                                      location.pathname ===
                                                                          item.detail +
                                                                              "/" +
                                                                              id.id ||
                                                                      location.pathname ===
                                                                          "/app/profile/edit" ||
                                                                      location.pathname ===
                                                                          "/app/admin/profile/edit"
                                                                          ? " active"
                                                                          : ""
                                                                  }`}
                                                              >
                                                                  <span>
                                                                      {getFormattedMessage(
                                                                          item.title
                                                                      )}
                                                                  </span>
                                                              </Link>
                                                          </div>
                                                      ) : (
                                                          ""
                                                      );
                                                  }
                                              )}
                                        {/* Report Dropdown  */}
                                        {location.pathname.includes(
                                            "report"
                                        ) && (
                                            <Dropdown className="d-flex align-items-stretch">
                                                <Dropdown.Toggle
                                                    className="hide-arrow bg-transparent border-0 p-0 d-flex align-items-center"
                                                    id="dropdown-basic"
                                                >
                                                    <div className="d-flex align-items-center justify-content-center">
                                                        <span className="ms-2 text-gray-600 d-none d-sm-block">
                                                            {getFormattedMessage(
                                                                "more-report.option.title"
                                                            )}
                                                        </span>
                                                    </div>
                                                    <FontAwesomeIcon
                                                        icon={faAngleDown}
                                                        className="text-gray-600 ms-2 d-none d-sm-block"
                                                    />
                                                </Dropdown.Toggle>
                                                <Dropdown.Menu className="mt-6">
                                                    {mainItems.items &&
                                                        mainItems.items.map(
                                                            (item, index) => {
                                                                if (
                                                                    index >= 5
                                                                ) {
                                                                    return (
                                                                        <Dropdown.Item
                                                                            key={
                                                                                index
                                                                            }
                                                                            className="px-0 py-0 fs-6"
                                                                            active={
                                                                                location.pathname ===
                                                                                    item.to ||
                                                                                location.pathname.includes(
                                                                                    item.to
                                                                                )
                                                                                    ? true
                                                                                    : false
                                                                            }
                                                                        >
                                                                            <div className="position-relative mx-xl-3 mb-3 mb-xl-0 ">
                                                                                <Link
                                                                                    to={
                                                                                        item.to
                                                                                    }
                                                                                    className={`nav-link px-3 py-2 ${
                                                                                        location.pathname ===
                                                                                            item.to ||
                                                                                        (mainItems.isSamePrefix
                                                                                            ? null
                                                                                            : location.pathname.includes(
                                                                                                  mainItems.to
                                                                                              )) ||
                                                                                        location.pathname ===
                                                                                            item.detail +
                                                                                                "/" +
                                                                                                id.id ||
                                                                                        location.pathname ===
                                                                                            "/app/profile/edit" ||
                                                                                        location.pathname ===
                                                                                            "/app/admin/profile/edit"
                                                                                            ? "text-white"
                                                                                            : ""
                                                                                    }`}
                                                                                >
                                                                                    <span>
                                                                                        {
                                                                                            item.title
                                                                                        }
                                                                                    </span>
                                                                                </Link>
                                                                            </div>
                                                                        </Dropdown.Item>
                                                                    );
                                                                }
                                                            }
                                                        )}
                                                </Dropdown.Menu>
                                            </Dropdown>
                                        )}
                                    </div>
                                );
                            })
                        )}
                    </div>
                </div>
            </nav>
        </>
    );
};

export default AsideTopSubMenuItem;
