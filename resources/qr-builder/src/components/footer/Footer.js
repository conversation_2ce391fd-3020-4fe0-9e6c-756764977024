import React from 'react';
import { getFormattedMessage } from '../../shared/sharedMethod';

const Footer = (props) => {
    const { config, frontSetting } = props
    const isAdmin = JSON.parse(localStorage.getItem('isAdmin'))
    return (
        <footer className='border-top w-100 pt-4 mt-7 d-flex justify-content-between'>
            <p className='fs-6 text-gray-600'>{getFormattedMessage("footer.All.Rights.Reserved")} &#169; {new Date().getFullYear()}
                <a href={isAdmin === true ? '/#/app/admin/dashboard' : '/#/app/dashboard'} className='text-decoration-none'> {frontSetting?.title}</a>
            </p>
            <div className="fs-6 text-gray-600">
                {config && config.version ? "v" + config.version : ""}
            </div>
        </footer>
    )
};

export default Footer;
