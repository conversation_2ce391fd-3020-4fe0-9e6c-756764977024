import React from 'react';
import { connect, useSelector } from 'react-redux';
import ProjectForm from './ProjectForm';
import { addProject } from '../../store/action/projectAction';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../components/header/HeaderTitle';
import { Filters } from '../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import { Helmet } from 'react-helmet';

const CreateProject = ( props ) => {
    const { addProject } = props;
    const navigate = useNavigate();
    const { frontSettings } = useSelector( state => state )
    const addProjectData = ( formValue ) => {
        addProject( formValue, navigate, Filters.OBJ );
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText( 'project.create.title' ) + ' | ' + frontSettings?.title} />
            <HeaderTitle title={getFormattedMessage( 'project.create.title' )} to='/app/collections' />
            <ProjectForm addProjectData={addProjectData} />
        </MasterLayout>
    );
}

export default connect( null, { addProject } )( CreateProject );
