import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Card, Table } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../shared/tab-title/TabTitle';
import { placeholderText } from '../../shared/sharedMethod';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";
import { fetchProject } from '../../store/action/projectAction';
import moment from "moment";
import HeaderTitle from '../header/HeaderTitle';
import RoundLoader from '../../shared/components/loaders/RoundLoader';

const ProjectDetail = (props) => {
    const { isLoading, project, fetchProject } = props;
    const { id } = useParams();

    useEffect(() => {
        fetchProject(id);
    }, []);

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('project.details.title')} />
            <HeaderTitle title={getFormattedMessage('project.details.title')} />
            {<>
                <div className='pt-5'>
                    <Card>
                        {/* <Card.Header as='h5'>{getFormattedMessage('project.details.title')}</Card.Header> */}
                        <Card.Body className=''>
                            {isLoading ? <RoundLoader /> : <Table responsive>
                                <tbody>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('react-data-table.name.column.title')}</td>
                                        <td className='py-4'>{project[0]?.attributes.name}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.color.lable')}</td>
                                        <td className='py-4'>
                                            <span className='badge' style={{ "backgroundColor": project[0]?.attributes.color, "color": project[0]?.attributes.color === "#ffffff" ? "#000000" : "#ffffff" }}>
                                                <span>{project[0]?.attributes.color}</span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.react-table.column.created-date.label')}</td>
                                        <td className='py-4'>{moment(project && project[0]?.attributes.created_at).fromNow()}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.react-table.column.updated-date.label')}</td>
                                        <td className='py-4'>{moment(project && project[0]?.attributes.updated_at).fromNow()}</td>
                                    </tr>
                                </tbody>
                            </Table>}
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </MasterLayout>
    )

};

const mapStateToProps = state => {
    const { isLoading, project } = state;
    return { isLoading, project }
};

export default connect(mapStateToProps, { fetchProject })(ProjectDetail);
