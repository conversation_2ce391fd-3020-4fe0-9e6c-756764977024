import React from 'react';
import {connect} from 'react-redux';
import { deleteProject } from '../../store/action/projectAction';
import DeleteModel from '../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../shared/sharedMethod';

const DeleteProject = (props) => {
    const {deleteProject, onDelete, deleteModel, onClickDeleteModel} = props;

    const deleteUserClick = () => {
        deleteProject(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                                         deleteUserClick={deleteUserClick} name={getFormattedMessage('project.title')}/>}
        </div>
    )
};

export default connect(null, {deleteProject})(DeleteProject);
