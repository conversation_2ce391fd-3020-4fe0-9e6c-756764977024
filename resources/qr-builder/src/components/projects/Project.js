import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../shared/table/ReactDataTable';
import { fetchProjects } from '../../store/action/projectAction';
import DeleteProject from './DeleteProject';
import TabTitle from '../../shared/tab-title/TabTitle';
import { getFormattedDate } from '../../shared/sharedMethod';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { placeholderText } from '../../shared/sharedMethod';
import ActionButton from '../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../shared/components/loaders/TopProgressBar";

const Project = ( props ) => {
    const { project, fetchProjects, totalRecord, isLoading } = props;
    const [ deleteModel, setDeleteModel ] = useState( false );
    const [ isDelete, setIsDelete ] = useState( null );

    const onClickDeleteModel = ( isDelete = null ) => {
        setDeleteModel( !deleteModel );
        setIsDelete( isDelete );
    };


    const itemsValue = project.length >= 0 && project.map( project => ( {
        date: getFormattedDate( project.attributes.created_at, "d-m-y" ),
        time: moment( project.attributes.created_at ).format( 'LT' ),
        name: project.attributes.name,
        color: project.attributes.color,
        id: project.id
    } ) );

    const onChange = ( filter ) => {
        fetchProjects( filter, true );
    };

    const goToEdit = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/collections/edit/' + id;
    };


    const goToDetails = ( id ) => {
        window.location.href = '#/app/collections/detail/' + id;
    };

    const columns = [
        {
            name: getFormattedMessage( 'react-data-table.name.column.title' ),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/collections/detail/${row.id}`} className='text-decoration-none'>{row.name}</Link>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( "globally.input.color.lable" ),
            selector: row => row.color,
            sortField: 'color',
            sortable: false,
            cell: row => {
                return ( <span className='badge' style={{ "backgroundColor": row.color, "color": row.color === "#ffffff" ? "#000000" : "#ffffff" }}>
                    <span>{row.color.substr( 1 )}</span>
                </span> )
            }
        },
        // {
        //     name: getFormattedMessage('globally.react-table.column.created-date.label'),
        //     selector: row => row.date,
        //     sortField: 'created_at',
        //     sortable: true,
        //     cell: row => {
        //         return (
        //             <span className='badge bg-light-info'>
        //                 <div className='mb-1'>{row.time}</div>
        //                 <div>{row.date}</div>
        //             </span>
        //         )
        //     }
        // },
        {
            name: getFormattedMessage( 'react-data-table.action.column.label' ),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton item={row} goToEditProduct={goToEdit} isEditMode={true}
                onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( 'projects.title' )} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                ButtonValue={getFormattedMessage( 'project.create.title' )}
                to='#/app/collections/create' totalRows={totalRecord} isLoading={isLoading} />
            <DeleteProject onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )
};

const mapStateToProps = ( state ) => {
    const { project, totalRecord, isLoading } = state;
    return { project, totalRecord, isLoading }
};
export default connect( mapStateToProps, { fetchProjects } )( Project );
