import React, { useEffect, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import ModelFooter from '../../shared/components/modelFooter';
import ReactColorPicker from '../../shared/colorpocker/ReactColorPicker';
import { editProject } from '../../store/action/projectAction';

const ProjectForm = ( props ) => {
    const { addProjectData, id, singleProject } = props;
    const Dispatch = useDispatch()
    const navigate = useNavigate();

    const [ projectValue, setProjectValue ] = useState( {
        name: '',
        color: ''
    } );
    const [ errors, setErrors ] = useState( {
        name: '',
    } );


    const disabled = singleProject && singleProject[ 0 ].name === projectValue.name
        && singleProject[ 0 ].color === projectValue.color


    useEffect( () => {
        if ( singleProject?.length ) {
            setProjectValue( {
                name: singleProject ? singleProject[ 0 ].name : '',
                color: singleProject ? singleProject[ 0 ].color : ''
            } )
        }
    }, [ singleProject ] )



    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if ( !projectValue[ 'name' ] || projectValue[ 'name' ]?.trim()?.length === 0 ) {
            errorss[ 'name' ] = getFormattedMessage( "user.input.first-name.validate.label" );
        } else {
            isValid = true;
        }
        setErrors( errorss );
        return isValid;
    };

    const onChangeInput = ( e ) => {
        e.preventDefault();
        setProjectValue( inputs => ( { ...inputs, [ e.target.name ]: e.target.value } ) )
        setErrors( '' );
    };

    const handleCallback = ( color ) => {
        setProjectValue( previousState => {
            return { ...previousState, color: color.hex }
        } );
    };


    const prepareFormData = ( data ) => {
        const formData = new FormData();
        formData.append( 'name', data.name );
        formData.append( 'color', data.color );
        return formData;
    };


    const onSubmit = ( event ) => {
        event.preventDefault();
        const valid = handleValidation();
        if ( singleProject && valid ) {
            if ( !disabled ) {
                Dispatch( editProject( id, prepareFormData( projectValue ), navigate ) );
            }
        } else {
            if ( valid ) {
                setProjectValue( projectValue );
                addProjectData( prepareFormData( projectValue ) );
            }
        }
    };


    return (
        <div className='card'>
            <div className='card-body'>
                <Form>
                    <div className='row'>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage( "project-name.title" )}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={projectValue.name}
                                placeholder={placeholderText( "project-name.title" )}
                                className='form-control' autoFocus={true}
                                onChange={( e ) => onChangeInput( e )} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors[ 'name' ] ? errors[ 'name' ] : null}</span>
                        </div>
                        <div className='col-md-1 mb-3'>
                            <label className='form-label'>
                                {getFormattedMessage( "globally.input.color.lable" )}:
                            </label>
                            <div style={{ "width": "40px" }}>
                                <ReactColorPicker onChangeColor={handleCallback} className={'40px'} selectedColor={projectValue.color} />
                            </div>
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors[ 'email' ] ? errors[ 'email' ] : null}</span>
                        </div>
                        <ModelFooter onEditRecord={singleProject} onSubmit={onSubmit} editDisabled={disabled}
                            link='/app/collections' addDisabled={!projectValue.name} />
                    </div>
                </Form>
            </div>
        </div>
    )
};

const mapStateToProps = ( state ) => {
    const {} = state;
    return {}
};

export default connect( mapStateToProps, {} )( ProjectForm );

