import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import ProjectForm from './ProjectForm';
import { fetchProject } from '../../store/action/projectAction';
import { useParams } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../components/header/HeaderTitle';
import { getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import TopProgressBar from '../../shared/components/loaders/TopProgressBar';
import TabTitle from "../../shared/tab-title/TabTitle";

const EditProject = ( props ) => {
    const { fetchProject, project } = props;
    const { id } = useParams();
    const [ isEdit, setIsEdit ] = useState( false );

    useEffect( () => {
        fetchProject( id );
        setIsEdit( true );
    }, [] );

    const itemsValue = project && project.length === 1 && project.map( project => ( {
        name: project.attributes.name,
        color: project.attributes.color,
        id: project.id
    } ) );

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( 'project.edit.title' )} />
            <HeaderTitle title={getFormattedMessage( 'project.edit.title' )} to='/app/collections' />
            {project.length && <ProjectForm singleProject={itemsValue} id={id} isEdit={isEdit} />}
        </MasterLayout>
    );
}

const mapStateToProps = ( state ) => {
    const { project } = state;
    return { project }
};

export default connect( mapStateToProps, { fetchProject } )( EditProject );
