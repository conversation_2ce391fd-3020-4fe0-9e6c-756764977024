import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import AsideDefault from './sidebar/asideDefault';
import Header from './header/Header';
import Footer from './footer/Footer';
import AsideTopSubMenuItem from './sidebar/asideTopSubMenuItem';
import { Tokens } from '../constants';
import asideConfig from '../config/asideConfig';
import { environment } from '../config/environment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';
import { fetchConfig } from '../store/action/configAction';
import { getFormattedMessage } from '../shared/sharedMethod';

const MasterLayout = (props) => {
    const { children, frontSettings, allConfigData, fetchConfig, config } = props;
    const [isResponsiveMenu, setIsResponsiveMenu] = useState(false);
    const [isMenuCollapse, setIsMenuCollapse] = useState(false);
    const [showNotification, setShowNotification] = useState(true)
    const [isHideNotificationClicked, setIsHideNotificationClicked] = useState(false)
    const newRoutes = prepareRoutes(frontSettings);
    const token = localStorage.getItem(Tokens.ADMIN);
    const getAdmin = localStorage.getItem('isAdmin');
    const [isAdmin, setIsAdmin] = useState(false);
    const user_role = localStorage.getItem(Tokens.USER_ROLE)

    useEffect(() => {
        if (token) {
            fetchConfig();
        }
        if (!token) {
            window.location.href = environment.URL + '#' + '/login';
        }
    }, []);

    const onAdminClick = () => {
        setIsAdmin(!isAdmin)
        localStorage.setItem("isAdmin", !isAdmin)
    };

    useEffect(() => {
        if ((getAdmin === 'true') && (token) && user_role === '1') {
            setIsAdmin(true)
            localStorage.setItem("isAdmin", getAdmin)
            window.location.href = '#/app/admin/dashboard';
        } else {
            setIsAdmin(false)

        }
    }, [isAdmin])

    const menuClick = () => {
        setIsResponsiveMenu(!isResponsiveMenu);
    };

    const menuIconClick = () => {
        setIsMenuCollapse(!isMenuCollapse)
    };

    useEffect(() => {
        frontSettings && (frontSettings?.plan_expire_notification === 1)
            ? setShowNotification(true) : null

    }, [frontSettings])

    useEffect(() => {
        const hideNoti = setTimeout(() => {
            onCLickHideNotification()
        }, 6000)

        return () => clearTimeout(hideNoti)
    }, [])

    useEffect(() => {
        const dnoneNoti = setTimeout(() => {
            setIsHideNotificationClicked(true)
        }, 8000)

        return () => clearTimeout(dnoneNoti)
    }, [])

    const onCLickHideNotification = () => {
        setShowNotification(false)
    }

    return (
        <div className='d-flex flex-row flex-column-fluid'>
            <AsideDefault isAdmin={isAdmin} asideConfig={newRoutes} frontSettings={frontSettings} isResponsiveMenu={isResponsiveMenu}
                menuClick={menuClick} menuIconClick={menuIconClick} isMenuCollapse={isMenuCollapse} />
            <div className={`${isMenuCollapse === true ? 'wrapper-res' : 'wrapper'} d-flex flex-column flex-row-fluid`}>
                <div className='d-flex align-items-stretch justify-content-between home-header header'>
                    <div className='container-fluid d-flex align-items-stretch justify-content-xxl-between flex-grow-1'>
                        <button type='button' className='btn d-flex align-items-center form-label d-xl-none px-0' title='Show aside menu'
                            onClick={menuClick}>
                            <FontAwesomeIcon icon={faBars} className='fs-1 form-label' />
                        </button>
                        <AsideTopSubMenuItem isMenuCollapse={isMenuCollapse} frontSettings={frontSettings} asideConfig={asideConfig} />
                        <Header newRoutes={newRoutes} onAdminClick={onAdminClick} frontSettings={frontSettings} />

                    </div>
                </div>
                <div className='content d-flex flex-column flex-column-fluid pt-6'>
                    <div className='d-flex flex-column-fluid'>
                        <div className='container-fluid'>
                            {
                                frontSettings?.plan_expired === true
                                    ?
                                    <div className={`text-white bg-danger my-3 p-3 text-start fs-5 shadow-md w-100 overflow-auto rounded ${showNotification === false && "expire-notification"} ${isHideNotificationClicked === true && "d-none"}`}>
                                        &#128533; {getFormattedMessage("plan.expired.continue.noti")}
                                    </div>
                                    :
                                    frontSettings?.plan_expire_notification === 1
                                    &&
                                    <div className={`badge ${frontSettings?.remaining_days_of_subscription <= 1 ? "bg-danger" : "bg-warning"} my-3 px-5 w-100 py-4 text-white text-start fs-5 shadow-md ${showNotification === false && "expire-notification"} ${isHideNotificationClicked === true && "d-none"}`}>
                                        <p className='p-0 m-0'>
                                            &#128533; {getFormattedMessage("plan.about.expire.noti")} {frontSettings.remaining_days_of_subscription || 0} {getFormattedMessage("days.title")}.
                                        </p>
                                    </div>
                            }
                            {
                                frontSettings && frontSettings?.announcement_enabled === '1' || frontSettings?.announcement_enabled === 1 ?
                                    <div className={`my-3 p-3 text-start fs-5 shadow-md w-100 overflow-auto rounded`} style={{ color: frontSettings?.user_text_color, background: frontSettings?.user_bg_color }}>

                                        {frontSettings?.user_announcement_content}

                                    </div>
                                    : ''
                            }
                            {children}
                        </div>
                    </div>
                </div>
                <div className='container-fluid'>
                    <Footer allConfigData={allConfigData} config={config} frontSetting={frontSettings} />
                </div>
            </div>
        </div>
    )
};

const getRouteWithSubMenu = (route) => {
    const subRoutes = route.subMenu ? route.subMenu.filter((item) => item.permission === '') : null
    const newSubRoutes = subRoutes ? { ...route, newRoute: subRoutes } : route
    return newSubRoutes
}

const prepareRoutes = (frontSettings) => {
    let filterRoutes = [];
    asideConfig.forEach((route) => {
        const permissionsRoute = getRouteWithSubMenu(route)
        if ((route.permission === '' || permissionsRoute.newRoute?.length) && (permissionsRoute.isAdmin === false)) {
            if (!route.to.includes('digital-business-cards')) {
                filterRoutes.push(permissionsRoute)
            } else {
                if (frontSettings.allow_create_business_card !== false) {
                    filterRoutes.push(permissionsRoute)
                }
            }
        }
    });
    return filterRoutes;
};

const mapStateToProps = (state) => {
    const newPermissions = [];
    const { permissions, settings, allConfigData, frontSettings, config } = state;

    if (permissions) {
        permissions.forEach((permission) =>
            newPermissions.push(permission.attributes.name)
        )
    }
    return { settings, allConfigData, frontSettings, config };
};

export default connect(mapStateToProps, { fetchConfig })(MasterLayout);
