import React from 'react'
import { Link } from 'react-router-dom'
import { typeOptions } from '../../../constants';
import { formatAmount, getFormattedMessage } from '../../../shared/sharedMethod';

const MonthlyPlanTab = (props) => {

    const { plans, isDefault } = props;

    const qrTypes = plans?.map((d) => d.qr_code_types)

    const selectedType = qrTypes.map((qrt) => {
        if (qrt.length < 2) {
            return [typeOptions.filter((to) => to.id === parseInt(qrt))[0]?.name]
        } else {
            let qr = qrt.split(",")
            const select = qr.map((qrd, i) => {
                return typeOptions.filter((to) => to.id === parseInt(qrd))[0]?.name
            })
            return select
        }
    })

    const finalplan = plans?.map((d, i) => {
        return {
            ...d,
            plan_features: {
                ...d.plan_features,
                qr_code_types: selectedType[i]
            }
        }
    })

    return (
        <div className='row justify-content-center align-items-stretch'>
            {
                finalplan.length > 0 ? finalplan.map((plan) => {
                    return (
                        <div className='col-xl-4 col-lg-5 col-md-5 col-sm-6 d-block h-auto my-3' key={plan.plan_id + 1}>
                            <div className="card pricing-card bg-light p-5 shadow-lg mb-8 h-100" >
                                <h1>{plan.name}</h1>
                                <h1 className="pricing-amount">
                                    {plan?.currency_symbol} {formatAmount(plan.price)}
                                </h1>
                                <div className="card-body p-3">
                                    <div className="pricing-description text-start">
                                        <div className="d-flex justify-content-between mb-4">
                                            <div className="fw-normal col-9">
                                                <p className='m-0'>{getFormattedMessage('plans.qr.codes.types')} :</p>
                                                <div className='d-flex flex-wrap'>
                                                    {plan?.plan_features?.qr_code_types.map((items, index) => { return <span className='me-1' key={index + 1} >{getFormattedMessage(items)}{index !== plan?.plan_features?.qr_code_types.length - 1 && ','}</span> })}
                                                </div>
                                            </div>
                                            <svg className="svg-inline--fa fa-circle-check fs-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle-check" role="img" xmlns="[http://www.w3.org/2000/svg](http://www.w3.org/2000/svg)" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM371.8 211.8C382.7 200.9 382.7 183.1 371.8 172.2C360.9 161.3 343.1 161.3 332.2 172.2L224 280.4L179.8 236.2C168.9 225.3 151.1 225.3 140.2 236.2C129.3 247.1 129.3 264.9 140.2 275.8L204.2 339.8C215.1 350.7 232.9 350.7 243.8 339.8L371.8 211.8z"></path></svg>
                                        </div>
                                        <div className="d-flex justify-content-between mb-4">
                                            <p className="fw-normal">{getFormattedMessage('globally.input.qr-codes-imit.lable')}  : {plan.qr_code_limit}</p>
                                            <svg className="svg-inline--fa fa-circle-check fs-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle-check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM371.8 211.8C382.7 200.9 382.7 183.1 371.8 172.2C360.9 161.3 343.1 161.3 332.2 172.2L224 280.4L179.8 236.2C168.9 225.3 151.1 225.3 140.2 236.2C129.3 247.1 129.3 264.9 140.2 275.8L204.2 339.8C215.1 350.7 232.9 350.7 243.8 339.8L371.8 211.8z"></path></svg>
                                        </div>
                                        <div className="d-flex justify-content-between mb-4">
                                            <p className="fw-normal">{getFormattedMessage('globally.input.links-imit.lable')} : {plan.links_limit}</p>
                                            <svg className="svg-inline--fa fa-circle-check fs-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle-check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM371.8 211.8C382.7 200.9 382.7 183.1 371.8 172.2C360.9 161.3 343.1 161.3 332.2 172.2L224 280.4L179.8 236.2C168.9 225.3 151.1 225.3 140.2 236.2C129.3 247.1 129.3 264.9 140.2 275.8L204.2 339.8C215.1 350.7 232.9 350.7 243.8 339.8L371.8 211.8z"></path></svg>
                                        </div>
                                        <div className="d-flex justify-content-between mb-4">
                                            <p className="fw-normal">{getFormattedMessage("globally.input.projects-imit.lable")} : {plan.projects_limit}</p>
                                            <svg className="svg-inline--fa fa-circle-check fs-2" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle-check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM371.8 211.8C382.7 200.9 382.7 183.1 371.8 172.2C360.9 161.3 343.1 161.3 332.2 172.2L224 280.4L179.8 236.2C168.9 225.3 151.1 225.3 140.2 236.2C129.3 247.1 129.3 264.9 140.2 275.8L204.2 339.8C215.1 350.7 232.9 350.7 243.8 339.8L371.8 211.8z"></path></svg>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-0">
                                    {
                                        isDefault === 1 ?
                                            (plan.is_active !== null) && (plan.plan_id === plan.is_active) ?
                                                <p className="btn btn-success rounded-pill mx-auto my-0">{getFormattedMessage("globally.currently.active.btn.title")}</p>
                                                : <Link className="btn btn-primary rounded-pill mx-auto my-0" to={`/app/selected-plan/${plan.plan_id}`}>{getFormattedMessage("globally.switch.plan.btn.title")}</Link>
                                            : <Link className="btn btn-primary rounded-pill mx-auto my-0" to={`/app/selected-plan/${plan.plan_id}`}>{getFormattedMessage("globally.choose.plan.btn.title")}</Link>
                                    }
                                </div>
                            </div>
                        </div>
                    )
                }) :  <div className="text-center fs-3 my-5 nodata">
                   {getFormattedMessage("globally.no.plan.available.title")}
                </div>
            }
        </div >
    )
}

export default MonthlyPlanTab
