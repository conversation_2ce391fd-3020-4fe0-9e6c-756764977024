import React, { useState, useEffect } from 'react'
import MasterLayout from '../../MasterLayout'
import { connect, useDispatch } from 'react-redux';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar'
import TabTitle from '../../../shared/tab-title/TabTitle'
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod'
import { Tab, Tabs } from 'react-bootstrap-v5'
import MonthlyPlanTab from './MonthlyPlanTab';
import YearlyPlanTab from './YearlyPlanTab';
import { fetchSubscribedAllPlan, fetchUserPlan } from '../../../store/action/plansAction';
import { Filters, toastType } from '../../../constants';
import { useNavigate } from 'react-router';
import { addToast } from '../../../store/action/toastAction';

const PlanTab = (props) => {
    const { plans, fetchUserPlan, subsctibedAllPlans, fetchSubscribedAllPlan } = props;
    const [monthPlan, setMonthPlan] = useState([])
    const [yearPlan, setYearPlan] = useState([])
    const [key, setKey] = useState('Monthly');
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const pending = placeholderText("globally.pending.label")
    useEffect(() => {
        fetchSubscribedAllPlan(Filters.OBJ)
        fetchUserPlan()
    }, [])

    const itemsValue = subsctibedAllPlans.length >= 0 && subsctibedAllPlans.map(subscribePlan => (
        subscribePlan?.transaction !== null ?
            subscribePlan?.transaction?.payment_mode === 4 ?
                subscribePlan?.transaction?.is_manual_payment === 0 ?
                    placeholderText("globally.pending.label")
                    :
                    subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
                :
                subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
            : subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
    ));

    useEffect(() => {
        if (subsctibedAllPlans.length >= 1) {
            if (itemsValue && itemsValue[0] === pending) {
                navigate('/app/manage-subscription');
                dispatch(addToast({ text: getFormattedMessage('payment.pending.error.message'), type: toastType.ERROR }));
            }
        }
    }, [subsctibedAllPlans])


    useEffect(() => {
        if (plans['0']) {
            setMonthPlan([])
            setYearPlan([])

            plans['0']?.length > 0 && plans['0']?.map(plan => {
                if (plan.type === 1) {
                    setMonthPlan(monthPlan => [...monthPlan, plan])
                } else {
                    setYearPlan(yearPlan => [...yearPlan, plan])
                }
            })
        }
    }, [plans])


    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('upgrade-plan.title')} />
                <div className='card'>
                    <div className='card-body'>
                        <div>
                            <Tabs defaultActiveKey='Monthly' variant="pills" id='uncontrolled-tab-example' onSelect={(k) => setKey(k)}
                                className='mt-7 mb-5 justify-content-center'>
                                <Tab eventKey='Monthly' title={getFormattedMessage('globally.input.monthly.lable')}
                                    tabClassName='position-relative  mb-3 me-5'>
                                    <div className='w-100 mx-auto'>
                                        {key === 'Monthly' && <MonthlyPlanTab plans={monthPlan} isDefault={plans && plans.user_has_plan} />}
                                    </div>
                                </Tab>
                                <Tab eventKey='Yearly' title={getFormattedMessage("globally.input.Yearly.lable")}
                                    tabClassName='position-relative mb-3'>
                                    <div className='w-100 mx-auto'>
                                        {key === 'Yearly' && <YearlyPlanTab plans={yearPlan} isDefault={plans && plans.user_has_plan} />}
                                    </div>
                                </Tab>
                            </Tabs>
                        </div>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
}

const mapStateToProps = (state) => {
    const { plans, totalRecord, isLoading, allConfigData, subsctibedAllPlans } = state;
    return { plans, totalRecord, isLoading, allConfigData, subsctibedAllPlans }
};
export default connect(mapStateToProps, { fetchUserPlan, fetchSubscribedAllPlan })(PlanTab);
