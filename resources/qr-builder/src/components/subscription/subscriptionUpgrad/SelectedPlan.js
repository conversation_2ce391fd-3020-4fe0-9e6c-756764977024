import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import MasterLayout from '../../MasterLayout';
import { fetchSelectedPlan, purchasePlan } from "../../../store/action/plansAction";
import TabTitle from "../../../shared/tab-title/TabTitle";
import { formatAmount, getFormattedMessage, getFormattedOptions, placeholderText } from "../../../shared/sharedMethod"
import { DefaultpaymentMethodOptions, paymentMethodOptions } from "../../../constants";
import ReactSelect from "../../../shared/select/reactSelect";
import { purchasePlanPaypal, purchasePlanStripe, purchasePlanRazorepay } from '../../../store/action/puchasePlanAction';
import { loadStripe } from '@stripe/stripe-js';
import { setLoading } from '../../../store/action/loadingAction';

const SelectedPlan = (props) => {
    const {
        fetchSelectedPlan,
        purchasePlan,
        purchasePlanStripe,
        purchasePlanPaypal,
        purchasePlanRazorepay,
        paymentSessionId,
        setLoading,
        selectedPlan,
        frontSettings
    } = props;
    const { id } = useParams();
    const navigate = useNavigate()
    const [paymentMethod, setPaymentMethodValue] = useState({ value: 4, label: placeholderText("globally.manually.payment.title") })
    const payable = selectedPlan[0]?.new_plan[1]?.payable_amount
    const [couponCodeValue, setCouponCodeValue] = useState({
        coupon_code: '',
        payable_amount: ''
    })

    useEffect(() => {
        setCouponCodeValue(inputs => ({ ...inputs, payable_amount: payable }));
    }, [payable])

    useEffect(() => {
        fetchSelectedPlan({ plan_id: id });
    }, []);

    useEffect(() => {
        if (paymentSessionId.stripe_key) {
            callToStripe(paymentSessionId.stripe_key)
        }
    }, [paymentSessionId])

    const removeCoupon = (e) => {
        e.preventDefault();
        fetchSelectedPlan({ plan_id: id });
        setCouponCodeValue(input => ({ ...input, coupon_code: "" }))
    }

    const callToStripe = async (pubKey) => {
        const stripe = await loadStripe(pubKey);
        const { err } = await stripe.redirectToCheckout({ sessionId: paymentSessionId.sessionId })
        setLoading(false)
    }

    const current_plan = selectedPlan[0]?.current_plan;
    const new_plan = selectedPlan[0]?.new_plan

    const paymentDefaultTypeOptions = getFormattedOptions(DefaultpaymentMethodOptions)
    const paymentTypeOptions = getFormattedOptions(paymentMethodOptions)
    const finalPaymentOptions = paymentTypeOptions.map((d, index) => {
        if (selectedPlan[0]?.payment_enable) {
            for (let key in selectedPlan[0]?.payment_enable) {
                if (selectedPlan[0]?.payment_enable[d.name.toLowerCase()] === 1) {
                    if (d.id === paymentDefaultTypeOptions[index].id) {
                        return paymentDefaultTypeOptions[index]
                    }
                }
            }
        }
    }).filter((d) => d !== undefined)

    const onCodeInput = (e) => {
        e.preventDefault();
        setCouponCodeValue(inputs => ({ ...inputs, [e.target.name]: e.target.value.trim() }))
    };

    const onClickCouponCode = () => {
        fetchSelectedPlan({ plan_id: id, coupon_code: couponCodeValue.coupon_code });
    }

    finalPaymentOptions.push({ id: 4, name: placeholderText("globally.manually.payment.title") })

    const paymentTypeDefaultValue = finalPaymentOptions.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const onPaymentMethodChange = (obj) => {
        setPaymentMethodValue(obj);
    }

    const onClickPayment = async (paymentMethodId, id) => {
        const data = {
            "plan_id": parseInt(id),
            "payment_mode": parseInt(paymentMethodId.value),
            "coupon_code": couponCodeValue.coupon_code
        }
        if (paymentMethod.value === 4) {
            purchasePlan(data)
            navigate("/app/manage-subscription")
        } else if (paymentMethod.value === 1) {
            purchasePlanStripe({ "plan_id": parseInt(id), "coupon_code": couponCodeValue.coupon_code })
        } else if (paymentMethod.value === 2) {
            purchasePlanPaypal({ "plan_id": parseInt(id), "coupon_code": couponCodeValue.coupon_code })
        } else if (paymentMethod.value === 3) {
            const script = document.createElement("script");
            script.src = "https://checkout.razorpay.com/v1/checkout.js";
            script.async = true;
            document.body.appendChild(script);
            purchasePlanRazorepay({ "plan_id": parseInt(id), "coupon_code": couponCodeValue.coupon_code }, navigate)
        }
    }

    return (
        <>
            <MasterLayout>
                <TabTitle title={placeholderText('account.account.title')} />
                <div className="card">
                    <div className="card-body">
                        <div className="row">
                            {frontSettings?.plan_expired !== true && current_plan &&
                                <div className="col-md-6">
                                    <div className="card p-5 me-2 shadow rounded">
                                        <div className="card-header py-0 px-0">
                                            <h3 className="align-items-start flex-column p-sm-5 p-0">
                                                <span
                                                    className="fw-bolder text-primary fs-1 mb-1 me-0">{getFormattedMessage('user.current-plan.title')}</span>
                                            </h3>
                                        </div>
                                        <div className="px-4">
                                            <div className="d-flex align-items-center py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.name.title')}</h4>
                                                <span
                                                    className="fs-5 w-50 text-muted fw-bold mt-1">{current_plan?.[0].name}</span>
                                            </div>
                                            <div className="d-flex align-items-center  py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-3 fw-bolder">{getFormattedMessage("globally.plan.price.title")}</h4>
                                                <span className="fs-5 text-muted fw-bold mt-1">
                                                    <span className="mb-2 me-1">
                                                        {current_plan[0]?.currency?.symbol}
                                                    </span>
                                                    {formatAmount(current_plan?.[0].price)}
                                                </span>
                                            </div>
                                            <div className="d-flex align-items-center  py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.start.date.title')}</h4>
                                                <span
                                                    className="fs-5 w-50 text-muted fw-bold mt-1">{current_plan?.[1]?.starts_at}</span>
                                            </div>
                                            <div className="d-flex align-items-center  py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.end.date.title')}</h4>
                                                <span
                                                    className="fs-5 w-50 text-muted fw-bold mt-1">{current_plan?.[1]?.expires_on}</span>
                                            </div>
                                            <div className="d-flex align-items-center  py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.used.days.title')}</h4>
                                                <span
                                                    className="fs-5 w-50 text-muted fw-bold mt-1">{current_plan?.[1]?.used_days}</span>
                                            </div>
                                            <div className="d-flex align-items-center  py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.remaining.days.title')}</h4>
                                                <span
                                                    className="fs-5 w-50 text-muted fw-bold mt-1"> {current_plan?.[1]?.remaining_days}</span>
                                            </div>
                                            <div className="d-flex align-items-center  py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.used.balance.title')}</h4>
                                                <span className="fs-5 w-50 text-muted fw-bold mt-1">
                                                    <span className="mb-2 me-1">
                                                        {current_plan[0]?.currency?.symbol}
                                                    </span>
                                                    {formatAmount(current_plan?.[1]?.used_balance)}
                                                </span>
                                            </div>
                                            <div className="d-flex align-items-center  py-2">
                                                <h4 className="fs-5 w-50 mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.remaining.balance.title')}</h4>
                                                <span className="fs-5 w-50 text-muted fw-bold mt-1">
                                                    <span className="mb-2 me-1">{current_plan[0]?.currency?.symbol}</span>
                                                    {formatAmount(current_plan?.[1]?.remaining_balance)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>}
                            <input id="amountToPay" name="amount_to_pay" type="hidden" defaultValue={10} />
                            <input id="planEndDate" name="plan_end_date" type="hidden" defaultValue="27th Jan, 2023" />
                            {new_plan && <div className={`${frontSettings?.plan_expired !== true ? 'col-md-6' : 'col-12'} `}>
                                <div className="card h-100 p-5 me-2 shadow rounded">
                                    <div className="card-header py-0 px-0">
                                        <h3 className="align-items-start flex-column p-sm-5 p-0">
                                            <span
                                                className="fw-bolder text-primary fs-1 mb-1 me-0">{getFormattedMessage('user.new-plan.title')}</span>
                                        </h3>
                                    </div>
                                    <div className="px-5 pb-5">
                                        <div className="d-flex align-items-center py-2">
                                            <h4 className="fs-5 w-50 plan-data mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.name.title')}</h4>
                                            <span
                                                className="fs-5 w-50 text-muted fw-bold mt-1">{new_plan?.[0].name}</span>
                                        </div>
                                        <div className="d-flex align-items-center py-2">
                                            <h4 className="fs-5 w-50 plan-data mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.price.title')}</h4>
                                            <span className="fs-5 w-50 text-muted fw-bold mt-1">
                                                <span className="mb-2 me-1">
                                                    {new_plan[1]?.currency_symbol}
                                                </span>
                                                {formatAmount(new_plan?.[0]?.price)}
                                            </span>
                                        </div>
                                        <div className="d-flex align-items-center  py-2">
                                            <h4 className="fs-5 w-50 plan-data mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.start.date.title')}</h4>
                                            <span
                                                className="fs-5 w-50 text-muted fw-bold mt-1">{new_plan?.[1]?.starts_at}</span>
                                        </div>
                                        <div className="d-flex align-items-center  py-2">
                                            <h4 className="fs-5 w-50 plan-data mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.end.date.title')}</h4>
                                            <span
                                                className="fs-5 w-50 text-muted fw-bold mt-1">{new_plan?.[1]?.expires_on}</span>
                                        </div>
                                        <div className="d-flex align-items-center  py-2">
                                            <h4 className="fs-5 w-50 plan-data mb-0 me-5 fw-bolder">{getFormattedMessage('globally.plan.total.days.title')}</h4>
                                            <span
                                                className="fs-5 w-50 text-muted fw-bold mt-1">{new_plan?.[1]?.total_days}</span>
                                        </div>
                                        <div className="d-flex align-items-center  py-2">
                                            <h4 className="fs-5 w-50 plan-data mb-0 me-5 fw-bolder">{getFormattedMessage('new.plan.remaining.prev.balance.title')}</h4>
                                            <span className="fs-5 w-50 text-muted fw-bold mt-1">
                                                {new_plan[1]?.currency_symbol} {formatAmount(new_plan?.[1]?.remaining_balance_of_prev_plan)}
                                            </span>
                                        </div>
                                        <div className="d-flex align-items-center  py-2">
                                            <h4 className="fs-5 w-50 plan-data mb-0 me-5 fw-bolder">{getFormattedMessage('new.plan.payable.amount.title')}</h4>
                                            <span className="fs-5 w-50 text-muted fw-bold mt-1">
                                                {new_plan[1]?.currency_symbol} {formatAmount(new_plan?.[1]?.payable_amount)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>}
                        </div>
                        {new_plan && new_plan?.[1]?.payable_amount !== 0 &&
                            <div className='d-flex justify-content-end my-3 '>
                                <div className='col-lg-4 col-md-6 col-12 ms-auto card p-5 form-label shadow rounded'>
                                    <div className={`d-flex ${selectedPlan[0]?.discount?.coupon_code?.code && "mb-1"} justify-content-between align-items-center`}>
                                        <div className='label-text'>{getFormattedMessage("globally.input.code.label")}:</div>
                                        <div className={`${selectedPlan[0]?.discount?.coupon_code?.code ? "text-danger" : ""}`}>
                                            {selectedPlan[0]?.discount?.coupon_code?.code ? <span className='badge bg-light-success text-dark d-flex align-items-center'>
                                                <div className='me-3'>{selectedPlan[0]?.discount?.coupon_code?.code}</div>
                                                <div><button className='btn badge bg-light-success text-dark p-0' onClick={(e) => removeCoupon(e)}>x</button></div>
                                            </span> : 'N/A'}
                                            {/* {selectedPlan[0]?.discount?.coupon_code?.code || "N/A"} */}
                                        </div>
                                    </div>
                                    <div className='d-flex justify-content-between align-items-center'>
                                        <div className='label-text'>{getFormattedMessage('plan.selected.subtotal.price.titel')}:</div>
                                        <div>{new_plan && new_plan[1]?.currency_symbol} {new_plan && formatAmount(new_plan?.[0]?.price) || new_plan && new_plan[1]?.currency_symbol + "0.00"}</div>
                                    </div>
                                    <div className='d-flex justify-content-between align-items-center'>
                                        <div className='label-text'>{getFormattedMessage('new.plan.remaining.prev.balance.title')}:</div>
                                        <div>
                                            {new_plan && new_plan[1]?.currency_symbol} {new_plan && formatAmount(new_plan?.[1]?.remaining_balance_of_prev_plan) || "0.00"}
                                        </div>
                                    </div>
                                    <div className='d-flex justify-content-between align-items-center'>
                                        <div className='label-text'>{getFormattedMessage('plan.selected.lass.amount.titel')}:</div>
                                        <div className={`${selectedPlan[0]?.discount?.coupon_code?.discount ? "text-primary" : ""}`}>{
                                            selectedPlan[0]?.discount?.coupon_code?.discount
                                                ?
                                                selectedPlan[0]?.discount?.coupon_code?.type === 1 ?
                                                    new_plan &&
                                                    new_plan[1]?.currency_symbol + (((new_plan?.[0]?.price * selectedPlan[0]?.discount?.coupon_code?.discount?.toFixed(2)) / 100).toFixed(2))
                                                    :
                                                    new_plan && new_plan[1]?.currency_symbol + selectedPlan[0]?.discount?.coupon_code?.discount
                                                :
                                                new_plan && new_plan[1]?.currency_symbol + "0.00"
                                        }</div>
                                    </div>
                                    <hr />
                                    <div className='d-flex justify-content-between align-items-center'>
                                        <div className='label-text'>{getFormattedMessage("new.plan.payable.amount.title")}:</div>
                                        <div className='text-success'>
                                            {new_plan && new_plan[1]?.currency_symbol} {new_plan && formatAmount(new_plan?.[1]?.payable_amount) || "0.00"}
                                        </div>
                                    </div>
                                </div>
                            </div>}

                        <div className="row justify-content-center d-flex align-items-center">
                            <div className="col-12 justify-content-center row mt-5 ">
                                {(new_plan && new_plan?.[1]?.payable_amount !== '0.00') &&
                                    <div className='col-lg-6 col-12 row justify-content-center text-lg-start text-center mb-3'>
                                        <div className={'col-md-6 col-12'}>
                                            <input autoComplete="off" type='text' name='coupon_code' className='form-control'
                                                placeholder={placeholderText("globally.input.coupon-code.placeholder.label")}
                                                onChange={(e) => onCodeInput(e)}
                                                disabled={selectedPlan && selectedPlan[0]?.discount?.coupon_code?.code ? true : false}
                                                value={couponCodeValue.coupon_code} />
                                        </div>
                                        <div className='col-md-6 col-12 mt-md-0 mt-2'>
                                            <button className={"btn btn-primary w-100"}
                                                disabled={selectedPlan && selectedPlan[0]?.discount?.coupon_code?.code ? true : couponCodeValue.coupon_code.length <= 0 ? true : false}
                                                onClick={() => {
                                                    onClickCouponCode(paymentMethod, id)
                                                }}>{getFormattedMessage('plan.apply.coupon.code.btn.titel')}</button>
                                        </div>
                                    </div>
                                }
                                <div className='col-lg-6 col-12 row justify-content-center text-lg-start text-center mb-3'>
                                    {((parseInt(new_plan?.[1]?.payable_amount) !== 0) || (parseInt(new_plan?.[1]?.payable_amount) !== 0.00)) &&
                                        <div className='col-md-6 col-12 cursor-pointer'>
                                            <ReactSelect
                                                className={"cursor-pointer"}
                                                isRequired
                                                defaultValue={paymentTypeDefaultValue[paymentTypeDefaultValue.length - 1]}
                                                multiLanguageOption={finalPaymentOptions}
                                                onChange={onPaymentMethodChange}
                                            />
                                        </div>
                                    }
                                    {((parseInt(new_plan?.[1]?.payable_amount) === 0) || (parseInt(new_plan?.[1]?.payable_amount) === 0.00)) ?
                                        <div className={"col-md-6 col-12 "}>
                                            <button className={"btn btn-primary"}
                                                onClick={() => {
                                                    onClickPayment(paymentMethod, id)
                                                }}>{getFormattedMessage('account.pay-switch.title')}</button>
                                        </div>
                                        :
                                        <div className={"col-md-6 col-12 mt-md-0 mt-2"}>
                                            <button className={"btn btn-primary w-100"}
                                                onClick={() => {
                                                    onClickPayment(paymentMethod, id)
                                                }}>{getFormattedMessage('account.payment.title')}</button>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </MasterLayout>
        </>
    )
}

const mapStateToProps = (state) => {
    const { paymentSessionId, selectedPlan, frontSettings } = state;
    return { paymentSessionId, selectedPlan, frontSettings }
};

export default connect(mapStateToProps, {
    fetchSelectedPlan,
    purchasePlan,
    purchasePlanPaypal,
    purchasePlanStripe,
    purchasePlanRazorepay,
    setLoading
})(SelectedPlan);
