import React, { useState, useEffect } from 'react'
import MasterLayout from '../MasterLayout'
import { connect, useDispatch } from 'react-redux';
import TopProgressBar from '../../shared/components/loaders/TopProgressBar'
import { fetchUserPlan } from '../../store/action/plansAction';
import { Badge, Card, Table } from 'react-bootstrap';
import { toastType, typeOptions } from '../../constants';
import { formatAmount, getFormattedDate, getFormattedMessage, placeholderText } from '../../shared/sharedMethod';
import { useNavigate } from 'react-router-dom';
import ManageSubscriptionList from './ManageSubscriptionList';
import TabTitle from "../../shared/tab-title/TabTitle"
import { Button } from 'react-bootstrap-v5';
import { addToast } from '../../store/action/toastAction';

const ManageSubscription = (props) => {
    const { plans, fetchUserPlan, frontSettings, subsctibedAllPlans } = props;

    const [activePlan, setActivePlan] = useState({})
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const pending = placeholderText("globally.pending.label")
    const [variants, setVariants] = useState(["success", "primary", "dark", "secondary", "info", "warning", "danger"])
    const [isPlan, setIsPlan] = useState(JSON.parse(localStorage.getItem('plan_expired')))

    useEffect(() => {
        if (isPlan) {
            dispatch(addToast({ text: getFormattedMessage("plan.expired.continue.noti"), type: toastType.ERROR }))
        }
    }, [location.href])

    useEffect(() => {
        fetchUserPlan()
    }, [])

    const itemsValue = subsctibedAllPlans.length >= 0 && subsctibedAllPlans.map(subscribePlan => (
        subscribePlan?.transaction !== null ?
            subscribePlan?.transaction?.payment_mode === 4 ?
                subscribePlan?.transaction?.is_manual_payment === 0 ?
                    placeholderText("globally.pending.label")
                    :
                    subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
                :
                subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
            : subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
    ));

    useEffect(() => {

        if (plans.length <= 0 || plans.length === undefined) {
            plans["0"]?.map((d) => {
                if (d.is_active === d.plan_id) {
                    const qrTypeName = d.qr_code_types.split(",").map((qrd) => {
                        return typeOptions.filter((qrtd) => {
                            if (parseInt(qrd) === qrtd.id) {
                                return qrtd.name
                            }
                        })[0].name
                    })
                    setActivePlan({
                        ...d,
                        qr_code_types: qrTypeName
                    })
                }
            })
        }
    }, [plans])




    const upgradePlan = (e) => {
        e.preventDefault();
        if (itemsValue && itemsValue[0] === pending) {
            dispatch(addToast({ text: getFormattedMessage('payment.pending.error.message'), type: toastType.ERROR }));
        } else {
            navigate('/app/upgrade-plan');
        }
    }

    return (
        <>
            <MasterLayout>
                <TabTitle title={placeholderText('header.manage.subscription.label')} />
                <TopProgressBar />
                <Card className='mb-15  '>
                    <Card.Body>
                        {
                            activePlan.name ?
                                <>
                                    <div className='d-flex justify-content-between align-items-center'>
                                        <div>
                                            <Card.Title className='fs-3'>
                                                {activePlan.name}
                                            </Card.Title>
                                            <Card.Subtitle className='text-success fs-5 mb-5'>
                                                {getFormattedMessage("globally.active-till.label")} {getFormattedDate(plans?.ends_at)}
                                            </Card.Subtitle>
                                        </div>
                                        <div>
                                            <Button className="btn btn-primary" onClick={(e) => upgradePlan(e)}>{getFormattedMessage("upgrade-plan.title")}</Button>
                                        </div>
                                    </div>
                                    <Card.Subtitle className='mt-5 fs-5'>
                                        {activePlan.currency_symbol} {formatAmount(activePlan.price)} / {activePlan.type === 1 ? "Month" : "Year"}
                                    </Card.Subtitle>
                                    {plans?.remaining_trial_days ? <Card.Text className='text-muted fs-6'>
                                        {`${placeholderText('globally.input.trial-days.lable')}: ${plans?.remaining_trial_days ? plans?.remaining_trial_days + placeholderText("globally.day-Remaining.title") : "N/A"}`}
                                    </Card.Text> : ''}
                                    <Card.Text className='text-muted mt-2 fs-6'>
                                        {getFormattedMessage("react-data-table.subscribed-date.column.label")}: {getFormattedDate(plans?.starts_at)}
                                    </Card.Text>
                                    <div className='row'>
                                        <div className='col-12'>
                                            <Table striped bordered hover className='mb-0'>
                                                <tbody>
                                                    <tr>
                                                        <td className='ps-3 w-25'>
                                                            {getFormattedMessage('react-data-table.qr-code-types.label')}
                                                        </td>
                                                        <td className="px-3 ">
                                                            {
                                                                activePlan.qr_code_types.map((d, i) => {
                                                                    return <Badge key={i + 1} bg={frontSettings.plan_expired === true ? 'danger' : variants[Math.floor(Math.random() * variants.length)]} className={i === 0 ? "m-1 ms-0" : "m-1"}>
                                                                        {getFormattedMessage(d)}</Badge>
                                                                })
                                                            }
                                                        </td>
                                                    </tr>
                                                    <tr >
                                                        <td className="ps-3">
                                                            {getFormattedMessage('globally.input.qr-codes-imit.lable')}
                                                        </td>
                                                        <td className="px-3 ">
                                                            {activePlan.qr_code_limit}
                                                        </td>
                                                    </tr>
                                                    <tr >
                                                        <td className="ps-3 ">
                                                            {getFormattedMessage("globally.input.projects-imit.lable")}
                                                        </td>
                                                        <td className="px-3 ">
                                                            {activePlan.projects_limit}
                                                        </td>
                                                    </tr>
                                                    <tr >
                                                        <td className='ps-3 '>
                                                            {getFormattedMessage('globally.input.links-imit.lable')}
                                                        </td>
                                                        <td className="px-3 ">
                                                            {activePlan.links_limit}
                                                        </td>
                                                    </tr>
                                                    {/* <tr >
                                                        <td scope='row' className='ps-3 '>{getFormattedMessage("globally.input.order.lable")}</td>
                                                        <td className="px-3">
                                                            {activePlan.order}
                                                        </td>
                                                    </tr> */}
                                                </tbody>
                                            </Table>
                                        </div>
                                    </div>
                                </>
                                :
                                <div className='d-flex justify-content-between align-items-center'>
                                    <Card.Text className='text-dark'>
                                        {getFormattedMessage("globally.no.active.plan.available.title")}
                                    </Card.Text>
                                    <div>
                                        <Button className="btn btn-primary" onClick={(e) => upgradePlan(e)}>{getFormattedMessage("upgrade-plan.title")}</Button>

                                    </div>
                                </div>
                        }
                    </Card.Body>
                </Card >

                <ManageSubscriptionList />
            </MasterLayout >
        </>
    )
}

const mapStateToProps = (state) => {
    const { plans, totalRecord, isLoading, allConfigData, frontSettings, subsctibedAllPlans } = state;
    return { plans, totalRecord, isLoading, allConfigData, frontSettings, subsctibedAllPlans }
};
export default connect(mapStateToProps, { fetchUserPlan })(ManageSubscription);
