import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import ReactDataTable from '../../shared/table/ReactDataTable';
import { formatAmount, getFormattedDate, placeholderText } from '../../shared/sharedMethod';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { fetchSubscribedAllPlan } from '../../store/action/plansAction';
import { Filters } from '../../constants';

const ManageSubscriptionList = (props) => {
    const { totalRecord, isLoading, fetchSubscribedAllPlan, subsctibedAllPlans } = props;

    useEffect(() => {
        fetchSubscribedAllPlan(Filters.OBJ, true)
    }, [])

    const itemsValue = subsctibedAllPlans.length >= 0 && subsctibedAllPlans.map(subscribePlan => ({
        start_date: getFormattedDate(subscribePlan?.start_date, "d-m-y"),
        end_date: getFormattedDate(subscribePlan?.end_date, "d-m-y"),
        plan_name: subscribePlan ? subscribePlan?.plan?.name : "",
        plan_price: subscribePlan ? subscribePlan?.plan?.currency?.symbol + ' ' + formatAmount(subscribePlan?.plan?.price) : "N/A",
        status: subscribePlan && subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label'),
        amount: subscribePlan.payable_amount !== null ? subscribePlan?.plan?.currency?.symbol + ' ' + formatAmount(subscribePlan.payable_amount) : subscribePlan?.plan?.currency?.symbol + ' ' + formatAmount(subscribePlan.price_of_plan),

        planStatus: subscribePlan?.transaction !== null ?
            subscribePlan?.transaction?.payment_mode === 4 ?
                subscribePlan?.transaction?.is_manual_payment === 0 ?
                    placeholderText("globally.pending.label")
                    :
                    subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
                :
                subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')
            : subscribePlan?.status === false ? placeholderText("globally.deactive.label") : placeholderText('globally.active.label')

    }));

    const onChange = (filter) => {
        fetchSubscribedAllPlan(filter, true);
    };

    const columns = [
        {
            name: getFormattedMessage('globally.plan.name.title'),
            selector: row => row.plan_name,
            sortField: 'plan_name',
            sortable: false,
        },
        {
            name: getFormattedMessage('globally.plan.price.title'),
            sortField: 'price_of_plan',
            sortable: true,
            cell: row => {
                return <div>
                    <span>{row?.plan_price}</span>
                </div>
            }
        },
        {
            name: getFormattedMessage('new.plan.payable.amount.title'),
            sortField: 'payable_ammount',
            sortable: false,
            cell: row => {
                return <div>
                    <span>{row?.amount}</span>
                </div>
            }
        },
        {
            name: getFormattedMessage('react-data-table.subscribed-date.column.label'),
            selector: row => row.start_date,
            sortField: 'start_date',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div>{row.start_date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.expired-date.column.label'),
            selector: row => row.end_date,
            sortField: 'end_date',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div>{row.end_date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('dashboard.recentSales.status.label'),
            selector: row => row.status,
            sortField: 'status',
            sortable: false,
            cell: row => {
                return (
                    <span className={`badge ${row.planStatus === placeholderText('globally.active.label') ? "bg-light-success" : row.planStatus === placeholderText("globally.pending.label") ? 'bg-light-primary' : "bg-light-danger"}`}>
                        <div>{row.planStatus}</div>
                    </span>
                )
            }
        },
    ];

    return (
        <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
            totalRows={totalRecord} isLoading={isLoading} />
    )
};

const mapStateToProps = (state) => {
    const { project_admin, totalRecord, isLoading, allConfigData, subsctibedAllPlans } = state;
    return { project_admin, totalRecord, isLoading, allConfigData, subsctibedAllPlans }
};
export default connect(mapStateToProps, { fetchSubscribedAllPlan })(ManageSubscriptionList);
