import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Tokens } from '../constants';
import asideConfig from '../config/asideConfig';
import { environment } from '../config/environment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from 'react-router-dom';
import Footer from '../components/footer/Footer';
import Header from './Header';
import Home from '../components/Home/Home';
import HomePage from './frontend/HomePage';


const MasterLayout = (props) => {
    const { children, newPermissions, frontSetting, frontSettings, allConfigData } = props;
    const [isResponsiveMenu, setIsResponsiveMenu] = useState(false);
    const [isMenuCollapse, setIsMenuCollapse] = useState(false);
    const newRoutes = prepareRoutes();
    const token = localStorage.getItem(Tokens.ADMIN);
    const getAdmin = localStorage.getItem('isAdmin');
    const navigate = useNavigate()
    const [isAdmin, setIsAdmin] = useState(false);
    const user_role = localStorage.getItem(Tokens.USER_ROLE)


useEffect(() => {
    if (token) {
    }
    if (!token) {
        // window.location.href = environment.URL + '#' + '/login';
    }
}, []);


const onAdminClick = () => {
    setIsAdmin(!isAdmin)
    localStorage.setItem("isAdmin", !isAdmin)
};

useEffect(() => {
    if ((getAdmin === 'true') && (token) && user_role === '1') {
        setIsAdmin(true)
        localStorage.setItem("isAdmin", getAdmin)
        window.location.href = '#/app/admin/dashboard';
    } else {
        setIsAdmin(false)

    }
}, [isAdmin])

const menuClick = () => {
    setIsResponsiveMenu(!isResponsiveMenu);
};

const menuIconClick = () => {
    setIsMenuCollapse(!isMenuCollapse)
};


return (
    <div className='d-flex flex-row flex-column-fluid'>
        <div className={` d-flex flex-column flex-row-fluid`}>
            <div className='d-flex align-items-stretch justify-content-between header'>
                <div className='container-fluid d-flex align-items-stretch justify-content-xxl-between flex-grow-1'>
                    <button type='button' className='btn d-flex align-items-center form-label d-xl-none px-0' title='Show aside menu'
                        onClick={menuClick}>
                        <FontAwesomeIcon icon={faBars} className='fs-1 form-label' />
                    </button>
                    <Header frontSettings={frontSettings} newRoutes={newRoutes} onAdminClick={onAdminClick} />
                </div>
            </div>
            <div className='content d-flex flex-column flex-column-fluid pt-7'>
                <div className='d-flex flex-column-fluid'>
                    <div className='container-fluid'>
                        {/* <Home /> */}
                        <HomePage />
                    </div>
                </div>
            </div>
            <div className='container-fluid'>
                <Footer allConfigData={allConfigData} frontSetting={frontSetting} />
            </div>
        </div>
    </div>
)
};


const getRouteWithSubMenu = (route) => {
    const subRoutes = route.subMenu ? route.subMenu.filter((item) => item.permission === '') : null
    const newSubRoutes = subRoutes ? { ...route, newRoute: subRoutes } : route
    return newSubRoutes
}


const prepareRoutes = () => {
    let filterRoutes = [];
    asideConfig.forEach((route) => {
        const permissionsRoute = getRouteWithSubMenu(route)
        if ((route.permission === '' || permissionsRoute.newRoute?.length) && (permissionsRoute.isAdmin === false)) {
            filterRoutes.push(permissionsRoute)
        }
    });
    return filterRoutes;
};


const mapStateToProps = (state) => {
    const newPermissions = [];
    const { permissions, settings, frontSetting, allConfigData, frontSettings } = state;


if (permissions) {
    permissions.forEach((permission) =>
        newPermissions.push(permission.attributes.name)
    )
}
return { newPermissions, settings, frontSetting, allConfigData, frontSettings };
};


export default connect(mapStateToProps, {})(MasterLayout);
