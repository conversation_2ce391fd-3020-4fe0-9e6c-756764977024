import React, { useEffect } from 'react'
import { Button, Navbar } from 'react-bootstrap-v5'
import { NavLink } from 'react-router-dom'
import { Tokens } from '../constants'
import { getFormattedMessage } from '../shared/sharedMethod'


const Header = (props) => {
    const { frontSettings } = props
    const token = localStorage.getItem(Tokens.ADMIN);


return (
    <>
        <Navbar className='d-flex align-items-center justify-content-between w-100'>
            <div>
                <a href='/' className='text-decoration-none sidebar-logo text-gray-900 fs-4'>
                    <div className={`${'image image-mini me-3'}`}>
                        <img src={frontSettings && frontSettings.logo}
                            className='img-fluid object-fit-contain'
                            alt='profile image' />
                    </div>
                </a>
            </div>
            <div>
                {token ?
                    <NavLink to="/app/dashboard">
                        <Button className='me-3'>
                            {getFormattedMessage('dashboard.title')}
                        </Button>
                    </NavLink>
                    :
                    <div>
                        <NavLink to="/login">
                            <Button className='me-3'>
                                {getFormattedMessage('login-form.title')}
                            </Button>
                        </NavLink>
                        <NavLink to="/register">
                            <Button>
                                {getFormattedMessage('register.title')}
                            </Button>
                        </NavLink>
                    </div>
                }
            </div>
        </Navbar >
    </>
)
}


export default Header
