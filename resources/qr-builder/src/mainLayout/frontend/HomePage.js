import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
    fetchAllFrontQrCodeTypes,
    fetchAllPlans,
    fetchFrontCms,
    fetchHomeFrontFeatures,
    fetchHomeFrontSubFeatures,
} from "../../store/action/adminActions/frontcmsActions";
import { useSelector } from "react-redux";
import { cssHandling } from "../../cssHandling/cssHandling";
import { Tokens } from "../../constants";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import HomeFooter from "./HomeFooter";
import HomeHeader from "./HomeHeader";
// import { fetchFrontPages } from "../../store/action/adminActions/pagesAction";
import { environment } from "../../config/environment";
import TabTitle from "../../shared/tab-title/TabTitle";
import TopBarButton from "../../shared/top-button/TopBarButton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRightLong } from "@fortawesome/free-solid-svg-icons";
import SubscribeSection from "./SubscribeSection";

const HomePage = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { frontCms, frontSettings } = useSelector((state) => state);
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const [isAdmin, setIsAdmin] = useState(
        JSON.parse(localStorage.getItem("isAdmin"))
    );
    const email_verified_at = localStorage.getItem(Tokens.EMAIL_VERIFY);
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );
    const [qrCodeTypesHoverId, setQrCodeTypesHoverId] = useState(null);

    useEffect(() => {
        // dispatch(fetchFrontPages());
        dispatch(fetchAllFrontQrCodeTypes());
    }, []);

    useEffect(() => {
        if (email_verified_at === "null") {
            localStorage.removeItem(Tokens.ADMIN);
            localStorage.removeItem(Tokens.USER);
            localStorage.removeItem(Tokens.IMAGE);
            localStorage.removeItem(Tokens.FIRST_NAME);
            localStorage.removeItem(Tokens.LAST_NAME);
            localStorage.removeItem(Tokens.USER_ROLE);
            localStorage.removeItem("loginUserArray");
            localStorage.removeItem("isAdmin");
            localStorage.removeItem(Tokens.UPDATED_EMAIL);
            localStorage.removeItem(Tokens.UPDATED_FIRST_NAME);
            localStorage.removeItem(Tokens.UPDATED_LAST_NAME);
            localStorage.removeItem(Tokens.USER_IMAGE_URL);
        }
    }, []);

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
        window.scroll({
            top: 0,
            left: 0,
            behavior: "smooth",
        });
    }, []);

    useEffect(() => {
        dispatch(fetchFrontCms());
        dispatch(fetchHomeFrontFeatures());
        dispatch(fetchHomeFrontSubFeatures());
        dispatch(fetchAllPlans());
        // document.body.classList.remove('dark-theme');
        cssHandling(updatedLanguage);
        return () => cssHandling(updatedLanguage);
    }, []);

    const inClickBtn = (e, link) => {
        e.preventDefault();
        navigate(link);
    };

    const getPage = (link, type) => {
        localStorage.setItem("generatQRType", JSON.stringify(type));
        navigate(link);
        window.scrollTo({
            top: 0,
            left: 100,
            behavior: "smooth",
        });
    };

    const qrCodeTypesDetails = [
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/text.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/text.png",
            name: "globally.input.text.lable",
            description: getFormattedMessage("globally.text.heading"),
            foot: getFormattedMessage("globally.text.title"),
            typeNo: 1,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/url.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/url_white.png",
            name: "globally.input.url.lable",
            description: getFormattedMessage("globally.url.heading"),
            foot: getFormattedMessage("globally.url.title"),
            typeNo: 2,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/phone.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/phone.png",
            name: "globally.input.phone.lable",
            description: getFormattedMessage("globally.phone.heading"),
            foot: getFormattedMessage("globally.phone.title"),
            typeNo: 3,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/email.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/email_white.png",
            name: "user.input.email.label",
            description: getFormattedMessage("globally.email.heading"),
            foot: getFormattedMessage("globally.email.title"),
            typeNo: 4,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/location.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/location_white.png",
            name: "globally.input.location.lable",
            description: getFormattedMessage("globally.location.heading"),
            foot: getFormattedMessage("globally.location.title"),
            typeNo: 5,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/wifi.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/wifi_white.png",
            name: "globally.input.wifi.lable",
            description: getFormattedMessage("globally.wifi.heading"),
            foot: getFormattedMessage("globally.wifi.title"),
            typeNo: 6,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/facetime.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/facetime_white.png",
            name: "globally.input.facetime.lable",
            description: getFormattedMessage("globally.facetime.heading"),
            foot: getFormattedMessage("globally.facetime.title"),
            typeNo: 7,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/event.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/event_white.png",
            name: "globally.input.event.lable",
            description: getFormattedMessage("globally.event.heading"),
            foot: getFormattedMessage("globally.event.title"),
            typeNo: 8,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/whatsapp.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/whatsapp.png",
            name: "globally.input.whatsapp.lable",
            description: getFormattedMessage("globally.whatsapp.heading"),
            foot: getFormattedMessage("globally.whatsapp.title"),
            typeNo: 9,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/crypto.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/crypto.png",
            name: "globally.input.crypto.lable",
            description: getFormattedMessage("globally.crypto.heading"),
            foot: getFormattedMessage("globally.crypto.title"),
            typeNo: 10,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/vcard.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/vcard_white.png",
            name: "globally.input.vcard.lable",
            description: getFormattedMessage("globally.vcard.heading"),
            foot: getFormattedMessage("globally.vcard.title"),
            typeNo: 11,
        },
        {
            image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/paypal.png",
            hover_image:
                environment.URL +
                "/default-images/frontEnd-images/QrCodeTypesImages/paypal_white.png",
            name: "globally.input.paypal.lable",
            description: getFormattedMessage("globally.paypal.heading"),
            foot: getFormattedMessage("globally.paypal.title"),
            typeNo: 12,
        },
    ];

    return (
        <>
            <TabTitle title={placeholderText("globally.home.title")} />
            {/* <!-- start header section --> */}
            <HomeHeader />
            {/* <!-- end header section --> */}

            <div className="home-page bg-white">
                <TopBarButton />
                {/*start hero-section */}
                <section className="hero-section position-relative w-100 bg_secondary">
                    <div className="container pt-100 pb-100">
                        <div className="row align-items-center justify-content-between">
                            <div className="col-lg-6 mb-lg-0 mb-50">
                                <div className="hero-content text-white">
                                    <p className="fs-20 mb-2">
                                        {frontCms?.cms &&
                                            frontCms?.cms[0]?.title}
                                    </p>
                                    <h1 className="mb-4 pb-lg-3 fw-700 text-white">
                                        <span className="text-primary">
                                            {frontSettings?.title}
                                        </span>
                                        :{" "}
                                        {frontCms?.cms &&
                                            frontCms?.cms[0]?.description}
                                    </h1>
                                    {isAdmin === null && (
                                        <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={(e) =>
                                                inClickBtn(e, "/register")
                                            }
                                        >
                                            {getFormattedMessage(
                                                "globally.sign-up.label"
                                            )}
                                        </button>
                                    )}
                                </div>
                            </div>
                            <div className="col-lg-6">
                                <div className="hero-img ms-lg-auto mx-auto">
                                    <img
                                        src={
                                            frontCms?.cms &&
                                            frontCms?.cms[0]?.image_url
                                                ? frontCms?.cms[0]?.image_url
                                                : environment.URL +
                                                  "/default-images/frontEnd-images/banner-img.png"
                                        }
                                        className="w-100 h-100 object-fit-cover"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                {/*end hero-section */}

                {/*start feature section */}
                <section className="feature-section pt-100 pb-100 position-relative">
                    <div className="feature-bg vector-1">
                        <img
                            src={
                                environment.URL +
                                "/default-images/frontEnd-images/feature-bg-vector1.png"
                            }
                            className="w-100"
                        />
                    </div>
                    <div className="feature-bg vector-2">
                        <img
                            src={
                                environment.URL +
                                "/default-images/frontEnd-images/feature-bg-vector2.png"
                            }
                            className="w-100"
                        />
                    </div>
                    <div className="container feature-section-container">
                        <div className="section-heading mb-50 text-center">
                            <h4 className="text-primary title-text ls-1 fw-600 mb-2 fs-20">
                                {getFormattedMessage("admin.feature.title")}
                            </h4>
                            <h2 className="text-dark fw-600">
                                {frontCms?.features &&
                                    frontCms?.features[0]?.attributes?.title}
                            </h2>
                            <p className="fs-20 text-gray-200 fw-400 mb-0">
                                {frontCms?.features &&
                                    frontCms?.features[0]?.attributes
                                        ?.description}
                            </p>
                        </div>
                        <div className="row justify-content-center">
                            <div className="col-lg-11">
                                <div className="row justify-content-center align-items-center">
                                    <div className="col-lg-6 mb-lg-0 mb-50">
                                        <div className="feature-img mx-auto">
                                            <img
                                                src={
                                                    frontCms?.features &&
                                                    frontCms?.features[0]
                                                        ?.attributes?.image
                                                        ? frontCms?.features[0]
                                                              ?.attributes
                                                              ?.image
                                                        : environment.URL +
                                                          "/default-images/frontEnd-images/feature-img.png"
                                                }
                                                className="w-100 h-100 object-fit-cover"
                                            />
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="feature-content d-flex align-items-center justify-content-center">
                                            <ul className="ps-0 mb-0">
                                                {frontCms?.features &&
                                                    frontCms?.features[0]
                                                        ?.attributes?.sub_titles
                                                        ?.length > 0 &&
                                                    frontCms?.features[0]?.attributes?.sub_titles?.map(
                                                        (item, i) => {
                                                            return (
                                                                item &&
                                                                item !== "" &&
                                                                item?.trim()
                                                                    ?.length !==
                                                                    0 && (
                                                                    <li
                                                                        className="d-flex align-items-center fs-20 mb-5"
                                                                        key={
                                                                            i +
                                                                            1
                                                                        }
                                                                    >
                                                                        <img
                                                                            src={
                                                                                environment.URL +
                                                                                "/default-images/frontEnd-images/check.svg"
                                                                            }
                                                                            alt="check"
                                                                            className="check-list-img"
                                                                        />
                                                                        {item}
                                                                    </li>
                                                                )
                                                            );
                                                        }
                                                    )}
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                {/* end feature section */}

                {/* start qr-personal section */}
                <section className="qr-personal-section pt-100 pb-100 bg-gray-100">
                    <div className="qrpersonal-bg vector-1">
                        <img
                            src={
                                environment.URL +
                                "/default-images/frontEnd-images/qrpersonal-bg-vector1.png"
                            }
                        />
                    </div>
                    <div className="qrpersonal-bg vector-2 text-end">
                        <img
                            src={
                                environment.URL +
                                "/default-images/frontEnd-images/qrpersonal-bg-vector2.png"
                            }
                        />
                    </div>
                    <div className="container">
                        <div className="section-heading mb-50 text-center">
                            <h2 className="text-dark fw-600">
                                {frontCms?.features &&
                                    frontCms?.features[1]?.attributes?.title}
                            </h2>
                            <p className="fs-20 text-gray-200 fw-400 mb-0">
                                {frontCms?.features &&
                                    frontCms?.features[1]?.attributes
                                        ?.description}
                            </p>
                        </div>
                        <div className="row justify-content-center">
                            <div className="col-lg-11">
                                <div className="row flex-lg-row flex-column-reverse justify-content-center align-items-center">
                                    <div className="col-lg-6">
                                        <div className="qr-personal-content d-flex align-items-center justify-content-center">
                                            <ul className="ps-0 mb-0">
                                                {frontCms?.features &&
                                                    frontCms?.features[1]
                                                        ?.attributes?.sub_titles
                                                        ?.length > 0 &&
                                                    frontCms?.features[1]?.attributes?.sub_titles?.map(
                                                        (item, i) => {
                                                            return (
                                                                item &&
                                                                item !== "" &&
                                                                item?.trim()
                                                                    ?.length !==
                                                                    0 && (
                                                                    <li
                                                                        className="d-flex align-items-center fs-20 mb-5"
                                                                        key={
                                                                            i +
                                                                            1
                                                                        }
                                                                    >
                                                                        <img
                                                                            src={
                                                                                environment.URL +
                                                                                "/default-images/frontEnd-images/check.svg"
                                                                            }
                                                                            alt="check"
                                                                            className="check-list-img"
                                                                        />
                                                                        {item}
                                                                    </li>
                                                                )
                                                            );
                                                        }
                                                    )}
                                            </ul>
                                        </div>
                                    </div>
                                    <div className="col-lg-6 mb-lg-0 mb-50">
                                        <div className="qr-personal-img mx-auto">
                                            <img
                                                src={
                                                    frontCms?.features &&
                                                    frontCms?.features[1]
                                                        ?.attributes?.image
                                                        ? frontCms?.features[1]
                                                              ?.attributes
                                                              ?.image
                                                        : environment.URL +
                                                          "/default-images/frontEnd-images/qr-personal-img.png"
                                                }
                                                className="w-100 h-100 object-fit-cover"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                {/* end qr-personal section */}

                {/*start qr-code-types section */}
                <section className="qr-code-types-section pt-100 pb-lg-5 position-relative">
                    <div className="qrcode-bg vector">
                        <img
                            src={
                                environment.URL +
                                "/default-images/frontEnd-images/qrcode-bg.png"
                            }
                            className="w-100"
                        />
                    </div>
                    <div className="qrcode-bg vector-1">
                        <img
                            src={
                                environment.URL +
                                "/default-images/frontEnd-images/qrcode-bg-vector1.png"
                            }
                            className="w-100"
                        />
                    </div>
                    <div className="qrcode-bg vector-2">
                        <img
                            src={
                                environment.URL +
                                "/default-images/frontEnd-images/qrcode-bg-vector2.png"
                            }
                            className="w-100"
                        />
                    </div>
                    <div className="container">
                        <div className="section-heading mb-50 text-center">
                            <h2 className="text-dark fw-600">
                                {getFormattedMessage(
                                    "home.12.types.of.qr.codes.title"
                                )}
                            </h2>
                            <p className="fs-20 text-gray-200 fw-400 mb-0">
                                {getFormattedMessage(
                                    "globally.qr-code-type.sub.title"
                                )}
                            </p>
                        </div>
                        <div className="row">
                            {qrCodeTypesDetails?.map((data, i) => {
                                const itemsGet =
                                    frontCms &&
                                    frontCms?.qrCodeTypes?.length >= 1 &&
                                    frontCms?.qrCodeTypes?.filter(
                                        (items) =>
                                            data.typeNo ===
                                            items?.attributes?.id
                                    );
                                if (
                                    data.typeNo === itemsGet[0]?.attributes?.id
                                ) {
                                    return (
                                        <div
                                            key={i + 1}
                                            className="col-xl-3 col-lg-4 col-sm-6 mb-40 px-md-3"
                                            onClick={() =>
                                                isAdmin === null
                                                    ? getPage("/login")
                                                    : isAdmin === true
                                                    ? getPage(
                                                          "/app/admin/qr-codes/create"
                                                      )
                                                    : getPage(
                                                          "/app/qrcode/create",
                                                          data.typeNo
                                                      )
                                            }
                                        >
                                            <div
                                                className="card qr-types-card"
                                                onMouseEnter={() =>
                                                    setQrCodeTypesHoverId(
                                                        data.typeNo
                                                    )
                                                }
                                                onMouseLeave={() =>
                                                    setQrCodeTypesHoverId(null)
                                                }
                                            >
                                                <div className="card-img-top">
                                                    {qrCodeTypesHoverId ===
                                                    data.typeNo ? (
                                                        <img
                                                            src={
                                                                data.hover_image
                                                            }
                                                            className=""
                                                            alt="paypal"
                                                        />
                                                    ) : (
                                                        <img
                                                            src={data.image}
                                                            className="h-100"
                                                            alt="paypal"
                                                        />
                                                    )}
                                                </div>
                                                <div className="card-body px-0">
                                                    <h5 className="card-title fs-20 fw-600 mb-4">
                                                        {getFormattedMessage(
                                                            data.name
                                                        )}
                                                    </h5>
                                                    <p className="card-text">
                                                        {
                                                            itemsGet[0]
                                                                ?.attributes
                                                                ?.description
                                                        }
                                                    </p>
                                                </div>
                                                <span className="type-btn">
                                                    <FontAwesomeIcon
                                                        icon={faArrowRightLong}
                                                    />
                                                    <i className="fa-solid fa-arrow-right-long" />
                                                </span>
                                            </div>
                                        </div>
                                    );
                                }
                            })}
                        </div>
                    </div>
                </section>
                {/*end qr-code-types section */}

                {/*start subscribe section */}
                <SubscribeSection />
                {/*end subscribe section */}
            </div>

            {/* <!-- start footer-section --> */}
            <HomeFooter />
            {/* <!-- end footer-section --> */}
        </>
    );
};

export default HomePage;
