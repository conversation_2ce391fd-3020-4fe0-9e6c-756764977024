import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import HomeFooter from "../HomeFooter";
import HomeHeader from "../HomeHeader";
import { updateThemeAction } from "../../../store/action/adminActions/themeAction";
import { Tokens } from "../../../constants";
import { cssHandling } from "../../../cssHandling/cssHandling";
import { unescape } from "lodash";
import { fetchFrontSetting } from "../../../store/action/frontSettingAction";
import TopBarButton from "../../../shared/top-button/TopBarButton";
import TabTitle from "../../../shared/tab-title/TabTitle";
import { placeholderText } from "../../../shared/sharedMethod";

const ForntPages = () => {
    const dispatch = useDispatch();
    const { frontPage, frontSettings } = useSelector((state) => state);
    const [html, setHtml] = useState("");
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);

    const logo = frontSettings?.favicon ? frontSettings?.favicon : "";
    let link = document.querySelector("link[rel~='icon']");
    if (!link) {
        link = document.createElement("link");
        link.rel = "icon";
        document.getElementsByTagName("head")[0].appendChild(link);
    }
    link.href = logo;

    useEffect(() => {
        dispatch(fetchFrontSetting());
    }, []);

    useEffect(() => {
        if (frontPage.length >= 1) {
            setHtml(unescape(frontPage[0]?.description));
        }
    }, [frontPage]);

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        dispatch(updateThemeAction(isDark));
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, [isDark]);

    useEffect(() => {
        cssHandling(updatedLanguage);
        return () => cssHandling(updatedLanguage);
    }, []);

    return (
        <div>
            <TabTitle title={placeholderText("settings.select.pages.label")} />
            <HomeHeader />
            <div className="home-page">
                <TopBarButton />
                <div className="container mt-3">
                    <h1 className="text-center">{frontPage[0]?.title}</h1>
                </div>
                <div className="container mt-3 pb-3 d-flex dynamic_page_content">
                    <div
                        dangerouslySetInnerHTML={{ __html: html }}
                        className="w-100 text-break dangerously_set_html"
                    ></div>
                </div>
            </div>
            <HomeFooter />
        </div>
    );
};

export default ForntPages;
