import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { addToast } from "../../store/action/toastAction";
import { toastType, Tokens } from "../../constants";
import * as EmailValidator from "email-validator";
import { addContactInfo } from "../../store/action/adminActions/frontcmsActions";
import { cssHandling } from "../../cssHandling/cssHandling";
import HomeHeader from "./HomeHeader";
import HomeFooter from "./HomeFooter";
import TabTitle from "../../shared/tab-title/TabTitle";
import TopBarButton from "../../shared/top-button/TopBarButton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faPhone,
    faLocationDot,
    faAt,
} from "@fortawesome/free-solid-svg-icons";
import SubscribeSection from "./SubscribeSection";
import { environment } from "../../config/environment";

const ContactUs = () => {
    const { frontSettings } = useSelector((state) => state);
    const captchaRef = useRef(null);
    const dispatch = useDispatch();
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const [isLoading, setIsLoading] = useState(false);
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );

    const [adminContactInfo, setAdminContactInfo] = useState({
        email: "",
        phone: "",
        address: "",
    });
    const [contactInfo, setContactInfo] = useState({
        first_name: "",
        last_name: "",
        email: "",
        subject: "",
        message: "",
    });

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
        window.scroll({
            top: 0,
            left: 0,
            behavior: "smooth",
        });
    }, []);

    useEffect(() => {
        cssHandling(updatedLanguage);
        return () => cssHandling(updatedLanguage);
    }, []);

    useEffect(() => {
        if (frontSettings) {
            setAdminContactInfo({
                email:
                    frontSettings && frontSettings.email
                        ? frontSettings.email
                        : "",
                phone:
                    frontSettings && frontSettings.phone
                        ? frontSettings.phone
                        : "",
                address:
                    frontSettings && frontSettings.address
                        ? frontSettings.address
                        : "",
            });
        }
    }, [frontSettings]);

    const handleChangeContact = (e) => {
        setContactInfo((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value,
        }));
    };

    const submitContact = async (event) => {
        event.preventDefault();
        let token =
            frontSettings &&
            frontSettings?.captcha_on_contact_page === "1" &&
            frontSettings?.captcha_site_key &&
            captchaRef && captchaRef?.current?.getValue();

        if (contactInfo.first_name === "") {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "globally.input.first-name.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else if (contactInfo.last_name === "") {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "globally.input.last-name.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else if (
            contactInfo.email === "" ||
            contactInfo.email.trim().length === 0
        ) {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "globally.input.email.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else if (!EmailValidator.validate(contactInfo.email)) {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "globally.input.email.valid.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else if (contactInfo.subject === "") {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "Please.enter.subject.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else if (contactInfo.message === "") {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "Please.enter.message.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else {
            if (true) {
                setIsLoading(true);
                dispatch(
                    addContactInfo(
                        {
                            name:
                                contactInfo.first_name +
                                " " +
                                contactInfo.last_name,
                            email: contactInfo.email,
                            subject: contactInfo.subject,
                            message: contactInfo.message,
                            token,
                        },
                        captchaRef,
                        setIsLoading
                    )
                );
                setContactInfo({
                    first_name: "",
                    last_name: "",
                    email: "",
                    subject: "",
                    message: "",
                });
            } else {
                setIsLoading(true);
                frontSettings &&
                    frontSettings?.captcha_on_contact_page === "0" &&
                    dispatch(
                        addContactInfo(
                            {
                                name:
                                    contactInfo.first_name +
                                    " " +
                                    contactInfo.last_name,
                                email: contactInfo.email,
                                subject: contactInfo.subject,
                                message: contactInfo.message,
                            },
                            false,
                            setIsLoading
                        )
                    );
                frontSettings &&
                    frontSettings?.captcha_on_contact_page === "0" &&
                    setContactInfo({
                        first_name: "",
                        last_name: "",
                        email: "",
                        subject: "",
                        message: "",
                    });
                frontSettings &&
                    frontSettings?.captcha_on_contact_page === "1" &&
                    frontSettings?.captcha_site_key &&
                    dispatch(
                        addToast({
                            text: getFormattedMessage(
                                "Please.verify.the.captcha.validate.label"
                            ),
                            type: toastType.ERROR,
                        })
                    );
                frontSettings &&
                    frontSettings?.captcha_on_contact_page === "1" &&
                    frontSettings?.captcha_site_key &&
                    setIsLoading(false);
            }
        }
    };

    return (
        <>
            <TabTitle title={placeholderText("globally.contact.title")} />
            <HomeHeader />
            <div className="contact-page">
                <TopBarButton />
                <div className="contact-page bg-white">
                    {/*start hero-section */}
                    <section className="hero-section position-relative bg_secondary">
                        <div className="container pt-lg-12 pt-md-4 pt-3 pb-60">
                            <div className="row align-items-center justify-content-center">
                                <div className="col-12 text-center">
                                    <h2 className="fs-45 text-white mb-md-4 fw-600">
                                        {getFormattedMessage(
                                            "globally.contact.title"
                                        )}
                                    </h2>
                                </div>
                            </div>
                        </div>
                    </section>
                    {/*end hero-section */}

                    {/*start get-in-touch section */}
                    <section className="get-in-touch-section pt-100 pb-60 position-relative">
                        <div className="feature-bg vector-1">
                            <img
                                src={
                                    environment.URL +
                                    "/default-images/frontEnd-images/feature-bg-vector1.png"
                                }
                                className="w-100"
                            />
                        </div>
                        <div className="feature-bg contact_side_vector1">
                            <img
                                src={
                                    environment.URL +
                                    "/default-images/frontEnd-images/contact_side_vector1.png"
                                }
                                className="w-100"
                            />
                        </div>
                        <div className="container">
                            <div className="row justify-content-around align-items-center">
                                <div className="col-lg-5 col-md-5 col-sm-9 col-12 zIndex-2 mb-md-0 mb-5 p-40 bg_dark_blue border-radius-10 position-relative contact_info_section">
                                    <h4 className="text-white fw-600 fs-28">
                                        {getFormattedMessage(
                                            "contact.contact-information.label"
                                        )}
                                    </h4>
                                    <p className="fs-18 text-gray-300 fw-400 mb-50">
                                        Reach Out to Us - We're Here to Help You
                                    </p>
                                    <div className="mt-100">
                                        <div className="d-flex justify-content-start align-items-center mb-4 pb-1">
                                            <div className="contact-icon me-3">
                                                <img
                                                    src={
                                                        environment.URL +
                                                        "/default-images/frontEnd-images/contact_call.png"
                                                    }
                                                    className="w-100"
                                                />
                                            </div>
                                            <a
                                                href={`tel:${adminContactInfo.phone}`}
                                                className="text-white fw-100 fs-16"
                                            >
                                                {adminContactInfo.phone}
                                            </a>
                                        </div>
                                        <div className="d-flex justify-content-start align-items-center mb-4 pb-1 mt-30">
                                            <div className="contact-icon me-3">
                                                <img
                                                    src={
                                                        environment.URL +
                                                        "/default-images/frontEnd-images/contact_email.png"
                                                    }
                                                    className="w-100"
                                                />
                                            </div>
                                            <a
                                                href={`mailto:${adminContactInfo.email}`}
                                                className="text-white fw-100 fs-16"
                                            >
                                                {adminContactInfo.email}
                                            </a>
                                        </div>
                                        <div className="d-flex justify-content-start align-items-center mb-4 pb-1 mt-30">
                                            <div className="contact-icon me-3">
                                                <img
                                                    src={
                                                        environment.URL +
                                                        "/default-images/frontEnd-images/contact_location.png"
                                                    }
                                                    className="w-100"
                                                />
                                            </div>
                                            <p className="text-white fw-100 fs-16 m-0">
                                                {adminContactInfo.address}
                                            </p>
                                        </div>
                                    </div>
                                    <img
                                        src={
                                            environment.URL +
                                            "/default-images/frontEnd-images/contact_small_circle.png"
                                        }
                                        className="contact_small_circle"
                                    />
                                    <img
                                        src={
                                            environment.URL +
                                            "/default-images/frontEnd-images/contact_big_circle.png"
                                        }
                                        className="contact_big_circle"
                                    />
                                </div>
                                <div className="col-lg-5 col-md-6 col-sm-9 col-12">
                                    <div className="contact-form bg-white">
                                        <img
                                            src={
                                                environment.URL +
                                                "/default-images/frontEnd-images/contact_left_top.png"
                                            }
                                            className="contact_left_top"
                                        />
                                        <img
                                            src={
                                                environment.URL +
                                                "/default-images/frontEnd-images/contact_right_top.png"
                                            }
                                            className="contact_right_top"
                                        />
                                        <img
                                            src={
                                                environment.URL +
                                                "/default-images/frontEnd-images/contact_left_bottom.png"
                                            }
                                            className="contact_left_bottom"
                                        />
                                        <img
                                            src={
                                                environment.URL +
                                                "/default-images/frontEnd-images/contact_right_bottom.png"
                                            }
                                            className="contact_right_bottom"
                                        />
                                        <form onSubmit={submitContact}>
                                            <div className="row">
                                                <div className="col-sm-6 col-12">
                                                    <label className="form_label">
                                                        {getFormattedMessage(
                                                            "user.input.first-name.label"
                                                        )}
                                                        :
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        placeholder={placeholderText(
                                                            "contact.your.first.name.placeholder"
                                                        )}
                                                        name="first_name"
                                                        value={
                                                            contactInfo.first_name
                                                        }
                                                        onChange={
                                                            handleChangeContact
                                                        }
                                                    />
                                                </div>
                                                <div className="col-sm-6 col-12 mt-sm-0 mt-3">
                                                    <label className="form_label">
                                                        {getFormattedMessage(
                                                            "user.input.last-name.label"
                                                        )}
                                                        :
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        placeholder={placeholderText(
                                                            "contact.your.last.name.placeholder"
                                                        )}
                                                        name="last_name"
                                                        value={
                                                            contactInfo.last_name
                                                        }
                                                        onChange={
                                                            handleChangeContact
                                                        }
                                                    />
                                                </div>
                                                <div className="col-12 mt-20">
                                                    <label className="form_label">
                                                        {getFormattedMessage(
                                                            "user.input.email.label"
                                                        )}
                                                        :
                                                    </label>
                                                    <input
                                                        type="email"
                                                        className="form-control"
                                                        placeholder={placeholderText(
                                                            "contact.your.email.address.placeholder"
                                                        )}
                                                        name="email"
                                                        value={
                                                            contactInfo.email
                                                        }
                                                        onChange={
                                                            handleChangeContact
                                                        }
                                                    />
                                                </div>
                                                <div className="col-12 mt-20">
                                                    <label className="form_label">
                                                        {getFormattedMessage(
                                                            "globally.input.subject.lable"
                                                        )}
                                                        :
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        placeholder={placeholderText(
                                                            "contact.your.subject.placeholder"
                                                        )}
                                                        name="subject"
                                                        value={
                                                            contactInfo.subject
                                                        }
                                                        onChange={
                                                            handleChangeContact
                                                        }
                                                    />
                                                </div>
                                                <div className="col-12 h-100 mt-20">
                                                    <label className="form_label">
                                                        {getFormattedMessage(
                                                            "globally.input.message.lable"
                                                        )}
                                                        :
                                                    </label>
                                                    <textarea
                                                        className="form-control h-100"
                                                        placeholder={placeholderText(
                                                            "contact.type.your.message.here.placeholder"
                                                        )}
                                                        rows={4}
                                                        name="message"
                                                        value={
                                                            contactInfo.message
                                                        }
                                                        onChange={
                                                            handleChangeContact
                                                        }
                                                    />
                                                </div>
                                                <div className="col-12 text-end mt-40">
                                                    <button
                                                        type="submit"
                                                        className="btn btn-primary w-100"
                                                        disabled={isLoading}
                                                    >
                                                        {isLoading
                                                            ? getFormattedMessage(
                                                                  "globally.loading.label"
                                                              )
                                                            : getFormattedMessage(
                                                                  "contact.send.message.title"
                                                              )}
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    {/* end get-in-touch section */}

                    {/*start subscribe section */}
                    <SubscribeSection />
                    {/*end subscribe section */}
                </div>
            </div>
            <HomeFooter />
        </>
    );
};

export default ContactUs;
