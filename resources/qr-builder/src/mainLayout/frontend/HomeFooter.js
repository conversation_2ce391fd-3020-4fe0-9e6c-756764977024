import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useSelector } from "react-redux";
import { getFormattedMessage } from "../../shared/sharedMethod";
import { environment } from "../../config/environment";

const HomeFooter = () => {
    const { frontSettings } = useSelector((state) => state);
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, []);

    return (
        <>
            {/* start footer-section */}
            <footer className="pt-100 bg_secondary position-relative">
                <div className="footer-vector position-absolute">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/footer-vector.png"
                        }
                        alt="vector"
                    />
                </div>
                <div className="container">
                    <div className="row align-items-center pt-lg-4 pt-5 mt-lg-0 mt-2 justify-content-between mb-sm-5 pb-sm-5">
                        <div className="col-sm-4 pe-sm-0 mb-sm-0 mb-5 pb-sm-0 pb-3">
                            <div
                                className="footer-logo d-flex justify-content-sm-start justify-content-center text-white fs-20 align-items-center cursor-pointer"
                                onClick={() => {
                                    window.scrollTo({
                                        top: 0,
                                        left: 100,
                                        behavior: "smooth",
                                    });
                                }}
                            >
                                <img
                                    src={
                                        frontSettings?.logo ||
                                        environment.URL +
                                            "/default-images/frontEnd-images/footer-vector.png"
                                    }
                                    alt="logo"
                                    className="img-fluid footer-logo-img me-3"
                                />
                                {frontSettings?.title}
                            </div>
                        </div>
                        <div className="col-xxl-6 col-xl-7 col-sm-8 mb-sm-0 mb-4 d-flex flex-wrap justify-content-sm-end justify-content-center">
                            <Link
                                to="/terms-conditions"
                                className="fw-4 fs-18 text-white d-block mx-lg-4 mx-sm-3 mx-4"
                            >
                                {getFormattedMessage(
                                    "admin.terms-condition.title"
                                )}
                            </Link>
                            <Link
                                to="/privacy-policy"
                                className="fw-4 fs-18 text-white d-block mx-lg-4 mx-sm-3 mx-4"
                            >
                                {getFormattedMessage(
                                    "admin.privacy-policy.title"
                                )}
                            </Link>
                        </div>
                    </div>
                    <div className="row pb-5">
                        <div className="col-12 text-center">
                            <a href="#!" className="fw-4 text-white">
                                {getFormattedMessage(
                                    "globally.copyright.title"
                                )}{" "}
                                &#169; {new Date().getFullYear()}{" "}
                                {frontSettings?.title &&
                                    frontSettings?.title + "."}
                            </a>
                        </div>
                    </div>
                </div>
            </footer>
            {/* end footer-section */}
        </>
    );
};

export default HomeFooter;
