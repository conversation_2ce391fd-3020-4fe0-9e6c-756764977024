import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Tokens } from "../../constants";
import { fetchTermsAndConditionData } from "../../store/action/adminActions/frontcmsActions";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { cssHandling } from "../../cssHandling/cssHandling";
import TabTitle from "../../shared/tab-title/TabTitle";
import faviIcon from "../../../../../public/default-images/favicon.png";
import HomeFooter from "./HomeFooter";
import HomeHeader from "./HomeHeader";
import TopBarButton from "../../shared/top-button/TopBarButton";
import { environment } from "../../config/environment";
// import SubscribeSection from "./SubscribeSection";

const PrivacyPolicye = () => {
    const dispatch = useDispatch();
    const { frontCms, frontSettings } = useSelector((state) => state);
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const [html, setHtml] = useState("");

    useEffect(() => {
        dispatch(fetchTermsAndConditionData());
        cssHandling(updatedLanguage);
        window.scroll({
            top: 0,
            left: 0,
            behavior: "smooth",
        });

        return () => cssHandling(updatedLanguage);
    }, []);

    useEffect(() => {
        if (frontCms?.allTremCondition) {
            setHtml(frontCms.allTremCondition.privacy_policy);
        }
    }, [frontCms]);

    const logo = frontSettings?.favicon
        ? frontSettings?.favicon
        : localStorage.getItem("website_favicon");
    let link = document.querySelector("link[rel~='icon']");
    if (!link) {
        link = document.createElement("link");
        link.rel = "icon";
        document.getElementsByTagName("head")[0].appendChild(link);
    }

    const faviLogo = logo === null ? faviIcon : logo;
    link.href = faviLogo;

    return (
        <>
            <TabTitle title={placeholderText("admin.privacy-policy.title")} />
            {/* <!-- start header section --> */}
            <HomeHeader />
            {/* <!-- end header section --> */}

            <TopBarButton />
            <section className="position-relative privacy_policy">
                <div className="feature-bg vector-1">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/feature-bg-vector1.png"
                        }
                        className="w-100"
                    />
                </div>
                <div className="feature-bg vector-2">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/feature-bg-vector2.png"
                        }
                        className="w-100"
                    />
                </div>
                <div className="qrcode-bg vector">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/qrcode-bg.png"
                        }
                        className="w-100"
                    />
                </div>
                <div className="qrcode-bg vector-1">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/qrcode-bg-vector1.png"
                        }
                        className="w-100"
                    />
                </div>
                <div className="qrcode-bg vector-2">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/qrcode-bg-vector2.png"
                        }
                        className="w-100"
                    />
                </div>
                <div className="qrpersonal-bg vector-1">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/qrpersonal-bg-vector1.png"
                        }
                    />
                </div>
                <div className="qrpersonal-bg vector-2 text-end">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/qrpersonal-bg-vector2.png"
                        }
                    />
                </div>
                <div className="feature-bg contact_side_vector1">
                    <img
                        src={
                            environment.URL +
                            "/default-images/frontEnd-images/contact_side_vector1.png"
                        }
                        className="w-100"
                    />
                </div>
                <div className="container  pt-100 pb-100">
                    <div>
                        <h1 className="text-center text-secondary">
                            {getFormattedMessage("admin.privacy-policy.title")}
                        </h1>

                        <div
                            dangerouslySetInnerHTML={{ __html: html }}
                            className="background rounded dangerously_set_html mx-auto mt-5"
                        ></div>
                    </div>
                </div>
            </section>

            {/* <div className="home-page">
                <SubscribeSection />
            </div> */}

            {/* <!-- start footer-section --> */}
            <HomeFooter />
            {/* <!-- end footer-section --> */}
        </>
    );
};

export default PrivacyPolicye;
