import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Tokens } from "../../constants";
import { cssHandling } from "../../cssHandling/cssHandling";
import TabTitle from "../../shared/tab-title/TabTitle";
import { fetchAllPlans } from "../../store/action/adminActions/frontcmsActions";
import {
    formatAmount,
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { fetchUserPlan } from "../../store/action/plansAction";
import HomeFooter from "./HomeFooter";
import HomeHeader from "./HomeHeader";
import TopBarButton from "../../shared/top-button/TopBarButton";
import SubscribeSection from "./SubscribeSection";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck } from "@fortawesome/free-solid-svg-icons";

const Pricing = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { frontCms, plans } = useSelector((state) => state);
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const [isAdmin, setIsAdmin] = useState(
        JSON.parse(localStorage.getItem("isAdmin"))
    );
    const [tokens, setTokens] = useState(localStorage.getItem(Tokens.ADMIN));
    const [planType, setPlanType] = useState(1);

    const [activePlanId, setActivePlanId] = useState(null);

    useEffect(() => {
        dispatch(fetchAllPlans());
        tokens !== null && isAdmin === false && dispatch(fetchUserPlan());
        cssHandling(updatedLanguage);
        window.scroll({
            top: 0,
            left: 0,
            behavior: "smooth",
        });

        return () => cssHandling(updatedLanguage);
    }, []);

    useEffect(() => {
        if (plans) {
            plans["0"]?.length > 0 &&
                setActivePlanId(
                    plans["0"]?.filter((d) => d?.plan_id === d?.is_active)[0]
                        ?.plan_id
                );
        }
    }, [plans]);

    const monthly = () => {
        setPlanType(1);
        // document.getElementById('monthly').style.display = 'block';
        // document.getElementById('annual').style.display = 'none';
        // document.getElementById('lifetime').style.display = 'none';
    };
    const annual = () => {
        setPlanType(2);
        // document.getElementById('annual').style.display = 'block';
        // document.getElementById('monthly').style.display = 'none';
        // document.getElementById('lifetime').style.display = 'none';
    };

    const handleChoosePlan = (e) => {
        e.preventDefault();
        if (isAdmin === null) {
            localStorage.setItem("choosePlan", true);
            navigate("/register");
        } else {
            if (isAdmin === false) {
                navigate("/app/upgrade-plan");
            } else {
                navigate("/app/admin/dashboard");
            }
        }
    };

    return (
        <>
            {/* <!-- start header section --> */}
            <TabTitle title={placeholderText("plans.title")} />
            <HomeHeader />
            {/* <!-- end header section --> */}
            <div className="pricing-page bg-white">
                <TopBarButton />

                {/*start hero-section */}
                <section className="hero-section position-relative bg_secondary">
                    <div className="container pt-lg-12 pt-md-4 pt-3 pb-60">
                        <div className="row align-items-center justify-content-center">
                            <div className="col-12 text-center">
                                <h2 className="fs-45 text-white mb-md-4 fw-600">
                                    {/* {getFormattedMessage("plans.title")} */}
                                    Pricing plans for teams of all size
                                </h2>
                                <p className="w-75 text-center text-white mx-auto fw-100 px-100">
                                    Choose and affordable plan that's packed
                                    with the best feature for engaging your
                                    audience, creating customer loyalty, and
                                    driving sales.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
                {/*end hero-section */}

                {/* start pricing-plan-section */}
                <section className="pricing-plan-section pt-100 pb-60">
                    <div className="container">
                        <div className="row align-items-center justify-content-center">
                            {/* <div className="col-lg-8 text-lg-start text-center">
                                <h2 className="text-black mb-lg-0 mb-4">
                                    Select Plan
                                </h2>
                            </div> */}
                            <div className="col-lg-4 d-flex justify-content-lg-end justify-content-center">
                                <div className="switch-field">
                                    <input
                                        type="radio"
                                        id="switchMonthly"
                                        name="switchPlan"
                                        defaultValue="Monthly"
                                        defaultChecked="checked"
                                        onClick={() => monthly()}
                                    />
                                    <input
                                        type="radio"
                                        id="switchYearly"
                                        name="switchPlan"
                                        defaultValue="Yearly"
                                        onClick={() => annual()}
                                    />
                                    <label htmlFor="switchMonthly">
                                        {getFormattedMessage(
                                            "globally.input.monthly.lable"
                                        )}
                                    </label>
                                    <label htmlFor="switchYearly">
                                        {getFormattedMessage(
                                            "globally.input.annually.lable"
                                        )}
                                    </label>
                                    <div className="switch-wrapper">
                                        <div className="switch">
                                            <div>
                                                {getFormattedMessage(
                                                    "globally.input.monthly.lable"
                                                )}
                                            </div>
                                            <div>
                                                {getFormattedMessage(
                                                    "globally.input.annually.lable"
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="pricing-plans">
                            <div className="row justify-content-start">
                                {frontCms?.allPlans &&
                                frontCms?.allPlans?.length > 0 ? (
                                    frontCms?.allPlans?.filter(
                                        (df) => df?.type === planType
                                    ).length > 0 ? (
                                        frontCms?.allPlans
                                            ?.filter(
                                                (df) => df?.type === planType
                                            )
                                            ?.map((d, i) => {
                                                return (
                                                    <div
                                                        className="col-xxl-3 col-lg-4 col-sm-6 mt-40"
                                                        key={i + 1}
                                                    >
                                                        <div className="card plan-card h-100 shadow-none">
                                                            <div className="card-body h-100 shadow-none">
                                                                <div className="h-100 d-flex flex-column justify-content-between">
                                                                    <div>
                                                                        <div
                                                                            className={`mb-4 ${
                                                                                (planType ===
                                                                                    1 &&
                                                                                    i ===
                                                                                        2) ||
                                                                                (planType ===
                                                                                    2 &&
                                                                                    i ===
                                                                                        1)
                                                                                    ? "d-flex align-items-center justify-content-between"
                                                                                    : ""
                                                                            }`}
                                                                        >
                                                                            <p className="fs-18 text-secondary py-1 m-0">
                                                                                {
                                                                                    d?.name
                                                                                }
                                                                            </p>
                                                                            {(planType ===
                                                                                1 &&
                                                                                i ===
                                                                                    2) ||
                                                                            (planType ===
                                                                                2 &&
                                                                                i ===
                                                                                    1) ? (
                                                                                <p className="text-secondary fw-600 py-1 px-3 bg-secondary-light rounded-pill m-0 text-center most_popular">
                                                                                    {getFormattedMessage(
                                                                                        "plan.most.popular.title"
                                                                                    )}
                                                                                </p>
                                                                            ) : null}
                                                                        </div>
                                                                        <p className="fw-100 m-0 mb-4">
                                                                            {d?.description ||
                                                                                ""}
                                                                        </p>
                                                                        <h4 className="fs-30 price mb-5">
                                                                            {
                                                                                d
                                                                                    ?.currency
                                                                                    ?.symbol
                                                                            }
                                                                            {formatAmount(
                                                                                d?.price
                                                                            )}
                                                                            <span className="fs-18">
                                                                                {planType ===
                                                                                1
                                                                                    ? `/${placeholderText(
                                                                                          "globally.heading.month.label"
                                                                                      )}`
                                                                                    : `/${placeholderText(
                                                                                          "globally.heading.year.label"
                                                                                      )}`}
                                                                            </span>
                                                                        </h4>
                                                                        {activePlanId !==
                                                                            null &&
                                                                        d
                                                                            ?.plan_features
                                                                            ?.plan_id ===
                                                                            activePlanId ? (
                                                                            <button className="plan-btn btn-orange-active w-100 mt-3 mb-2 disabled text-white rounded">
                                                                                {getFormattedMessage(
                                                                                    "globally.currently.active.btn.title"
                                                                                )}
                                                                            </button>
                                                                        ) : (
                                                                            <button
                                                                                onClick={(
                                                                                    e
                                                                                ) =>
                                                                                    handleChoosePlan(
                                                                                        e
                                                                                    )
                                                                                }
                                                                                disabled={
                                                                                    isAdmin ===
                                                                                    true
                                                                                        ? true
                                                                                        : false
                                                                                }
                                                                                className="plan-btn choose-plan-btn w-100 rounded"
                                                                            >
                                                                                {getFormattedMessage(
                                                                                    "globally.choose.plan.btn.title"
                                                                                )}
                                                                            </button>
                                                                        )}
                                                                        <ul className="list ps-0 mt-5">
                                                                            <li className="d-flex align-items-center mb-4">
                                                                                <span
                                                                                    className={`plan_check_icon_container ${
                                                                                        d
                                                                                            ?.plan_features
                                                                                            ?.qr_code_types
                                                                                            .length >
                                                                                        0
                                                                                            ? "bg_primary"
                                                                                            : "bg-secondary"
                                                                                    } `}
                                                                                >
                                                                                    <FontAwesomeIcon
                                                                                        icon={
                                                                                            faCheck
                                                                                        }
                                                                                    />
                                                                                </span>
                                                                                <span>
                                                                                    {
                                                                                        d?.plan_features?.qr_code_types?.split(
                                                                                            ","
                                                                                        )
                                                                                            ?.length
                                                                                    }{" "}
                                                                                    {getFormattedMessage(
                                                                                        "globally.qr-code-type.title"
                                                                                    )}
                                                                                </span>
                                                                            </li>
                                                                            <li className="d-flex align-items-center mb-4">
                                                                                <span
                                                                                    className={`plan_check_icon_container ${
                                                                                        d
                                                                                            ?.plan_features
                                                                                            ?.qr_code_limit >
                                                                                        0
                                                                                            ? "bg_primary"
                                                                                            : "bg-secondary"
                                                                                    }`}
                                                                                >
                                                                                    <FontAwesomeIcon
                                                                                        icon={
                                                                                            faCheck
                                                                                        }
                                                                                    />
                                                                                </span>
                                                                                <span>
                                                                                    {
                                                                                        d
                                                                                            ?.plan_features
                                                                                            ?.qr_code_limit
                                                                                    }{" "}
                                                                                    {getFormattedMessage(
                                                                                        "globally.qrcode.title"
                                                                                    )}
                                                                                </span>
                                                                            </li>
                                                                            <li className="d-flex align-items-center mb-4">
                                                                                <span
                                                                                    className={`plan_check_icon_container ${
                                                                                        d
                                                                                            ?.plan_features
                                                                                            ?.links_limit >
                                                                                        0
                                                                                            ? "bg_primary"
                                                                                            : "bg-secondary"
                                                                                    }`}
                                                                                >
                                                                                    <FontAwesomeIcon
                                                                                        icon={
                                                                                            faCheck
                                                                                        }
                                                                                    />
                                                                                </span>
                                                                                <span>
                                                                                    {
                                                                                        d
                                                                                            ?.plan_features
                                                                                            ?.links_limit
                                                                                    }{" "}
                                                                                    {getFormattedMessage(
                                                                                        "globally.dynamic-link.title"
                                                                                    )}
                                                                                </span>
                                                                            </li>
                                                                            <li className="d-flex align-items-center mb-4">
                                                                                <span
                                                                                    className={`plan_check_icon_container ${
                                                                                        d
                                                                                            ?.plan_features
                                                                                            ?.projects_limit >
                                                                                        0
                                                                                            ? "bg_primary"
                                                                                            : "bg-secondary"
                                                                                    }`}
                                                                                >
                                                                                    <FontAwesomeIcon
                                                                                        icon={
                                                                                            faCheck
                                                                                        }
                                                                                    />
                                                                                </span>
                                                                                <span>
                                                                                    {
                                                                                        d
                                                                                            ?.plan_features
                                                                                            ?.projects_limit
                                                                                    }{" "}
                                                                                    {getFormattedMessage(
                                                                                        "projects.title"
                                                                                    )}
                                                                                </span>
                                                                            </li>
                                                                            <li className="d-flex align-items-center">
                                                                                <span
                                                                                    className={`plan_check_icon_container bg_primary`}
                                                                                >
                                                                                    <FontAwesomeIcon
                                                                                        icon={
                                                                                            faCheck
                                                                                        }
                                                                                    />
                                                                                </span>
                                                                                <span>
                                                                                    {getFormattedMessage(
                                                                                        "globally.included-analytics.title"
                                                                                    )}
                                                                                </span>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            })
                                    ) : (
                                        <div className="col-12 mt-40">
                                            <div className="card plan-card">
                                                <div className="card-body">
                                                    <div className="text1">
                                                        <p className="fs-18 m-0 text-center">
                                                            {getFormattedMessage(
                                                                "globally.input.no-plan-available.lable"
                                                            )}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )
                                ) : null}
                            </div>
                        </div>
                    </div>
                </section>
                {/* end pricing-plan-section */}

                {/*start subscribe section */}
                <SubscribeSection />
                {/*end subscribe section */}
            </div>

            {/* <!-- start footer-section --> */}
            <HomeFooter />
            {/* <!-- end footer-section --> */}
        </>
    );
};

export default Pricing;
