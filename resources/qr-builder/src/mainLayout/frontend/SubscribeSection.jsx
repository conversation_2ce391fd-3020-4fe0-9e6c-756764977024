import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router";
import { toastType } from "../../constants";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { addSubscribeUser } from "../../store/action/adminActions/frontcmsActions";
import { addToast } from "../../store/action/toastAction";
import * as EmailValidator from "email-validator";

const SubscribeSection = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const [subscribeValue, setSubscribeValue] = useState({
        email: "",
    });

    const onChangeInput = (e) => {
        setSubscribeValue({ email: e.target.value });
    };

    const handleSubscribe = (event) => {
        event.preventDefault();
        if (subscribeValue.email.trim() === "") {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "globally.input.email.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else if (!EmailValidator.validate(subscribeValue.email)) {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "user.input.email.valid.validate.label"
                    ),
                    type: toastType.ERROR,
                })
            );
        } else {
            dispatch(
                addSubscribeUser(
                    { email: subscribeValue.email },
                    navigate,
                    "/home-plans"
                )
            );
            setSubscribeValue({ email: "" });
        }
    };

    return (
        <>
            <section className="subscribe-section">
                <div className="container">
                    <div className="subscribe-box bg-gray-100">
                        <div className="row align-items-center">
                            <div className="col-lg-6 mb-lg-0 mb-4">
                                <h2 className="text-secondary fw-500 mb-0">
                                    {getFormattedMessage("subscribe.now.title")}
                                </h2>
                            </div>
                            <div className="col-lg-6">
                                <form
                                    onSubmit={handleSubscribe}
                                    className="form-group position-relative"
                                >
                                    <input
                                        className="form-control subscribe-input"
                                        type="email"
                                        name="email"
                                        placeholder={placeholderText(
                                            "user.input.email.label"
                                        )}
                                        onChange={onChangeInput}
                                        value={subscribeValue.email}
                                    />
                                    <button
                                        className="btn subscribe-btn h-100"
                                        // disabled={
                                        //     subscribeValue.email.trim()
                                        //         .length === 0
                                        //         ? true
                                        //         : false
                                        // }
                                        onClick={handleSubscribe}
                                    >
                                        {getFormattedMessage(
                                            "globally.subscribe.btn.title"
                                        )}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
};

export default SubscribeSection;
