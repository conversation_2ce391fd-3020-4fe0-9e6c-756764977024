import React, { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
    getFormattedMessage,
    placeholderText,
} from "../../shared/sharedMethod";
import { updateThemeAction } from "../../store/action/adminActions/themeAction";
// import { fetchFrontPages } from "../../store/action/adminActions/pagesAction";
import { fetchFrontPage } from "../../store/action/adminActions/pagesFrontAction";
import DropdownMenu from "../../shared/dropdown/DropdownMenu";
import { environment } from "../../config/environment";

const HomeHeader = () => {
    const { frontSettings, pages } = useSelector((state) => state);
    const [isAdmin, setIsAdmin] = useState(
        JSON.parse(localStorage.getItem("isAdmin"))
    );
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );

    const DarkMod = localStorage.getItem("isDarkMod");
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { name } = useParams();

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, []);

    useEffect(() => {
        // dispatch(fetchFrontPages());
        name !== undefined && dispatch(fetchFrontPage(name));
    }, []);

    useEffect(() => {
        if (DarkMod === "true") {
            setIsDark(true);
            dispatch(updateThemeAction(true));
        } else {
            dispatch(updateThemeAction(false));
            setIsDark(false);
        }
    }, []);

    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        dispatch(updateThemeAction(isDark));
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, [isDark]);

    // const getPages = (e, slug) => {
    //     dispatch(fetchFrontPage(slug));
    //     navigate("/pages/" + slug);
    // };

    return (
        <>
            {/* start header section */}
            <header className="zIndex-3">
                <nav className="navbar navbar-expand-lg navbar-light justify-content-end bg_secondary">
                    <div className="container">
                        <a className="navbar-brand" href="/#/">
                            <img
                                src={
                                    frontSettings?.logo ||
                                    environment.URL +
                                        "/default-images/frontEnd-images/footer-vector.png"
                                }
                                alt="Logo"
                                className="img-fluid navbar-logo"
                            />
                        </a>
                        <button
                            className="navbar-toggler border-0 p-0"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#navbarNav"
                            aria-controls="navbarNav"
                            aria-expanded="false"
                            aria-label="Toggle navigation"
                        >
                            <div
                                className="navbar-toggler-icon"
                                id="toggler-icon"
                            >
                                <span className="icon-bar top-bar" />
                                <span className="icon-bar middle-bar" />
                                <span className="icon-bar bottom-bar" />
                            </div>
                        </button>
                        <div
                            className="collapse navbar-collapse justify-content-lg-between justify-content-end"
                            id="navbarNav"
                        >
                            <ul className="navbar-nav d-flex justify-content-end align-items-lg-center w-100">
                                <li className="nav-item">
                                    <Link
                                        className={`nav-link ${
                                            window.location.hash === "#/"
                                                ? "active"
                                                : ""
                                        }`}
                                        to="/"
                                    >
                                        {getFormattedMessage(
                                            "globally.home.title"
                                        )}
                                    </Link>
                                </li>
                                <li className="nav-item">
                                    <Link
                                        className={`nav-link ${
                                            window.location.href.includes(
                                                "contact"
                                            )
                                                ? "active"
                                                : ""
                                        }`}
                                        to="/contact"
                                    >
                                        {getFormattedMessage(
                                            "globally.contact.title"
                                        )}
                                    </Link>
                                </li>
                                <li className="nav-item">
                                    <Link
                                        className={`nav-link ${
                                            window.location.href.includes(
                                                "home-plans"
                                            )
                                                ? "active"
                                                : ""
                                        }`}
                                        to="/home-plans"
                                    >
                                        {getFormattedMessage("plans.title")}
                                    </Link>
                                </li>
                                {/* <li className="nav-item">
                                    <DropdownMenu
                                        title={placeholderText(
                                            "settings.select.pages.label"
                                        )}
                                        list={pages?.filter(
                                            (item, index) =>
                                                item?.visibility === 1
                                        )}
                                        itemClickFunc={getPages}
                                    />
                                </li> */}
                                <div className="mt-lg-0 mt-2">
                                    {isAdmin === null ? (
                                        <>
                                            <Link
                                                to="/login"
                                                type="button"
                                                className="btn btn-primary active mx-lg-3 ms-0 me-3"
                                            >
                                                {getFormattedMessage(
                                                    "login-form.title"
                                                )}
                                            </Link>
                                            <Link
                                                to="/register"
                                                type="button"
                                                className="btn btn-primary"
                                            >
                                                {getFormattedMessage(
                                                    "globally.sign-up.label"
                                                )}
                                            </Link>
                                        </>
                                    ) : (
                                        <Link
                                            to={
                                                isAdmin
                                                    ? "/app/admin/dashboard"
                                                    : "/app/dashboard"
                                            }
                                            type="button"
                                            className="btn btn-primary"
                                        >
                                            {getFormattedMessage(
                                                "dashboard.title"
                                            )}
                                        </Link>
                                    )}
                                </div>
                            </ul>
                        </div>
                    </div>
                </nav>
            </header>
            {/* end header section */}
        </>
    );
};

export default HomeHeader;
