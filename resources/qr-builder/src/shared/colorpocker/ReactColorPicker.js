import React from 'react'
import { SketchPicker } from 'react-color'
import reactCSS from 'reactcss'

class ReactColorPicker extends React.Component {

  state = {
    showPicker: false,
    color: { hex: this.props.selectedColor === "" || this.props.selectedColor === null ? "#A561FF" : this.props.selectedColor },
  };

  componentDidMount () {
    this.props.onChangeColor( this.state.color )
  }

  onClick = () => {
    this.setState( {
      showPicker: !this.state.showPicker
    } )
  };

  onClose = () => {
    this.setState( {
      showPicker: false
    } )
  };

  onChange = ( color ) => {
    this.setState( {
      color: { hex: color.hex },
    } )
    this.props.onChangeColor( color )
  };

  render () {
    const styles = reactCSS( {
      'default': {
        color: {
          width: '40px',
          height: `${this.props.class ? this.props.class : '25px'}`,
          borderRadius: '3px',
          background: `${this.props.selectedColor ? this.props.selectedColor : this.state.color.hex}`,
        },
        popover: {
          position: 'absolute',
          zIndex: '3',
        },
        cover: {
          position: 'fixed',
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        swatch: {
          padding: '6px',
          background: '#ffffff',
          borderRadius: '2px',
          cursor: 'pointer',
          display: 'inline-block',
          boxShadow: '0 0 0 1px rgba(0,0,0,.2)',
        },
      },
    } );

    return (
      <div className='w-100 color-picker'>
        <div className='w-100' onClick={this.onClick}>
          <div style={styles.color} className='w-100 custome_color_picker' />
        </div>
        {this.state.showPicker ? <div style={styles.popover}>
          <div style={styles.cover} onClick={this.onClose} />
          <SketchPicker color={this.state.color} onChange={this.onChange} />
        </div> : null}

      </div>
    )
  }
}

export default ReactColorPicker
