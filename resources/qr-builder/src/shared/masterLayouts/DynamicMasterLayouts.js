import AdminMasterLayout from "../../frontend/components/MasterLayout";
import UserMasterLayout from "../../components/MasterLayout";


const DynamicLayout = ({ children }) => {
    const isAdmin = JSON.parse(localStorage.getItem('isAdmin'))
    if (isAdmin) {
        return <AdminMasterLayout>{children}</AdminMasterLayout>
    } else {
        return <UserMasterLayout>{children}</UserMasterLayout>
    }
}
export default DynamicLayout;
