import React, { useCallback, useEffect, useRef } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { Dropdown } from 'react-bootstrap';
import ReactSelect from '../select/reactSelect';
import { getFormattedMessage, getFormattedOptions } from '../sharedMethod';
import { faFilter } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { paymentStatusOptions, paymentTypeOptions, planStatusOptions } from "../../constants";

const FilterDropdown = (props) => {
    const {
        planStatus,
        onPaymentStatusChange,
        onResetClick,
        isPlanStatus,
        isResetBtn
    } = props;

    const dispatch = useDispatch();
    const isReset = useSelector((state) => state.resetOption);
    const isShow = useSelector((state) => state.dropDownToggle);
    const menuRef = useRef(null);
    const paymentFilterOptions = getFormattedOptions(planStatusOptions)

    const paymentStatusDefaultValue = paymentFilterOptions.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const onReset = () => {
        dispatch({ type: 'RESET_OPTION', payload: true })
        onResetClick();
    };

    const onToggle = () => {
        dispatch({ type: 'ON_TOGGLE', payload: !isShow })
    };

    const escFunction = useCallback((event) => {
        if (event.keyCode === 27) {
            dispatch({ type: 'ON_TOGGLE', payload: false })
        }
    }, []);

    useEffect(() => {
        document.addEventListener('keydown', escFunction, false);
        return () => {
            document.removeEventListener('keydown', escFunction, false);
        };
    }, []);

    useEffect(() => {
        const onClickOutside = (event) => {
            if (menuRef.current.contains(event.target)) {
                return
            }
            dispatch({ type: 'ON_TOGGLE', payload: false })
        };
        document.body.addEventListener("click", onClickOutside)
        return () => {
            document.body.removeEventListener("click", onClickOutside);
        }
    }, [])

    return (
        <Dropdown className='me-3 mb-2 filter-dropdown order-1 order-sm-0' show={isShow} ref={menuRef}>
            <Dropdown.Toggle variant='primary' className='text-white btn-icon hide-arrow' id='filterDropdown'
                onClick={() => onToggle()}>
                <FontAwesomeIcon icon={faFilter} />
            </Dropdown.Toggle>
            <Dropdown.Menu className='px-7 py-5'>
                    {isPlanStatus ?
                    <Dropdown.Header onClick={(e) => {
                        e.stopPropagation();
                    }} eventkey='2' className='mb-5 p-0'>
                        <ReactSelect multiLanguageOption={paymentFilterOptions} onChange={onPaymentStatusChange} name='plan_status'
                            title={getFormattedMessage('dashboard.recentSales.status.label')}
                            value={isReset ? paymentStatusDefaultValue[0] : planStatus} isRequired
                            defaultValue={paymentStatusDefaultValue[0]}
                            placeholder={getFormattedMessage('dashboard.recentSales.status.label')}
                        />
                    </Dropdown.Header>
                    : null}

                {isResetBtn ? <div className='btn btn-secondary me-5' onClick={onReset}>{getFormattedMessage("date-picker.filter.reset.label")}</div> : '' }
            </Dropdown.Menu>
        </Dropdown>
    )
};

export default connect(null, {})(FilterDropdown);
