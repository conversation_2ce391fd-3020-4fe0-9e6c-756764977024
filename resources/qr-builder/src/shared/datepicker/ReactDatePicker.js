import React, { useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCalendarAlt } from '@fortawesome/free-solid-svg-icons';
import { registerLocale } from "react-datepicker";
import { enGB, es, de, tr, fr, ar, vi, zhCN } from "date-fns/locale";
import { useSelector } from "react-redux";
import { Tokens } from "../../constants";
import { range } from 'lodash';
import { getMonth, getYear } from 'date-fns';
import moment from 'moment';

const ReactDatePicker = (props) => {
    const { onChangeDate, newStartDate, isShowTimeSelect = false, minDate, maxDate, selectYear = false } = props;
    const [startDate, setStartDate] = useState();
    const [language, setLanguage] = useState(enGB);
    const [languageCode, setLanguageCode] = useState("enGB");
    const years = range(1990, getYear(new Date()) + 1, 1);
    const months = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE)
    const { selectedLanguage, updateLanguage } = useSelector(state => state)
    const messages = updatedLanguage ? updatedLanguage : selectedLanguage;

    useEffect(() => {
        if (messages === "en") {
            setLanguage(enGB)
            setLanguageCode("enGB")
        } else if (messages === "sp") {
            setLanguage(es)
            setLanguageCode("es")
        } else if (messages === "gr") {
            setLanguage(de)
            setLanguageCode("de")
        } else if (messages === "fr") {
            setLanguage(fr)
            setLanguageCode("fr")
        } else if (messages === "ar") {
            setLanguage(ar)
            setLanguageCode("ar")
        } else if (messages === "tr") {
            setLanguage(tr)
            setLanguageCode("tr")
        } else if (messages === "vi") {
            setLanguage(vi)
            setLanguageCode("vi")
        } else if (messages === "cn") {
            setLanguage(zhCN)
            setLanguageCode("cn")
        }
    }, [messages])

    registerLocale(language, languageCode);

    const handleCallback = (date) => {
        setStartDate(date);
        onChangeDate(date);
    };

    useEffect(() => {
        setStartDate(startDate);
    }, [startDate]);

    const onDatepickerRef = (el) => {
        if (el && el.input) {
            el.input.readOnly = true;
        }
    };

    const format = () => {
        let format

        if (format === "d-m-y") {
            return isShowTimeSelect ? 'dd-MM-yyyy h:mm aa' : "dd-MM-yyyy"
        }
        else if (format === "m-d-y") {
            return isShowTimeSelect ? "MM-dd-yyyy h:mm aa" : "MM-dd-yyyy"
        } else if (format === "y-m-d") {
            return isShowTimeSelect ? "yyyy-MM-dd h:mm aa" : "yyyy-MM-dd"
        } else if (format === "m/d/y") {
            return isShowTimeSelect ? "MM/dd/yyyy h:mm aa" : "MM/dd/yyyy"
        } else if (format === "d/m/y") {
            return isShowTimeSelect ? "dd/MM/yyyy h:mm aa" : "dd/MM/yyyy"
        } else if (format === "y/m/d") {
            return isShowTimeSelect ? "yyyy/MM/dd h:mm aa" : "yyyy/MM/dd"
        } else if (format === "m.d.y") {
            return isShowTimeSelect ? "MM.dd.yyyy h:mm aa" : "MM.dd.yyyy"
        } else if (format === "d.m.y") {
            return isShowTimeSelect ? "dd.MM.yyyy h:mm aa" : "dd.MM.yyyy"
        } else if (format === "y.m.d") {
            return isShowTimeSelect ? "yyyy.MM.dd h:mm aa" : "yyyy.MM.dd"
        }
        else { return isShowTimeSelect ? 'yyyy-MM-dd h:mm aa' : "yyyy-MM-dd" }
    }

    return (
        <div className='position-relative datepicker p-0'>
            <DatePicker wrapperClassName='w-100' locale={language} className='datepicker__custom-datepicker px-4'
                name='date'
                selected={newStartDate ? newStartDate : startDate}
                dateFormat={format()}
                onChange={(date) => handleCallback(date)}
                showTimeSelect={isShowTimeSelect}
                ref={el => onDatepickerRef(el)}
                minDate={minDate}
                maxDate={maxDate}
                renderCustomHeader={({
                    date,
                    changeYear,
                    changeMonth,
                    decreaseMonth,
                    increaseMonth,
                    prevMonthButtonDisabled,
                    nextMonthButtonDisabled,
                }) => (
                    selectYear && <div style={{
                        margin: 10,
                        display: "flex",
                        justifyContent: "center",
                    }} >
                        <select value={getYear(date)}
                            onChange={({ target: { value } }) => changeYear(value)}   >
                            {years.map((option) => (
                                <option key={option} value={option}>
                                    {option}
                                </option>
                            ))}
                        </select>

                        <select value={months[getMonth(date)]}
                            onChange={({ target: { value } }) =>
                                changeMonth(months.indexOf(value))
                            }   >
                            {months.map((option) => (
                                <option key={option} value={option}>
                                    {option}
                                </option>
                            ))}
                        </select>
                    </div>
                )}
            />
            <FontAwesomeIcon icon={faCalendarAlt} className='input-icon' />
        </div>
    )
}

export default ReactDatePicker;
