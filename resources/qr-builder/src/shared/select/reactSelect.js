import React, { useEffect } from 'react';
import { Form } from 'react-bootstrap-v5';
import Select from 'react-select';
import { productActionType } from "../../constants";
import { useDispatch, useSelector } from "react-redux";
import { getFormattedMessage } from '../sharedMethod';

const ReactSelect = (props) => {
    const { title, placeholder, data, defaultValue, onChange, errors, value, isRequired, multiLanguageOption, isWarehouseDisable, addSearchItems, className, disabled } = props;
    const dispatch = useDispatch();
    const isOptionDisabled = useSelector((state) => state.isOptionDisabled);

    const option = data ? data.map((da) => {
        return {
            value: da.value ? da.value : da.id ? da?.tenant_id ? da?.tenant_id : da.id : da.id,
            label: da.label ? da.label : da.attributes?.symbol ? da.attributes?.symbol : da.attributes?.name ? da.attributes?.name : da.name
        }
    }) : multiLanguageOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })


    useEffect(() => {
        addSearchItems ? dispatch({ type: 'DISABLE_OPTION', payload: true }) : dispatch({ type: 'DISABLE_OPTION', payload: false })
    }, []);

    const styles = {
        option: (provided, state) => ({
            ...provided,
            cursor: "pointer",
        }),
    };

    return (
        <Form.Group className={`form-group w-100 ${className || ""}`} controlId='formBasic'>
            {title ? <Form.Label>{title}:</Form.Label> : ''}
            {isRequired ? '' : <span className='required' />}
            <Select
                className={className}
                placeholder={placeholder}
                value={value}
                defaultValue={defaultValue}
                onChange={onChange}
                options={option}
                styles={styles}
                noOptionsMessage={() => getFormattedMessage('no-option.label')}
                isDisabled={disabled}
                disabled={disabled}
            />
            {errors ? <span className='text-danger d-block fw-400 fs-small mt-2'>{errors ? errors : null}</span> : null}
        </Form.Group>
    )
};
export default ReactSelect;
