import React from 'react';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap-v5';
import { Link } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from "../sharedMethod";
import { useSelector } from "react-redux";

const ModelFooter = (props) => {
    const { onEditRecord, onSubmit, editDisabled, clearField, addDisabled, link, ref, modelhide, cancelNotShow, className, onClickCancelButton } = props;
    const { isSaving, isLoading } = useSelector(state => state)

    return (
        <>
            {
                link ?
                    <div className={`d-flex ${className !== "mt-0" ? "mt-5" : "mt-0"} justify-content-start`}>
                        {isLoading ? <button className='btn btn-primary me-3' ref={ref} type='submit' disabled={addDisabled || isSaving}>
                            <div className="spinner-border text-dark" style={{ width: '1rem', height: '1rem' }} role="status">
                                <span className="sr-only">Loading...</span>
                            </div>
                        </button>
                            :
                            onEditRecord ?
                                <button onClick={(event) => onSubmit(event)} className='btn btn-primary me-3' type='submit'
                                    disabled={editDisabled || isSaving} ref={ref}>
                                    {isSaving ? placeholderText("globally-saving-btn-label") : placeholderText("globally.save-btn")}
                                </button>
                                :
                                <button onClick={(event) => onSubmit(event)} className='btn btn-primary me-3' type='submit'
                                    disabled={addDisabled || isSaving} ref={ref}>
                                    {isSaving ? placeholderText("globally-saving-btn-label") : placeholderText("globally.save-btn")}
                                </button>


                        }
                        {modelhide ?
                            <Link to={link} onClick={() => modelhide(false)}
                                className='btn btn-secondary'>
                                {getFormattedMessage("globally.cancel-btn")}
                            </Link>
                            :
                            <Link to={link}
                                onClick={() => onClickCancelButton && onClickCancelButton()}
                                className='btn btn-secondary'>
                                {getFormattedMessage("globally.cancel-btn")}
                            </Link>
                        }
                    </div> :
                    <Modal.Footer children='justify-content-start' className='pt-0 justify-content-start'>
                        {isLoading ? <button className='btn btn-primary me-2' ref={ref} type='submit' disabled={addDisabled || isSaving}>
                            <div className="spinner-border text-dark" style={{ width: '1rem', height: '1rem' }} role="status">
                                <span className="sr-only">Loading...</span>
                            </div>
                        </button>
                        :
                         onEditRecord ?
                            <button onClick={(event) => onSubmit(event)} className='btn btn-primary me-2' type='submit'
                                disabled={editDisabled}>
                                {placeholderText("globally.save-btn")}
                            </button> :
                            <button onClick={(event) => onSubmit(event)} className='btn btn-primary me-2' type='submit'
                                disabled={addDisabled}>
                                {placeholderText("globally.save-btn")}
                            </button>
                        }
                        {!cancelNotShow ?
                            <button onClick={() => clearField(false)}
                                className='btn btn-secondary'>
                                {getFormattedMessage("globally.cancel-btn")}
                            </button> : null}

                    </Modal.Footer>
            }
        </>
    )
};
export default ModelFooter;
