import React, { useEffect, useState } from "react";
import { updateThemeAction } from "../../../store/action/adminActions/themeAction";
import { useDispatch } from "react-redux";
import { cssHandling } from "../../../cssHandling/cssHandling";
import { Tokens } from "../../../constants";
import <PERSON>tie from "react-lottie-player";
import loader from "./loader.json";

const WindowSpinner = () => {
    const [isDark, setIsDark] = useState(
        JSON.parse(localStorage.getItem("isDarkMod"))
    );
    const dispatch = useDispatch();
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    useEffect(() => {
        localStorage.setItem("isDarkMod", isDark);
        dispatch(updateThemeAction(isDark));
        if (isDark === true) {
            document.body.classList.add("dark-theme");
        } else {
            document.body.classList.remove("dark-theme");
        }
    }, [isDark]);

    useEffect(() => {
        cssHandling(updatedLanguage);
        return () => cssHandling(updatedLanguage);
    }, []);

    return (
        <Lottie
            loop
            animationData={loader}
            play
            style={{ width: "50%", height: "100vh", margin: "0 auto" }}
            className="window_loader"
        />
    );
};

export default WindowSpinner;
