.loader-parent{
    height: 100vh !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  .loader {
    width: 112px;
    height: 112px;
  }

  .dark-theme .box1{
    box-shadow: none
  }
  .dark-theme .box2{
    box-shadow: none
  }
  .dark-theme .box3{
    box-shadow: none
  }

  .box1,
  .box2,
  .box3 {
    border: 16px solid #A561FF;
    box-sizing: border-box;
    position: absolute;
    display: block;
    box-shadow:  6px 6px 14px #cacaca,
               -6px -6px 14px #f6f6f6;
  }

  .box1 {
    width: 112px;
    height: 48px;
    margin-top: 64px;
    margin-left: 0px;
    animation: abox1 2.5s 0s forwards ease-in-out infinite;
  }

  .box2 {
    width: 48px;
    height: 48px;
    margin-top: 0px;
    margin-left: 0px;
    animation: abox2 2.5s 0s forwards ease-in-out infinite;
  }

  .box3 {
    width: 48px;
    height: 48px;
    margin-top: 0px;
    margin-left: 64px;
    animation: abox3 2.5s 0s forwards ease-in-out infinite;
  }

  @keyframes abox1 {
    0% {
      width: 112px;
      height: 48px;
      margin-top: 64px;
      margin-left: 0px;
    }

    12.5% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 0px;
    }

    25% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 0px;
    }

    37.5% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 0px;
    }

    50% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 0px;
    }

    62.5% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 0px;
    }

    75% {
      width: 48px;
      height: 112px;
      margin-top: 0px;
      margin-left: 0px;
    }

    87.5% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 0px;
    }

    100% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 0px;
    }
  }

  @keyframes abox2 {
    0% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 0px;
    }

    12.5% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 0px;
    }

    25% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 0px;
    }

    37.5% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 0px;
    }

    50% {
      width: 112px;
      height: 48px;
      margin-top: 0px;
      margin-left: 0px;
    }

    62.5% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 64px;
    }

    75% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 64px;
    }

    87.5% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 64px;
    }

    100% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 64px;
    }
  }

  @keyframes abox3 {
    0% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 64px;
    }

    12.5% {
      width: 48px;
      height: 48px;
      margin-top: 0px;
      margin-left: 64px;
    }

    25% {
      width: 48px;
      height: 112px;
      margin-top: 0px;
      margin-left: 64px;
    }

    37.5% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 64px;
    }

    50% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 64px;
    }

    62.5% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 64px;
    }

    75% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 64px;
    }

    87.5% {
      width: 48px;
      height: 48px;
      margin-top: 64px;
      margin-left: 64px;
    }

    100% {
      width: 112px;
      height: 48px;
      margin-top: 64px;
      margin-left: 0px;
    }
  }

 /* new loader */

 @keyframes move {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  70% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

 .example {
    position: fixed;
    top: 50%;
    left: 50%;
    height: 2rem;
    width: 2rem;
    transform: translateX(-50%) translateY(-50%);
}

.block {
  position: absolute;
  top: 0;
  left: 0;
  height: 2rem;
  width: 2rem;
}
.block > .item {
  position: absolute;
  height: 2rem;
  width: 2rem;
  background: #A561FF;
  animation: move 0.5s linear infinite;
}
.block > .item:nth-of-type(1) {
  top: -2rem;
  left: -2rem;
  animation-delay: 0s;
}
.block > .item:nth-of-type(2) {
  top: -2rem;
  left: 0;
  animation-delay: -0.5s/8;
}
.block > .item:nth-of-type(3) {
  top: -2rem;
  left: 2rem;
  animation-delay: -0.125s;
}
.block > .item:nth-of-type(4) {
  top: 0;
  left: 2rem;
  animation-delay: -0.1875s;
}
.block > .item:nth-of-type(5) {
  top: 2rem;
  left: 2rem;
  animation-delay: -0.25s;
}
.block > .item:nth-of-type(6) {
  top: 2rem;
  left: 0;
  animation-delay: -0.3125s;
}
.block > .item:nth-of-type(7) {
  top: 2rem;
  left: -2rem;
  animation-delay: -0.375s;
}
.block > .item:nth-of-type(8) {
  top: 0;
  left: -2rem;
  animation-delay: -0.4375s;
}

