import React from "react";
import './Spinner.scss'

const Spinner = (props) => {
    const {columns} = props
    const numberOfRows = 6;
    const totalCol = 5

    const renderedRows = [...Array(numberOfRows)].map((e, i) => (
           <div className="skeleton" key={i}></div>
      ));

    return <>
    <div className="maindata">
    <div className="animated-background">
    <div className="background-masker" />
    </div>
        { [...Array(totalCol)].map((e, i) => (
            <div className="rowdata" key={i}>{renderedRows}</div>
      ))}
    </div></>;
}

export default Spinner;
