import React from 'react';
import { Helmet } from 'react-helmet';
import { useSelector } from "react-redux";

const TabTitle = (props) => {
    const { title } = props;
    const { frontSettings } = useSelector(state => state)

    return (
        <Helmet>
            <title>{title !== undefined ? title + '  | ' : ""} {frontSettings ? `${frontSettings?.title !== undefined ? frontSettings?.title : ""}` : ""}</title>
            {frontSettings && <link rel="icon" type="image/png" href={frontSettings ? frontSettings?.favicon : "./../../../public/favicon.ico"} sizes="16x16" />}
        </Helmet>
    )
}

export default TabTitle;
