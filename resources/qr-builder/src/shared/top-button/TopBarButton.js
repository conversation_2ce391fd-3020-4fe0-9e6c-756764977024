import { faAngleUp } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, { useEffect, useRef } from "react";

const TopBarButton = () => {
    const scrollTop = useRef();

    useEffect(() => {
        window.addEventListener("scroll", (e) => {
            if (scrollTop.current) {
                window.scrollY > 200
                    ? (scrollTop.current.style.display = "flex")
                    : (scrollTop.current.style.display = "none");
            }
        });
    }, []);

    const onClickScrollToTop = () => {
        window.scroll({
            top: 0,
            left: 0,
            behavior: "smooth",
        });
    };

    return (
        <>
            <span
                onClick={onClickScrollToTop}
                className="circle cursor-pointer align-items-center justify-content-center"
                ref={scrollTop}
            >
                <FontAwesomeIcon icon={faAngleUp} />
            </span>
        </>
    );
};

export default TopBarButton;
