import React from 'react'
import { getFormattedMessage } from '../sharedMethod'

const DropdownMenu = ({ title, list, itemClickFunc }) => {
    return (
        <>

            <div className='custom_dropdown'>
                <div className="nav__menu-item nav-item">
                    <div className={`nav-link ${window.location.href.toLowerCase().includes(title.toLowerCase()) ? 'active' : ''}`}>
                        {title}
                    </div>
                    <ul className="nav__submenu">
                        {
                            list?.length > 0 ?
                                list?.map((item, index) => {
                                    return <li className="nav__submenu-item px-3 py-2" key={index + 1} onClick={(e) => itemClickFunc(e, item?.slug, item.id)}>
                                        {item?.title}
                                    </li>
                                })
                                :
                                <li className='p-1 ps-3'>
                                    {getFormattedMessage("no-data-available.title")}
                                </li>
                        }
                    </ul>
                </div>

            </div>
        </>
    )

}

export default DropdownMenu
