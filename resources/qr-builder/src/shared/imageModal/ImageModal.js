import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClose, faDownload } from '@fortawesome/free-solid-svg-icons';

function ImageModal({ display, closeSlider, onDownloadClick }) {

    return (
        <div className={` ${display.display ? "d-flex h-100vh w-100 image_modal_parent position-fixed " : "d-none"} `}>
                <div>
                    <div className='d-flex flex-row flex-wrap align-content-center align-items-center justify-content-between mb-5'>
                        <div className='text-white'>
                     <FontAwesomeIcon icon={faDownload} className={'fs-1 cursor-pointer'} onClick={(e) => onDownloadClick(e, display.src, display.name, "JPEG")} />
                        </div>
                        <div className='text-white'>
                        <FontAwesomeIcon icon={faClose} className={'fs-1 cursor-pointer'} onClick={() => {
                        closeSlider()
                    }} />
                        </div>
                    </div>
                    <div>
                        <div>
                        <img
                        className="d-block img-fluid"
                        src={display?.src}
                        alt="First slide"
                        />
                        </div>
                    </div>
                </div>
        </div>
    );

}

export default ImageModal;
