import { useState } from "react";
import { getFormattedMessage, placeholderText } from "../sharedMethod";
import { InputGroup } from "react-bootstrap-v5";
import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";

const PasswordHandling = (props) => {
    const {
        onChangeInput,
        passwordValue,
        IsRequired,
        showForgotPassword = false,
        forgotPasswordURL,
        current_password = false,
        new_password = false,
    } = props;
    const [passwordType, setPasswordType] = useState("password");
    const togglePassword = () => {
        if (passwordType === "password") {
            setPasswordType("text");
        } else {
            setPasswordType("password");
        }
    };

    return (
        <>
            <div className="d-flex justify-content-between mt-n5">
                <div className="d-flex justify-content-between w-100">
                    <label className="form-label">
                        {current_password
                            ? getFormattedMessage(
                                  "change-password.input.current.label"
                              )
                            : new_password
                            ? getFormattedMessage(
                                  "change-password.input.new.label"
                              )
                            : getFormattedMessage("user.input.password.label")}
                        :{IsRequired ? <span className="required" /> : ""}
                    </label>
                    {showForgotPassword && (
                        <Link
                            to={forgotPasswordURL}
                            className="text-secondary fs-6 text-decoration-none"
                        >
                            {getFormattedMessage(
                                "login-form.forgot-password.label"
                            )}
                        </Link>
                    )}
                </div>
            </div>
            <InputGroup>
                <input
                    type={passwordType}
                    name={
                        current_password
                            ? "current_password"
                            : new_password
                            ? "new_password"
                            : "password"
                    }
                    className="form-control"
                    placeholder={
                        current_password
                            ? placeholderText(
                                  "change-password.input.current.label"
                              )
                            : new_password
                            ? placeholderText("change-password.input.new.label")
                            : placeholderText("user.input.password.label")
                    }
                    onChange={(e) => onChangeInput(e)}
                    value={passwordValue}
                    autoComplete={"new-password"}
                />
                <InputGroup.Text
                    className="cursor-pointer"
                    onClick={() => togglePassword()}
                >
                    {passwordType === "password" ? (
                        <FontAwesomeIcon icon={faEyeSlash} />
                    ) : (
                        <FontAwesomeIcon icon={faEye} />
                    )}
                </InputGroup.Text>
            </InputGroup>
        </>
    );
};
export default PasswordHandling;
