import React, { useState } from 'react'
import { getFormattedMessage, placeholderText } from '../sharedMethod'
import { InputGroup } from 'react-bootstrap-v5';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';

const ConfrimPasswordHandling = (props) => {
    const { onChangeInput, passwordValue, IsRequired = false } = props
    const [passwordType, setPasswordType] = useState("password");
    const togglePassword = () => {
        if (passwordType === "password") {
            setPasswordType("text")
            return;
        }
        setPasswordType("password")
    }
    return (
        <>
            <label className='form-label'>
                {getFormattedMessage("user.input.confirm-password.label")}:{IsRequired ? <span className='required' /> : ''}
            </label>
            <InputGroup>
                <input type={passwordType} name='confirm_password'
                    className='form-control'
                    placeholder={placeholderText("user.input.confirm-password.label")}
                    onChange={(e) => onChangeInput(e)}
                    value={passwordValue}
                    autoComplete={"new-password"} />
                <InputGroup.Text className="cursor-pointer" onClick={togglePassword}>
                    {passwordType === "password" ? <FontAwesomeIcon icon={faEyeSlash} /> : <FontAwesomeIcon icon={faEye} />}
                </InputGroup.Text>
            </InputGroup>
        </>
    )
}
export default ConfrimPasswordHandling
