import React from "react";
import { Tooltip } from "react-bootstrap";

const Widget = (props) => {
    const {
        title,
        value,
        currencySym,
        icon,
        className,
        onClick,
        isAmount = false,
    } = props;

    const renderTooltip = (props) => (
        <Tooltip id="button-tooltip" {...props}>
            {value}
        </Tooltip>
    );

    return (
        <>
            <div className="col-xxl-3 col-xl-3 col-sm-6 widget dashboard-widget mb-3">
                <div
                    className={`${className} gradient-style1 text-white box-shadow border-radius-10 height-100-p widget-style3`}
                    onClick={onClick}
                >
                    <div className="d-flex flex-wrap align-items-center">
                        <div className="widget-data">
                            <div className="weight-400 font-20">{title}</div>
                            <div className="weight-300 font-30 ">
                                {currencySym}{" "}
                                <span
                                    className="widgetValue"
                                    animation_on_amount={`${isAmount}`}
                                    animation_amount={`${value}`}
                                >
                                    0
                                </span>
                            </div>
                        </div>
                        <div className="icon">
                            <div className="icon">{icon}</div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};
export default Widget;
