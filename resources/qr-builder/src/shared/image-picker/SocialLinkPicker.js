import React from 'react';
import { getFormattedMessage, placeholderText } from '../sharedMethod';
import { faPencil, faQuestionCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const SocialLinkPicker = (props) => {
    const { imagePreviewUrl, handleImageChange, imageTitle, avtarName, user, isRequired, tooltipContent, tooltipId, isEdite, tooltip = false, index, classs } = props;
    let fileInput = React.createRef();

    return (
        <div className='w-auto'>
            {/* <div className='d-block'> */}
                <div
                    className='social-image-picker image-picker'>
                    <div
                        className={`image previewImage imagePreviewUrl ${imagePreviewUrl ? null : "d-flex justify-content-center align-items-center"}`}>
                        {imagePreviewUrl ?
                            <img src={imagePreviewUrl ? imagePreviewUrl : null} alt='img' width={50}
                                height={50} className={`image image-circle image-mini h-100 ${classs}`} />
                            : avtarName ?
                                <span className='custom-user-avatar w-100 h-100'>{avtarName}</span>
                                :
                                <img src={user ? user : null} alt='img' width={50} height={50}
                                    className='image image-circle image-mini h-100' />
                        }
                        {isEdite && <span
                            className='picker-edit rounded-circle text-gray-500 fs-small cursor-pointer'>
                            <input
                                className='upload-file cursor-pointer'
                                title={`${placeholderText('update-profile.input.change-image.label')}`}
                                type='file'
                                accept='.png, .jpg, .jpeg, .svg'
                                onChange={(e) => handleImageChange(e, index)}
                                ref={fileInput}
                            />
                            <FontAwesomeIcon icon={faPencil} />
                        </span>}
                    </div>
                </div>
            {/* </div> */}
        </div>)
}

export default SocialLinkPicker;

