import React from 'react'
import { But<PERSON> } from 'react-bootstrap'
import { <PERSON> } from 'react-router-dom'


const OverviewFooter = (props) => {


    const { createTitle, createURL, editTitle, editURL, cancelURL, cancelTitle, backURL, backTitle } = props

    return (
        <>
            {editURL && <Link to={editURL} className="me-2">
                <Button>
                    {editTitle}
                </Button>
            </Link>}
            {createURL && <Link to={createURL} className="me-2">
                <Button>
                    {createTitle}
                </Button>
            </Link>}
            {cancelURL && <Link to={cancelURL} className="me-2">
                <Button>
                    {cancelTitle}
                </Button>
            </Link>}
            {backURL && <Link to={backURL}>
                <Button>
                    {backTitle}
                </Button>
            </Link>}
        </>
    )
}


export default OverviewFooter
