import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChartLine, faEye, faPaste, faPenToSquare, faTrash } from '@fortawesome/free-solid-svg-icons';
import { placeholderText } from '../sharedMethod';

const ActionButton = (props) => {
    const { goToEditProduct, item, isAnalytics = false, goToAnalytics, onClickDeleteModel = true, isDeleteMode = true, isEditMode = true, goToDetailScreen, isViewIcon = false, isCopyBtn, onCopybtn } = props;
    return (
        <>
        {isAnalytics ?
                <button title={placeholderText('globally.input.analytics.lable')}
                    className='btn text-success fs-3 border-0 px-xxl-1 px-1 cursor-pointer pe-1 me-1'
                    onClick={(e) => {
                        e.stopPropagation();
                        goToAnalytics(item)
                    }}>
                    <FontAwesomeIcon icon={faChartLine} />
                </button> : null
            }
            {isCopyBtn ?
                <button title={placeholderText('globally.copy-link.tool-tip.message')}
                    className='btn text-blue coppyBtn px-1 fs-3 ps-0 border-0'
                    onClick={(e) => {
                        e.stopPropagation();
                        onCopybtn(item)
                    }}>
                    <FontAwesomeIcon icon={faPaste} />
                </button> : null
            }
            {isViewIcon ?
                <button title={placeholderText('globally.view.tooltip.label')}
                    className='btn text-success px-1 fs-3 ps-0 border-0'
                    onClick={(e) => {
                        e.stopPropagation();
                        goToDetailScreen(item.id)
                    }}>
                    <FontAwesomeIcon icon={faEye} />
                </button> : null
            }
            {item.name === 'admin' || item.email === '<EMAIL>' || isEditMode === false ? null :
                <button title={placeholderText('globally.edit.tooltip.label')}
                    className='btn text-primary fs-3 border-0 px-xxl-1 px-1'
                    onClick={(e) => {
                        e.stopPropagation();
                        goToEditProduct(item);
                    }}
                >
                    <FontAwesomeIcon icon={faPenToSquare} />
                </button>
            }
            {item.name === 'admin' || item.email === '<EMAIL>' || isDeleteMode === false ? null :
                <button title={placeholderText('globally.delete.tooltip.label')}
                    className='btn px-1 pe-0 text-danger fs-3 border-0'
                    onClick={(e) => {
                        e.stopPropagation();
                        onClickDeleteModel(item);
                    }}
                >
                    <FontAwesomeIcon icon={faTrash} />
                </button>
            }
        </>
    )
};
export default ActionButton;
