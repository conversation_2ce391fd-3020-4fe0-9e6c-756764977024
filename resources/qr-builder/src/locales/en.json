{"dashboard.title": "Dashboard", "header.profile-menu.logout.label": "Logout", "header.profile-menu.profile.label": "Profile", "update-profile.input.change-image.label": "Change Image", "update-profile.input.change-profile.label": "Change Profile", "header.profile-menu.change-password.label": "Change Password", "header.profile-menu.change-language.label": "Change Language", "dashboard.recentSales.status.label": "Status", "dashboard.recentUser.title": "Recent Registered Users", "dashboard.ThisWeekEarnings.title": "Last 7 day's Earning", "dashboard.5mostvisitedlinksof.title": "Top 5 most visited URL", "dashboard.Earning.title": "Earning", "globally.input.email.placeholder.label": "Email", "settings.select.language.label": "Language", "settings.select.languages.label": "Languages", "settings.select.language.placeholder": "Select Language", "settings.select.currency.placeholder": "Select Currency", "settings.select.language.arabic.label": "Arabic", "settings.select.language.chinese.label": "Chinese", "settings.select.language.english.label": "English", "settings.select.language.french.label": "French", "settings.select.language.german.label": "German", "settings.select.language.hindi.label": "Hindi", "settings.select.language.italian.label": "Italian", "settings.select.language.persian.label": "Persian", "settings.select.language.portuguese.label": "Portuguese", "settings.select.language.russian.label": "Russian", "settings.select.language.spanish.label": "Spanish", "settings.select.language.turkish.label": "Turkish", "users.title": "Users", "user.create.title": "Add User", "user.edit.title": "Edit User", "user.input.first-name.label": "First Name", "user.input.last-name.label": "Last Name", "user.input.email.label": "Email", "user.input.phone-number.label": "Phone Number", "user.input.password.label": "Password", "user.input.confirm-password.label": "Confirm Password", "user.input.role.label": "Role", "user.input.first-name.placeholder.label": "Enter First Name", "user.input.name.placeholder.label": "Enter Name", "user.input.name.percentage.label": "Enter Percentage", "user.input.price.placeholder.label": "Enter Price", "user.input.qr-code-limit.placeholder.label": "Enter QR Codes Limit", "user.input.order.placeholder.label": "Enter Order", "user.input.trial-days.placeholder.label": "Enter Trial Days", "user.input.link-limit.placeholder.label": "Enter Minify Link Limit", "user.input.project-limit.placeholder.label": "Enter Collections Limit", "user.input.last-name.placeholder.label": "Enter Last Name", "user.input.email.placeholder.label": "Email", "user.input.phone-number.placeholder.label": "Enter Phone Number", "user.input.password.placeholder.label": "Enter Password", "user.input.confirm-password.placeholder.label": "Enter Confirm Password", "user.input.role.placeholder.label": "Choose <PERSON>", "users.table.user.column.title": "User", "users.table.user-name.column.title": "User Name", "users.table.phone-number.column.title": "Phone Number", "users.table.role.column.title": "Role", "users.table.date.column.title": "Added date", "user-details.title": "User Details", "plan-details.title": "Plan Details", "user-details.table.created-on.row.label": "Added On", "user-details.table.title": "Overview", "user.input.first-name.validate.label": "Please enter name", "user.input.last-name.validate.label": "Please enter last name", "user.input.email.validate.label": "Please enter email address", "user.input.company.validate.label": "Please enter company name", "user.input.job-title.validate.label": "Please enter job title", "user.input.email.valid.validate.label": "Please enter valid email address", "user.input.phone-number.validate.label": "Please enter phone number", "user.input.password.validate.label": "Please enter password", "user.input.confirm-password.validate.label": "Please enter confirm password", "user.input.role.validate.label": "Please select role", "user.success.create.message": "User created successfully.", "qr-code.success.create.message": "QR Code saved successfully.", "user.success.edit.message": "User updated successfully.", "qr-code.success.edit.message": "QR Code updated successfully.", "user.success.delete.message": "User deleted successfully.", "qr-code.success.delete.message": "QR Code deleted successfully.", "react-data-table.searchbar.placeholder": "Search", "react-data-table.download.placeholder": "Download", "react-data-table.action.column.label": "Action", "react-data-table.date.column.label": "Date", "react-data-table.subscribed-date.column.label": "Subscribed Date", "react-data-table.expired-date.column.label": "Expired Date", "react-data-table.no-record-found.label": "There are no records to display", "react-data-table.records-per-page.label": "Records per page", "react-data-table.qr-code-name.label": "QR Code Name", "react-data-table.qr-code-types.label": "QR Code Types", "react-data-table.make-default.label": "Make Default", "delete-modal.title": "Delete!", "delete-modal.msg": "Are you sure want to delete this", "delete-modal.yes-btn": "Yes, Delete!", "delete-modal.no-btn": "No, Cancel", "toast.successful.title": "Successful", "toast.error.title": "Something Went Wrong!", "react-data-table.name.column.title": "Name", "projects.title": "Collections", "project.title": "Collection", "project-name.title": "Collection Name", "project.create.title": "Add Collection", "project.edit.title": "Edit Collection", "project.success.create.message": "Collection saved successfully.", "project.success.edit.message": "Collection updated successfully.", "project.success.delete.message": "Collection deleted successfully.", "globally.heading.style.title": "Style", "globally.heading.currency.title": "<PERSON><PERSON><PERSON><PERSON>", "globally.heading.frequency.title": "Frequency", "currency.edit.title": "<PERSON>", "globally.heading.active-plans.label": "Active Plans", "globally.active-till.label": "Active till", "globally.active.label": "Active", "globally.pending.label": "Pending", "globally.deactive.label": "Deactive", "globally.approved.label": "Approved", "globally.disabled.label": "Disabled", "globally.reject.label": "Reject", "globally.paid.label": "Paid", "globally.heading.month.label": "Month", "globally.heading.year.label": "Year", "globally.qrcode.title": "QR Codes", "globally.links.title": "Minify Link", "globally.input.name.label": "Name", "globally.input.favicon.label": "Web Icon", "globally.input.enable-new-user-registration.label": "Enable new users registration", "globally.input.website-title.label": "Web Title", "globally.placeholder.website-title.label": "Enter Web Title", "globally.input.default-language.label": "Default Language", "globally.input.event-name.label": "Event Name", "globally.input.description.lable": "Description", "globally.input.order.lable": "Order", "globally.input.trial-days.lable": "Trial Days", "globally.input.monthly-price.lable": "Monthly Price", "globally.input.monthly.lable": "Monthly", "globally.input.Yearly.lable": "Yearly", "globally.input.annual.lable": "Annual", "globally.input.no-plan-available.lable": "No Plan Available", "globally.input.annual-price.lable": "Annual Price", "globally.input.lifetime-deal-price.lable": "Lifetime deal price", "globally.input.applied-taxes.lable": "Applied Taxes", "globally.input.applied-codes.lable": "Applied Codes", "globally.input.color.lable": "Color", "globally.input.status.lable": "Status", "globally.input.project.lable": "Collection", "globally.input.type.lable": "Type", "globally.input.text-content.lable": "Content", "globally.input.geo-location.lable": "GEO Location Name", "globally.input.amount.label": "Amount", "globally.input.background-color-transparency.label": "Transparency", "globally.input.background-color.label": "Background Color", "globally.input.foreground-gradient-style.label": "Foreground Gradient Style", "globally.btn.gradient.label": "Gradient", "globally.input.foreground-color-1.label": "Foreground Color 1", "globally.input.foreground-color-2.label": "Foreground Color 2", "globally.input.foreground-color.label": "Foreground Color", "globally.input.custom-eyes-color.label": "Custom Eyes Color", "globally.input.enable-custom-eyes-color.label": "Enable Custom Eyes Color", "globally.input.eyes-inner-color.label": "Eye Ball Color", "globally.input.eyes-outer-color.label": "Eye Frame Color", "globally.input.logo.label": "Logo", "currency.success.create.message": "<PERSON><PERSON><PERSON><PERSON> saved successfully.", "currency.success.edit.message": "Currency updated successfully.", "currency.success.delete.message": "Currency deleted successfully.", "side-menu.empty.message": "No Menu Available", "no-option.label": "No Options Available", "option.label": "Options", "bar.title": "Bar", "line.title": "Line", "date-picker.filter.today.label": "Today", "date-picker.filter.this-week.label": "This Week", "date-picker.filter.last-week.label": "Last Week", "date-picker.filter.this-month.label": "This Month", "date-picker.filter.last-month.label": "Last Month", "date-picker.filter.Custom-Range.label": "Custom Range", "date-picker.filter.reset.label": "Reset", "date-picker.filter.apply.label": "Apply", "date-picker.filter.placeholder.label": "Select Date", "globally.input.qr-codes-imit.lable": "Maximum QR Codes", "globally.input.links-imit.lable": "Maximum Minify Link", "globally.input.customer-imit.lable": "Customer Limit", "globally.input.projects-imit.lable": "Maximum Collections", "globally.input.pixels-imit.lable": "Pixels Limit", "globally.input.custom-domains-imit.lable": "Custom Domains Limit", "globally.input.link-statistics-retention-days.lable": "Link statistics retention (days)", "globally.input.additional-domains.lable": "Additional Domains", "globally.input.no-ads.lable": "No Ads", "globally.input.no.lable": "No", "globally.input.yes.lable": "Yes", "globally.input.analytics.lable": "Analytics", "globally.input.link-analytics.lable": "Minify Link Analytics", "globally.input.business-card-analytics.lable": "Business Card Analytics", "globally.input.password-protection.lable": "Password Protection", "globally.input.qr-codes-reader.lable": "QR Code reader", "globally.input.text.lable": "Text", "globally.input.phone.lable": "Phone", "globally.input.phone-number.lable": "Phone Number", "globally.input.email.label": "Email", "globally.input.email-confirmation.label": "Email Confirmation", "globally.input.facetime.lable": "Facetime", "globally.input.wifi.lable": "Wi-Fi", "globally.input.wifi-ssid.lable": "Wi-Fi Name (SSID)", "globally.input.crypto.lable": "Crypto", "globally.input.paypal.lable": "PayPal", "globally.input.url.lable": "URL", "globally.input.event-url.lable": "Event URL", "globally.input.sms.lable": "SMS", "globally.input.size.lable": "Size", "globally.input.margin-size.lable": "<PERSON><PERSON>", "globally.input.prefilled-message.lable": "Email Message", "globally.input.prefilled-subject.lable": "Email Subject", "globally.input.subject.lable": "Subject", "globally.input.message.lable": "Message", "globally.input.whatsapp.message.lable": "Whatsapp Message", "globally.input.whatsapp.lable": "Whatsapp", "globally.input.location.lable": "Location", "globally.input.event.lable": "Event", "globally.input.vcard.lable": "Vcard", "globally.react-table.column.created-date.label": "Created On", "globally.react-table.column.created-dates.label": "Created Date", "globally.react-table.column.created-time.label": "Created Time", "globally.react-table.column.status.label": "STATUS", "globally.react-table.column.stats.label": "Stats", "globally.react-table.column.image.label": "Image", "globally.react-table.column.email-verified.label": "<PERSON><PERSON>", "globally.react-table.column.default-plan.label": "Default Plan", "globally.input.latitude.lable": "Latitude", "globally.input.longitude.lable": "Longitude", "globally.input.encryption.lable": "Encryption", "globally.input.password.lable": "Password", "globally.input.timezone.lable": "Timezone", "globally.input.coin.lable": "Coin", "globally.input.start-date.lable": "Starts On", "globally.input.ends-date.lable": "Ends On", "globally.input.wifi-hidden.lable": "WI-FI Is Hidden", "globally.input.first-name.label": "First Name", "globally.input.last-name.label": "Last Name", "globally.input.company.label": "Company", "globally.input.job-title.label": "Job Title", "globally.input.zip.label": "Zip", "globally.input.region.label": "Region", "globally.input.birth-date.label": "Birth Date", "globally.input.title.label": "Title", "globally.input.discount.label": "Discount", "globally.input.percentage.label": "Percentage", "globally.input.fixed.label": "Fixed", "globally.input.quantity.label": "Quantity", "globally.input.price.label": "Price", "globally.input.code.label": "Coupon Code", "globally.input.currency.code.label": "Currency Code", "globally.input.error-correction-capability.label": "Error Correction Capability", "globally.react-table.column.payment-type.label": "Payment Type", "globally.react-table.column.payment-date.label": "Payment Date", "globally.react-table.column.payment-status.label": "Payment Status", "globally.input.name.placeholder.label": "Name", "globally.input.phone-number.label": "Phone Number", "globally.input.phone-number.placeholder.label": "Enter Phone Number", "globally.input.country.label": "Country", "globally.input.device.label": "<PERSON><PERSON>", "globally.input.os.label": "Operating System", "globally.input.browser.label": "Browser", "globally.input.country.placeholder.label": "Enter Country", "globally.input.coupon-code.placeholder.label": "Enter Coupon Code", "globally.input.city.label": "City", "globally.input.city.placeholder.label": "City", "globally.input.address.label": "Address", "globally.input.address.placeholder.label": "Enter Address", "globally.input.notes.label": "Notes", "globally.input.notes.placeholder.label": "Enter Notes", "globally.input.title.validate.label": "Please enter title", "globally.input.name.validate.label": "Please enter name", "globally.input.max-titel.validate.label": "Please enter max 50 characters in title", "globally.input.geo-location.validate.label": "Please enter GEO location", "globally.input.text-content.validate.label": "Please enter text", "globally.input.email.validate.label": "Please enter email address", "globally.input.email.valid.validate.label": "Please enter valid email address", "globally.input.URL.valid.validate.label": "Please enter valid URL", "globally.input.country.validate.label": "Please enter country", "globally.input.city.validate.label": "Please enter city", "globally.input.state.validate.label": "Please enter state", "globally.input.zip.validate.label": "Please enter zip code", "globally.input.zip-length.validate.label": "Please enter 6 digits of zip code", "globally.input.phone-number.validate.label": "Please enter phone Number", "globally.input.phonenumber.validate.label": "Please enter valid phone Number.", "globally.input.address.validate.label": "Please enter address", "globally.input.notes.validate.label": "The notes must not be greater than 100 characters", "globally.require-input.validate.label": "This field is required", "globally.date.validate.label": "Please enter date", "globally.tax-length.validate.label": "The tax must not be greater than 100", "globally.discount-length.validate.label": "The discount must not be greater than 100", "globally.discount-cost-length.validate.label": "The discount must not be greater than the product cost", "globally.discount-price-length.validate.label": "The discount must not be greater than the product price", "globally.type.label": "Type", "globally.back-btn": "Back", "globally.add-btn": "Add", "globally.next-btn": "Next", "globally.save-btn": "Save", "globally.confirm-btn": "Confirm", "globally.cancel-btn": "Cancel", "globally.edit-btn": "Edit", "globally.submit-btn": "Submit", "globally.edit.tooltip.label": "Edit", "globally.delete.tooltip.label": "Delete", "globally.view.tooltip.label": "View", "update-profile.input.full-name.label": "Full Name", "update-profile.title": "Profile Details", "update-profile.tab.title": "Edit Profile", "update-profile.success.update.message": "Profile updated successfully.", "change-password.input.current.label": "Current Password", "change-password.input.new.label": "New Password", "change-password.input.confirm.label": "Confirm Password", "change-password.input.current.placeholder.label": "Current Password", "change-password.input.new.placeholder.label": "New Password", "change-password.input.confirm.placeholder.label": "Confirm Password", "change-password.input.current.validate.label": "Please enter current password", "change-password.input.new.validate.label": "Please enter new password", "change-password.input.confirm.validate.label": "Please enter confirm password", "change-password.input.valid.validate.label": "Please enter valid password", "change-password.input.characters.valid.validate.label": "Please enter minimum 6 characters in password field", "change-password.input.confirm.valid.validate.label": "Password and confirm password does not match", "login-form.title": "Sign In", "login-form.login-btn.label": "<PERSON><PERSON>", "login-via-google.btn.label": "Login via Google", "login-via-facebook.btn.label": "Login via Facebook", "register-form.register-btn.label": "Register", "login-form.forgot-password.label": "Forgot Password?", "globally.loading.label": "Please wait...", "login-form.go-to-sign-in.label": "Back To Sign In", "forgot-password-form.reset-link-btn.label": "Send Password Reset Link", "forgot-password-form.success.reset-link.label": "We have emailed your password reset link!", "login.success.message": "Logged in successfully.", "logout.success.message": "<PERSON><PERSON><PERSON> successfully.", "registration.success.message": "Registration successful.", "register.title": "Register", "change-language.update.success.message": "Language updated successfully.", "reset-password.title": "Reset Password", "reset-password.password.validate.label": "The confirm password and password must match", "reset-password.success.update.message": "Your password has been reset!", "admin.dashboard.title": "Admin Dashboard", "plans.title": "Plans", "cash-payments.title": "Cash Payments", "subscribed-plan.title": "Subscribed User Plans", "subscribers.title": "Subscribers", "subscriber.title": "Subscriber", "codes.title": "Coupon Codes", "plans.settings": "Plan Options", "plans.create.title": "Add Plan", "code.create.title": "Add Coupon Code", "plans.create-plan.title": "Add Plan", "plans.enabled.qr.codes.types": "Enabled QR Codes Types", "plans.qr.codes.types": "QR Codes Types", "plans.qr.codes-limit.types": "Maximum QR Code", "plan.edit.title": "Edit Plan", "edit.subscription.plan.title": "Edit Subscription Plan", "plan.coupon-code.title": "Edit Coupon Code", "plan.success.edit.message": "Plan updated successfully.", "plan.success.delete.message": "Plan deleted successfully.", "globally.contact.title": "Contact", "contact.success.delete.message": "Contact-info deleted successfully.", "enquiries.success.delete.message": "Enquiry deleted successfully.", "subscriber.success.delete.message": "Subscriber deleted successfully.", "coupon.success.delete.message": "Coupon deleted successfully.", "plan.success.create.message": "Plan created successfully.", "plan.table.plan.column.title": "Plan", "plan.input.all-type.title": "All Types", "globally.all-filter.label": "All", "already.register.title": "Already Registered?", "register.here.title": "Register Here", "qrcode.title": "QR Codes", "qr.title": "QR", "qrcode.create.title": "Add QR Code", "qrcode.edit.title": "Edit QR Code", "qrcode.lable": "QR Code", "qrcode.wep.label": "WEP", "qrcode.wpa-wpa2.label": "WPA/WPA2", "qrcode.no-ncryption.label": "No Encryption", "not-registered.title": "Not registered yet?", "latest-qr.title": "Newest QR Codes", "links.create.title": "Add URL", "link.edit.title": "Edit URL", "link.success.edit.message": "URL updated successfully.", "link.success.delete.message": "URL deleted successfully.", "link.success.create.message": "URL created successfully.", "link.table.plan.column.title": "Minify Link", "links.title": "Minify Link", "destination.url.title": "Destination URL", "destination.input.placeholder.label": "Destination URL", "URL.alias.title": "URL Alias", "alias-url.input.placeholder.label": "URL Alias", "destination.input.validate.label": "Please enter destination URL", "website.input.validate.label": "Please enter website URL", "job-title.input.validate.label": "Please enter job title", "company.input.validate.label": "Please enter company name", "website.input.valid.validate.label": "Please enter valid website URL", "alias.input.validate.label": "Please enter alias URL", "user.input.validate.label": "Please select user", "qrcode-size.input.validate.label": "Please enter QR Code size between 100px to 400px", "qrcode-margin-size.input.validate.label": "Please enter margin between 10px to 50px", "link.create.title": "Add URL", "account.account.title": "Account", "account.plan.title": "Plan", "account.api.title": "API", "account.payment.title": "Payment", "account.pay-switch.title": "Pay/ Switch Plan", "admin.currencies.title": "Currencies", "admin.frontcms.title": "Front CMS", "admin.feature.title": "Features", "admin.sub-feature.title": "Sub Features", "admin.terms-condition.title": "Terms & Conditions", "admin.privacy-policy.title": "Privacy Policy", "admin.settings.title": "Settings", "admin.setting.payment-configuration.title": "Payment Configuration", "admin.setting.social-setting.title": "Social Setting", "admin.codes.title": "Coupon Codes", "admin.codes.name.title": "Coupon Code Name", "admin.settings.main-settings.title": "Main Settings", "admin.settings.main-setting.title": "Main Setting", "currency.create.title": "Add <PERSON>cy", "currency.modal.input.name.placeholder.label": "Currency Name", "currency.modal.input.code.placeholder.label": "Currency Code", "currency.modal.input.symbol.placeholder.label": "Currency Symbol", "currency.modal.input.code.label": "Currency Code", "currency.modal.input.symbol.label": "Currency Symbol", "settings.clear-cache.title": "<PERSON>ache", "settings.clear-cache.success.message": "<PERSON><PERSON> cleared successfully.", "settings.updated-setting.success.message": "Settings updated successfully.", "globally.input.client-id.label": "Client ID", "admin.paypal.settings.secret.title": "Secret Key", "admin.paypal.settings.secret.placeholder": "Enter Secret Key", "settings.paypal.success.message": "Paypal setting updated successfully.", "settings.paypal.title": "<PERSON><PERSON> Setting", "settings.stripe.title": "<PERSON>e Setting", "settings.Ads.title": "Ads Setting", "front.enquiries.title": "Front Enquiries", "enquiry.title": "Enquiry", "front.enquiries.details.title": "Enquiry Details", "front.features.title": "Front Features", "front.sub-features.title": "Front Sub Features", "front.terms-conditions.title": "Terms And Conditions", "settings.smtp.title": "SMTP Settings", "settings.social.login.title": "Social Login Setting", "settings.social.title": "Social Setting", "settings.stripe.success.message": "Stripe setting updated successfully.", "settings.razorpay.success.message": "Razorpay setting updated successfully.", "settings.razorpay.title": "Razorpay Setting", "settings.announcement.success.message": "Announcement setting updated successfully.", "settings.announcement.toggle.message": "Enable Announcement", "settings.announcement.label": "Announcement Setting", "settings.custom.style.success.message": "Custom style setting updated successfully.", "settings.custom.style.title": "Custom Style Setting", "settings.enable.paypal-payment.label": "Enable Paypal Payment", "settings.enable.stripe-payment.label": "Enable Stripe Payment", "settings.enable.razorpay-payment.label": "Enable Razorpay Payment", "settings.enable.facebook-login.label": "Enable Facebook Login", "settings.enable.google-login.label": "Allow Google Authentication", "settings.enable.captcha.label": "Enable <PERSON><PERSON>", "settings.paypal.mode.label": "Mode", "admin.stripe.settings.secret.title": "Secret Key", "admin.stripe.settings.secret.placeholder": "Enter Secret Key", "admin.smtp.settings.title": "SMTP Settings", "admin.ads.settings.title": "Ads", "admin.custom-style.settings.title": "Custom Style", "admin.announcement.settings.title": "Announcement Settings", "admin.social-login.settings.title": "Google Authentication", "admin.stripe.settings.key.title": "Stripe Key", "admin.stripe.settings.key.placeholder": "Enter Stripe Key", "admin.captcha.settings.key.placeholder": "Enter <PERSON><PERSON>", "admin.captcha.settings.key.title": "<PERSON><PERSON> <PERSON>", "admin.captcha.settings.title": "<PERSON><PERSON>", "admin.custom.js.style.title": "Custom JS", "admin.custom.js.style.placeholder": "Enter Custom JS", "admin.custom.css.style.placeholder": "Enter Custom CSS", "admin.custom.css.style.title": "Custom CSS", "admin.razorpay.settings.secret.title": "Secret Key", "admin.razorpay.settings.key.title": "Razorpay Key", "admin.razorpay.settings.secret.placeholder": "Enter Secret Key", "admin.razorpay.settings.key.placeholder": "Enter Razorpay Key", "settings.ads.app-header.label": "App <PERSON><PERSON>", "settings.ads.app-header.placeholder": "<PERSON><PERSON>", "settings.ads.app-footer.label": "A<PERSON><PERSON>", "settings.ads.app-footer.placeholder": "<PERSON><PERSON>", "settings.login.app-id.label": "App ID", "settings.login.app-id.placeholder": "Enter App ID", "settings.login.client-id.label": "Client ID", "settings.login.client-id.placeholder": "Enter Client ID", "admin.guest-announcement-content.settings.key.title": "Guest Announcement Content", "admin.user-announcement-content.settings.key.title": "User Announcement Content", "admin.user-announcement-content.settings.key.placeholder": "Enter User Announcement Content", "user.plan.purchase.success.message": "Plan purchase successfully.", "header.manage.subscription.label": "Manage Subscription", "header.delete-account.label": "Delete Account", "user.current-plan.title": "Current Plan", "user.new-plan.title": "New Plan", "globally.plan.name.title": "Plan Name", "globally.product.code.title": "Product Code", "globally.product.name.title": "Product Name", "globally.plan.price.title": "Plan Price", "globally.plan.start.date.title": "Start Date", "globally.plan.end.date.title": "End Date", "globally.plan.total.days.title": "Total Days", "globally.plan.used.days.title": "Used Days", "globally.plan.used.title": "Used", "globally.plan.remaining.days.title": "Remaining Days", "globally.plan.used.balance.title": "Used Balance", "globally.plan.remaining.balance.title": "Remaining Balance", "new.plan.remaining.prev.balance.title": "Remaining Balance of Prev. Plan", "new.plan.payable.amount.title": "Payable Amount", "globally.input.latitude.validate.label": "Please enter latitude", "globally.input.longitude.validate.label": "Please enter longitude", "globally.input.url.validate.label": "Please enter URL", "globally.input.url.valid.validate.label": "Please enter valid URL", "globally.input.whatsapp-phone-number.validate.label": "Please enter whatsapp phone number", "globally.input.whatsapp-phonenumber.validate.label": "Please enter a valid WhatsApp phone number.", "globally.input.number-or-email.validate.label": "Please enter phone number or email", "globally.input.wifi-name.validate.label": "Please enter WI-FI name (SSID)", "globally.input.event-name.validate.label": "Please enter event name", "globally.input.first-name.validate.label": "Please enter first name", "globally.input.last-name.validate.label": "Please enter last name", "globally.input.paypal-email.validate.label": "Please enter paypal email", "globally.input.currency-code.validate.label": "Please enter currency code", "globally.input.price.validate.label": "Please enter price", "globally.input.product-title.validate.label": "Please enter product title", "globally.input.thank-url.validate.label": "Please enter thank you URL", "globally.input.thank-url.valid.validate.label": "Please enter valid thank you URL", "globally.input.cancel-url.valid.validate.label": "Please enter valid cancel you URL", "globally.input.cancel-url.validate.label": "Please enter cancel URL", "globally.input.amount.validate.label": "Please enter amount", "globally.input.how-many-people-can-use?.validate.label": "Enter How Many People Can Use?", "globally.copy-link.message": "Link copied successfully.", "globally.react-table.column.updated-date.label": "Updated On", "globally.copy-link.tool-tip.message": "Copy Link", "coupon.code.how-many.use?.title": "How Many People Can Use?", "coupon.code.how-many.use.title": "How Many People Can Use", "home.singup-to-save.btn.title": "Sign up to save it", "setting.plan-expire-notification.title": "Plan Expire Notification (In Day)", "setting.placeholder.plan-expire-notification.title": "Enter Plan Expire Notification (In Day)", "coupon.code.applied.message": "Coupon code applied successfully.", "subscription.titile": "Subscription", "transactions.title": "Transactions", "transactions.details.title": "Transaction Details", "cashpayment.details.title": "Cash Payment Details", "schedule-time.title": "Schedule Time", "transaction.details.heading.title": "Transaction Details", "upgrade-plan.title": "Upgrade Plan", "currency.modal.input.code.validate.label": "Please enter currency code", "currency.modal.input.symbol.validate.label": "Please enter currency symbol", "currency.modal.input.code.valid.validate.label": "Currency code contains only 3 characters", "link.details.title": "Link Details", "project.details.title": "Collection Details", "qr-code.details.title": "QR Code Details", "coupon-code.details.title": "Coupon Code Details", "plan.details.title": "Plan Details", "user.details.title": "User Details", "payment.pending.error.message": "Your payment request is pending, please contact administrator.", "admin.emailsetting.title": "Email Notifications", "admin.email.setting.title": "Email Notifications Setting", "globally.input.state.label": "State", "admin.enquiries.title": "Enquiries", "globally.input.currency.label": "Crypto Currency Address", "react-data-table.iso-date.column.label": "ISO CODE", "react-data-table.translation.column.label": "Translation", "language.add.title": "Add Language", "edit-translation.title": "Edit Translation", "edit-language.title": "Edit Language", "translation.manager.title": "Translation Manager", "language.save.success.message": "Language saved successfully.", "language.edit.success.message": "Language updated successfully.", "language.updated.success.message": "Language updated successfully.", "language.deleted.success.message": "Language deleted successfully.", "dropdown.choose-user.placeholder": "Choose User", "visitors.title": "Visitors", "visitor.title": "Visitor", "number-of-visitors.title": "Numbers Of Visitors", "no-data-available.title": "No Data Available", "view-more.title": "View more", "enable.access.to.set.a.password.protection.for.links": "Enable access to set a password protection for links.", "choose.which.QR.Codes.types.you.want.to.give.access.to": "Choose which QR Codes types you want to give access to.", "coupon.added.successfully.message": "Coupon Code saved successfully.", "coupon.code.updated.successfully.message": "Coupon Code updated successfully.", "Add.currency.code.as.per.three-letter.ISO.code": "Add currency code as per three-letter ISO code.", "you.can.find.out.here": "you can find out here.", "google.reCaptcha.v2.checkbox": "Google ReCaptcha v2 checkbox", "enable.captcha.on.the.lost.password.page": "Enable captcha on the lost password page", "enable.captcha.on.the.login.and.registration.page": "Enable captcha on the login and registration page", "enable.captcha.on.the.contact.page": "Enable captcha on the contact page", "captcha.title": "Google v2 Captcha", "socials.title": "Socials", "SMTP.title": "SMTP", "Announcement.title": "Announcement", "emails.to.be.notified.label": "Admin Notification Emails", "emails.to.be.notified.placeholder": "Enter Emails to be notified", "emails.that.will.receive.a.notification.when.one.of.the.actions.from.below.are.performed.add.valid.email.addresses.separated.by.a.comma": "Emails that will receive a notification when one of the actions from below are performed. Add valid email addresses separated by a comma.", "new.user.title": "New User", "receive.an.email.when.a.new.users.registers.to.the.website": "Receive an email when a new users registers to the website.", "delete.user.title": "Delete User", "receive.an.email.when.any.user.deletes.their.account": "Receive an email when any user deletes their account.", "new.payment.title": "New Payment", "receive.an.email.when.a.new.payment.is.successfully.processed": "Receive an email when a new payment is successfully processed.", "contact.page.emails.title": "Contact page emails", "enable.the.contact.system": "Enable the contact system.", "from.name.title": "Sender Name", "enter.from.name.placeholder": "Enter Sender Name", "from.email.title": "Sender <PERSON><PERSON>", "enter.from.email.placeholder": "Enter Sender Email", "host.title": "SMTP Host", "enter.host.placeholder": "Enter SMTP Host", "port.title": "Port Number", "enter.port.placeholder": "Enter Port Number", "username.title": "Username", "enter.username.placeholder": "<PERSON><PERSON> Username", "text-color.title": "Text Color", "background-color.title": "Background Color", "edit.feature.title": "Edit Feature", "sub-title.title": "Sub Title", "enter.sub.title.placeholder": "Enter Sub Title", "enter-title.placeholder": "Enter Title", "enter-description.placeholder": "Enter Description", "you.can.enter.Max.200.characters.in.The.description": "You can enter Max 200 characters in The description.", "you.can.enter.Max.90.characters.in.The.subTitle": "You can enter Max 90 characters in The subTitle.", "you.can.enter.Max.50.characters.in.The.title": "You can enter Max 50 characters in The title. Add the Hero Section Title to the meta tag", "you.can.enter.Max.100.characters-with-meta.in.The.title": "It will be also used as meta Title And you can enter Max 100 characters in The Hero Section Title.", "The.image.must.be.of.pixel.612.x.315": "The image must be of pixel 612 x 315", "The.image.must.be.of.pixel.220.x.172": "The image must be of pixel 220 x 172", "feature.updated.successfully.message": "Feature updated successfully.", "front.CMS.updated.successfully.message": "Front CMS updated successfully.", "sub.feature.updated.successfully.message": "Sub Feature updated successfully.", "subscribed.successfully.message": "Subscribed successfully.", "terms.conditions.are.updated.successfully.message": "Terms & Conditions updated successfully.", "message.sent.successfully.message": "Message sent successfully.", "you.can.enter.Max.100.characters.in.The.description.tooltip": "You can enter Max 100 characters in The Hero Section Sub Title.", "you.can.enter.Max.100.characters-with-meta.in.The.description.tooltip": "It will be also used as meta sub Title And you can enter Max 100 characters in The Hero Section Sub Title.", "qrcode.btn.square.label": "Square", "qrcode.btn.dot.label": "Dot", "qrcode.btn.round.label": "Round", "qrcode.btn.foreground.label": "Foreground", "qrcode.foreground-first.label": "Foreground First", "qrcode.foreground-second.label": "Foreground Second", "qrcode.btn.background.label": "Background", "qrcode.btn.px.label": "PX", "qrcode.error-correction-capability.low.label": "L - low (7%)", "qrcode.error-correction-capability.medium.label": "M - medium (15%)", "qrcode.error-correction-capability.high.label": "Q - high (25%)", "qrcode.error-correction-capability.best.label": "H - best (30%)", "globally.header.generate-qr-code.label": "Generate QR Code", "globally.generate-qr-code.title": "QR Code generator", "globally.choose-your-plan.title": "Choose Your Plan", "globally.choose-your-plan.sub.title": "Select the most convenient plan for you", "globally.generate-qr-code.sub.title": "Generate easy & customizable QR Codes in minutes.", "globally.header.pricing.label": "Pricing", "globally.qr-code-type.title": "Types of QR Codes", "globally.qr-code-type.sub.title": "A Various Types of QR Code templates to choose from and get started.", "globally.text.heading": "Simple and clear text embedded into a QR Code.", "globally.url.heading": "Send someone to a URL.", "globally.phone.heading": "Get calls from people by scanning the QR Code.", "globally.email.heading": "Get custom emails from people by scanning the QR Code.", "globally.location.heading": "Embed location coordinates for people to get directions more easily.", "globally.wifi.heading": "Create WiFi QR Codes and let people connect to your WiFi more easily.", "globally.facetime.heading": "Get Facetime calls from people by scanning the QR Code.", "globally.event.heading": "Generate a custom life calendar event and embed it into a QR Code.", "globally.whatsapp.heading": "Get custom messages on whatsapp from people by scanning the QR Code.", "globally.crypto.heading": "Make payments of crypto currencies easily by scanning the QR Code.", "globally.vcard.heading": "Create Vcard QR Codes and mainly used to make it easier to share contact details on mobile devices.", "globally.paypal.heading": "Make payments on PayPal by scanning the QR Code.", "globally.text.title": "Generate Text QR Code", "globally.url.title": "Generate URL QR Code", "globally.phone.title": "Generate Phone QR Code", "globally.email.title": "Generate Email QR Code", "globally.location.title": "Generate Location QR Code", "globally.wifi.title": "Generate Wifi QR Code", "globally.facetime.title": "Generate Facetime QR Code", "globally.whatsapp.title": "Generate Whatsapp QR Code", "globally.event.title": "Generate Event QR Code", "globally.crypto.title": "Generate Crypto QR Code", "globally.vcard.title": "Generate Vcard QR Code", "globally.paypal.title": "Generate Paypal QR Code", "globally.subscriber.get-start.title": "Get Started", "globally.copyright.title": "Copyright", "cash.payment.status.updated.successfully.message": "Cash payment status updated successfully.", "captcha.update.successfully.message": "Captcha updated successfully.", "email.notifications.updated.successfully.message": "Email notifications updated successfully", "update.socials.settings.successfully.message": "Update Socials settings successfully", "update.ads.Settings.successfully.message": "Update Ads Settings successfully", "Update.SMTP.Mail.Settings.successfully.message": "Update SMTP Mail Settings successfully", "Social.Login.updated.successfully.message": "Social Login updated successfully.", "Your.account.is.currently.disabled.please.contact.administrator.message": "Your account is currently disabled, please contact administrator.", "none.title": "None", "globally.currently.active.btn.title": "Currently Active", "globally.choose.plan.btn.title": "Choose <PERSON>", "globally.switch.plan.btn.title": "Switch Plan", "globally.no.plan.available.title": "No plans available.", "globally.qrcode.type.no.available.title": "Qr code types no available please select user", "globally.qrcode.type.no.is.available.title": "Qr code types no available", "globally.day-Remaining.title": "Days remaining", "ends.on.date.must.be.a.greater.than.Starts.on.date.error.message": "Ends on date must be a greater than Starts on date.", "invalid.bitcoin.address.error.message": "Invalid bitcoin address", "invalid.ethereum.address.error.message": "Invalid ethereum address", "invalid.Elrond.address.error.message": "Invalid Elrond address", "please.enter.valid.phone.number.or.email.address.error.message": "Please enter valid phone number or email address", "please.enter.price.greater.than.1.error.message": "Please enter price greater than 1", "please.enter.QR.Code.limit.error.message": "Please enter QR Code limit.", "please.enter.links.limit.error.message": "Please enter links limit.", "please.enter.projects.limit.error.message": "Please enter collection limit.", "please.enter.the.code.error.message": "Please enter the code.", "please.enter.the.percentage.error.message": "Please enter the percentage.", "please.enter.percentage.between.0.to.100.error.message": "Please enter percentage between 0 to 100.", "please.enter.the.quantity.error.message": "Please enter the quantity.", "globally.input.iso-code.validate.label": "Please enter ISO code.", "globally.input.iso-code.character.validate.label": "ISO code only have 2 characters.", "Please.Enter.The.Website.Name.validate.label": "Please Enter The Website Name.", "Please.Select.Currency.validate.label": "Please Select Currency.", "Please.Enter.plan.expire.notification.day.validate.label": "Please Enter plan expire notification day.", "Please.Enter.App.ID.validate.label": "Please Enter App ID.", "Please.Enter.Client.ID.validate.label": "Please Enter Client ID.", "Please.Enter.The.From.Name.validate.label": "Please Enter The From Name.", "Please.Enter.The.From.Email.validate.label": "Please Enter The From Email.", "Please.Enter.The.Host.validate.label": "Please Enter The Host.", "Please.Enter.The.Port.validate.label": "Please Enter The Port.", "Please.Enter.Username.validate.label": "Please Enter Username.", "Please.enter.description.validate.label": "Please enter description", "Please.enter.sub.title.validate.label": "Please enter sub title", "qrcode.bitcoin.title": "Bitcoin BTC", "qrcode.ethereum.title": "Ethereum ETH", "qrcode.elrond.title": "Elrond EGLD", "globally.TLS.title": "TLS", "globally.SSL.title": "SSL", "qrcode.buy-now.lable": "Buy Now", "qrcode.add-to-cart.lable": "Add to Cart", "qrcode.horizontal.lable": "Horizontal", "qrcode.radial.lable": "Radial", "setting.sandbox.lable": "Sandbox", "setting.live.lable": "Live", "You.have.already.added.this.feature.validate.label": "You have already added this feature.", "Only.6.sub.titles.are.available.to.add.validate.label": "Only 6 sub titles are available to add.", "Please.enter.text.in.Sub.Title.validate.label": "Please enter text in Sub Title.", "Please.enter.terms.validate.label": "Please enter terms.", "Please.enter.privacy.policy.validate.label": "Please enter privacy policy.", "unread.label": "Unread", "read.label": "Read", "The.image.must.be.of.pixel.90.x.60": "The image must be of pixel 90 x 60", "The.image.must.be.of.pixel.16.x.16": "The image must be of pixel 16 x 16", "This.Type.of.QR.Code.is.Not.Available.in.Your.Plan": "This Type of QR Code is Not Available in Your Plan.", "Please.enter.secret.validate.label": "Please enter secret.", "Please.enter.razorpay.key.validate.label": "Please enter razorpay key.", "Please.enter.razorpay.secret.validate.label": "Please enter razorpay secret.", "Please.enter.stripe.key.validate.label": "Please enter stripe key.", "Please.enter.stripe.secret.validate.label": "Please enter stripe secret.", "qrcode.paypal.thank-you-url.title": "Thank You", "qrcode.paypal.cancel-url.title": "Cancel", "qrcode.paypal.vcard.title": "Street", "globally.dynamic-link.title": "Minify Links", "globally.included-analytics.title": "Analytics Included", "globally.subscribe.btn.title": "Subscribe", "globally.no.active.plan.available.title": "No Active Plan Available", "plan.expired.continue.noti": "Your plan has been expired, please choose new plan to continue service.", "plan.about.expire.noti": "Your Plan is about to expired in", "days.title": "days", "plan.selected.subtotal.price.titel": "Subtotal", "plan.selected.lass.amount.titel": "Amount to be less", "plan.apply.coupon.code.btn.titel": "Apply Coupon Code", "globally.stripe.payment.title": "Stripe", "globally.paypal.payment.title": "<PERSON><PERSON>", "globally.razorpay.payment.title": "Razorpay", "globally.manually.payment.title": "Manually", "footer.All.Rights.Reserved": "All Rights Reserved", "Please.enter.subject.validate.label": "Please enter subject", "Please.enter.message.validate.label": "Please enter message", "Please.verify.the.captcha.validate.label": "Please verify the captcha", "globally.number-or-email.label": "Phone or Email", "globally.wifi-name.label": "Wifi Name", "globally.paypal.type.title": "Paypal Type", "globally.paypal.email.title": "<PERSON><PERSON>", "globally.product-title.label": "Product Title", "qrcode.thank-you-url.title": "Thanks URL", "qrcode.cancel-you-url.title": "Cancel URL", "qrcode.website-url.title": "Website URL", "globally.sign-up.label": "Sign Up", "Minimum.100px.and.Maximum.400px": "Minimum 100px and Maximum 400px", "Minimum.10px.and.Maximum.50px": "Minimum 10px and Maximum 50px", "Edit.Sub.Feature": "Edit Sub Feature", "The.URL.alias.has.been.already.taken.error.message": "The URL alias has been already taken.", "The.destination.URL.must.be.a.valid.URL.error.message": "The destination URL must be a valid URL.", "The.name.has.already.been.taken.error.message": "The name has already been taken.", "globally.select.label": "Select", "globally.input.latitude.placeholder": "Enter Latitude", "globally.input.longitude.placeholder": "Enter Longitude", "globally.input.wifi-name.placeholder": "Enter Wi-Fi Name (SSID)", "globally.input.event-name.placeholder": "Enter Event Name", "globally.input.deo-location-name.placeholder": "Enter GEO Location Name", "globally.input.event-url.placeholder": "Enter Event URL", "globally.input.crypto-currency-address.placeholder": "Enter Crypto Currency Address", "globally.input.amount.placeholder": "Enter Amount", "admin.setting.social.youtube.label": "YouTube", "admin.setting.social.facebook.label": "Facebook", "admin.setting.social.instagram.label": "Instagram", "admin.setting.social.linkedin.label": "Linkedin", "admin.setting.social.tiktok.label": "Tiktok", "admin.setting.social.twitter.label": "Twitter", "globally.input.text-content.placeholder": "Enter Text Content", "globally.input.url.placeholder": "Enter URL", "globally.input.prefilled-message.placeholder": "Enter Prefilled Message", "globally.input.prefilled-subject.placeholder": "Enter Prefilled Subject", "globally.input.email.placeholder": "<PERSON><PERSON>", "globally.input.phone-or-email.placeholder": "Enter Phone Number or Email", "globally.input.cancel-url.placeholder": "Enter Cancel URL", "globally.input.thank-url.placeholder": "Enter Thank You URL", "globally.input.product.code.placeholder": "Enter Product Code", "globally.input.product.name.placeholder": "Enter Product Name", "globally.input.paypal.email.placeholder": "Enter PayPal Email", "globally.input.region.placeholder": "Enter Region", "globally.input.zip.placeholder": "<PERSON><PERSON>", "globally.input.city.placeholder": "Enter City", "globally.input.street.address.placeholder": "Enter Street Address", "globally.input.job.title.placeholder": "Enter Job Title", "globally.input.company.placeholder": "Enter Company", "globally.placeholder.website-url.label": "Enter Website URL", "globally.placeholder.size.label": "<PERSON><PERSON> Si<PERSON>", "globally.placeholder.margin-size.label": "<PERSON><PERSON>", "globally.input.hero-image.label": "Hero Section Image", "admin.frontcms.hero-section.title": "Hero Section", "admin.frontcms.hero-section-title.title": "Hero Section Title", "admin.frontcms.hero-section-sub-title.title": "Hero Section Sub Title", "globally.input.hero-section-title.placeholder": "Enter Hero Section Title", "globally.input.hero-section-sub-title.placeholder": "Enter Hero Section Sub Title", "privacy-policy.are.updated.successfully.message": "Privacy Policy updated successfully.", "settings.select.pages.label": "Pages", "settings.select.page.label": "Page", "page.create.title": "Add Page", "page.edit.title": "Edit Page", "globally.input.show-menu-on-page.title": "Show Menu On Page", "globally.input.slug.title": "Slug", "globally.input.meta-title.title": "Meta Title", "globally.input.meta_description.title": "Meta Description", "globally.input.visibility.title": "Visibility", "globally.input.show-breadcrumb.title": "Show Breadcrumb", "globally.input.show-right.title": "Show Right", "globally.input.show-title.title": "Show Title", "globally.input.show-only-to-registered-users.title": "Show Only to Registered Users", "globally.input.main-menu.title": "Main Menu", "globally.input.dont-add-menu.title": "Don't <PERSON>d <PERSON>u", "page.success.create.message": "<PERSON> saved successfully.", "page.success.delete.message": "<PERSON> deleted successfully.", "globally.input.meta-title.validate.label": "Please enter meta title.", "page.success.edit.message": "Page updated successfully.", "page.visibility.success.edit.message": "Visibility updated successfully.", "globally.input.meta-description.validate.label": "Please enter meta description.", "contact.send.message.title": "Send Message", "contact.sending.message.title": "Sending...", "globally.email.notifications.setting.title": "Notifications (Email)", "globally.input.fill-color-1.title": "Fill Color 1", "globally.input.fill-color-2.title": "Fill Color 2", "globally.input.solid-color.title": "Solid Color", "settings.paypal.is-live.label": "Is Live", "contact.us.main.title": "Make Meeting Easy With QR Builder", "subscribe.now.title": "Subscribe now.", "start.your.free.trial.title": "Start your free trial.", "month.name.jan.title": "January", "month.name.feb.title": "February", "month.name.mar.title": "March", "month.name.apr.title": "April", "month.name.may.title": "May", "month.name.jun.title": "June", "month.name.jul.title": "July", "month.name.aug.title": "August", "month.name.sep.title": "September", "month.name.act.title": "October", "month.name.nov.title": "November", "month.name.dec.title": "December", "globally.total-earning.lable": "Total Earning", "globally.today-earning.lable": "Today's Earning", "globally.total-subscriptions.lable": "Total Subscriptions", "globally.active-subscriptions.lable": "Active Subscriptions", "you.have.used.this.message": "you have used this", "subtitle.more.than.once.message": "subtitle more than once.", "digital.business.card.title": "Digital Business Cards", "digital.business.card.add.title": "Add Business Card", "digital.business.card.edit.title": "Edit Business Card", "globally.allow-to-create-business-card.title": "Allow to create Business Card", "globally.input.business-card-imit.lable": "Business Cards Limit", "please.enter.business-card.limit.error.message": "Please enter business card limit.", "input.template.status.label": "Template Status", "input.template.label": "Template", "globally.content.title": "Content", "globally.editqr-types.content.title": "Edit QR Code Types Content", "globally.enter-content.placeholder": "Enter Content", "you.can.enter.Max.100.characters.in.The.content": "You can enter Max 100 characters in The content.", "globally.input.content.validate.label": "Please enter content", "globally.input.cover-image.lable": "Cover Image", "please.select.profile-image.error.message": "Please select profile image.", "please.select.cover-image.error.message": "Please select cover image.", "business.card.added.successfully.message": "Business card added successfully.", "business.card.updated.successfully.message": "Business card updated successfully.", "status.updated.successfully.message": "Status updated successfully.", "business.card.deleted.successfully.message": "Business card deleted successfully.", "digital.business.card.from.title": "Business Card Form", "digital.business.card.url.title": "Social Business Card URLs", "edit.digital.business.card.url.title": "Edit Business Social URLs", "business.card.Social-link.updated.successfully.message": "Business card URLs updated successfully.", "globally.input.website.url.placeholder": "WebSite URL", "globally.input.twitter.url.placeholder": "Twitter URL", "globally.input.facebook.url.placeholder": "Facebook URL", "globally.input.instagram.url.placeholder": "Instagram URL", "globally.input.raddit.url.placeholder": "Raddit URL", "globally.input.tumblr.url.placeholder": "Tumblr URL", "globally.input.youtube.url.placeholder": "Youtube URL", "globally.input.linkedin.url.placeholder": "Linkedin URL", "globally.input.whatsapp.url.placeholder": "Whatsapp URL", "globally.input.pinterest.url.placeholder": "Pinterest URL", "globally.input.tiktok.url.placeholder": "Tiktok URL", "globally.source.url.title": "Source URL", "globally.shorten.name.title": "Shorten Name", "qr-code-type.updated.successfully.message": "Qr codes types updated successfully.", "globally.home.title": "Home", "default.plan.changed.successfully.message": "Default plan changed successfully.", "digital.business.card.detail.title": "Business Card Details", "page.detail.title": "Page Details", "single.digital.business.card.title": "Digital Business Card", "globally.select.plan.title": "Select Plan", "globally.user.status.title": "User Status", "globally.coupon.code.name.title": "Coupon Code Name", "globally.coupon.code.type.title": "Coupon Code Type", "globally.enter.coupon.code.name.placeholder": "Enter Coupon Code Name", "globally.enter.qr.code.name.placeholder": "Enter QR Code Name", "globally.basic.details.label": "Basic details", "globally.design.label": "Design", "globally.qr.code.types.required.error": "The QR Code types is required.", "contact.contact-information.label": "Contact Information", "contact.your.first.name.placeholder": "Your first name", "contact.your.last.name.placeholder": "Your last name", "contact.your.email.address.placeholder": "Your email address", "contact.your.subject.placeholder": "Your subject", "contact.type.your.message.here.placeholder": "Type your message here...", "home.12.types.of.qr.codes.title": "12 Types of QR Codes", "plan.most.popular.title": "Most Popular", "globally.input.annually.lable": "Annually"}