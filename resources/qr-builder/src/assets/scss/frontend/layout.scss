@import "variable.scss";

header {
    .navbar {
        padding: 25px 0 !important;
        @media (max-width: 991px) {
            padding: 20px 0 !important;
        }
        .navbar-brand {
            .navbar-logo {
                max-height: 50px !important;
                height: 50px !important;
            }
        }
        .navbar-toggler {
            @media (max-width: 991px) {
                box-shadow: none !important;
            }
            .navbar-toggler-icon {
                background-image: none !important;
                height: 26px !important;
                width: 26px !important;
                position: relative !important;
                margin: auto !important;
                cursor: pointer !important;
                .icon-bar {
                    position: absolute !important;
                    height: 2px !important;
                    background-color: $white !important;
                    width: 26px !important;
                    display: block !important;
                    border-radius: 2px !important;
                    margin-top: 4px !important;
                    transition: 0.35s ease all !important;
                }
                .top-bar {
                    top: 0px !important;
                }
                .middle-bar {
                    top: 7px !important;
                    opacity: 1 !important;
                    width: 20px !important;
                }
                .bottom-bar {
                    top: 14px !important;
                }
                &.open {
                    .top-bar {
                        top: 7px !important;
                        animation: rotateDown 0.3s forwards !important;
                        animation-delay: 0.3s !important;
                    }
                    .middle-bar {
                        opacity: 0 !important;
                        width: 0% !important;
                    }
                    .bottom-bar {
                        top: 7px !important;
                        animation: rotateUp 0.3s forwards !important;
                        animation-delay: 0.3s !important;
                    }
                }
            }
            &:focus {
                box-shadow: none !important;
            }
        }
        .navbar-collapse {
            @media (max-width: 991px) {
                position: absolute !important;
                width: calc(100% - 24px) !important;
                top: 100% !important;
                background-color: $secondary !important;
                right: 12px !important;
                margin: auto !important;
                max-width: 300px !important;
                border-radius: 0.625rem !important;
                box-shadow: 0 0 20px rgb(173 181 189 / 38%) !important;
                z-index: 1024 !important;
                padding: 20px !important;
            }
            .navbar-nav {
                .nav-item {
                    padding: 0 20px !important;

                    @media (max-width: 991px) {
                        padding: 0 0 5px 0 !important;
                        width: fit-content;
                    }
                    .nav-link {
                        padding: 0 !important;
                        font-size: 18px !important;
                        color: $white !important;
                        position: relative !important;
                        &:after {
                            position: absolute !important;
                            content: "" !important;
                            width: 0 !important;
                            height: 2px !important;
                            bottom: -5px !important;
                            margin: 0 auto !important;
                            left: 0 !important;
                            right: 0 !important;
                            border-radius: 5px !important;
                            background-color: $primary !important;
                            transition: 0.5s !important;
                        }
                        &.active,
                        &:hover {
                            &:after {
                                width: 50px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

@keyframes rotateDown {
    from {
        transform: rotate(0deg) !important;
    }
    to {
        transform: rotate(-45deg) !important;
    }
}
@keyframes rotateUp {
    from {
        transform: rotate(0deg) !important;
    }
    to {
        transform: rotate(45deg) !important;
    }
}

footer {
    z-index: 1;
    .footer-vector {
        left: 60px;
        top: -25px;
    }
    .footer-logo {
        .footer-logo-img {
            max-height: 50px !important;
            height: 50px !important;
        }
    }
}
