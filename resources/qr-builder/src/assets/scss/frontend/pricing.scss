@import "variable.scss";

.pricing-plan-section {
    .plan-card {
        margin: 0 5px;
        border: 2px solid #e4e3e9;
        border-radius: 20px;
        box-shadow: none !important;

        &:hover,
        &:focus,
        &.active {
            border: 2px solid $primary;
            .card-body {
                .choose-plan-btn {
                    color: $white !important;
                    background-color: $secondary !important;
                }
            }
        }
        .card-body {
            font-weight: 500;
            padding: 20px;
            .most_popular {
                width: 55% !important;
            }
            #annual {
                display: none;
            }
            .price {
                color: $secondary;
                span {
                    color: $gray-200;
                }
            }
            .list {
                li {
                    display: flex;
                    align-items: center;
                    font-size: 15px;
                    font-weight: 400;
                    .plan_check_icon_container {
                        width: 20px !important;
                        height: 20px !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        border-radius: 50% !important;
                        color: white !important;
                        margin-right: 8px !important;

                        svg {
                            width: 12px !important;
                            height: 12px !important;
                        }
                    }
                    span {
                        font-weight: 500;
                    }
                }
            }
            .choose-plan-btn {
                color: $secondary;
                box-shadow: 0px 2px 5px #afafaf !important;
                border: none !important;
                outline: none !important;
            }
        }
    }

    .switch-field {
        width: 260px;
        height: 50px;
        position: relative;
        display: flex;
        padding: 4px;
        position: relative;
        background-color: $white;
        line-height: 3rem;
        border-radius: 3rem;
        border: 1px solid $gray-100;
        border-radius: 25px;
        box-shadow: 0 10px 20px $gray-100;
        @media (max-width: 575px) {
            height: 50px;
        }
        input {
            visibility: hidden;
            position: absolute;
            top: 0;
        }
        label {
            width: 50%;
            padding: 0;
            margin: 0;
            font-weight: 500;
            color: $dark;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            cursor: pointer;
        }
    }
    .switch-wrapper {
        position: absolute;
        top: 3px;
        bottom: 0;
        width: 48%;
        z-index: 3;
        transition: transform 0.5s cubic-bezier(0.77, 0, 0.175, 1);
    }
    .switch {
        border-radius: 3rem;
        background-color: $primary;
        height: 42px;
        div {
            width: 100%;
            text-align: center;
            opacity: 0;
            display: block;
            color: $white;
            font-weight: 500;
            transition: opacity 0.2s cubic-bezier(0.77, 0, 0.175, 1) 0.125s;
            will-change: opacity;
            position: absolute;
            top: -3px;
            left: 0;
        }
    }

    .switch-field input:nth-of-type(1):checked ~ .switch-wrapper {
        transform: translateX(0%);
    }
    .switch-field input:nth-of-type(2):checked ~ .switch-wrapper {
        transform: translateX(100%);
    }
    .switch-field
        input:nth-of-type(1):checked
        ~ .switch-wrapper
        .switch
        div:nth-of-type(1) {
        opacity: 1;
    }
    .switch-field
        input:nth-of-type(2):checked
        ~ .switch-wrapper
        .switch
        div:nth-of-type(2) {
        opacity: 1;
    }
}
