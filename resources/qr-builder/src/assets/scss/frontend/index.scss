@import "variable.scss";
.bg_dark_blue {
    background-color: $blue !important;
}
.hero-section {
    z-index: 2;
    .hero-img {
        max-width: 510px !important;
        width: 100% !important;
    }
}
.feature-section {
    .feature-bg {
        position: absolute;
        z-index: -1;
        &.vector-1 {
            left: 0;
            top: -220px;
            z-index: 1;
            @media (max-width: 1199px) {
                top: -150px;
            }
            @media (max-width: 768px) {
                top: -100px;
            }
            @media (max-width: 575px) {
                top: -50px;
            }
        }
        &.vector-2 {
            right: 0;
            bottom: -130px;
            z-index: 1;
            @media (max-width: 1199px) {
                bottom: -50px;
            }
            @media (max-width: 575px) {
                bottom: -20px;
            }
        }
    }
    .feature-section-container {
        position: relative;
        z-index: 2;
    }
    .feature-img {
        max-width: 480px !important;
        width: 100% !important;
    }
    .feature-content {
        .check-list-img {
            width: 20px !important;
            margin-right: 15px !important;
        }
    }
}
.qr-personal-section {
    position: relative;
    z-index: 2;
    .qrpersonal-bg {
        position: absolute;
        z-index: 0;
        img {
            @media (max-width: 991px) {
                width: 70%;
            }
            @media (max-width: 575px) {
                width: 50%;
            }
        }
        &.vector-1 {
            left: 0;
            top: -50px;
            @media (max-width: 991px) {
                top: -35px;
            }
            @media (max-width: 575px) {
                top: -25px;
            }
        }
        &.vector-2 {
            right: 0;
            bottom: -58px;
            @media (max-width: 991px) {
                bottom: -42px;
            }
            @media (max-width: 575px) {
                bottom: -28px;
            }
        }
    }
    .qr-personal-img {
        max-width: 460px !important;
        width: 100% !important;
    }
    .qr-personal-content {
        .check-list-img {
            width: 20px !important;
            margin-right: 15px !important;
        }
    }
}

.qr-code-types-section {
    .qrcode-bg {
        position: absolute;
        &.vector {
            left: 35px;
            bottom: 160px;
        }
        &.vector-1 {
            left: 0;
            top: 75px;
        }
        &.vector-2 {
            right: 0;
            bottom: -150px;
        }
    }
    .qr-types-card {
        height: 100% !important;
        border: 0 !important;
        border-radius: 20px !important;
        padding: 25px !important;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.5s !important;
        &:hover,
        &:focus,
        &.active,
        &:active {
            background-color: $secondary !important;
            .card-body {
                color: $white !important;
            }
            .type-btn {
                color: $primary !important;
            }
        }
        .card-img-top {
            width: auto !important;
            height: 45px !important;
        }
        .card-body {
            padding-top: 30px !important;
            padding-bottom: 20px !important;
            color: $dark !important;
        }
        .type-btn {
            width: 20px !important;
            height: 20px !important;
            font-size: 20px !important;
            display: flex !important;
            color: $secondary !important;
            cursor: pointer !important;
        }
    }
}
.get-in-touch-section {
    .feature-bg {
        position: absolute;
        z-index: -1;
        &.vector-1 {
            left: 0;
            top: -220px;
            z-index: 1;
            @media (max-width: 1199px) {
                top: -150px;
            }
            @media (max-width: 768px) {
                top: -100px;
            }
            @media (max-width: 575px) {
                top: -50px;
            }
        }
        &.contact_side_vector1 {
            right: 3.125rem;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1 !important;
        }
    }
    .contact-icon {
        width: 19px;
        height: 19px;
        min-width: 19px;
        background-color: transparent;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .contact_info_section {
        height: 650px !important;
        overflow: hidden;
    }
    .contact_big_circle {
        height: 269px !important;
        width: 269px !important;
        position: absolute;
        bottom: -4vh;
        right: -4vh;
    }
    .contact_small_circle {
        height: 140px !important;
        width: 140px !important;
        position: absolute;
        bottom: 12vh;
        right: 12vh;
    }
    .contact-form {
        border-radius: 10px;
        padding: 25px;
        position: relative;
        z-index: 2;
        @media (max-width: 575px) {
            padding: 15px;
        }

        .contact_left_top {
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
        }
        .contact_right_top {
            position: absolute;
            right: 0;
            top: 0;
            z-index: -1;
        }
        .contact_left_bottom {
            position: absolute;
            left: 0;
            z-index: -1;
            bottom: 0;
        }
        .contact_right_bottom {
            position: absolute;
            bottom: 0;
            z-index: -1;
            right: 0;
        }

        form {
            .form_label {
                font-weight: 500;
                font-size: 18px;
                color: $dark;
                margin-bottom: 10px;
            }
            .form-control {
                padding: 15px;
                height: 50px;
                background-color: white;
                border: 1px solid #dddddd;
                border-radius: 10px;
                color: $gray-200;
                @media (max-width: 575px) {
                    height: 50px;
                }
                &::placeholder {
                    color: $gray-200;
                    font-size: 16px;
                }
                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }
            .btn-primary {
                border-radius: 10px !important;
                background-color: $blue !important;
                border-color: $blue !important;
                height: 60px !important;
                font-size: 18px;
                font-weight: 500;

                &:hover,
                &:focus,
                &.active,
                &:active {
                    background-color: transparent !important;
                    border-color: $blue !important;
                    color: $blue !important;
                }
            }
        }
    }
}
