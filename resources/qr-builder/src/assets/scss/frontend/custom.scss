@import "variable.scss";

@font-face {
    font-family: "Circular Std" !important;
    font-style: normal !important;
    font-weight: 400 !important;
    src: url(../../fonts/frontendFonts/CircularStd-Book.otf) format("truetype") !important;
    font-display: swap !important;
}

@font-face {
    font-family: "Circular Std" !important;
    font-style: normal !important;
    font-weight: 500 !important;
    src: url(../../fonts/frontendFonts/CircularStd-Medium.otf)
        format("truetype") !important;
    font-display: swap !important;
}

@font-face {
    font-family: "Circular Std" !important;
    font-style: normal !important;
    font-weight: 700 !important;
    src: url(../../fonts/frontendFonts/CircularStd-Bold.otf) format("truetype") !important;
    font-display: swap !important;
}
body {
    font-weight: 500 !important;
}
.bg_secondary {
    background-color: $secondary !important;
}
.zIndex-3 {
    z-index: 3 !important ;
}
.border-radius-10 {
    border-radius: 10px !important;
}
.mt-40 {
    margin-top: 40px;
    @media (max-width: 575px) {
        margin-top: 30px;
    }
}

.mt-20 {
    margin-top: 20px !important;
    @media (max-width: 575px) {
        margin-top: 10px !important;
    }
}
.mt-100 {
    margin-top: 100px !important;
    @media (max-width: 575px) {
        margin-top: 75px !important;
    }
}
.mt-30 {
    margin-top: 30px !important;
    @media (max-width: 575px) {
        margin-top: 20px !important;
    }
}
.ls-1 {
    letter-spacing: 1.5px !important;
}

.px-100 {
    padding-left: 100px !important;
    padding-right: 100px !important;
}

.pt-100 {
    padding-top: 100px !important;
    @media (max-width: 991px) {
        padding-top: 50px !important;
    }
}
.pb-100 {
    padding-bottom: 100px !important;
    @media (max-width: 991px) {
        padding-bottom: 50px !important;
    }
}
.pt-40 {
    padding-top: 40px !important;
}
.pb-40 {
    padding-bottom: 40px !important;
}

.p-40 {
    padding: 40px !important;
}
.pt-60 {
    padding-top: 60px !important;
    @media (max-width: 991px) {
        padding-top: 50px !important;
    }
}
.pb-60 {
    padding-bottom: 60px !important;
    @media (max-width: 991px) {
        padding-bottom: 50px !important;
    }
}
.mb-40 {
    margin-bottom: 40px !important;
    @media (max-width: 991px) {
        margin-bottom: 30px !important;
    }
}
.mb-50 {
    margin-bottom: 50px !important;
    @media (max-width: 991px) {
        margin-bottom: 40px !important;
    }
}
.fs-14 {
    font-size: 14px !important;
}
.fs-16 {
    font-size: 16px !important;
    @media (max-width: 575px) {
        font-size: 14px !important;
    }
}
.fs-18 {
    font-size: 18px !important;
    @media (max-width: 575px) {
        font-size: 16px !important;
    }
}
.fs-20 {
    font-size: 20px !important;
    @media (max-width: 991px) {
        font-size: 18px !important;
    }
}
.fs-24 {
    font-size: 24px !important;
    @media (max-width: 991px) {
        font-size: 22px !important;
    }
    @media (max-width: 575px) {
        font-size: 20px !important;
    }
}

.fs-28 {
    font-size: 28px !important;
    @media (max-width: 991px) {
        font-size: 26px !important;
    }
    @media (max-width: 575px) {
        font-size: 24px !important;
    }
}

.fs-30 {
    font-size: 30px !important;
    @media (max-width: 991px) {
        font-size: 26px !important;
    }
    @media (max-width: 575px) {
        font-size: 22px !important;
    }
}
.fs-34 {
    font-size: 34px !important;
    @media (max-width: 991px) {
        font-size: 30px !important;
    }
    @media (max-width: 575px) {
        font-size: 26px !important;
    }
}

.fw-700 {
    font-weight: 700 !important;
}

.fw-100 {
    font-weight: 100 !important;
}

.fw-600 {
    font-weight: 600 !important;
}

.fw-500 {
    font-weight: 500 !important;
}

.fw-4 {
    font-weight: 400 !important;
}
a {
    text-decoration: none !important;
}

h1,
.fs-54 {
    font-size: 54px !important;
    line-height: 64px !important;
    @media (max-width: 991px) {
        font-size: 40px !important;
        line-height: 50px !important;
    }
    @media (max-width: 480px) {
        font-size: 33px !important;
        line-height: 43px !important;
    }
}
h2,
.fs-40 {
    font-size: 40px !important;
    @media (max-width: 991px) {
        font-size: 36px !important;
    }
    @media (max-width: 575px) {
        font-size: 30px !important;
    }
}
.object-fit-cover {
    object-fit: cover !important;
}
.img-fluid {
    max-width: 100% !important;
    height: auto !important;
}
.text-gray-100 {
    color: $gray-100 !important;
}
.bg-gray-100 {
    background-color: $gray-100 !important;
}
.text-gray-200 {
    color: $gray-200 !important;
}

.text-gray-300 {
    color: $gray-light !important;
}

.text-secondary {
    color: $secondary !important;
}
.text-primary {
    color: $primary !important;
}
.bg_secondary {
    background-color: $secondary !important;
}
.bg_primary {
    background-color: $primary !important;
}
.bg-secondary-light {
    background-color: #281a7720 !important;
}
.shadow {
    box-shadow: 0px 3px 25px rgba(0, 0, 0, 0.1) !important;
}
.plan-btn {
    padding: 10px 30px !important;
    font-weight: 500 !important;
    box-shadow: none !important;
    background-color: transparent !important;

    &:focus {
        box-shadow: none !important;
    }
}
.btn {
    border-radius: 25px !important;
    padding: 10px 30px !important;
    font-weight: 500 !important;
    box-shadow: none !important;

    &:focus {
        box-shadow: none !important;
    }
}
.btn-primary-light {
    border: 1px solid $primary !important;
    background-color: transparent !important;
    color: $white !important;
    box-shadow: none !important;
    &:hover,
    &:focus,
    &:active &.active {
        box-shadow: none !important;
        background-color: $primary !important;
        color: $white !important;
        border: 1px solid $primary !important;
    }
}

.btn-primary {
    border: 1px solid $primary !important;
    background-color: $primary !important;
    color: $white !important;
    box-shadow: none !important;
    &:hover,
    &:focus,
    &:active,
    &.active {
        box-shadow: none !important;
        background-color: transparent !important;
        color: $white !important;
        border: 1px solid $primary !important;
    }
}

.auth_pages {
    .btn-primary {
        &:hover {
            color: $primary !important;
        }
    }
}

.form-check-input[type="checkbox"] {
    border-radius: 2px !important;
}
.form-check-input:checked {
    background-color: $secondary !important;
    border-color: $secondary !important;
}
.form-check-input:focus {
    box-shadow: none !important;
    border: 1px solid $secondary !important;
}

.subscribe-section {
    position: relative;
    z-index: 2;
    transform: translateY(55px) !important;
    .subscribe-box {
        padding: 45px 60px !important;
        border-radius: 20px !important;
        @media (max-width: 991px) {
            padding: 40px 50px !important;
        }
        @media (max-width: 575px) {
            padding: 25px 30px !important;
        }
        .form-control {
            padding: 10px 20px !important;
            height: 50px !important;
            background-color: $white !important;
            font-size: 18px !important;
            color: $gray-200 !important;
            border-radius: 10px !important;
            border: 0 !important;
            &:focus {
                box-shadow: none !important;
            }
            &.subscribe-input {
                padding-right: 160px !important;
                @media (max-width: 575px) {
                    padding-right: 20px !important;
                }
            }
        }
        .subscribe-btn {
            background-color: $primary !important;
            color: $white !important;
            border: 1px solid $primary !important;
            border-radius: 0 10px 10px 0 !important;
            font-size: 18px !important;
            position: absolute !important;
            right: 0 !important;
            top: 0 !important;
            @media (max-width: 575px) {
                position: relative !important;
                display: flex !important;
                margin: 20px auto 0 auto !important;
                border-radius: 10px !important;
            }
        }
    }
}

.terms_condition,
.privacy_policy {
    background-image: radial-gradient(
        circle farthest-corner at 10% 20%,
        rgba(166, 239, 253, 0.25) 0%,
        rgba(166, 239, 253, 0.25) 90.1%
    );
    z-index: 0;

    .dangerously_set_html {
        width: 90% !important;
        padding: 20px !important;
        text-align: justify !important;

        > p {
            font-weight: 200 !important;

            > strong {
                font-weight: bold !important;
            }
        }
        > ul,
        > * > ul,
        > * > * > ul {
            margin-left: 2rem !important;
            font-weight: 200 !important;

            > strong,
            strong,
            > li strong {
                font-weight: bold !important;
            }
        }

        > ol,
        > * > ol,
        > * > * > ol {
            font-weight: 200 !important;

            > strong,
            strong,
            > li strong {
                font-weight: bold !important;
            }
        }
    }

    p,
    ul,
    ol {
        font-size: 17.5px !important;
        line-height: 1.625 !important;
        // margin-bottom: 14px !important;
        font-weight: 300 !important;

        &.mb-0 {
            margin-bottom: 0 !important;
        }
    }

    .feature-bg {
        position: absolute;
        z-index: -1;
        &.vector-1 {
            left: 0;
            top: 0px;
            right: 0;
            z-index: -1;
        }
        &.vector-2 {
            right: 0;
            top: 100rem;
            z-index: -1;
        }
        &.contact_side_vector1 {
            right: 3.125rem;
            top: 50%;
            z-index: -1 !important;
        }
    }

    .qrcode-bg {
        position: absolute;
        z-index: -1;
        &.vector {
            left: 3rem;
            top: 45rem;
        }
        &.vector-1 {
            left: 0;
            right: 0;
            bottom: 100rem;
        }
        &.vector-2 {
            right: 0;
            left: 0;
            bottom: 0px;
        }
    }

    .qrpersonal-bg {
        position: absolute;
        z-index: -1;
        img {
            @media (max-width: 991px) {
                width: 70%;
            }
            @media (max-width: 575px) {
                width: 50%;
            }
        }
        &.vector-1 {
            right: 0;
            top: 175rem;
        }
        &.vector-2 {
            left: 0;
            bottom: 175rem;
        }
    }

    .background {
        background-color: #fff !important;
        background: #fff !important;
    }
}
