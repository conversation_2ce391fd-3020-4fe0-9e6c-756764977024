@import "variable.scss";
.body {
    background-color: #eff3f7 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    height: 100vh !important;
    @media (max-width: 768px) {
        height: 100% !important;
    }
}
.flex-column-fluid {
    flex: 1 0 auto !important;
}
.width-540 {
    max-width: 540px !important;
    width: 100% !important;
}
.shadow-md {
    box-shadow: 0 5px 20px rgb(173 181 189 / 20%) !important;
}
.image {
    display: inline-block !important;
    flex-shrink: 0 !important;
    -o-object-fit: cover !important;
    object-fit: cover !important;
    position: relative !important;
}
.logo-fix-size {
    max-height: 60px !important;
    max-width: 120px !important;
    width: 100% !important;
    -o-object-fit: cover !important;
    object-fit: cover !important;
}

.sign-in-form {
    padding: 40px 20px !important;
    border-radius: 0.938rem !important;
    @media (max-width: 576px) {
        padding: 25px !important;
    }
    form {
        .form-group {
            label {
                font-size: 16px !important;
                margin-bottom: 8px !important;
                font-weight: 400 !important;
            }
            .form-control {
                background-color: #fff !important;
                border: 1px solid #ced4da !important;
                border-radius: 0.313rem !important;
                color: #6c757d !important;
                font-size: 15px !important;
                font-weight: 400 !important;
                line-height: 1.5 !important;
                padding: 0.688rem 0.938rem !important;
                transition: border-color 0.15s ease-in-out,
                    box-shadow 0.15s ease-in-out !important;
                width: 100% !important;
                &:focus {
                    outline: 0 !important;
                    box-shadow: none !important;
                }
            }
        }
        .btn-primary {
            &:hover,
            &:focus,
            &.active,
            &:active {
                color: $primary !important;
            }
        }
    }
}

.required:after {
    color: #f62947 !important;
    content: "*" !important;
    font-size: inherit !important;
    font-weight: 700 !important;
    position: relative !important;
}
