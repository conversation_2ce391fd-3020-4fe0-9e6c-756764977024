@import "variables";

.carousel {
    &:after {
        position: absolute;
        content: "";
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        overflow: hidden;
        background-color: rgba($gray-900, 0.4);
        border-radius: $border-radius-sm;
    }

    .carousel-title {
        font-size: 2rem;

        @media (max-width: 767px) {
            font-size: 1.5rem;
        }
    }

    .carousel-img {
        &:before {
            content: "";
            display: block;
            padding-top: 65%;
        }

        img {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: auto;
            min-height: 100%;
            min-width: 100%;
            margin: auto;
            height: auto;
        }
    }

    .carousel-caption {
        position: absolute;
        right: auto;
        bottom: auto;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;

        p {
            @media (max-width: 767px) {
                font-size: 0.875rem !important;
            }
        }
    }

    .carousel-control-prev, .carousel-control-next {
        width: 4.375rem;
        height: 4.375rem;
        border: 0.125rem solid $white;
        border-radius: 50%;
        opacity: 1;
        top: 0;
        bottom: 0;
        margin: auto;

        @media (max-width: 767px) {
            width: 2.5rem;
            height: 2.5rem;
        }
    }

    .carousel-control-prev-icon, .carousel-control-next-icon {
        @media (max-width: 767px) {
            width: 1.25rem;
            height: 1.25rem;
        }
    }

    .carousel-control-prev {
        transform: rotate(-180deg);
        left: 50px;

        @media (max-width: 767px) {
            left: 20px;
        }
    }

    .carousel-control-next {
        right: 50px;

        @media (max-width: 767px) {
            right: 20px;
        }
    }
}
