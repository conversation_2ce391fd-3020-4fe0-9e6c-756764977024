//mixin-font-face
@function str-replace($string, $search, $replace: "") {
    $index: str-index($string, $search);
    @if $index {
        @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);
    }
    @return $string;
}

@mixin font-face($name, $path, $weight: null, $style: null, $exts: woff2 woff ttf svg) {
    $src: null;
    $extmods: (
        svg: "#" + str-replace($name, " ", "_")
    );
    $formats: (
        ttf: "truetype"
    );
    @each $ext in $exts {
        $extmod: if(map-has-key($extmods, $ext), $ext + map-get($extmods, $ext), $ext);
        $format: if(map-has-key($formats, $ext), map-get($formats, $ext), $ext);
        $src: append($src, url(quote($path + "." + $extmod)) format(quote($format)), comma);
    }
    @font-face {
        font-family: quote($name);
        font-style: $style;
        font-weight: $weight;
        src: $src;
        font-display: swap;
    }
}

//@include font-face("Poppins", "../../assets/fonts/Poppins-Regular", 400, normal, ttf);
//@include font-face("Poppins", "../../assets/fonts/Poppins-Bold", 700, normal, ttf);
//@include font-face("Poppins", "../../assets/fonts/Poppins-BoldItalic", 700, italic, ttf);
//@include font-face("Poppins", "../../assets/fonts/Poppins-Medium", 500, normal, ttf);
//@include font-face("Poppins", "../../assets/fonts/Poppins-MediumItalic", 500, italic, ttf);
