@import "variables";

.btn-success, .btn-warning, .btn-secondary {
    color: $gray-900;

    &:hover, &:focus, &:active {
        color: $gray-900;
    }
}

.btn {
    box-shadow: none !important;

    &:focus {
        box-shadow: none !important;
    }
}

.btn-outline-light {
    color: $dark;
}

.btn.btn-icon {
    font-size: 1.125rem;
    padding: 0.375rem !important;
    height: 2.563rem !important;
    width: 2.563rem !important;
}

.btn-success:not(.btn-outline):not(.btn-icon) {
    color: $white;
}
