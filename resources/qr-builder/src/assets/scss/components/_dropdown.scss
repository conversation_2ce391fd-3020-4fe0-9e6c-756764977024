@import "variables";

.dropdown-toggle {
    &:after {
        border: solid whitesmoke;
        border-width: 0 2px 2px 0;
        display: inline-block;
        padding: 4px;
        transform: rotate(45deg);
        -webkit-transform: rotate(45deg);
        margin-left: 10px;
        vertical-align: 2px;
    }
}

.country_code_dropdown {
    .dropdown-menu {
        position: absolute !important;
        overflow: auto !important;
        left: 0px !important;
        max-height: 250px;
        min-width: 75px;
        .dropdown-item {
            font-size: 14px;
            padding: 5px;
        }
    }
}

.country_code_dropdown.input-group .btn {
    z-index: 0 !important;
}

.table-dropdown {
    .dropdown-menu {
        // @media (max-width: 925px) {
        position: absolute !important;
        transform: translate3d(0.5px, -24px, 0px) !important;
        will-change: transform !important;
        top: auto !important;
        right: 10px !important;
        margin-bottom: 55px;
        margin-top: -30px;
        z-index: 1111111;
        // }
    }
}

.dropdown-menu {
    box-shadow: $theme-box-shadow;
    min-width: 270px;
    transition: all 400ms ease-in-out;
    transform: translate(0, 80px);

    .dropdown-item {
        &:hover {
            .dropdown-icon {
                color: $cyan;
            }
        }

        .dropdown-icon {
            width: 20px;
        }
    }
}

.dropdown.custom-dropdown {
    .dropdown-menu {
        @media (min-width: 575px) {
            min-width: 355px;
        }
    }
}

.dropdown-toggle.hide-arrow {
    &:after {
        content: none;
    }
}

//dropdown-bg-transparent
.dropdown.dropdown-transparent {
    .dropdown-toggle {
        &:after {
            content: none;
        }
    }
}

//dropdown-hover
.dropdown.dropdown-hover {
    .dropdown-menu {
        display: block;
        min-width: 210px;
        top: 0;
        opacity: 0;
        visibility: hidden;
        transform: translate(0, 20px);
    }

    &:hover {
        .dropdown-menu {
            transform: translate(0, 0);
            opacity: 1;
            visibility: visible;
        }
    }
}

// all dropdown input css
.css-13cymwt-control,
.css-t3ipsp-control {
    cursor: pointer !important;
}

// .sideMenu-footer{
//     position: absolute;
//     bottom: 0;
//     width: 100%;
//     margin: 0 auto;
//     padding: 10px 5px;
// }

// .sideMenu-footer .dropdown-menu{
//     // position: absolute;
//     position: fixed !important;
//     width: fit-content;
//     min-width: 255px !important;
//     inset: auto auto 0px 0px;
//     transform: translate(0px, -50px);
//     inset: auto auto 0px 0px;
//     padding: 10px !important;
//     height: auto;
//     box-sizing: border-box;
//     box-shadow: 0 5px 20px rgb(173 181 189 / 20%);
//     transition: all 400ms ease-in-out;
//     transform: translate3d(0.5px, -24px, 0px) !important;
//     will-change: transform !important;
//     top: auto !important;
//     left: 5px !important;
//     margin-bottom: 32px !important;
// }
