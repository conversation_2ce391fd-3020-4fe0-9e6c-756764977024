// Mode
$mode: dark;

// Initialize
@import "init";

// Components
@import "components/components";

// Layouts
@import "layout/layout";

body.dark-theme {
    background-color: #0b2647f7;

    .border {
        border: 1px solid #6c757d !important;
    }

    .shadow-md {
        box-shadow: 0 5px 20px rgb(8 10 23);
    }

    .h1,
    .h2,
    h1,
    h2,
    .btn-success,
    .btn-warning,
    .btn-secondary,
    h3,
    .h3,
    h4,
    .h4,
    h5,
    .h5,
    h6,
    .h6 {
        color: #ffffff !important;
    }

    ::-webkit-scrollbar-thumb:vertical,
    ::-webkit-scrollbar-thumb:horizontal {
        background: #6c757d;
    }

    .border-top,
    .border-bottom {
        border-color: #495057 !important;
    }

    .text-theme-white {
        color: #0a2647 !important;
    }

    .text-cyan-100 {
        color: #262832 !important;
    }

    .text-cyan-200 {
        color: #282d66 !important;
    }

    .text-cyan-300 {
        color: #3d4499 !important;
    }

    .text-cyan-400 {
        color: #515acc !important;
    }

    .text-cyan-500 {
        color: #A561FF !important;
    }

    .text-cyan-600 {
        color: #848dff !important;
    }

    .text-cyan-700 {
        color: #a3aaff !important;
    }

    .text-cyan-800 {
        color: #c1c6ff !important;
    }

    .text-cyan-900 {
        color: #e8dcf7 !important;
    }

    .text-gray-100 {
        color: #1d1f28 !important;
    }

    .text-gray-200 {
        color: #262832 !important;
    }

    .text-gray-300 {
        color: #495057 !important;
    }

    .text-gray-400 {
        color: #6c757d !important;
    }

    .text-gray-500 {
        color: #adb5bd !important;
    }

    .text-gray-600 {
        color: #b7c3e3 !important;
    }

    .text-gray-700 {
        color: #dee2e6 !important;
    }

    .text-gray-800 {
        color: #e9ecef !important;
    }

    .text-gray-900 {
        color: #ffffff !important;
    }

    .text-blue-100 {
        color: #001f32 !important;
    }

    .text-blue-200 {
        color: #003d64 !important;
    }

    .text-blue-300 {
        color: #005c97 !important;
    }

    .text-blue-400 {
        color: #007ac9 !important;
    }

    .text-blue-500 {
        color: #0099fb !important;
    }

    .text-blue-600 {
        color: #33adfc !important;
    }

    .text-blue-700 {
        color: #66c2fd !important;
    }

    .text-blue-800 {
        color: #99d6fd !important;
    }

    .text-blue-900 {
        color: #ccebfe !important;
    }

    .text-green-100 {
        color: #022617 !important;
    }

    .text-green-200 {
        color: #044d2e !important;
    }

    .text-green-300 {
        color: #067346 !important;
    }

    .text-green-400 {
        color: #089a5d !important;
    }

    .text-green-500 {
        color: #0ac074 !important;
    }

    .text-green-600 {
        color: #3bcd90 !important;
    }

    .text-green-700 {
        color: #6cd9ac !important;
    }

    .text-green-800 {
        color: #9de6c7 !important;
    }

    .text-green-900 {
        color: #cef2e3 !important;
    }

    .text-yellow-100 {
        color: #332507 !important;
    }

    .text-yellow-200 {
        color: #664a0d !important;
    }

    .text-yellow-300 {
        color: #996e14 !important;
    }

    .text-yellow-400 {
        color: #cc931a !important;
    }

    .text-yellow-500 {
        color: #ffb821 !important;
    }

    .text-yellow-600 {
        color: #ffc64d !important;
    }

    .text-yellow-700 {
        color: #ffd47a !important;
    }

    .text-yellow-800 {
        color: #ffe3a6 !important;
    }

    .text-yellow-900 {
        color: #fff1d3 !important;
    }

    .text-red-100 {
        color: #31080e !important;
    }

    .text-red-200 {
        color: #62101c !important;
    }

    .text-red-300 {
        color: #94192b !important;
    }

    .text-red-400 {
        color: #c52139 !important;
    }

    .text-red-500 {
        color: #f62947 !important;
    }

    .text-red-600 {
        color: #f8546c !important;
    }

    .text-red-700 {
        color: #fa7f91 !important;
    }

    .text-red-800 {
        color: #fba9b5 !important;
    }

    .text-red-900 {
        color: #fdd4da !important;
    }

    .text-hover-white:hover {
        color: #ffffff !important;
    }

    .text-hover-light:hover {
        color: #0a2647 !important;
    }

    .text-hover-primary:hover {
        color: #A561FF !important;
    }

    .text-hover-secondary:hover {
        color: #adb5bd !important;
    }

    .text-hover-success:hover {
        color: #0ac074 !important;
    }

    .text-hover-info:hover {
        color: #0099fb !important;
    }

    .text-hover-warning:hover {
        color: #ffb821 !important;
    }

    .text-hover-danger:hover {
        color: #f62947 !important;
    }

    .text-hover-dark:hover {
        color: #ffffff !important;
    }

    .bg-theme-white {
        background-color: #1c3255 !important;
    }

    .bg-cyan-100 {
        background-color: #262832 !important;
    }

    .bg-cyan-200 {
        background-color: #282d66 !important;
    }

    .bg-cyan-300 {
        background-color: #3d4499 !important;
    }

    .bg-cyan-400 {
        background-color: #515acc !important;
    }

    .bg-cyan-500 {
        background-color: #A561FF !important;
    }

    .bg-cyan-600 {
        background-color: #848dff !important;
    }

    .bg-cyan-700 {
        background-color: #a3aaff !important;
    }

    .bg-cyan-800 {
        background-color: #c1c6ff !important;
    }

    .bg-cyan-900 {
        background-color: #e8dcf7 !important;
    }

    .bg-gray-100 {
        background-color: #1d1f28 !important;
    }

    .bg-gray-200 {
        background-color: #262832 !important;
    }

    .bg-gray-300 {
        background-color: #495057 !important;
    }

    .bg-gray-400 {
        background-color: #6c757d !important;
    }

    .bg-gray-500 {
        background-color: #adb5bd !important;
    }

    .bg-gray-600 {
        background-color: #6c757e !important;
    }

    .bg-gray-700 {
        background-color: #dee2e6 !important;
    }

    .bg-gray-800 {
        background-color: #e9ecef !important;
    }

    .bg-gray-900 {
        background-color: #ffffff !important;
    }

    .bg-blue-100 {
        background-color: #001f32 !important;
    }

    .bg-blue-200 {
        background-color: #003d64 !important;
    }

    .bg-blue-300 {
        background-color: #005c97 !important;
    }

    .bg-blue-400 {
        background-color: #007ac9 !important;
    }

    .bg-blue-500 {
        background-color: #0099fb !important;
    }

    .bg-blue-600 {
        background-color: #33adfc !important;
    }

    .bg-blue-700 {
        background-color: #66c2fd !important;
    }

    .bg-blue-800 {
        background-color: #99d6fd !important;
    }

    .bg-blue-900 {
        background-color: #ccebfe !important;
    }

    .bg-green-100 {
        background-color: #022617 !important;
    }

    .bg-green-200 {
        background-color: #044d2e !important;
    }

    .bg-green-300 {
        background-color: #067346 !important;
    }

    .bg-green-400 {
        background-color: #089a5d !important;
    }

    .bg-green-500 {
        background-color: #0ac074 !important;
    }

    .bg-green-600 {
        background-color: #3bcd90 !important;
    }

    .bg-green-700 {
        background-color: #6cd9ac !important;
    }

    .bg-green-800 {
        background-color: #9de6c7 !important;
    }

    .bg-green-900 {
        background-color: #cef2e3 !important;
    }

    .bg-yellow-100 {
        background-color: #332507 !important;
    }

    .bg-yellow-200 {
        background-color: #664a0d !important;
    }

    .bg-yellow-300 {
        background-color: #996e14 !important;
    }

    .bg-yellow-400 {
        background-color: #cc931a !important;
    }

    .bg-yellow-500 {
        background-color: #ffb821 !important;
    }

    .bg-yellow-600 {
        background-color: #ffc64d !important;
    }

    .bg-yellow-700 {
        background-color: #ffd47a !important;
    }

    .bg-yellow-800 {
        background-color: #ffe3a6 !important;
    }

    .bg-yellow-900 {
        background-color: #fff1d3 !important;
    }

    .bg-purple-700 {
        background-color: #22143d3d !important;
    }

    .bg-pink-700 {
        background-color: #1f10165e !important;
    }

    .bg-red-100 {
        background-color: #31080e !important;
    }

    .bg-red-200 {
        background-color: #62101c !important;
    }

    .bg-red-300 {
        background-color: #94192b !important;
    }

    .bg-red-400 {
        background-color: #c52139 !important;
    }

    .bg-red-500 {
        background-color: #f62947 !important;
    }

    .bg-red-600 {
        background-color: #f8546c !important;
    }

    .bg-red-700 {
        background-color: #fa7f91 !important;
    }

    .bg-red-800 {
        background-color: #fba9b5 !important;
    }

    .bg-red-900 {
        background-color: #fdd4da !important;
    }

    .bg-light-white {
        color: #ffffff;
    }

    .bg-light-light {
        color: #0a2647;
    }

    .bg-light-primary {
        color: #A561FF;
        background-color: #262832;
    }

    .widget-light-primary {
        background-color: #003d64a1;
    }

    .bg-light-secondary {
        color: #adb5bd;
    }

    .bg-light-success {
        color: #0ac074;
        background-color: #022617;
    }

    .bg-orange-500 {
        background-color: rgba(34, 20, 61, 0.2392156863) !important;
    }

    .bg-light-info {
        color: #0099fb;
        background-color: #001f32;
    }

    .bg-light-warning {
        color: #ffb821;
        background-color: #332507;
    }

    .bg-light-danger {
        color: #f62947;
        background-color: #31080e;
    }

    .bg-light-dark {
        color: #ffffff;
    }

    .bg-white {
        --bs-bg-opacity: 1;
        background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
    }

    .bg-light {
        background-color: #262832 !important;
        color: whitesmoke;
    }

    .bg-dark {
        background-color: #ffffff !important;
        color: black;
    }

    .btn-danger {
        border-color: #f62947 !important;
    }

    .btn-light {
        color: #fff;
        background-color: #0a2647;
        border-color: #0a2647;
    }

    // from handling
    .form-label,
    .dropdown-item {
        color: #ffffff;
    }

    .form-control {
        background-color: #0a2647;
        border-color: #6c757d;
        border: none;
        color: whitesmoke;

        &::placeholder {
            color: #b7c3e3;
        }
    }

    .form-control[readonly] {
        background-color: #0a2647 !important;
    }

    // html text writing handling
    .ql-snow .ql-picker {
        color: #b7c3e3;
    }
    .ql-snow .ql-stroke {
        fill: none;
        stroke: #b7c3e3;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 2;
    }
    .ql-container.ql-snow {
        color: whitesmoke;
    }

    // react table handling
    .css-13cymwt-control,
    .css-t3ipsp-control {
        background-color: #0a2647 !important;
        border-color: #6c757d !important;
        border: none !important;
        color: #b7c3e3 !important;

        .css-1jqq78o-placeholder {
            color: whitesmoke !important;
        }
    }
    .css-13cymwt-control:hover,
    .css-t3ipsp-control:hover {
        background-color: #001f32 !important;
    }
    .css-13cymwt-control > :nth-child(1) > :nth-child(1),
    .css-t3ipsp-control > :nth-child(1) > :nth-child(1) {
        color: whitesmoke;
    }

    // drop down handling
    .css-1nmdiq5-menu > :nth-child(1) > * {
        background-color: #212125;
    }
    .css-1nmdiq5-menu > :nth-child(1) > * {
        &:hover {
            background-color: #e8dcf7;
            color: #212529;
        }
    }
    .css-1nmdiq5-menu {
        background-color: #0a2647;
        color: whitesmoke;
    }

    .css-1nmdiq5-menu > :nth-child(1) > .css-848612-option {
        background-color: #A561FF;
        color: whitesmoke;
    }

    .input-group-text {
        color: #b7c3e3;
        background-color: #626c9d;
        border: none;
    }

    .table-bordered > :not(caption) > * {
        border-top-color: #6c757d;
    }

    .dropdown-menu {
        background-color: #0a2647;
        box-shadow: 0 5px 20px 0 #090b18;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: #1d1f28;
        color: #A561FF;
    }

    .dropdown-toggle {
        &:after {
            border: solid whitesmoke;
            border-width: 0 2px 2px 0;
            display: inline-block;
            padding: 4px;
            transform: rotate(45deg);
            -webkit-transform: rotate(45deg);
            margin-left: 10px;
            vertical-align: 2px;
        }
    }

    .modal-backdrop {
        background-color: #ffffff;
    }

    .modal-content {
        background-color: #1c3255;
        border: 0 solid rgba(6, 9, 23, 0.2);

        .modal-header {
            border-bottom: 0 solid #495057;

            &:after {
                border-bottom-color: #262832;
            }
        }
    }

    .card-body {
        background-color: #294065 !important;
        border-radius: 0.625rem !important;
    }

    .card {
        background-color: #294065 !important;

        .card-header {
            background-color: #294065 !important;
            // border-bottom: 1px solid #1d1f28 !important;
        }
    }

    .table > thead > tr > th {
        background-color: #262832;
        border-bottom: 1px solid #1d1f28;
        color: #b7c3e3 !important;
    }

    .table {
        --bs-table-border-color: #495057;
        -bs-table-striped-color: #6c757e;
        --bs-table-striped-bg: #0a2647;
        --bs-table-active-color: #6c757e;
        --bs-table-active-bg: rgba(6, 9, 23, 0.1);
        --bs-table-hover-color: #6c757e;
        --bs-table-hover-bg: rgba(6, 9, 23, 0.075);
    }

    .sweet-alert {
        background-color: #0a2647 !important;

        h2 {
            color: #ffffff !important;
        }

        .sweet-text {
            color: #b7c3e3 !important;
        }
    }

    .Toastify__toast {
        background: #0a2647;
        box-shadow: 0 5px 20px 0 #adb5bd33;
    }

    .toast-card__toast-message {
        color: #ffffff;
    }

    .toast-card__close-btn {
        color: #dee2e6;
    }

    .css-1s2u09g-control,
    .css-1pahdxg-control,
    .datepicker__custom-datepicker,
    .sc-ciZhAO.jrYYEB input,
    .sc-ciZhAO.kEOAOP input,
    .sc-ciZhAO.jrYYEB .sc-himrzO.lmTBZl,
    .sc-ciZhAO.jrYYEB .sc-himrzO.fIFUgZ,
    .sc-ciZhAO.kEOAOP .sc-himrzO.lmTBZl,
    .sc-ciZhAO.kEOAOP .sc-himrzO.fIFUgZ {
        background-color: #0a2647 !important;
        border-color: #6c757d !important;
        // box-shadow: 0 5px 20px 0 #090b18;
        color: whitesmoke;
    }

    .grp-select .select-box .css-26l3qy-menu {
        background-color: #0a2647;
        color: #ffffff;
        box-shadow: 0 5px 20px 0 #090b18 !important;

        .css-4ljt47-MenuList {
            border-color: #6c757d;
        }
    }

    .pro-sidebar,
    .pro-sidebar-inner {
        background-color: #0a2647 !important;
    }

    .pro-menu {
        .pro-menu-item {
            .pro-inner-item {
                &:hover {
                    background-color: #0b0f1a;
                    border-left-color: #A561FF !important;
                    color: #fff;

                    svg,
                    .pro-item-content a {
                        color: #fff;
                    }
                }
            }

            &.active {
                .pro-inner-item {
                    background-color: #262832;
                    border-left-color: #A561FF !important;

                    svg,
                    .pro-item-content a {
                        color: #fff;
                    }
                }
            }

            .pro-icon-wrapper {
                .pro-icon {
                    color: #6c757e;
                }
            }

            .pro-item-content {
                a {
                    color: #b7c3e3;
                }
            }
        }
    }

    .header-active,
    .header-tabs:hover,
    .header-tabs:focus {
        color: #ffffff !important;
    }

    .header {
        background-color: #0a2647 !important;
        box-shadow: 0 0 20px rgba(173, 181, 189, 0.1);
    }

    .header .navbar-nav .nav-item .nav-link,
    .header .navbar-nav .nav-item .active.nav-link,
    .sc-ciZhAO.jrYYEB .sc-himrzO.lmTBZl .ellipsis span,
    .sc-ciZhAO.jrYYEB .sc-himrzO.fIFUgZ .ellipsis span,
    .sc-ciZhAO.kEOAOP .sc-himrzO.lmTBZl .ellipsis span,
    .sc-ciZhAO.kEOAOP .sc-himrzO.fIFUgZ .ellipsis span,
    .sc-ciZhAO.jrYYEB .sc-himrzO.lmTBZl > ul li:before,
    .sc-ciZhAO.jrYYEB .sc-himrzO.fIFUgZ > ul li:before,
    .sc-ciZhAO.kEOAOP .sc-himrzO.lmTBZl > ul li:before,
    .sc-ciZhAO.kEOAOP .sc-himrzO.fIFUgZ > ul li:before {
        color: #ffffff;
    }

    .data-table header {
        background-color: transparent !important;
    }

    .rdt_Table {
        background-color: #14151f;
        box-shadow: none;

        .rdt_TableHead {
            .rdt_TableHeadRow {
                .rdt_TableCol {
                    @media (max-width: 925px) {
                        color: #b7c3e3 !important;
                        background-color: #1d1f28 !important;
                        border-bottom: 1px solid #262832 !important;
                        border-right: 0px !important;
                        border-left: 0px !important;
                    }
                }
            }
        }
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow {
        background-color: #1d1f28 !important;
        border-bottom: 1px solid #262832;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow {
        .rdt_TableCol {
            color: #b7c3e3 !important;
        }
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow {
        background-color: #0a2647;
        color: #d0dce6;
        border-bottom: 1px solid #262832;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow:nth-child(odd) {
        background-color: #0b0f1a;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow:nth-child(even) {
        background-color: #212125;
        .rdt_TableCell {
            @media (max-width: 925px) {
                background-color: #212125 !important;
                border-top: 0.5px solid #212125;
                border-bottom: 0.5px solid #212125;
                padding-top: 10px;
                padding-bottom: 10px;
            }
        }
    }

    // user Profile handling
    .profilename {
        color: whitesmoke !important;
    }

    // widget handling
    .widget > :nth-child(1) {
        box-shadow: 0 5px 20px rgb(173 181 189 / 20%) !important;
    }

    // copy handling
    .coppyBtn {
        svg {
            color: aliceblue;
        }
    }

    // emty data handling
    .nodata {
        color: whitesmoke;
    }

    // table handling
    tbody > :nth-child(odd) {
        background-color: #0b0f1a;
        color: whitesmoke;
    }

    tbody > :nth-child(even) {
        background-color: #212125;
        color: whitesmoke;
    }

    tbody > tr > td {
        color: whitesmoke !important;
    }

    .fwKvpK {
        background-color: #0a2647;
        color: #6c757e;
    }

    // Pagination handling
    .rdt_Pagination {
        background-color: transparent !important;
        color: #d0dce6;
    }

    .rdt_Pagination .sc-gicCDI {
        background-color: #0a2647;
        border-color: #495057;
        color: #6c757e !important;
    }

    .rdt_Pagination div:nth-child(4) button {
        background-color: transparent !important;
        fill: #d0dce6 !important;
    }

    .rdt_Pagination .sc-gicCDI:hover,
    .rdt_Pagination .sc-gicCDI:focus {
        fill: #A561FF !important;
        background-color: #262832 !important;
        border-color: #495057;
    }

    .rdt_Pagination div:first-of-type select {
        background-color: #1d1f28;
    }

    .image-picker .previewImage,
    .image-picker .picker-edit {
        border-color: #262832;
    }

    .image-picker .picker-edit {
        background-color: #0a2647;
    }

    .popover {
        background-color: #0a2647;
        font-family: "Poppins", Helvetica, sans-serif;

        .popover-body {
            color: #6c757e;
        }

        .list-group-item {
            background-color: #0a2647;
            color: #6c757e;
        }
    }

    .bs-popover-bottom > .popover-arrow::before,
    .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before,
    .bs-popover-bottom > .popover-arrow::after,
    .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after {
        border-bottom-color: #0a2647;
    }

    .bs-popover-start > .popover-arrow::after,
    .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after {
        border-left-color: #0a2647;
    }

    .datepicker .react-datepicker {
        background-color: #0a2647 !important;
        box-shadow: none !important;
        color: #6c757d !important;
    }

    .react-datepicker__time-list {
        background-color: #0a2647 !important;
        box-shadow: none !important;
        color: whitesmoke !important;
    }

    .datepicker .react-datepicker__day {
        color: #6c757d;
    }

    .react-datepicker-time__header,
    .react-datepicker__header {
        color: #6c757e;
    }

    .datepicker .react-datepicker__current-month,
    .datepicker .react-datepicker__day-name,
    .modal-content .modal-header .close {
        color: #ffffff;
    }

    .react-datepicker__day:hover,
    .react-datepicker__month-text:hover,
    .react-datepicker__quarter-text:hover,
    .react-datepicker__year-text:hover,
    .react-datepicker__time-list-item:hover {
        background-color: #A561FF !important;
        color: #0a2647 !important;
    }

    .input-icon {
        color: #6c757e;
    }

    .react-datepicker__year-read-view--down-arrow,
    .react-datepicker__month-read-view--down-arrow,
    .react-datepicker__month-year-read-view--down-arrow,
    .react-datepicker__navigation-icon::before,
    .react-datepicker__time-list-item:hover {
        border-color: #6c757e;
    }

    .css-1insrsq-control {
        background-color: #262832;
        border-color: #262832;
    }

    .css-26l3qy-menu .css-1n7v3ny-option:hover,
    .css-26l3qy-menu .css-1n7v3ny-option:not(:active) {
        color: #ffffff !important;
        background-color: #282d66 !important;
    }

    .barcode-main {
        border: 1px solid #495057;

        &__barcode-item {
            border: 1px dotted #495057;
        }
    }

    .menu-icon-grid button {
        color: #ffffff;
    }

    .nav-tabs > li > button {
        color: #b7c3e3;
    }

    .nav-tabs > .nav-link {
        color: #b7c3e3;
    }

    .nav-tabs > .nav-link.active {
        color: #A561FF;
        color: #fff;
    }

    .form-switch {
        color: whitesmoke;
    }

    .form-label {
        fill: whitesmoke;
    }

    .text-muted {
        color: #b7c3e3 !important;
    }

    .figure-caption {
        color: #b7c3e3 !important;
    }

    .image-effect {
        -webkit-transform: scale(1);
        transform: scale(1);
        -webkit-transition: 0.3s ease-in-out;
        transition: 0.3s ease-in-out;
        &:hover {
            -webkit-transform: scale(1.3);
            transform: scale(1.1);
            box-shadow: 0px 0px 15px #58585d, -0px -0px 15px #58585d;
            -webkit-box-shadow: 0px 0px 15px #58585d, -0px -0px 15px #58585d;
            -moz-box-shadow: 0px 0px 15px #58585d, -0px -0px 15px #58585d;
        }
    }

    .image-effect.active {
        transform: scale(1.1);
        box-shadow: 0px 0px 15px #58585d, -0px -0px 15px #58585d;
        -webkit-box-shadow: 0px 0px 15px #58585d, -0px -0px 15px #58585d;
        -moz-box-shadow: 0px 0px 15px #58585d, -0px -0px 15px #58585d;
        background: transparent !important;
    }

    label {
        color: whitesmoke !important;
    }

    .pricing-card:hover {
        box-shadow: 0 2px 40px 0 rgb(205 205 205 / 23%) !important;
    }

    .label-text {
        color: whitesmoke !important;
    }

    .text-dark {
        color: whitesmoke !important;
    }

    #search {
        border: 1px solid #55598f !important;
    }

    .react-multi-email {
        background-color: #0a2647;
        border: none;
        color: whitesmoke;

        &::placeholder {
            color: #b7c3e3;
        }

        [data-tag="true"] {
            background-color: #0a2647 !important;
            border: 1px solid #55598f !important;
            color: #b7c3e3 !important;
        }

        input {
            background-color: #0a2647;
            border: none;
            color: whitesmoke;

            &::placeholder {
                color: #b7c3e3;
            }
        }
    }
}

// home layout dark mode
body.dark-theme {
    .fillcolor_options_parent,
    .bgcolor_options_parent,
    .ffcolor_options_parent,
    .fscolor_options_parent,
    .eicolor_options_parent,
    .eicolor_options_parent1 {
        .custome_color_picker {
            border: 1px solid #fff;
        }
    }

    .section_title {
        color: #A561FF !important;
    }

    .custom_dropdown .nav__submenu {
        background-color: #0a2647 !important;
        color: whitesmoke !important;
    }

    @keyframes gradient {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link.active {
        color: #A561FF !important;
    }

    .home-header .active-header {
        box-shadow: 0px 10px 24px 0px hsl(214.14deg 59.25% 34.1%) !important;
    }

    header .navbar {
        background-color: #0a2647 !important;
        // box-shadow: 0px 10px 24px 0px hsl(214.14deg 59.25% 34.1%) !important;

        .navbar-collapse .navbar-nav .nav-item .nav-link {
            color: whitesmoke !important;

            &:hover {
                color: #A561FF !important;
            }
        }
    }

    header .navbar .navbar-collapse {
        @media (max-width: 991px) {
            background: #0d2647 !important;
        }
    }

    header .navbar .navbar-collapse > :nth-child(1) > :nth-child(3) {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
    }

    .nav-bropdown,
    .nav-bropdown-mini {
        #dropdown-basic {
            z-index: 99999 !important;
            &:hover {
                background-color: transparent !important;
            }
        }
        span {
            font-weight: 500;
        }
        .dropdown-menu {
            border-radius: 10px;
            border: transparent;
            padding: 5px 0px !important;

            a {
                color: #d0dce6 !important;
                font-size: 15px !important;
                padding: 5px 0px;
            }
        }
    }

    @keyframes gradient {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    .hero-content {
        .btn-primary-light {
            position: relative !important;
        }

        .btn-primary-light::before,
        .btn-primary-light::after {
            content: "" !important;
            position: absolute !important;
            top: 0 !important;
            right: 0 !important;
            height: 2px !important;
            width: 0 !important;
            background: #fff !important;
            box-shadow: -1px -1px 5px 0px #fff, 7px 7px 20px 0px #0003, 4px 4px 5px 0px #0002 !important;
            transition: 400ms ease all !important;
        }
        .btn-primary-light::after {
            right: inherit !important;
            top: inherit !important;
            left: 0 !important;
            bottom: 0 !important;
        }
        .btn-primary-light:hover::before,
        .btn-primary-light:hover::after {
            width: 100% !important;
            transition: 800ms ease all !important;
        }

        .btn-primary-light:hover {
            background: transparent !important;
            border: none !important;
        }
    }

    .about-card {
        border: 1px solid #55598f !important;
        .card-text {
            color: #d0dce6 !important;
        }
    }

    .contact-info-section {
        .text-gray-300 {
            color: #cfcfcf !important;
        }
    }

    .contact-info-card {
        &:hover {
            box-shadow: 0px 5px 17px -5px #fff !important;
        }
    }

    .contact-form {
        .form-control {
            background-color: #fff !important;
        }
    }

    .subscribe-content {
        .text-primary {
            color: #cfcfcf !important;
        }
    }

    .static-qr-code-section {
        .static-content {
            p {
                color: #d0dce6 !important;
            }
        }
    }
    .dynamic-qr-code-section {
        .dynamic-content {
            p {
                color: #d0dce6 !important;
            }
        }
    }
    .static-qr-code-section,
    .dynamic-qr-code-section {
        .feature-box {
            box-shadow: 0px 10px 24px 0px hsl(214.14deg 59.25% 34.1%);
        }
    }

    .feature_pera {
        color: hsl(229deg, 6%, 66%) !important;
    }

    .qr-code-types-section {
        .qr-code-types {
            span {
                color: whitesmoke;
            }
            p {
                color: whitesmoke;
            }
        }
    }

    .sign-up-section {
        background-color: #0a2647 !important;
        box-shadow: 0px 0px 20px 10px rgb(18 44 76) !important;
        @media (max-width: 767px) {
            text-align: center;
        }
        p {
            color: #b7c3e3 !important;
        }
        .form-control {
            border: 1px solid #55598f;
            color: whitesmoke;
        }
        .form-control::placeholder {
            color: #b7c3e3 !important;
        }
    }
    footer > :nth-child(1) > :nth-child(2) {
        color: #b7c3e3 !important;
    }

    footer .footer-logo {
        color: whitesmoke;
        @media (max-width: 919px) {
            justify-content: center;
        }
    }
    footer .active {
        color: #A561FF !important;
    }
    footer > :nth-child(1) > :nth-child(1) > :nth-child(2) {
        @media (max-width: 919px) {
            text-align: center;
        }
    }
}

// pricing layout dark mode
body.dark-theme {
    .pricing-plan-section > :nth-child(1) > :nth-child(1) > :nth-child(1) {
        p {
            color: #b7c3e3 !important;
        }
    }
    .pricing-plans {
        color: #d0dce6;
    }

    .pricing-plan-section .pricing-plans .pricing-card .amount {
        color: #d0dce6 !important;
    }

    .pricing-card:hover {
        box-shadow: 1px 4px 41px -8px rgb(174 172 172) !important;
    }

    .sign-up-section {
        background-color: #0a2647 !important;
        box-shadow: 0px 0px 20px 10px rgb(18 44 76) !important;
        @media (max-width: 767px) {
            text-align: center;
        }
        p {
            color: #b7c3e3 !important;
        }
        .form-control {
            border: 1px solid #55598f;
            color: whitesmoke;
        }
        .form-control::placeholder {
            color: #b7c3e3 !important;
        }
    }
    .switch-field {
        border: 1px solid #55598f !important;
    }
    .pricing-plan-section .pricing-plans .pricing-card {
        border: 1px solid #55598f !important;
    }
    .pricing-plan-section .pricing-plans .pricing-card .card-title {
        border: 1px solid #55598f !important;
    }
}

// QR Code generator layout dark mode
body.dark-theme {
    .home {
        background-color: rgba(11, 38, 71, 0.968627451) !important;
    }
    .home > :nth-child(1) {
        h5 {
            color: #b7c3e3 !important;
        }
    }
}

// terms & condition and privacy policy layout dark mode
body.dark-theme {
    .terms_condition,
    .privacy_policy,
    .dynamic_page_content {
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        div,
        span,
        p,
        ul,
        ol,
        li,
        i,
        b,
        u,
        strong,
        em,
        br {
            color: #b7c3e3 !important;
        }
    }
}

// tiny custome style
body.dark-theme {
    .tox:not(.tox-tinymce-inline) .tox-editor-header {
        background-color: #0a2647 !important;

        svg {
            fill: #b7c3e3 !important ;
        }
    }

    .tox-toolbar__primary {
        background-color: #0a2647;
        border: none;
        color: whitesmoke !important;

        &::placeholder {
            color: #b7c3e3;
        }

        svg {
            fill: #b7c3e3 !important ;
        }
    }

    .tox .tox-menubar {
        background-color: #0a2647 !important;
        color: whitesmoke !important;
    }

    .tox .tox-mbtn {
        color: whitesmoke !important;
    }

    .tox .tox-menu.tox-collection.tox-collection--list {
        background-color: #294065 !important;
    }

    .tox .tox-collection--list .tox-collection__item--state-disabled {
        color: #b7c3e3 !important;
    }

    .tox .tox-collection--list .tox-collection__item--active:not(.tox-collection__item--state-disabled) {
        color: #000 !important;
    }

    .tox .tox-collection__item {
        color: whitesmoke !important;
    }

    .tox .tox-edit-area__iframe {
        background-color: #0a2647 !important;
        border: none;

        &::placeholder {
            color: #b7c3e3;
        }
    }

    .tox .tox-dialog-wrap__backdrop {
        background-color: rgb(93 86 86 / 75%);
    }

    .tox .tox-dialog {
        background-color: #0a2647 !important;
    }

    .tox .tox-dialog__header {
        background-color: #0a2647 !important;
        color: whitesmoke !important;
    }

    .tox .tox-dialog__footer {
        background-color: #0a2647 !important;
        color: whitesmoke !important;
    }

    .tox .tox-tbtn--bespoke {
        background: #0a2647 !important;
        background-color: #0a2647 !important;
        color: #b7c3e3;
        border: 1px solid #6c757d !important;
    }

    .tox .tox-button--icon .tox-icon svg,
    .tox .tox-button.tox-button--icon .tox-icon svg {
        fill: whitesmoke !important;
    }
}

body.dark-theme {
    .tab-style .nav-item .nav-link {
        color: whitesmoke;
        background-color: #262832 !important;
    }

    .tab-style .nav-item .nav-link.active {
        background-color: #A561FF !important;
        color: white !important;
        border-radius: 50px;
    }
    .setting-tab .nav-item .nav-link {
        color: whitesmoke;
        background-color: #294065 !important;
    }
    .setting-tab .nav-item .nav-link.active {
        background-color: #A561FF !important;
        color: white !important;
    }
    .qrcodeDesign .nav-tabs {
        width: 20%;
        height: fit-content;
        background: #262833;
        flex-direction: column;
        text-align: center;
        margin-top: 0px !important;
        border-radius: 0.625rem !important;
        @media (max-width: 991px) {
            width: 100%;
            // font-size: 12px !important;
        }
    }
    .qrcodeDesign .nav-tabs .nav-link.active {
        background-color: #A561FF;
        border: 0 !important;
        color: whitesmoke !important;
    }
}

body.dark-theme {
    .nav-bropdown .dropdown-menu {
        box-shadow: 1px 4px 41px -8px rgb(174 172 172) !important;
    }

    .chart-dropdown {
        .nav-item .nav-link {
            color: #fff !important;
        }
    }
}

//charts style
body.dark-theme {
    .charts_style {
        .card {
            background-color: #0f0c2a !important;
        }
        .card-body {
            background-color: #0f0c2a !important;
        }
        .card-header {
            background-color: #0f0c2a !important;
        }
    }
}
