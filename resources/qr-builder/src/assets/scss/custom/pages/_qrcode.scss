.qr_color_btn {
    &:hover {
        background-color: #A561FF !important;
        color: #fff !important;
    }
}
.qr_style_btn {
    .btn-light {
        &:hover {
            background-color: #A561FF !important;
            color: #fff !important;
        }
    }
    .active {
        background-color: #A561FF !important;
        color: #fff !important;
    }
}

.qr_code-card {
    height: fit-content !important;
    top: 1rem !important;
    width: min-content !important;
}

.dropdown-menu {
    height: fit-content !important;
    transform: translate(0px, 43px) !important;
}

.qrShow {
    position: relative;
}
.qrCodeDemo {
    position: absolute;
    top: 50%;
    text-align: center;
    left: 50%;
    right: 50%;
    color: white;
    backdrop-filter: blur(15px);
    width: 94%;
    height: 50%;
    bottom: 50%;
    transform: translate(-50%, -50%);
}

.qr_code-card {
    @media (max-width: 765px) {
        width: 100% !important;
        .qrcode {
            width: 100% !important;
        }
    }
}

#canvas {
    @media (max-width: 765px) {
        width: 100% !important;
        canvas {
            width: 100% !important;
        }
    }
}

.top-10 {
    top: 10% !important;
}
