@import "../../frontend/variable.scss";
.window_loader {
    width: 100% !important;
    height: 100% !important;
    svg {
        width: 29vh !important;
        height: 29vh !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        g {
            g {
                g {
                    path {
                        fill: #a561ff !important;
                    }
                }
            }
        }
    }
}

// main setting tabs
.responsive-setting-tab {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    align-content: center;
}

.me-xsm-2 {
    margin-right: 0.5rem !important;
    @media (max-width: 350px) {
        margin-right: 0rem !important;
    }
}

// cash payment table
.cash-payment-table {
    .data-table > :nth-child(2) {
        .rdt_TableHeadRow > :nth-child(1) {
            max-width: 15% !important;
        }
        .rdt_TableHeadRow > :nth-child(2) {
            max-width: 40% !important;
        }
        .rdt_TableHeadRow > :nth-child(5) {
            max-width: 20% !important;
        }
        .rdt_TableHeadRow {
            .rdt_TableCol {
                max-width: 15%;
            }
        }
        .rdt_TableRow > :nth-child(1) {
            max-width: 15% !important;
        }
        .rdt_TableRow > :nth-child(2) {
            max-width: 40% !important;
        }
        .rdt_TableRow > :nth-child(5) {
            max-width: 20% !important;
        }
        .rdt_TableRow {
            .rdt_TableCell {
                max-width: 15%;
            }
        }
    }
    .rdt_Table
        .rdt_TableHead
        .rdt_TableHeadRow
        > :nth-child(3)
        > :nth-child(1) {
        // justify-content: center;
    }
    // .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(4) > :nth-child(1) {
    //     justify-content: center;
    // }
}

.link-table-columns {
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(1) {
        max-width: 25% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(1) {
        max-width: 25% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(2) {
        max-width: 35% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(2) {
        max-width: 35% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(3) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(3) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(4) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(4) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(5) {
        padding-left: 10px;
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(5) {
        max-width: 15% !important;
    }
}

.business-card-table-user {
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(1) {
        max-width: 30% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(1) {
        max-width: 30% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(2) {
        max-width: 40% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(2) {
        max-width: 40% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(3) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(3) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(4) {
        max-width: 12% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(4) {
        max-width: 12% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(5) {
        max-width: 12% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(5) {
        max-width: 12% !important;
    }
}

.business-card-table {
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(1) {
        max-width: 30% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(1) {
        max-width: 30% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(2) {
        max-width: 40% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(2) {
        max-width: 40% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(3) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(3) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(4) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(4) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(5) {
        max-width: 12% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(5) {
        max-width: 12% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(6) {
        padding-left: 10px;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(6) {
        padding-left: 10px;
    }
}
.link-table {
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(1) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(1) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(2) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(2) {
        max-width: 15% !important;
    }

    // .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(4) {
    //     max-width: 10% !important;
    // }

    // .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(4) {
    //     max-width: 10% !important;
    // }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(4) {
        max-width: 25% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(4) {
        max-width: 25% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(5) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(5) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(6) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(6) {
        max-width: 10% !important;
    }
}

.plan_table {
    .rdt_Table
        .rdt_TableHead
        .rdt_TableHeadRow
        > :nth-child(2)
        > :nth-child(1) {
        // justify-content: center;
    }
}

.transactions-table {
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(1) {
        max-width: 20% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(1) {
        max-width: 20% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(2) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(2) {
        max-width: 15% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(3) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(3) {
        max-width: 15% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(4) {
        max-width: 15% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(4) {
        max-width: 15% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(5) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(5) {
        max-width: 10% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(6) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(6) {
        max-width: 10% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(7) {
        max-width: 10% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(7) {
        max-width: 10% !important;
    }
    .rdt_Table .rdt_TableHead .rdt_TableHeadRow > :nth-child(8) {
        max-width: 5% !important;
    }

    .rdt_Table .rdt_TableBody .rdt_TableRow > :nth-child(8) {
        max-width: 5% !important;
    }
}

// expire-notification
.expire-notification {
    transform: translate(0);
    transition: opacity 2s linear;
    opacity: 0 !important;
}

// lending page
.over-flow-text {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-family: circular std book, Arial, Helvetica, "sans-serif";
    font-size: 18px;
    letter-spacing: 0.54px;
    opacity: 0.9;
    overflow: hidden;
    text-overflow: ellipsis;
}

// qr code dowenload button
.Qr-Code-Download {
    &::after {
        display: none !important;
    }
    &::before {
        display: none !important;
    }
}

//static header
.home-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    background: #fff !important;
}
@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

//react data table
.reactTableStyle .rdt_Table {
    height: calc(100vh - 20vh) !important;
    overflow-y: scroll !important;

    &::-webkit-scrollbar {
        width: 8px !important;
    }
    &::-webkit-scrollbar-track {
        background: transparent !important;
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
        border-radius: 10px !important;
    }

    &::-webkit-scrollbar-thumb {
        background-color: #a561ff !important;
        border-radius: 1px !important;
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5) !important;
    }
}

.reactTableStyle .rdt_Table .rdt_TableHead {
    position: sticky !important;
    top: 0 !important;
    z-index: 1 !important;
}

.home.px-display {
    height: 44px !important;
}

.px-display {
    background: #a561ff !important;
    height: 47px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-left: -5px;
    border-radius: 0px 5px 5px 0px;
}

.long-tooltip {
    width: 200px !important;
}

.h-100vh {
    height: 100vh !important;
}

.image_modal_header {
    display: block !important;
    margin-top: -50px;
    font-size: 25px !important;
    color: whitesmoke !important;
    // position: absolute;
    top: 19%;
    left: 0px;
    right: 0px;

    @media (max-width: 1440px) {
        top: 15%;
    }

    svg {
        height: 25px !important;
    }
}

.image_modal_parent {
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: rgb(0 0 0 / 59%) !important;
    z-index: 9999 !important;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    align-items: center;
}

.image_modal {
    display: block !important;
}

//   .styles-module_tooltip__mnnfp {
//     visibility: hidden;
//     width: max-content;
//     top: 0;
//     left: 0;
//     padding: 8px 16px;
//     border-radius: 3px;
//     font-size: 90%;
//     pointer-events: none;
//     opacity: 0;
//     transition: opacity 0.3s ease-out;
//     will-change: opacity, visibility;
//   }

//   .styles-module_show__2NboJ {
//     position:absolute !important;
//     visibility: visible !important;
//     opacity: 0.9 !important;
//   }

// .subscription-plan-date-picker
//     > :nth-child(2)
//     > :nth-child(2)
//     > :nth-child(2)
//     > :nth-child(1)
//     > :nth-child(1)
//     > :nth-child(2)
//     > :nth-child(1) {
//     display: block !important;
// }

.subscription-plan-date-picker
    .datepicker
    .react-datepicker
    .react-datepicker__month-container {
    width: 100%;
}

.datepicker .react-datepicker__header {
    fill: #6c757e;
    border-bottom: 0 !important;
    background: transparent !important;
    color: #6c757e;
    padding: 0.5rem 1rem 0 !important;
    line-height: 2;
    text-align: center !important;
    position: relative !important;
    user-select: none;
    overflow: hidden;
    flex: 1;
    width: 100%;
    height: auto;
}

.datepicker .react-datepicker__day-names {
    position: relative;
    top: 0px;
    text-align: center;
    overflow: hidden;
    display: flex;
    margin-top: 0px !important;
}

.datepicker .react-datepicker__day-name {
    width: 30px !important;
    height: 30px !important;
    color: #3f4254;
    font-size: none;
    font-weight: 500;
}

.datepicker .react-datepicker__time-container .react-datepicker__header {
    height: 50px;
    width: 75px;
}

.subscription-plan-date-picker
    > :nth-child(2)
    > :nth-child(2)
    > :nth-child(2)
    > :nth-child(1)
    > :nth-child(1) {
    width: 100% !important;
}

.form-check-input[type="radio"] {
    border-radius: 50%;
}

.form-check-input {
    width: 1.563em;
    height: 1.563em;
    margin-top: -0.0315em;
    vertical-align: top;
    background-color: #6c757d;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin-right: 0.4rem;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
}

.tox .tox-statusbar {
    display: none !important;
}

.nav-bropdown-mini {
    display: none !important;
    @media (max-width: 991px) {
        display: block !important;
    }
    button {
        &:focus {
            background-color: transparent !important;
        }
    }
    #dropdown-basic {
        &:hover {
            background-color: transparent !important;
        }
    }
    span {
        font-weight: 500;
    }
    .dropdown-menu {
        border-radius: 10px;
        border: transparent;
        padding: 5px 0px !important;

        a {
            text-align: center;
            font-size: 15px !important;
            padding: 5px 0px;
        }
    }
}

.nav-bropdown {
    @media (max-width: 991px) {
        display: none !important;
    }
    button {
        &:focus {
            background-color: transparent !important;
        }
    }
    #dropdown-basic {
        &:hover {
            background-color: transparent !important;
        }
    }
    span {
        font-weight: 500;
    }
    .dropdown-menu {
        border-radius: 10px;
        border: transparent;
        padding: 5px 0px !important;

        a {
            text-align: center;
            font-size: 15px !important;
            padding: 5px 0px;
        }
    }
}

.nav-bropdown .dropdown-menu {
    box-shadow: 0 2px 40px 0 rgba(205, 205, 205, 0.55);
}

// dynamic page custome style
.ql-align-center {
    text-align: center !important;
}

.ql-align-right {
    text-align: right !important;
}

.ql-align-justify {
    text-align: justify !important;
}

.ql-font-serif {
    font-family: serif !important;
}

.ql-font-monospace {
    font-family: monospace !important;
}

.tab-style {
    display: block;
    width: 100% !important;
    border-radius: 50px;
    // background: white;
    border-bottom: 0;

    .nav-item {
        display: flex;
        width: 33.33% !important;
        border-radius: 50px;
        .nav-link {
            box-shadow: 0 0.5rem 1rem rgba(6, 9, 23, 0.15) !important;
            padding: 20px !important;
            border: 0;
            transition: 0.3s;
            border-radius: 50px !important;
            /* padding: 20px 154px !important; */
            font-size: 1rem;
            background-color: transparent;
            width: 100%;
            margin: 0px 3px !important;
            background-color: white !important;

            &:after {
                @include after-border-bottom;
            }

            &.active {
                background-color: $primary !important;
                color: white !important;
                border-radius: 50px;

                &:after {
                    border-bottom-color: transparent !important;
                }
            }
        }

        &:hover {
            .nav-link {
                &:after {
                    border-bottom-color: transparent !important;
                }
            }
        }
    }
}
.setting-card {
    .card-body {
        border-radius: 0px 0px 0.625rem 0.625rem !important;
    }
    border-radius: 0px 0px 0.625rem 0.625rem !important;
}
.payment-setting-tab {
    .nav-item {
        width: 33.33% !important;
    }
    > :nth-child(1),
    > :nth-child(2) {
        @media (max-width: 455px) {
            width: 50% !important;
        }
    }
    > :nth-child(3) {
        @media (max-width: 455px) {
            width: 100% !important;
        }
    }
}

.setting-tab {
    display: block;
    width: 100% !important;
    margin-bottom: 1px !important;
    // border-radius: 50px;
    // background: white;
    border-bottom: 0;
    .nav-item {
        display: flex;
        width: 25%;
        height: 5rem;

        @media (max-width: 455px) {
            width: 50%;
        }

        .nav-link {
            box-shadow: 0 0.5rem 1rem rgba(6, 9, 23, 0.15) !important;
            padding: 20px !important;
            border: 0;
            transition: 0.3s;
            font-size: 1rem;
            background-color: transparent;
            width: 100%;
            height: 100% !important;
            background-color: white !important;
            @media (max-width: 875px) {
                padding: 15px !important;
            }
            @media (max-width: 538px) {
                font-size: 12px !important;
            }

            &:after {
                @include after-border-bottom;
            }

            &.active {
                background-color: $primary !important;
                color: white !important;
                // border-radius: 50px;

                &:after {
                    border-bottom-color: transparent !important;
                }
            }
        }

        &:hover {
            .nav-link {
                &:after {
                    border-bottom-color: transparent !important;
                }
            }
        }
    }
    > :nth-child(1) {
        button {
            margin-left: 0px !important;
            @media (max-width: 455px) {
                margin-right: 0.5rem !important;

                margin-bottom: -5px !important;
            }
        }
    }
    > :nth-child(2) {
        button {
            @media (max-width: 455px) {
                margin: 0px !important;

                margin-bottom: -5px !important;
            }
        }
    }
    > :nth-child(4) {
        button {
            margin-right: 0px !important;
        }
    }
}
.qrcodeDesign {
    @media (max-width: 825px) {
        flex-direction: column;
    }
    .qr_code-card {
        @media (max-width: 825px) {
            margin-top: 2% !important;
            margin: 0 auto;
        }
    }
    .nav-tabs {
        width: 20%;
        height: fit-content;
        background: white;
        flex-direction: column;
        text-align: center;
        margin-top: 0px !important;
        border-radius: 0.625rem !important;
        @media (max-width: 991px) {
            width: 100%;
        }
    }

    .nav-tabs .nav-link.active {
        background-color: $primary;
        color: whitesmoke;
        border-radius: 0.625rem !important;
    }
    .nav-tabs .nav-link:hover,
    .nav-tabs .nav-link:focus {
        border-color: transparent !important;
    }
    .tab-content {
        width: 80%;
        @media (max-width: 991px) {
            width: 100%;
        }
    }
    .nav-tabs .nav-link {
        margin-bottom: calc(-1 * var(--bs-nav-tabs-border-width));
        background: none;
        border: var(--bs-nav-tabs-border-width) solid transparent;
        border-radius: 0.313rem !important;
    }
}

.badge-text-color {
    color: #281a77 !important;
}

// custom dropdown style
.custom_dropdown {
    $color-blue: #a561ff;
    $color-blue-dark: #0d2035;
    $submenu-width: 13rem;
    transition: 0.3s;
    cursor: pointer;

    ul {
        list-style: none;
        padding-left: 0;
        margin-top: 0;
        margin-bottom: 0;
    }

    .nav__submenu {
        font-weight: 300;
        display: none;
        text-transform: none;
        position: absolute;
        width: $submenu-width;
        background-color: #fff;
        color: #212529;
        box-shadow: 0 2px 40px 0 rgb(205 205 205 / 23%) !important;
        border-radius: 5px;
        overflow: hidden !important;
        left: -50px;
        transform: translateX(-75px) !important;
        z-index: 9999;
        transition: all 0.3s;

        &:hover {
            display: block !important;
            transition: all 0.3s;
        }
    }

    @media (max-width: 991px) {
        .nav__submenu {
            left: 0px;
            transform: translateX(0px) !important;
        }
    }

    .nav__menu-item {
        display: inline-block;
        position: relative;
        transition: 0.3s;
    }

    .nav__menu-item:hover .nav__submenu {
        display: block !important;
        transition: all 0.3s;

        &:hover {
            display: block;
            transition: all 0.3s;
        }
    }

    .nav__submenu-item {
        &:hover {
            background: $color-blue;
            color: #fff;
            transition: all 0.3s;
        }
    }

    .nav__submenu-item:hover .nav__submenu {
        display: block !important;
        transition: all 0.3s;

        &:hover {
            display: block;
            transition: all 0.3s;
        }
    }
}

.bg-orange-500 {
    background-color: #ffc2a3 !important;
}

.fillcolor_options_parent > div,
.bgcolor_options_parent > div,
.ffcolor_options_parent > div,
.fscolor_options_parent > div,
.eicolor_options_parent1 > div,
.eicolor_options_parent > div {
    width: 25px !important;
    height: 25px;
    border-radius: 50% !important;
    background-color: #fff;
    cursor: pointer;
}

.fillcolor_options_parent,
.bgcolor_options_parent,
.ffcolor_options_parent,
.fscolor_options_parent,
.eicolor_options_parent,
.eicolor_options_parent1 {
    .custome_color_picker {
        border-radius: 50% !important;
        border: 1px solid #212529;
    }
}

.fillcolor_options_parent > :nth-child(1) {
    background-color: #fe3500 !important;
}
.fillcolor_options_parent > :nth-child(2) {
    background-color: #200d51 !important;
}
.fillcolor_options_parent > :nth-child(3) {
    background-color: #7f2aff !important;
}
.fillcolor_options_parent > :nth-child(4) {
    background-color: #003fff !important;
}
.fillcolor_options_parent > :nth-child(5) {
    background-color: #9062ed !important;
}

.bgcolor_options_parent > :nth-child(1) {
    background-color: #fff2cc !important;
}
.bgcolor_options_parent > :nth-child(2) {
    background-color: #e9edc9 !important;
}
.bgcolor_options_parent > :nth-child(3) {
    background-color: #ffacac !important;
}
.bgcolor_options_parent > :nth-child(4) {
    background-color: #d9acf5 !important;
}
.bgcolor_options_parent > :nth-child(5) {
    background-color: #b7c5ff !important;
}

.ffcolor_options_parent > :nth-child(1) {
    background-color: #845ec2 !important;
}
.ffcolor_options_parent > :nth-child(2) {
    background-color: #d65db1 !important;
}
.ffcolor_options_parent > :nth-child(3) {
    background-color: #ff6f91 !important;
}
.ffcolor_options_parent > :nth-child(4) {
    background-color: #ff9671 !important;
}
.ffcolor_options_parent > :nth-child(5) {
    background-color: #ffc75f !important;
}

.fscolor_options_parent > :nth-child(1) {
    background-color: #008f7a !important;
}
.fscolor_options_parent > :nth-child(2) {
    background-color: #008e9b !important;
}
.fscolor_options_parent > :nth-child(3) {
    background-color: #0089ba !important;
}
.fscolor_options_parent > :nth-child(4) {
    background-color: #0081cf !important;
}
.fscolor_options_parent > :nth-child(5) {
    background-color: #ff8066 !important;
}

.eicolor_options_parent > :nth-child(1) {
    background-color: #5a61e4 !important;
}
.eicolor_options_parent > :nth-child(2) {
    background-color: #18036b !important;
}
.eicolor_options_parent > :nth-child(3) {
    background-color: #839192 !important;
}
.eicolor_options_parent > :nth-child(4) {
    background-color: #1f618d !important;
}
.eicolor_options_parent > :nth-child(5) {
    background-color: #af7ac5 !important;
}

.eicolor_options_parent1 > :nth-child(1) {
    background-color: #d33621 !important;
}
.eicolor_options_parent1 > :nth-child(2) {
    background-color: #950319 !important;
}
.eicolor_options_parent1 > :nth-child(3) {
    background-color: #4072bd !important;
}
.eicolor_options_parent1 > :nth-child(4) {
    background-color: #891f8d !important;
}
.eicolor_options_parent1 > :nth-child(5) {
    background-color: #c57ab0 !important;
}

.qr_style_btn {
    .form-label {
        width: 75% !important;
        display: inherit;
    }
}

.feature_pera {
    color: #464f57 !important;
    font-weight: 300 !important;
    line-height: 25.6px !important;
}
.hero_section_p {
    font-size: 22px !important;
    font-weight: 600 !important;
    margin-top: 0;
    margin-bottom: 1rem !important;
    color: #a561ff;
}

.hero_section_h1 {
    font-size: 48px !important;
    color: #212529;
    font-weight: 700 !important;
    margin-top: 0 !important;
    line-height: 1.2 !important;
}

.qr-type {
    .image {
        overflow: hidden;
        height: 230px;
        width: 235px;
        position: relative;
        cursor: pointer;
        margin: 5px 15px;
        box-shadow: 2px 2px 7px 1px rgba(0, 0, 0, 0.3);
        transition: 0.5s;
        background-color: #e8dcf7;
        border-radius: 10px;
    }

    @media (max-width: 425px) {
        .image {
            max-width: 100% !important;
        }
    }

    .image:after {
        content: "";
        position: absolute;
        z-index: 0;
        top: 50%;
        left: 50%;
        width: 500px;
        height: 500px;
        transform: translate(-140%, -50%);
        background-color: #a561ff;
        opacity: 0.8;
        border-radius: 50%;
        transition: 0.8s;
    }

    .image:hover:after {
        transform: translate(-50%, -50%);
    }

    .image:hover img {
        transform: translate(-50%, -50%) scale(1.3) rotate(20deg);
    }

    img {
        position: absolute;
        height: 110%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        transition: 0.8s;
    }

    span {
        position: absolute;
        z-index: 2;
        top: 50%;
        left: 50%;
        transform: translate(-2000px, -50%);
        color: #fff;
        transition: 0.8s;
        transition-timing-function: ease-in;
        font-size: 2rem;
    }

    .image:hover span {
        transform: translate(-50%, -50%);
        transition-timing-function: ease;
    }

    .image.active span {
        transform: translate(-50%, -50%);
        transition-timing-function: ease;
    }
    .image.active:after {
        transform: translate(-50%, -50%);
    }

    .image.active img {
        transform: translate(-50%, -50%) scale(1.3) rotate(20deg);
    }
}

//style to react select
.css-1nmdiq5-menu {
    z-index: 9999 !important;
}

.search-box {
    .form-control {
        border: 0;
    }
}

.front-features-header::after {
    border-bottom: 0 !important;
}

.country_code_dropdown {
    button {
        height: 100%;
    }
}

.page-view-switch {
    width: fit-content !important;
}

.download-qr-code {
    .dropdown-menu {
        margin-bottom: 20px !important;
        margin-right: -9.4px !important;
    }
}

.template-hover-sec {
    .image-wrap {
        // width: 360px;
        height: 300px;
        padding: 3px;
        border: 1px solid silver;
        overflow: hidden;
        border-radius: 5px;
    }
    .image-wrap.active {
        border: 3px solid blue;
    }
    .image-wrap img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: top center;
        transition: all 5s ease-in-out;
    }
    .image-wrap:hover img {
        object-position: bottom center;
    }
}

.w-28px {
    width: 28px !important;
    height: 28px !important;
}

.circle {
    height: 3rem !important;
    width: 3rem !important;
    position: fixed;
    display: none;
    bottom: 10px;
    right: 1%;
    line-height: 55px;
    border-radius: 21%;
    font-size: 30px;
    color: #ffe;
    background: #281a77 !important;
    padding: 0px 15px;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 0px 10px 0px #fff;
    font-weight: bolder;
    z-index: 9999;
}

.nav-sm {
    @media (max-width: 320px) {
        .nav-item {
            margin-top: -10px;
            span {
                font-size: 12px;
            }
        }
    }
}

.content-wrap-100 {
    @media (max-width: 462px) {
        width: 100% !important;
    }
}

.content-wrap-50 {
    @media (min-width: 462px) {
        width: 50% !important;
    }
}

.content-wrap-75 {
    @media (min-width: 462px) {
        width: 75% !important;
    }
}

.qr-color-picker {
    .color-picker > :nth-child(2) {
        transform: translatex(-185px) !important;
    }
}

.btn-orange-active {
    background-color: #e46c25 !important;
}
