@import "../../frontend/variable.scss";

.contents {
    background: #eef0f8 !important;
}

#root {
    display: flex;
    flex: 1;
    flex-direction: column;
}

// .home-header.header{
//     box-shadow: 0rem 0px 1rem rgba(6, 9, 23, 0.15) !important;
// }

.home-header {
    width: 100%;
    position: fixed;
    z-index: 9000;
    transition: all 0.15s linear;

    .navbar {
        box-shadow: 0 0 0 rgba(0, 0, 0, 0.15) !important;
    }
}

.home-header {
    .active-header {
        box-shadow: 0rem 0px 1rem rgba(6, 9, 23, 0.15) !important;
    }
}

.toolbar {
    padding: 0 !important;
}

.btn-pink {
    color: $pink;
}

.accordion-button::after {
    //background-image: url("../media/download.svg");
    background-size: 10px;
    opacity: 0.5;
}

.modal-content {
    .modal-header {
        .close {
            border: none;
            font-size: 23px;
            background: none;
        }
    }
}

.object-fit-contain {
    object-fit: contain !important;
}

.object-fit-cover {
    object-fit: cover;
}

.brand-image {
    height: 140px;
}

.text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.user_avatar {
    align-items: center;
    background: $primary;
    color: $white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    width: 80px;
    height: 80px;

    span {
        font-size: 15px;
    }
}
.custom-user-avatar {
    align-items: center;
    background: $primary;
    color: $white;
    border-radius: 50%;
    display: flex;
    width: 50px;
    height: 50px;
    justify-content: center;
    font-size: 25px;
}

.image-input {
    img {
        object-fit: contain;
    }
}

.form-check-input {
    &:focus {
        outline: none !important;
        border-color: #ebedf3;
    }
}

.custom-label {
    width: fit-content;
}

.nav-item {
    .nav-link {
        padding: 0 !important;
    }
}

.custom-loading {
    color: $primary;
    // height: 673px;
    height: calc(100vh - 41vh);
    display: flex;
    justify-content: center;
    align-items: center;
}

.custom-loading-detail {
    color: $primary;
    height: 565px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-company-logo {
    width: 68px !important;
    @media (max-width: 575px) {
        max-width: 68px !important;
    }
}

.custom-img {
    max-width: 40px;
    object-fit: contain;
}

.image > img {
    flex-shrink: 0;
    display: inline-block;
    object-fit: cover;
}

.custom-card-header {
    min-height: unset !important;
    padding: unset !important;
}

.custom-cash-card {
    border: 0 !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 20px 1px rgb(0 0 0 / 6%), 0 1px 4px rgb(0 0 0 / 8%) !important;
}

.react-datepicker-popper {
    z-index: 999 !important;
}

.custom-line-height {
    line-height: 1.6;
    letter-spacing: 0.3px;
}

.form-group {
    .input-group {
        .form-control-solid {
            border-radius: 0.475rem !important;
        }

        .input-group-text {
            position: absolute;
            right: 0;
            top: 2px;
            background-color: transparent;
            border: none;
            border-radius: 0.475rem;
            z-index: 9;
            color: $gray-600;
            font-size: 14px;
            font-weight: 600;
        }
    }
}

.custom-overlay {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.wrapper-res {
    @media (min-width: 1200px) {
        transition: padding-left 0.3s ease;
        padding-left: 4.563rem;
    }
}
.required:after {
    font-weight: 400 !important;
}

.custom-qty {
    max-width: 140px;

    input[type="number"] {
        width: 35px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type="number"] {
        -moz-appearance: textfield;
    }
}

.table > :not(caption) > * > * {
    vertical-align: middle !important;
}

.logo-height {
    height: 45px;
}

.email-template-padding {
    padding-left: 100px !important;
    @media (max-width: 768px) {
        padding-left: 0px !important;
    }
}

.custom-text-center {
    text-align: center !important;
}

.profit-loss {
    padding: 20px 10px;
    background: white;
    margin-top: -1pc;
    border-radius: 0px 0px 10px 10px;
}
.sms_api {
    .sms-api-main {
        background-color: $primary;
        color: $white !important;
        padding: 2.5rem 2.5rem;

        ul {
            padding-left: 41px;
            li {
                list-style: disc;
            }
        }

        h1 {
            color: $white !important;
        }
    }
}

.form-control[readonly] {
    background-color: hsl(0, 0%, 95%) !important;
}

// create product INPUT model
.model-dtn {
    top: 29px;
    right: 1px;
    border-radius: 0 0.475rem 0.475rem 0 !important;
    height: -webkit-fill-available;
}

.dropdown-side-btn {
    .css-tj5bde-Svg {
        margin-right: 4rem;
    }
}
