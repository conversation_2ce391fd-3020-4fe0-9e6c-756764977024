@import "../../components/variables";

.dropdown-menu {
    position: absolute !important;
    padding: 1rem 0 !important;
    top: 0 !important;
    box-shadow: 0 5px 20px rgb(173 181 189 / 20%);
    transition: all 400ms ease-in-out;
    right: 0 !important;
    left: auto !important;

    .dropdown-item {
        &:active {
            background-color: $gray-100;
            color: $primary;
        }
    }
}

.separator {
    border-bottom: 1px solid #eff2f5 !important;
    display: block;
    height: 0;
}

.navbar-expand-lg .navbar-nav .dropdown-menu {
    @media (max-width: 400px) {
        min-width: 230px !important;
        width: 100% !important;
        left: unset !important;
    }
}

.menu-icon-grid {
    button {
        align-items: center;
        color: $gray-900;
        display: flex;
        flex-direction: column;
        padding: 10px;
    }
}

.language-btn {
    &:hover {
        background-color: $primary !important;
        color: $white !important;
    }

    &:focus {
        border-color: $white !important;
    }

    &.language-btn-active {
        background-color: $primary !important;
        border-color: $primary !important;

        span {
            color: $white !important;
        }
    }

    img {
        max-width: 20px;
    }
}

.hide-arrow .dropdown-toggle {
    &:after {
        content: none !important;
    }
}

.pos-button {
    border: 1px solid $primary;
    color: whitesmoke;
    padding: 10px 20px;
    border-radius: 50%;
    text-decoration: none;
    transition-duration: 0.4s;
    cursor: pointer;
    background-color: $primary;
    transition: all 0.6s ease-in-out;
    box-shadow: 0 0 17px 1px rgb(173 181 189 / 5%), 0 6px 20px 0 rgb(0 0 0 / 15%);
}

.pos-button-highlight:hover {
    background-color: transparent;
    color: $primary;
}

.toggle-checkbox {
    opacity: 0;
    position: absolute;
}

.toggle-label {
    width: 50px;
    height: 26px;
    background-color: #111;
    display: flex;
    border-radius: 50px;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    position: relative;
    transform: scale(1.2);
    cursor: pointer !important;
}

.toggle-ball {
    width: 20px;
    height: 20px;
    background-color: white;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 2px;
    border-radius: 50%;
    transition: transform 0.2s linear;
    cursor: pointer !important;
}

/*  target the elemenent after the label*/
.toggle-checkbox:checked + .toggle-label .toggle-ball {
    transform: translateX(24px) translateY(-50%);
}

.fa-moon {
    color: pink;
}

.fa-sun {
    color: yellow;
}
