@import "../../components/variables";

.rdt_TableHeader {
    border-bottom: 1px solid $gray-200;
    padding: 2rem 2.25rem !important;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    @media (max-width: 925px) {
        border: 0;
    }
}

//.sc-dJjYzT {
//    justify-content: space-between !important;
//}
//
//.gzECfY, .bowUGI {
//    padding-left: 0 !important;
//    padding-right: 0 !important;
//}

.search-icon {
    z-index: 1;
}

.data-table > :nth-child(2) {
    box-shadow: 0 0.5rem 1rem rgba(6, 9, 23, 0.15) !important;
}

.data-table > :last-child {
    box-shadow: none !important;
}

.rdt_Table {
    // margin-top: 1.25rem !important;
    border-radius: 0.625rem;
    box-shadow: $theme-box-shadow;
    @media (max-width: 925px) {
        overflow: overlay;
    }
    @media (max-width: 705px) {
        overflow: visible;
    }

    .rdt_TableHead {
        .rdt_TableHeadRow {
            background-color: $gray-100 !important;
            border-bottom: 1px solid $gray-200;
            border-top-left-radius: $border-radius-sm;
            border-top-right-radius: $border-radius-sm;
            @media (max-width: 925px) {
                border: 0;
            }

            .rdt_TableCol {
                color: $gray-600 !important;
                text-transform: uppercase !important;
                text-align: left !important;
                font-size: 0.875rem;
                font-weight: normal !important;
                padding-left: 0;

                @media (max-width: 925px) {
                    background-color: $gray-100 !important;
                    border-bottom: 1px solid $gray-200;
                }

                &:first-child {
                    padding-left: 30px !important;
                    white-space: normal !important;
                }

                &:last-child {
                    padding-right: 30px !important;
                }

                .rdt_TableCol_Sortable {
                    // justify-content: space-between;
                    // white-space: nowrap;
                    // overflow: unset;

                    span {
                        padding: 0px 5px;
                    }

                    &:hover {
                        opacity: 1;
                    }
                }

                //.gsClDX, .khOwDb {
                //    color: $primary;
                //}

                &:hover {
                    color: $primary !important;
                }

                span.__rdt_custom_sort_icon__ {
                    i {
                        font-size: 10px;
                        width: auto;
                        height: auto;
                    }
                }
            }
        }
    }

    .rdt_TableBody {
        //overflow: auto;

        .rdt_TableRow {
            color: #3f4254;
            border-bottom: 1px solid $gray-200;
            padding: 10px 0;
            @media (max-width: 925px) {
                border: 0;
            }

            &:nth-child(even) {
                background-color: $gray-100;
                @media (max-width: 925px) {
                    padding: 0;
                    background-color: transparent !important;
                }
                .rdt_TableCell {
                    @media (max-width: 925px) {
                        background-color: $gray-100 !important;
                        border-top: 0.5px solid $gray-200;
                        border-bottom: 0.5px solid $gray-200;
                        padding-top: 10px;
                        padding-bottom: 10px;
                    }
                }
            }

            &:last-child {
                border-bottom-left-radius: $border-radius-sm;
                border-bottom-right-radius: $border-radius-sm;
                border-bottom: 0;
            }

            .btn-transparent {
                background: none !important;
                padding: 0 !important;
                border: 0;

                &:hover,
                &:active,
                &:focus {
                    background: none !important;
                }
            }

            .rdt_TableCell {
                font-size: 0.875rem;
                padding-left: 0;

                &:first-child {
                    padding-left: 30px;
                    @media (max-width: 1199px) {
                        padding-left: 23px;
                    }
                    @media (max-width: 705px) {
                        padding-left: 30px;
                    }
                }

                &:last-child {
                    padding-right: 30px;
                    @media (max-width: 1199px) {
                        padding-right: 23px;
                    }
                    @media (max-width: 705px) {
                        padding-right: 30px;
                    }
                }
                .qrcodeDownload {
                    .dropdown-menu.show {
                        transform: translate(180px, 47px) !important;
                    }
                }

                .dropdown-menu.show {
                    transform: translate(0, 40px) !important;
                    padding: 10px !important;
                    min-width: 180px !important;
                    height: auto;
                    // min-height: 155px;
                    display: inline-block;
                    box-sizing: border-box;
                }
            }
        }
    }
    //.bNAywb {
    //    min-width: 120px;

    .rdt_TableCol_Sortable {
        justify-content: start;
    }
    //}
}

div {
    .rdt_Pagination {
        border-top: 0 !important;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-top: 30px;
        min-height: 0 !important;
        background-color: $page-body-bg !important;
        font-size: 1rem !important;

        div:nth-child(4) {
            margin-left: auto !important;
            button {
                background-color: $page-body-bg !important;
                border-color: $page-body-bg !important;
                fill: $gray-600 !important;
                border-radius: 6px !important;
                margin-left: 6px !important;
                width: 38px !important;
                height: 38px !important;

                svg {
                    width: 23px !important;
                    height: 23px !important;
                }

                &:hover,
                &:focus {
                    fill: $primary !important;
                    background-color: $gray-200 !important;
                    border-color: $gray-200 !important;
                }
            }
        }

        div:first-of-type {
            select {
                background-color: $white;
                padding: 6px 35px 6px 20px;
                border-radius: 8px;
            }

            svg {
                top: 5px;
                right: 11px;
            }
        }
    }
}

//.sc-gicCDI {
//    background-color: $page-body-bg;
//    border-color: $page-body-bg;
//    fill: $gray-600 !important;
//    border-radius: 6px;
//    margin-left: 6px;
//    width: 38px;
//    height: 38px;
//
//    svg {
//        width: 23px;
//        height: 23px;
//    }
//
//    &:hover, &:focus {
//        fill: $primary !important;
//        background-color: $gray-200 !important;
//        border-color: $gray-200;
//    }
//}

//    .sc-ezbkAF.kNbUsB {
//        margin-left: auto !important;
//        margin-right: 0 !important;
//    }
//
//    .sc-llJcti {
//        outline: none;
//    }
//}

//.kNbUsB, .bZWDBO {
//    @media (max-width: 599px) {
//        justify-content: center !important;
//    }
//}

.form-check-input {
    width: 18px;
    height: 18px;
    background-color: #ebedf3;
    border-color: #ebedf3;

    &:checked[type="checkbox"] {
        background-size: auto;
    }
}

#pagination-first-page {
    outline: none;

    &:hover:not(:disabled) {
        svg {
            fill: $white;
        }
    }

    &:focus {
        border: 1px solid $primary;
    }
}

//.gPLhoV:disabled {
//    opacity: 0.5;
//}
//
//.sc-cxpSdN {
//    border: 1px solid rgba(0, 0, 0, 0.54) !important;
//    border-radius: 8px !important;
//
//    &:focus-visible {
//        outline: none !important;
//    }
//}

.Custom-product-img {
    object-fit: cover;

    @media (max-width: 576px) {
        height: 300px;
    }
}

.rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell:first-child {
    @media (max-width: 1200px) {
        width: 600px;
    }
}
.rdt_Table .rdt_TableBody .rdt_TableRow .rdt_TableCell {
    @media (max-width: 873px) {
        word-break: break-word !important;
        // padding-left: 10px;
        // min-width: auto !important;
    }
}
//.sc-dlVxhl.fSQziN, .sc-dlVxhl.csxlVi {
//    overflow-y: auto;
//}

//.fQmYxz, .dqzAlz {
//    @media (max-width: 599px) {
//        padding-left: 5px;
//    }
//}

.data-table {
    header {
        padding: 0;
        background-color: $page-body-bg;
        align-items: start !important;
        margin-bottom: 1.25rem !important;
    }
}

.data-table > :nth-child(2) {
    > :nth-child(1) {
        position: relative;
    }
}

.front_qr_types_table {
    .rdt_Table {
        .rdt_TableHeadRow > :nth-child(1) {
            max-width: 15% !important;
        }
        .rdt_TableHeadRow > :nth-child(2) {
            max-width: 15% !important;
        }
        .rdt_TableRow > :nth-child(1) {
            max-width: 15% !important;
        }
        .rdt_TableRow > :nth-child(2) {
            max-width: 15% !important;
        }
    }
}
//.ifOHjV {
//    display: block !important;
//}
//
//.sc-llYSUQ  {
//    .sc-cxpSdN.fWWeYy, .sc-cxpSdN.bTQCKa {
//        background-color: $gray-100;
//        border-color: $gray-100 !important;
//        border-radius: 0.475rem !important;
//    }
//
//    svg {
//        fill: #A1A5B7 !important;
//    }
//}
//
//.sc-ivTmOn {
//    border-radius: $border-radius-lg;
//}
//
//.flhMgd {
//    min-width: 140px !important;
//}
//
//.flvcDD .bLyqNT {
//    justify-content: center !important;
//}
//
//.dNvvkQ {
//    max-width: 136px !important;
//}
