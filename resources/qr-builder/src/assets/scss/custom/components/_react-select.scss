.css-26l3qy-menu {
    background-color: #fff;
    border: 0;
    border-radius: 0.475rem;
    box-shadow: 0 0 50px 0 rgb(82 63 105 / 10%) !important;
    padding: 0;
    margin-top: 0 !important;
    z-index: 999 !important;
    color: #6c757d;
}

.css-26l3qy-menu .css-1n7v3ny-option,
.css-26l3qy-menu .css-9gakcf-option,
.css-1nmdiq5-menu .css-848612-option {
    background-color: #a561ff !important;
    cursor: pointer !important;
}

.css-26l3qy-menu .css-1n7v3ny-option:hover,
.css-1nmdiq5-menu > :nth-child(1) div:not(.css-848612-option):hover,
.css-1nmdiq5-menu > :nth-child(1) .css-1oayvia-option,
.css-26l3qy-menu .css-1n7v3ny-option:not(:active) {
    color: #000000 !important;
    background-color: #e8dcf7 !important;
}

.css-4ljt47-MenuList,
.css-1n6sfyn-MenuList {
    border: 1px solid #ced4da;
    border-radius: 0.475rem !important;
}

.css-tlfecz-indicatorContainer .css-tj5bde-Svg,
.css-1gtu0rj-indicatorContainer .css-tj5bde-Svg {
    color: $gray-600 !important;
    width: 24px;
    height: 24px;
}

.css-13cymwt-control:hover,
.css-t3ipsp-control:hover {
    border: 1px solid #ced4da !important;
    background-color: #ffffff !important;
}

.css-t3ipsp-control {
    border-color: red;
}

.css-yt9ioa-option {
    cursor: pointer !important;
}
.css-13cymwt-control,
.css-t3ipsp-control,
.css-1insrsq-control {
    box-shadow: unset !important;
    height: 45px !important;
}

.css-13cymwt-control,
.css-t3ipsp-control {
    background-color: #ffffff !important;
    border: 1px solid #ced4da !important;
}

.css-1okebmr-indicatorSeparator,
.css-109onse-indicatorSeparator {
    width: unset !important;
}

.css-tlfecz-indicatorContainer,
.css-1gtu0rj-indicatorContainer {
    color: hsl(0, 0%, 80%) !important;

    &:hover {
        color: hsl(0, 0%, 80%) !important;
    }

    .css-tj5bde-Svg {
        color: #a1a5b7;
    }
}

.css-319lph-ValueContainer {
    font-family: inherit;
}

.css-13cymwt-control,
.css-t3ipsp-control,
.css-1insrsq-control {
    padding: 2.5px;
    border-radius: 0.475rem !important;
    height: 45px;
}

.css-13cymwt-control:hover,
.css-t3ipsp-control:hover {
    border: 1px solid #ced4da !important;
    background-color: #ffffff !important;
}

.css-14el2xx-placeholder {
    color: #6c757d !important;
    font-family: Poppins, Helvetica, "sans-serif";
    font-size: 14px;
}

.css-qc6sy-singleValue {
    color: #6c757d !important;
    font-family: Poppins, Helvetica, "sans-serif";
    font-size: 14px;
}

.css-1hb7zxy-IndicatorsContainer,
.css-1hb7zxy-IndicatorsContainer {
    .css-tlfecz-indicatorContainer,
    .css-1gtu0rj-indicatorContainer {
        padding: 6px 6px 6px 0 !important;
    }
}
