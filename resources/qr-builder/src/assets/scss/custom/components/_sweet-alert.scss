@import "../../components/variables";

.sweet-alert {
    will-change: unset !important;
    border-radius: 0.625rem !important;
    background-color: $white !important;
    width: 375px !important;

    h2 {
        color: #212529 !important;
        font-size: 1.125rem !important;
        font-weight: 500 !important;
        margin-bottom: 13px !important;
    }

    .sweet-text {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    p {
        flex-direction: row-reverse;
    }
}

.terms-box {
    .quill {
        height: 27rem;

        .ql-container {
            height: 24.5rem !important;
            @media (max-width: 485px) and (min-width: 372px) {
                height: 23rem !important;
            }
            @media (max-width: 371px) and (min-width: 324px) {
                height: 21.5rem !important;
            }
            @media (max-width: 323px) and (min-width: 301px) {
                height: 20rem !important;
            }
            @media (max-width: 300px) {
                height: 19rem !important;
            }
        }
    }
}

.privacy_policy,
.terms_condition,
.dynamic_page_content {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    div,
    span,
    p,
    ul,
    ol,
    li,
    i,
    b,
    u,
    strong,
    em,
    br,
    a,
    blockquote,
    pre {
        margin-bottom: 0px !important;
    }
}

.privacy_policy,
.terms_condition,
.dynamic_page_content {
    ul {
        margin-left: 25px !important;
        li {
            list-style: disc !important;
        }
    }

    ol {
        padding: 0 !important;
        margin-left: 25px !important;
    }

    img {
        width: 100%;
    }
}
