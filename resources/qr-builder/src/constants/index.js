//API Base URL
export const apiBaseURL = {
    QR_CODE: "/user/qr-codes",
    QR_CODE_TYPES: '/user/qrcode-types',
    ADMIN_QR_CODE: "/admin/qr-codes",
    UPDATED_QR_CODE: "/user/update-qrcode",
    LANGUAGES: '/languages',
    CHANGE_LANGUAGE: '/change-language',
    EDIT_PROFILE: '/edit-profile',
    UPDATE_PROFILE: '/update-profile',
    USERS: '/admin/users',
    ALL_USERS: '/admin/get-users',
    EMAIL_VERIFY: '/admin/verify-email',
    USER_STATUS_CHANGE: '/admin/change-status',
    USER_STATUS: '/admin/update-status',
    UPDATED_USER: '/admin/update-user',
    PLANS: '/admin/plans',
    PAGES: "/admin/pages",
    FRONT_PAGE: "/get-page",
    FRONT_PAGES: "/pages",
    UPDATE_PAGE: '/admin/update-page',
    SUBSCRIBERS: 'admin/subscribers',
    SUBSCRIPTION_PLANS: '/user/subscription-plans',
    SELECTED_PLAN: "/user/choose-payment-type",
    PURCHASE_PLAN: "/user/purchase-plan",
    SUBSCRIBED_ALL_PLANS: "/user/user-subscriptions",
    PURCHASE_PLAN_STRIPE: "/user/stripe-onboard",
    PURCHASE_PLAN_PAYPAL: "/user/paypal-onboard",
    PURCHASE_PLAN_RAZORPAY: "/user/razorpay-onboard",
    COUPON_CODES: '/admin/coupon-codes',
    EDIT_COUPON_CODES: '/admin/update-coupon-code',
    DEFAULT_PLAN: '/admin/make-plan-default',
    UPDATE_PLANS: '/admin/update-plan',
    STRIPE_SETTING: "/admin/stripe-settings",
    CUSTOM_STYLE_SETTING: "/admin/custom-style-settings",
    RAZORPAY_SETTING: '/admin/razorpay-settings',
    PAYPAL_SETTING: '/admin/paypal-settings',
    CAPTCHA_SETTING: '/admin/captcha-settings',
    EMAIL_NOTIFICATION: '/admin/email-notification-settings',
    ADS_SETTINGS: '/admin/ads-settings',
    SMPT_MAIL_SETTINGS: '/admin/mail-settings',
    SOCIAL_SETTING: '/admin/social-settings',
    SOCIAL_GOOGLE_LOGIN_SETTING: '/admin/google-login',
    SOCIAL_FACEBOOK_LOGIN_SETTING: '/admin/facebook-login',
    ANNOUNCEMENT_SETTING: "/admin/announcement-settings",
    PROJECT: '/user/projects',
    ADMIN_PROJECT: '/admin/projects',
    LINK: '/user/links',
    ADD_BUSINESS_CARD: '/user/digital-business-cards',
    ADD_BUSINESS_CARD_LINK: '/user/social-links',
    ADMIN_ADD_BUSINESS_CARD_LINK: '/admin/digital-business-cards',
    UPDATE_BUSINESS_CARD_LINK: '/user/business-card-social-links',
    ADMIN_UPDATE_BUSINESS_CARD_LINK: '/admin/business-card-social-link',
    CHANGE_STATUS_BUSINES_CARD: "/admin/digital-business-cards/change-status",
    ADMIN_ADD_BUSINESS_CARD: '/admin/digital-business-cards',
    LINK_ANALITYCS: '/link/',
    BUSINESS_CARD_ANALITYCS: '/business-card/',
    CHART_LINK_ANALITYCS: '/link/chart-data',
    BUSINESS_CHART_LINK_ANALITYCS: '/business-card/chart-data',
    ADMIN_LINK: '/admin/links',
    ADMIN_PROJECT_ADD: '/admin/projects',
    UPDATED_LINK: '/user/update-link',
    UPDATE_PROJECT: '/user/update-project',
    QRCODE_ADMIN: '/admin/qr-codes',
    LINKS_ADMIN: '/admin/links',
    PROJECT_ADMIN: '/admin/projects',
    ADMIN_DASHBOARD: '/admin/dashboard',
    USER_DASHBOARD: "/user/dashboard",
    CURRENCY: '/admin/currencies',
    MAIN_SETTINGS: '/admin/main-settings',
    CACHE_CLEAR: '/admin/cache-clear',
    CHANGE_PASSWORD: 'change-password',
    ADMIN_FORGOT_PASSWORD: 'forgot-password',
    // ADMIN_RESET_PASSWORD: 'change-password',
    ADMIN_RESET_PASSWORD: 'reset-password',
    VALIDATE_AUTH_TOKEN: "validate-auth-token",
    REGISTER_USER: "registration",
    FRONT_SETTING: "front-setting",
    CONFIG: "config",
    REMAINING_SUBSCRIPTION_PLAN: "user/remaining-subscription",
    ANNOUNCEMENT_NOTIFICATION: "user/announcements",
    LOGIN_PASSWORD_CHANGE: 'change-login-password',
    CASH_PAYMENTS: 'admin/cash-payments',
    CHANGE_PAYMENT_STATUS: 'admin/change-payment-status',
    SUBSCRIBED_USER_PLAN: 'admin/subscribed-user-plans',
    ADMIN_PROJECT_COLLECT: "admin/project-list",
    ADMIB_QRCODE_TYPES: 'admin/qrcode-types',
    EDIT_SUBSCRIBED_USER_PLAN_END_DATE: "admin/update-subscription-date",
    LOGIN_VIA_GOOGLE: "login/google",
    LOGIN_VIA_FACEBOOK: "login/facebook",
    FETCH_TRANSACTIONS: "admin/transactions",
    FETCH_TRANSACTION_BY_ID: "admin/transaction",
    FETCH_FRONT_CMS: "front-cms",
    UPDATE_FRONT_CMS: "admin/update-front-cms",
    FETCH_FRONT_FEATURES: "features",
    UPDATE_FRONT_FEATURES: "admin/update-features",
    UPDATE_FRONT_QR_CODE_TYPES: "qr-code-types",
    FETCH_FRONT_SUB_FEATURES: "sub-features",
    FETCH_FRONT_QR_CODE_TYPES: "qr-code-types",
    UPDATE_FRONT_SUB_FEATURES: "admin/update-sub-features",
    FETCH_ALL_PLANS: "public-subscription-plans",
    ADD_SUBSCRIBE: "subscribe",
    CONTACT_INFO: "contact",
    ADMIN_CONTACT_INFO: "admin/contacts",
    ADMIN_DELETE_CONTACT_INFO: "admin/delete-contact",
    ADMIN_CONTACT_INFO_DETAILS: "/admin/contact",
    TERM_CONDITIOMS: "admin/term-conditions",
    FETCH_TERM_CONDITIOMS: "term-conditions",
    VERIFY_CAPTCHA: "verify-captcha",
    DASHBOARD_WEEK_EARNING: "/admin/get-week-earnings",
    TOP_VISITED_LINK_ADMIN: "/admin/most-visited-link",
    TOP_VISITED_LINK_USER: "/user/most-visited-link"
};

export const authActionType = {
    LOGIN_USER: 'LOGIN_USER',
    LOGOUT_USER: 'LOGOUT_USER',
    CHANGE_PASSWORD: 'CHANGE_PASSWORD',
    ADMIN_FORGOT_PASSWORD: 'ADMIN_FORGOT_PASSWORD',
    ADMIN_RESET_PASSWORD: 'ADMIN_RESET_PASSWORD',
    REGISTER_USER: 'REGISTER_USER',
    CHANGE_LOGIN_PASSWORD: 'CHANGE_LOGIN_PASSWORD'
};

export const configActionType = {
    FETCH_CONFIG: "FETCH_CONFIG",
    FETCH_ALL_CONFIG: "FETCH_ALL_CONFIG"
};


export const settingActionType = {
    FETCH_SETTING: 'FETCH_SETTING',
    EDIT_SETTINGS: 'EDIT_SETTINGS',
    FETCH_CACHE_CLEAR: 'FETCH_CACHE_CLEAR',
    FETCH_MAIN_SETTINGS: "FETCH_MAIN_SETTINGS",
    EDIT_MAIN_SETTINGS: "EDIT_MAIN_SETTINGS",
    FETCH_MAIL_SETTINGS: "FETCH_MAIL_SETTINGS",
    EDIT_MAIL_SETTINGS: "EDIT_MAIL_SETTINGS",
    FETCH_CAPTCHA_SETTING: 'FETCH_CAPTCHA_SETTING',
    EDIT_CAPTCHA_SETTINGS: "EDIT_CAPTCHA_SETTINGS",
    EDIT_PAYPAL_SETTINGS: "EDIT_PAYPAL_SETTINGS",
    FETCH_PAYPAL_SETTINGS: "FETCH_PAYPAL_SETTINGS",
    FETCH_EMAIL_NOTIFICATION_SETTING: 'FETCH_EMAIL_NOTIFICATION_SETTING',
    EDIT_EMAIL_NOTIFICATION_SETTINGS: "EDIT_EMAIL_NOTIFICATION_SETTINGS",
    EDIT_STRIPE_SETTINGS: "EDIT_STRIPE_SETTINGS",
    FETCH_STRIPE_SETTINGS: "FETCH_STRIPE_SETTINGS",
    FETCH_CUSTOM_STYLE_SETTING: "FETCH_CUSTOM_STYLE_SETTING",
    EDIT_CUSTOM_STYLE_SETTING: "EDIT_CUSTOM_STYLE_SETTING",
    FETCH_RAZORPAY_SETTING: "FETCH_RAZORPAY_SETTING",
    EDIT_RAZORPAY_SETTINGS: "EDIT_RAZORPAY_SETTINGS",
    FETCH_ADS_SETTINGS: 'FETCH_ADS_SETTINGS',
    EDIT_ADS_SETTINGS: "EDIT_ADS_SETTINGS",
    FETCH_SMTP_MAIL_SETTINGS: 'FETCH_SMTP_MAIL_SETTINGS',
    EDIT_SMTP_MAIL_SETTINGS: "EDIT_SMTP_MAIL_SETTINGS",
    CALL_SETTING: 'CALL_SETTING',
    EDIT_SOCIAL_SETTING: "EDIT_SOCIAL_SETTING",
    FETCH_SOCIAL_SETTING: "FETCH_SOCIAL_SETTING",
    EDIT_ANNOUNCEMENT_SETTING: "EDIT_ANNOUNCEMENT_SETTING",
    FETCH_ANNOUNCEMENT_SETTING: "FETCH_ANNOUNCEMENT_SETTING",
    EDIT_GOOGLE_LOGIN_SETTING: "EDIT_GOOGLE_LOGIN_SETTING",
    FETCH_GOOGLE_LOGIN_SETTING: "FETCH_GOOGLE_LOGIN_SETTING",
    EDIT_FACRBOOK_LOGIN_SETTING: "EDIT_FACRBOOK_LOGIN_SETTING",
    FETCH_FACRBOOK_LOGIN_SETTING: "FETCH_FACRBOOK_LOGIN_SETTING",
};

export const frontCmsActionTyps = {
    FETCH_FRONT_CMS: "FETCH_FRONT_CMS",
    UPDATE_FRONT_CMS: "UPDATE_FRONT_CMS",
    FETCH_FRONT_FEATURES: "FETCH_FRONT_FEATURES",
    FETCH_FRONT_ENQUIRIES: "FETCH_FRONT_ENQUIRIES",
    DELETE_FRONT_ENQUIRIES: "DELETE_FRONT_ENQUIRIES",
    UPDATE_FRONT_FEATURES: "UPDATE_FRONT_FEATURES",
    FETCH_FRONT_SUB_FEATURES: "FETCH_FRONT_SUB_FEATURES",
    FETCH_FRONT_QR_CODE_TYPES: "FETCH_FRONT_QR_CODE_TYPES",
    FETCH_FRONT_TREM_CONDITION: "FETCH_FRONT_TREM_CONDITION",
    UPDATE_FRONT_SUB_FEATURES: "UPDATE_FRONT_SUB_FEATURES",
    FETCH_ALL_PLANS: "FETCH_ALL_PLANS",
}


export const rolesActionType = {
    FETCH_ROLES: 'FETCH_ROLES',
    FETCH_ROLE: 'FETCH_ROLE',
    ADD_ROLES: 'ADD_ROLES',
    EDIT_ROLES: 'EDIT_ROLES',
    DELETE_ROLES: 'DELETE_ROLES',
    FETCH_ALL_ROLES: 'FETCH_ALL_ROLES'
};

export const productImageActionType = {
    DELETE_PRODUCT_IMAGE: 'DELETE_PRODUCT_IMAGE',
};


export const subscribedUserPlansActionTypes = {
    SUBSCRIBED_USER_PLAN: "SUBSCRIBED_USER_PLAN",
    EDIT_SUBSCRIBED_USER_PLAN_END_DATE: " EDIT_SUBSCRIBED_USER_PLAN_END_DATE",
}

export const transactionsActionsTypes = {
    FETCH_TRANSACTIONS: "FETCH_TRANSACTIONS",
    FETCH_TRANSACTION_BY_ID: "FETCH_TRANSACTION_BY_ID"
}

export const themeActionType = {
    UPDATE_THEME: 'UPDATE_THEME',
};

export const permissionActionType = {
    FETCH_PERMISSIONS: 'FETCH_PERMISSIONS',
};

export const currencyActionType = {
    FETCH_CURRENCIES: 'FETCH_CURRENCIES',
    FETCH_CURRENCY: 'FETCH_CURRENCY',
    ADD_CURRENCY: 'ADD_CURRENCY',
    EDIT_CURRENCY: 'EDIT_CURRENCY',
    DELETE_CURRENCY: 'DELETE_CURRENCY'
};

export const userActionType = {
    FETCH_USERS: 'FETCH_USERS',
    FETCH_USER: 'FETCH_USER',
    ADD_USER: 'ADD_USER',
    EDIT_USER: 'EDIT_USER',
    DELETE_USER: 'DELETE_USER',
    EMAIL_VERIFY_USER: 'EMAIL_VERIFY_USER',
    USER_STATUS: "USER_STATUS"
};

export const userDetailsActionType = {
    FETCH_QRCODE: 'FETCH_QRCODE',
    FETCH_LINK: 'FETCH_LINK',
    FETCH_LINK_BY_ID: "FETCH_LINK_BY_ID"
};

export const languageFileOptions = [
    { id: 1, name: 'language.json' },
    { id: 2, name: 'Error Messages' },
    { id: 3, name: 'Success Messages' },
]

export const qrcodeActionType = {
    FETCH_QRCODES: 'FETCH_QRCODES',
    FETCH_QRCODE: 'FETCH_QRCODE',
    ADD_QRCODE: 'ADD_QRCODE',
    ADD_HOME_QRCODE: 'ADD_HOME_QRCODE',
    EDIT_QRCODE: 'EDIT_QRCODE',
    DELETE_QRCODE: 'DELETE_USER',
    FETCH_QRCODES_TYPES: 'FETCH_QRCODES_TYPES',
};

export const qrCodesAdminActionType = {
    FETCH_QR_CODES: 'FETCH_QR_CODES',
    DELETE_QR_CODE: 'DELETE_QR_CODE',
    ADMIN_QR_CODE: "ADMIN_QR_CODE"
};

export const linksAdminActionType = {
    FETCH_LINKS: 'FETCH_LINKS',
    DELETE_LINKS: 'DELETE_LINKS',
    FETCH_LINKS_ANALYTICS: 'FETCH_LINKS_ANALYTICS',
    FETCH_CHART_LINKS_ANALYTICS: 'FETCH_CHART_LINKS_ANALYTICS',
    ADMIN_ADD_LINK: 'ADMIN_ADD_LINK'
};

export const dashboardAdminActionType = {
    FETCH_DASHBOARD_DETAILS: 'FETCH_DASHBOARD_DETAILS',
    WEEKLY_EARNINGS: "WEEKLY_EARNINGS",
    TOP_VISITED_LINK_ADMIN: "TOP_VISITED_LINK_ADMIN",
    TOP_VISITED_LINK_USER: "TOP_VISITED_LINK_USER"
};

export const projectAdminActionType = {
    FETCH_PROJECTS: 'FETCH_PROJECTS',
    DELETE_PROJECT: 'DELETE_PROJECT',
    ADMIN_PROJECT: 'ADMIN_PROJECT',
    ADMIN_ADD_PROJECT: 'ADMIN_ADD_PROJECT'
};

export const projectActionType = {
    FETCH_PROJECTS: 'FETCH_PROJECTS',
    FETCH_PROJECT: 'FETCH_PROJECT',
    ADD_PROJECT: 'ADD_PROJECT',
    EDIT_PROJECT: 'EDIT_PROJECT',
    DELETE_PROJECT: 'DELETE_PROJECT'
};

export const linkActionType = {
    FETCH_LINKS: 'FETCH_LINKS',
    FETCH_LINK: 'FETCH_LINK',
    ADD_LINK: 'ADD_LINK',
    EDIT_LINK: 'EDIT_LINK',
    DELETE_LINK: 'DELETE_LINK'
};

export const businessCardActionType = {
    FETCH_BUSINESS_CARDS: 'FETCH_BUSINESS_CARDS',
    FETCH_BUSINESS_CARD: 'FETCH_BUSINESS_CARD',
    ADD_BUSINESS_CARD: 'ADD_BUSINESS_CARD',
    EDIT_BUSINESS_CARD: 'EDIT_BUSINESS_CARD',
    DELETE_BUSINESS_CARD: 'DELETE_BUSINESS_CARD'
};

export const businessCardLinkActionType = {
    FETCH_BUSINESS_CARDS: 'FETCH_BUSINESS_CARDS',
    FETCH_BUSINESS_CARD_LINK: 'FETCH_BUSINESS_CARD_LINK',
    ADD_BUSINESS_CARD: 'ADD_BUSINESS_CARD',
    EDIT_BUSINESS_CARD_LINK: 'EDIT_BUSINESS_CARD_LINK',
    DELETE_BUSINESS_CARD: 'DELETE_BUSINESS_CARD'
};

export const adminBusinessCardActionType = {
    ADMIN_FETCH_BUSINESS_CARDS: 'ADMIN_FETCH_BUSINESS_CARDS',
    DELETE_ADMIN_BUSINESS_CARD: 'DELETE_ADMIN_BUSINESS_CARD'
};

export const plansActionType = {
    FETCH_PLANS: 'FETCH_PLANS',
    FETCH_PLAN: 'FETCH_PLAN',
    ADD_PLAN: 'ADD_PLAN',
    EDIT_PLAN: 'EDIT_PLAN',
    DELETE_PLAN: 'DELETE_PLAN',
    FETCH_USER_PLAN: 'FETCH_USER_PLAN',
    FETCH_SELECTED_PLAN: 'FETCH_SELECTED_PLAN',
    PURCHASE_PLAN: 'PURCHASE_PLAN',
    SUBSCRIBED_ALL_PLANS: 'SUBSCRIBED_ALL_PLANS',
};

export const pagesActionType = {
    FETCH_PAGES: 'FETCH_PAGES',
    FETCH_PAGE: 'FETCH_PAGE',
    FRONT_FETCH_PAGE: 'FRONT_FETCH_PAGE',
    ADD_PAGE: 'ADD_PAGE',
    EDIT_PAGE: 'EDIT_PAGE',
    DELETE_PAGE: 'DELETE_PAGE'
};

export const subscribersActionType = {
    FETCH_SUBSCRIBERS: 'FETCH_SUBSCRIBERS',
    DELETE_SUBSCRIBER: 'DELETE_SUBSCRIBER',
};

export const couponCodesActionType = {
    FETCH_CODES: 'FETCH_CODES',
    FETCH_CODE: 'FETCH_CODE',
    ADD_CODE: 'ADD_CODE',
    EDIT_CODE: 'EDIT_CODE',
    DELETE_CODE: 'DELETE_CODE'
};

export const languageActionType = {
    UPDATE_LANGUAGE: 'UPDATE_LANGUAGE',
    UPDATED_LANGUAGE: 'UPDATED_LANGUAGE',
};

export const profileActionType = {
    FETCH_PROFILE: 'FETCH_PROFILE',
    UPDATE_PROFILE: 'UPDATE_PROFILE'
};

export const languagesActionType = {
    FETCH_LANGUAGES: 'FETCH_LANGUAGES',
    FETCH_LANGUAGE: 'FETCH_LANGUAGE',
    ADD_LANGUAGE: 'ADD_LANGUAGE',
    EDIT_LANGUAGE: 'EDIT_LANGUAGE',
    DELETE_LANGUAGE: 'DELETE_LANGUAGE',
    FETCH_ALL_LANGUAGES: 'FETCH_ALL_LANGUAGES',
    EDIT_LANGUAGE_DATA: 'EDIT_LANGUAGE_DATA',
    FETCH_LANGUAGE_DATA: 'FETCH_LANGUAGE_DATA'
};

export const frontSettingActionType = {
    FETCH_FRONT_SETTING: 'FETCH_FRONT_SETTING',
    FETCH_USER_SUBSCRIPTION_END: 'FETCH_USER_SUBSCRIPTION_END',
    ANNOUNCEMENT_NOTIFICATION: 'ANNOUNCEMENT_NOTIFICATION',
};

export const tokenValidationActionType = {
    FETCH_VALIDATION: 'FETCH_VALIDATION',
};

export const customerActionType = {
    FETCH_CUSTOMERS: 'FETCH_CUSTOMERS',
    FETCH_CUSTOMER: 'FETCH_CUSTOMER',
    ADD_CUSTOMER: 'ADD_CUSTOMER',
    EDIT_CUSTOMER: 'EDIT_CUSTOMER',
    DELETE_CUSTOMER: 'DELETE_CUSTOMER',
    FETCH_ALL_CUSTOMER: 'FETCH_ALL_CUSTOMER',
    FETCH_CUSTOMERS_REPORT: "FETCH_CUSTOMERS_REPORT",
    FETCH_CUSTOMERS_PAYMENT_REPORT: "FETCH_CUSTOMERS_PAYMENT_REPORT"
};

export const dashboardActionType = {
    FETCH_ALL_DATA: "FETCH_ALL_DATA"
};

export const Filters = {
    PAGE: 1,
    OBJ: {
        order_By: '',
        page: 1,
        pageSize: 10,
        direction: 'asc',
        search: '',
        adminName: 'admin',
        categoryId: '',
        created_at: 'created_at',
        status: '',
        payment_status: '',
        payment_type: '',
        product_unit: '',
        base_unit: ''
    }
};

export const constants = {
    SET_TOTAL_RECORD: 'SET_TOTAL_RECORD',
    UPDATE_TOTAL_RECORD_AFTER_DELETE: 'UPDATE_TOTAL_RECORD_AFTER_DELETE',
    UPDATE_TOTAL_RECORD_AFTER_ADD: 'UPDATE_TOTAL_RECORD_AFTER_ADD',
    IS_LOADING: 'IS_LOADING',
    SET_LANGUAGE: 'SET_LANGUAGE',
    DATE_ACTION: 'DATE_ACTION',
    CALL_SALE_API: "CALL_SALE_API",
    CALL_IMPORT_PRODUCT_API: "CALL_IMPORT_PRODUCT_API",
    SET_PRODUCT_UNIT_ID: "SET_PRODUCT_UNIT_ID",
    SET_DATE_FORMAT: "SET_DATE_FORMAT",
    CALL_UPDATE_BRAND_API: "CALL_UPDATE_BRAND_API",
    SET_SAVING: "SET_SAVING",
    SET_DEFAULT_COUNTRY: "SET_DEFAULT_COUNTRY"
};

export const dateLabelSelector = {
    CLEAN: 'clean',
    TODAY: 'today',
    THIS_WEEK: 'this_week',
    LAST_WEEK: 'last_week',
    THIS_MONTH: 'this_month',
    LAST_MONTH: 'last_month',
    CUSTOM: 'custom',
};

export const dateFormat = {
    DEFAULT_MOMENT: 'YYYY-MM-DD hh:mm:ss',
    NATIVE: 'YYYY-MM-DD',
    CHART_DATE: 'YYYY/MM/DD',
    CHART_CUSTOM_DATE: 'MMM_YYYY',
};

export const toastType = {
    ADD_TOAST: 'ADD_TOAST',
    REMOVE_TOAST: 'REMOVE_TOAST',
    ERROR: 'error'
};

export const membershipPlanActionType = {
    FETCH_MEMBERSHIP_PLANS: 'FETCH_MEMBERSHIP_PLANS',
    FETCH_SESSION_ID: "FETCH_SESSION_ID"
};

export const Tokens = {
    ADMIN: 'auth_token',
    USER_ROLE: 'user_role',
    USER: 'user',
    FIRST_NAME: 'first_name',
    LAST_NAME: 'last_name',
    IMAGE: 'image',
    REGISTER_USER: 'register_user',
    GET_PERMISSIONS: 'get_permissions',
    USER_IMAGE_URL: 'user_image_url',
    UPDATED_EMAIL: 'updated_email',
    UPDATED_FIRST_NAME: 'updated_first_name',
    UPDATED_LAST_NAME: 'updated_last_name',
    LANGUAGE: 'language',
    UPDATED_LANGUAGE: 'updated_language',
    WEBSITE_LOGO: 'website_logo',
    QRCODE_DETAILS: "qrcode_details",
    EMAIL_VERIFY: "email_verified_at"
};

export const errorMessage = {
    TOKEN_NOT_PROVIDED: 'Token not provided',
    TOKEN_EXPIRED: 'Token has expired',
    TOKEN_INVALID: 'Could not decode token: Error while decoding to JSON: Syntax error',
    TOKEN_INVALID_SIGNATURE: 'Token Signature could not be verified.'
};

export const Permissions = {
    MANAGE_DASHBOARD: 'manage_dashboard',
};

//POS Screen Constants
export const settingsKey = {
    LANGUAGE: 'language',
    DEFAULT_LOCALE: 'en',
    LOCALE_ARABIC: 'ar',
    LOCALE_PERSIAN: 'pe',
    LOCAL_GERMAN: 'gr'
};

export const languageOptions = [
    { id: 'ar', name: 'settings.select.language.arabic.label', display_name: 'Arabic' },
    { id: 'cn', name: 'settings.select.language.chinese.label', display_name: 'Chinese' },
    { id: 'en', name: 'settings.select.language.english.label', display_name: 'English' },
    { id: 'fr', name: 'settings.select.language.french.label', display_name: 'French' },
    { id: 'gr', name: 'settings.select.language.german.label', display_name: 'German' },
    { id: 'it', name: 'settings.select.language.italian.label', display_name: 'Italian' },
    { id: 'pe', name: 'settings.select.language.persian.label', display_name: 'Persian' },
    { id: 'po', name: 'settings.select.language.portuguese.label', display_name: 'Portuguese' },
    { id: 'ru', name: 'settings.select.language.russian.label', display_name: 'Russian' },
    { id: 'sp', name: 'settings.select.language.spanish.label', display_name: 'Spanish' },
    { id: 'tr', name: 'settings.select.language.turkish.label', display_name: 'Turkish' },
];


export const paymentStatusOptions = [
    { id: 0, name: 'unit.filter.all.label' },
    { id: 1, name: 'payment-status.filter.paid.label' },
    { id: 2, name: 'payment-status.filter.unpaid.label' },
    { id: 3, name: 'payment-status.filter.partial.label' },
];

export const planStatusOptions = [
    { id: 0, name: 'globally.all-filter.label' },
    { id: 1, name: 'globally.active.label' },
    { id: 2, name: 'globally.deactive.label' }
];

export const paymentTypeOptions = [
    { id: 0, name: 'unit.filter.all.label' },
    { id: 1, name: 'payment-type.filter.cash.label' },
    { id: 2, name: 'payment-type.filter.cheque.label' },
    { id: 3, name: 'payment-type.filter.bank-transfer.label' },
    { id: 4, name: 'payment-type.filter.other.label' },
];

export const CashPaymentActionType = {
    CASH_PAYMENTS: 'CASH_PAYMENTS',
    CASH_PAYMENTS_DETAILS: 'CASH_PAYMENTS_DETAILS',
    CHANGE_PAYMENT_STATUS: "CHANGE_PAYMENT_STATUS"
};

export const plansStatusOptions = [
    { id: 0, name: "globally.disabled.label" },
    { id: 1, name: "'globally.active.label" }
]

export const plansFrequencyOptions = [
    { id: 1, name: "globally.heading.month.label" },
    { id: 2, name: "globally.heading.year.label" }
]

export const couponCodeTypeOptions = [
    { id: 1, name: "globally.input.percentage.label" },
    { id: 2, name: "globally.input.fixed.label" }
]

export const typeDynamicOptions = [
    { name: "Text", id: 1 },
    { name: "URL", id: 2 },
    { name: "Phone", id: 3 },
    { name: "SMS", id: 4 },
    { name: "Email", id: 5 },
    { name: "Whatsapp", id: 6 },
    { name: "Facetime", id: 7 },
    { name: "Location", id: 8 },
    { name: "WiFi", id: 9 },
    { name: "Event", id: 10 },
    { name: "Crypto", id: 11 },
    { name: "Vcard", id: 12 },
    { name: "PayPal", id: 13 },
]


export const typeOptions = [
    { name: "globally.input.text.lable", id: 1 },
    { name: "globally.input.url.lable", id: 2 },
    { name: "globally.input.phone.lable", id: 3 },
    { name: "globally.input.sms.lable", id: 4 },
    { name: "user.input.email.label", id: 5 },
    { name: "globally.input.whatsapp.lable", id: 6 },
    { name: "globally.input.facetime.lable", id: 7 },
    { name: "globally.input.location.lable", id: 8 },
    { name: "globally.input.wifi.lable", id: 9 },
    { name: "globally.input.event.lable", id: 10 },
    { name: "globally.input.crypto.lable", id: 11 },
    { name: "globally.input.vcard.lable", id: 12 },
    { name: "globally.input.paypal.lable", id: 13 },
]

export const currencyCodeOptions = [
    { name: "USD", id: 1 },
    { name: "AUD", id: 2 },
    { name: "CAD", id: 3 },
    { name: "CHF", id: 4 },
    { name: "EUR", id: 5 },
    { name: "GBP", id: 6 },
    { name: "HKD", id: 7 },
    { name: "JPE", id: 8 },
    { name: "NZD", id: 9 },
    { name: "SGD", id: 10 },
    { name: "TWD", id: 11 },
]

export const encryptionOptions = [
    { name: "qrcode.wep.label", id: 1 },
    { name: "qrcode.wpa-wpa2.label", id: 2 },
    { name: "qrcode.no-ncryption.label", id: 3 },
]

export const smtpSettingsencryptionOptions = [
    { name: "globally.TLS.title", id: 0 },
    { name: "globally.SSL.title", id: 1 },
]

export const wifiHiddenOptions = [
    { name: "globally.input.yes.lable", id: 1 },
    { name: "globally.input.no.lable", id: 2 },
]

export const cryptoCoinOptions = [
    { name: "qrcode.bitcoin.title", id: 1 },
    { name: "qrcode.ethereum.title", id: 2 },
    { name: "qrcode.elrond.title", id: 3 },
]

export const payPalTypesOptions = [
    { name: "qrcode.buy-now.lable", id: 1 },
    { name: "qrcode.add-to-cart.lable", id: 2 },
    // { name: "Donation", id: 3 },
]

export const forgroundGradientStyleOptions = [
    { name: "qrcode.horizontal.lable", id: 1 },
    { name: "qrcode.radial.lable", id: 2 },
]

export const customeEyeColorOptions = [
    { name: "globally.input.no.lable", id: 2 },
    { name: "globally.input.yes.lable", id: 1 },
]

export const errorCorrectionArr = [
    { name: "qrcode.error-correction-capability.low.label", id: 1 },
    { name: "qrcode.error-correction-capability.medium.label", id: 2 },
    { name: "qrcode.error-correction-capability.high.label", id: 3 },
    { name: "qrcode.error-correction-capability.best.label", id: 4 },
]

export const backgroundTransparencyArr = [
    { id: 0, name: "FF" },
    { id: 1, name: "FC" },
    { id: 2, name: "FA" },
    { id: 3, name: "F7" },
    { id: 4, name: "F5" },
    { id: 5, name: "F2" },
    { id: 6, name: "F0" },
    { id: 7, name: "ED" },
    { id: 8, name: "EB" },
    { id: 9, name: "E8" },
    { id: 10, name: "E6" },
    { id: 11, name: "E3" },
    { id: 12, name: "E0" },
    { id: 13, name: "DE" },
    { id: 14, name: "DB" },
    { id: 15, name: "D9" },
    { id: 16, name: "D6" },
    { id: 17, name: "D4" },
    { id: 18, name: "D1" },
    { id: 19, name: "CF" },
    { id: 20, name: "CC" },
    { id: 21, name: "C9" },
    { id: 22, name: "C7" },
    { id: 23, name: "C4" },
    { id: 24, name: "C2" },
    { id: 25, name: "BF" },
    { id: 26, name: "BD" },
    { id: 27, name: "BA" },
    { id: 28, name: "B8" },
    { id: 29, name: "B5" },
    { id: 30, name: "B3" },
    { id: 31, name: "B0" },
    { id: 32, name: "AD" },
    { id: 33, name: "AB" },
    { id: 34, name: "A8" },
    { id: 35, name: "A6" },
    { id: 36, name: "A3" },
    { id: 37, name: "A1" },
    { id: 38, name: "9E" },
    { id: 39, name: "9C" },
    { id: 40, name: "99" },
    { id: 41, name: "96" },
    { id: 42, name: "94" },
    { id: 43, name: "91" },
    { id: 44, name: "8F" },
    { id: 45, name: "8C" },
    { id: 46, name: "8A" },
    { id: 47, name: "87" },
    { id: 48, name: "85" },
    { id: 49, name: "82" },
    { id: 50, name: "80" },
    { id: 51, name: "7D" },
    { id: 52, name: "7A" },
    { id: 53, name: "78" },
    { id: 54, name: "75" },
    { id: 55, name: "73" },
    { id: 56, name: "70" },
    { id: 57, name: "6E" },
    { id: 58, name: "6B" },
    { id: 59, name: "69" },
    { id: 60, name: "66" },
    { id: 61, name: "63" },
    { id: 62, name: "61" },
    { id: 63, name: "5E" },
    { id: 64, name: "5C" },
    { id: 65, name: "59" },
    { id: 66, name: "57" },
    { id: 67, name: "54" },
    { id: 68, name: "52" },
    { id: 69, name: "4F" },
    { id: 70, name: "4D" },
    { id: 71, name: "4A" },
    { id: 72, name: "47" },
    { id: 73, name: "45" },
    { id: 74, name: "42" },
    { id: 75, name: "40" },
    { id: 76, name: "3D" },
    { id: 77, name: "3B" },
    { id: 78, name: "38" },
    { id: 79, name: "36" },
    { id: 80, name: "33" },
    { id: 81, name: "30" },
    { id: 82, name: "2E" },
    { id: 83, name: "2B" },
    { id: 84, name: "29" },
    { id: 85, name: "26" },
    { id: 86, name: "24" },
    { id: 87, name: "21" },
    { id: 88, name: "1F" },
    { id: 89, name: "1C" },
    { id: 90, name: "1A" },
    { id: 91, name: "17" },
    { id: 92, name: "14" },
    { id: 93, name: "12" },
    { id: 94, name: "0F" },
    { id: 95, name: "0D" },
    { id: 96, name: "0A" },
    { id: 97, name: "08" },
    { id: 98, name: "05" },
    { id: 99, name: "03" },
    { id: 100, name: "00" },
]

export const downloadExtentionOptions = [
    { id: 1, name: "png" },
    { id: 2, name: "jpeg" },
    { id: 3, name: "webp" },
    { id: 4, name: "svg" },
]



export const captchaProviderOptions = [
    { id: 0, name: "Basic captcha (self-hosted)" },
    { id: 1, name: "Google ReCaptcha v2 checkbox" },
    { id: 2, name: "hCaptcha" },
    { id: 3, name: "Cloudflare Turnstile" },
]

export const captchaLostPasswordOptions = [
    { id: 0, name: "globally.input.no.lable" },
    { id: 1, name: "globally.input.yes.lable" }
]

export const captchaResendActivationOptions = [
    { id: 0, name: "globally.input.no.lable" },
    { id: 1, name: "globally.input.yes.lable" }
]

export const captchaContactPageOptions = [
    { id: 0, name: "globally.input.no.lable" },
    { id: 1, name: "globally.input.yes.lable" }
]

export const enabledPaypalPaymentsOptions = [
    { id: 0, name: "globally.input.no.lable" },
    { id: 1, name: "globally.input.yes.lable" }
]

export const paypalModeOptions = [
    { id: 0, name: "setting.sandbox.lable" },
    { id: 1, name: "setting.live.lable" }
]

export const commonYesOrNoOptions = [
    { id: 0, name: "globally.input.no.lable" },
    { id: 1, name: "globally.input.yes.lable" }
]

export const DefaultpaymentMethodOptions = [
    { id: 1, name: "globally.stripe.payment.title" },
    { id: 2, name: "globally.paypal.payment.title" },
    { id: 3, name: "globally.razorpay.payment.title" },
    { id: 4, name: "globally.manually.payment.title" }
]

export const paymentMethodOptions = [
    { id: 1, name: "Stripe" },
    { id: 2, name: "Paypal" },
    { id: 3, name: "Razorpay" },
    { id: 4, name: "Manually" }
]

export const paymentMethodLocalizedOptions = [
    { id: 1, name: "globally.stripe.payment.title" },
    { id: 2, name: "globally.paypal.payment.title" },
    { id: 3, name: "globally.razorpay.payment.title" },
    { id: 4, name: "globally.manually.payment.title" }
]

export const cashPaymentStatusMethodOptions = [
    { id: 0, name: "globally.pending.label" },
    { id: 1, name: "globally.approved.label" },
    { id: 2, name: "globally.reject.label" }
]
