import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faPieChart,
    faUser,
    faLink,
    faBookmark,
    faProjectDiagram,
    faQrcode,
    faBoxArchive,
    faDollarSign,
    faWrench,
    faTags,
    faMoneyBill,
    faPaperPlane,
    faMoneyBill1Wave,
    faHome,
    faShare,
    faEnvelope,
    faLanguage,
    faPager,
    faIdCard,
} from "@fortawesome/free-solid-svg-icons";
import { getFormattedMessage } from "../shared/sharedMethod";

export default [
    {
        title: "dashboard.title",
        name: "dashboard",
        fontIcon: <FontAwesomeIcon icon={faPieChart} />,
        to: "/app/dashboard",
        class: "d-flex",
        permission: "",
        isAdmin: false,
        items: [
            {
                title: getFormattedMessage("dashboard.title"),
                to: "/app/dashboard",
            },
        ],
    },
    {
        title: "dashboard.title",
        name: "dashboard",
        fontIcon: <FontAwesomeIcon icon={faPie<PERSON><PERSON>} />,
        to: "/app/admin/dashboard",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("dashboard.title"),
                to: "/app/admin/dashboard",
            },
        ],
    },
    {
        title: "users.title",
        name: "users",
        fontIcon: <FontAwesomeIcon icon={faUser} />,
        to: "/app/admin/users",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("users.title"),
                to: "/app/admin/users",
            },
        ],
    },
    {
        title: "globally.qrcode.title",
        name: "QR Codes",
        fontIcon: <FontAwesomeIcon icon={faQrcode} />,
        to: "/app/admin/qr-codes",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("globally.qrcode.title"),
                to: "/app/admin/qr-codes",
            },
        ],
    },
    {
        title: "digital.business.card.title",
        name: "Projects",
        fontIcon: <FontAwesomeIcon icon={faIdCard} />,
        to: "/app/admin/digital-business-cards",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("digital.business.card.title"),
                to: "/app/admin/digital-business-cards",
            },
        ],
    },
    {
        title: "projects.title",
        name: "Projects",
        fontIcon: <FontAwesomeIcon icon={faBoxArchive} />,
        to: "/app/admin/collections",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("projects.title"),
                to: "/app/admin/collections",
            },
        ],
    },
    {
        title: "globally.links.title",
        name: "Links",
        fontIcon: <FontAwesomeIcon icon={faLink} />,
        to: "/app/admin/minify-link",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("globally.links.title"),
                to: "/app/admin/minify-link",
            },
        ],
    },
    {
        title: "plans.title",
        name: "plans",
        fontIcon: <FontAwesomeIcon icon={faBookmark} />,
        to: "/app/admin/plans",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("plans.title"),
                to: "/app/admin/plans",
            },
        ],
    },
    {
        title: "cash-payments.title",
        name: "cash payments",
        fontIcon: <FontAwesomeIcon icon={faMoneyBill} />,
        to: "/app/admin/cash-payments",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("cash-payments.title"),
                to: "/app/admin/cash-payments",
            },
        ],
    },
    {
        title: "subscribers.title",
        name: "subscribers",
        fontIcon: <FontAwesomeIcon icon={faEnvelope} />,
        to: "/app/admin/subscribers",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("subscribers.title"),
                to: "/app/admin/subscribers",
            },
        ],
    },
    {
        title: "subscribed-plan.title",
        name: "subscribed user plans",
        fontIcon: <FontAwesomeIcon icon={faPaperPlane} />,
        to: "/app/admin/subscribed-plans",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("subscribed-plan.title"),
                to: "/app/admin/subscribed-plans",
            },
        ],
    },
    {
        title: "transactions.title",
        name: "transactions",
        fontIcon: <FontAwesomeIcon icon={faMoneyBill1Wave} />,
        to: "/app/admin/transactions",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("transactions.title"),
                to: "/app/admin/transactions",
            },
        ],
    },
    {
        title: "admin.codes.title",
        name: "Coupon Codes",
        fontIcon: <FontAwesomeIcon icon={faTags} />,
        to: "/app/admin/codes",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("admin.codes.title"),
                to: "/app/admin/codes",
            },
        ],
    },
    {
        title: "qrcode.title",
        name: "plans",
        fontIcon: <FontAwesomeIcon icon={faQrcode} />,
        to: "/app/qrcode",
        class: "d-flex",
        permission: "",
        isAdmin: false,
        items: [
            {
                title: getFormattedMessage("qrcode.title"),
                to: "/app/qrcode",
            },
        ],
    },
    {
        title: "digital.business.card.title",
        name: "Digital Business Card",
        fontIcon: <FontAwesomeIcon icon={faIdCard} />,
        to: "/app/digital-business-cards",
        class: "d-flex",
        permission: "",
        isAdmin: false,
        items: [
            {
                title: getFormattedMessage("digital.business.card.title"),
                to: "/app/digital-business-cards",
            },
        ],
    },
    {
        title: "projects.title",
        name: "Projects",
        fontIcon: <FontAwesomeIcon icon={faProjectDiagram} />,
        to: "/app/collections",
        class: "d-flex",
        permission: "",
        isAdmin: false,
        items: [
            {
                title: getFormattedMessage("projects.title"),
                to: "/app/collections",
            },
        ],
    },

    {
        title: "links.title",
        name: "Links",
        fontIcon: <FontAwesomeIcon icon={faLink} />,
        to: "/app/minify-link",
        class: "d-flex",
        permission: "",
        isAdmin: false,
        items: [
            {
                title: getFormattedMessage("links.title"),
                to: "/app/minify-link",
            },
        ],
    },
    {
        title: "admin.currencies.title",
        name: "currencies",
        fontIcon: <FontAwesomeIcon icon={faDollarSign} />,
        to: "/app/admin/currencies",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("admin.currencies.title"),
                to: "/app/admin/currencies",
            },
        ],
    },
    {
        title: "settings.select.languages.label",
        name: "Language",
        fontIcon: <FontAwesomeIcon icon={faLanguage} />,
        to: "/app/admin/languages",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("settings.select.languages.label"),
                to: "/app/admin/languages",
            },
        ],
    },
    // {
    //     title: "settings.select.pages.label",
    //     name: "Admin Pages",
    //     fontIcon: <FontAwesomeIcon icon={faPager} />,
    //     to: "/app/admin/custom-pages",
    //     class: "d-flex",
    //     permission: "",
    //     isAdmin: true,
    //     items: [
    //         {
    //             title: getFormattedMessage("settings.select.pages.label"),
    //             to: "/app/admin/custom-pages",
    //         },
    //     ],
    // },
    {
        title: "admin.settings.title",
        name: "website settings",
        fontIcon: <FontAwesomeIcon icon={faWrench} />,
        to: "/app/admin/settings-main",
        setting_to: "/app/admin/setting",
        payment_to: "/app/admin/payment",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("admin.settings.title"),
                to: "/app/admin/settings-main",
            },
        ],
    },
    {
        title: "admin.frontcms.title",
        name: "front cms",
        fontIcon: <FontAwesomeIcon icon={faHome} />,
        to: "/app/admin/front-hero",
        front_to: "/app/admin/front",
        class: "d-flex",
        permission: "",
        isAdmin: true,
        items: [
            {
                title: getFormattedMessage("admin.frontcms.title"),
                to: "/app/admin/front-hero",
            },
        ],
    },
];
