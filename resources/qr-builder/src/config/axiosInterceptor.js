import { Tokens, errorMessage } from '../constants';
import { environment } from './environment'

export default {
    setupInterceptors: (axios, isToken = false, isFormData = false) => {
        axios.interceptors.request.use((config) => {
            if (isToken) {
                return config;
            }
            let isToken = localStorage.getItem(Tokens.ADMIN);
            if (isToken) {
                config.headers['Authorization'] = `Bearer ${isToken}`;
            }
            if (!isToken) {
                if (!window.location.href.includes('login')
                    && !window.location.href.includes('reset-password')
                    && !window.location.href.includes('forgot-password')
                    && !window.location.href.includes('register')
                    && !window.location.href.includes('home-plans')
                    && !window.location.href.includes('contact')
                    && !window.location.href.includes('generate-qr-code')
                    && !window.location.href.includes('terms-conditions')
                    && !window.location.href.includes('/pages')
                    && !window.location.href.includes('privacy-policy')) {
                    window.location.href = environment.URL + '#' + "/";
                }
            }
            if (isFormData) {
                config.headers['Content-Type'] = 'multipart/form-data';
            }
            return config;
        },
            (error) => {
                return Promise.reject(error);
            }
        );
        axios.interceptors.response.use(
            response => successHandler(response),
            error => errorHandler(error)
        );
        const errorHandler = (error) => {
            if (error.response.status === 401
                || error.response.data.message === errorMessage.TOKEN_NOT_PROVIDED
                || error.response.data.message === errorMessage.TOKEN_INVALID
                || error.response.data.message === errorMessage.TOKEN_INVALID_SIGNATURE
                || error.response.data.message === errorMessage.TOKEN_EXPIRED) {
                localStorage.removeItem(Tokens.ADMIN);
                localStorage.removeItem("isAdmin");
                localStorage.removeItem(Tokens.USER);
                window.location.href = environment.URL + '#' + '/login';
            } else if (error.response.status === 403 || error.response.status === 404) {
                const user_role = localStorage.getItem(Tokens.USER_ROLE)
                if (user_role === '1') {
                    window.location.href = environment.URL + '#' + '/app/admin/dashboard';
                } else {
                    window.location.href = environment.URL + '#' + '/app/dashboard';
                }
            } else if (error.response.status === 500) {
                if (error.response.data.message.includes("Too Many Attempts.")) {
                    return Promise.reject({ ...error })
                } else {
                    localStorage.setItem("server_error", JSON.stringify(true))
                }
            } else {
                return Promise.reject({ ...error })
            }
        };
        const successHandler = (response) => {
            return response;
        };
    }
};
