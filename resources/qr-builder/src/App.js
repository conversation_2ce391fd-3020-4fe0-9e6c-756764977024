import React, { useEffect, useMemo, useState } from "react";
import { Route, Navigate, Routes } from "react-router-dom";
import "../../qr-builder/src/assets/sass/style.react.scss";
import { useDispatch, useSelector } from "react-redux";
import { IntlProvider } from "react-intl";
import { settingsKey, toastType, Tokens } from "./constants";
import Toasts from "./shared/toast/Toasts";
import { addRTLSupport } from "./shared/sharedMethod";
import Login from "./components/auth/Login";
import ResetPassword from "./components/auth/ResetPassword";
import ForgotPassword from "./components/auth/ForgotPassword";
import AdminApp from "./AdminApp";
import Dashboard from "./components/dashboard/Dashboard";
import Register from "./components/auth/Register";
import Home from "./components/Home/Home";
import { fetchFrontSetting } from "./store/action/frontSettingAction";
import ResetLoginPassword from "./components/auth/ResetLoginPassword";
import HomePage from "./mainLayout/frontend/HomePage";
import Pricing from "./mainLayout/frontend/Pricing";
import TermsConditions from "./mainLayout/frontend/TermsConditions";
import PrivacyPolicye from "./mainLayout/frontend/PrivacyPolicye";
import { fetchConfig } from "./store/action/configAction";
import WindowSpinner from "./shared/components/loaders/WindowSpinner";
// import MasterLayout from './mainLayout/MasterLayout';
import { getFiles } from "./locales/index";
import ForntPages from "./mainLayout/frontend/pages/ForntPages";
import ContactUs from "./mainLayout/frontend/ContactUs";
import { addToast } from "./store/action/toastAction";

function App() {
    //do not remove updateLanguage
    const dispatch = useDispatch();
    const token = localStorage.getItem(Tokens.ADMIN);
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    const loginUserArray = JSON.parse(localStorage.getItem("loginUserArray"));
    const { selectedLanguage, updateLanguage, config, language, loginUser } =
        useSelector((state) => state);
    const isAdmin = localStorage.getItem("isAdmin");
    const [loading, setLoading] = useState(false);
    const DarkMod = localStorage.getItem("isDarkMod");
    const [allLocales, setAllLocales] = useState({});
    const getData = getFiles();
    const [messages, setMessages] = useState(getData["en"]);
    const [userEditedMessage, setUserEditedMessage] = useState({});
    const updatesLanguage =
        allLocales[updatedLanguage ? updatedLanguage : selectedLanguage];
    const [languageData, setLanguageData] = useState([]);
    const [errorNum, setErrorNum] = useState(0);

    const server_error = JSON.parse(localStorage.getItem("server_error"));

    useEffect(() => {
        (server_error === true) & localStorage.removeItem("server_error");
    });

    useMemo(() => {
        if (server_error === true) {
            if (errorNum === 0) {
                localStorage.removeItem("server_error");
                dispatch(
                    addToast({
                        text: "Internal Server Close.",
                        type: toastType.ERROR,
                    })
                );
                setErrorNum(errorNum + 1);
            }
        }
    }, [errorNum, server_error]);

    useEffect(() => {
        const getData = getFiles();
        setAllLocales(getData);
    }, [language, updateLanguage?.lang_json_array, loginUser?.user]);

    useEffect(() => {
        if (updateLanguage?.iso_code === updatedLanguage && languageData) {
            setUserEditedMessage(updateLanguage?.lang_json_array);
        }
    }, [
        language,
        languageData,
        loginUser?.user,
        updateLanguage,
        updatedLanguage,
    ]);

    // updated language handling*
    useEffect(() => {
        if (Object.values(userEditedMessage).length !== 0) {
            setMessages(userEditedMessage);
        } else {
            if (updateLanguage?.iso_code === updatedLanguage) {
                const updateLanguages = updateLanguage?.lang_json_array;
                setMessages(updateLanguages);
            } else {
                if (
                    updatesLanguage === undefined ||
                    updatesLanguage === null ||
                    updatesLanguage === ""
                ) {
                    const defaultUpdateLanguage = allLocales["en"];
                    setMessages(defaultUpdateLanguage);
                } else {
                    if (
                        updatesLanguage === undefined ||
                        updatesLanguage === null
                    ) {
                        const defaultUpdateLanguage = allLocales["en"];
                        setMessages(defaultUpdateLanguage);
                    } else {
                        setMessages(updatesLanguage);
                    }
                }
            }
        }
    }, [
        allLocales,
        updateLanguage?.lang_json_array,
        loginUser?.user,
        updatesLanguage,
        userEditedMessage,
        updatedLanguage,
    ]);

    useEffect(() => {
        if (DarkMod === "true") {
            localStorage.setItem("isDarkMod", true);
        } else {
            localStorage.setItem("isDarkMod", false);
        }
    }, []);

    useEffect(() => {
        if (!loading) {
            setLoading(true);
        }
    }, []);

    useEffect(() => {
        if (loading) {
            setTimeout(() => {
                setLoading(false);
            }, 2500);
        }
    }, [loading]);

    useEffect(() => {
        dispatch(fetchFrontSetting());
        dispatch(fetchConfig());
    }, []);

    //Language Handling

    useEffect(() => {
        addRTLSupport(updatedLanguage ? updatedLanguage : selectedLanguage);
    }, [updatedLanguage, selectedLanguage]);

    return (
        <div className="d-flex flex-column flex-root">
            <IntlProvider
                locale={settingsKey.DEFAULT_LOCALE}
                messages={messages}
            >
                <Routes>
                    <Route
                        path="/home-plans"
                        element={loading ? <WindowSpinner /> : <Pricing />}
                    />
                    <Route
                        path="/contact"
                        element={loading ? <WindowSpinner /> : <ContactUs />}
                    />
                    <Route
                        path="/login"
                        element={loading ? <WindowSpinner /> : <Login />}
                    />
                    <Route
                        path="/pages/:name"
                        element={loading ? <WindowSpinner /> : <ForntPages />}
                    />
                    <Route
                        path="/register"
                        element={loading ? <WindowSpinner /> : <Register />}
                    />
                    <Route
                        path="app/*"
                        element={<AdminApp config={config} />}
                    />
                    {/* <Route path='/app/dashboard' element={<Dashboard />} /> */}
                    <Route
                        path="reset-password/:token/:email"
                        element={
                            loading ? <WindowSpinner /> : <ResetPassword />
                        }
                    />
                    {!loginUserArray && (
                        <Route
                            path="/app/reset-password"
                            element={
                                loading ? (
                                    <WindowSpinner />
                                ) : (
                                    <ResetLoginPassword />
                                )
                            }
                        />
                    )}
                    <Route
                        path="forgot-password"
                        element={
                            loading ? <WindowSpinner /> : <ForgotPassword />
                        }
                    />
                    {/* {isAdmin !== "true" && <Route path='/generate-qr-code' element={loading
                        ?
                        <WindowSpinner />
                        : <Home />} />} */}
                    <Route
                        path="/terms-conditions"
                        element={
                            loading ? <WindowSpinner /> : <TermsConditions />
                        }
                    />
                    <Route
                        path="/privacy-policy"
                        element={
                            loading ? <WindowSpinner /> : <PrivacyPolicye />
                        }
                    />
                    <Route
                        path="/"
                        element={loading ? <WindowSpinner /> : <HomePage />}
                    />
                    {/* <Route path='/' element={<MasterLayout />} /> */}

                    <Route
                        path="/"
                        element={
                            token ? (
                                <Navigate
                                    replace
                                    to={
                                        isAdmin === "true"
                                            ? "/app/admin/dashboard"
                                            : "app/dashboard"
                                    }
                                />
                            ) : loading ? (
                                <WindowSpinner />
                            ) : (
                                <HomePage />
                            )
                        }
                    />
                    {/* <Route path='/' element={<Navigate replace to={token ? isAdmin === "true" ? "/app/admin/dashboard" : "/app/dashboard" : "/login"} />} /> */}
                    <Route path="*" element={<Navigate replace to={"/"} />} />
                </Routes>
                <Toasts
                    language={
                        updatedLanguage ? updatedLanguage : selectedLanguage
                    }
                />
            </IntlProvider>
        </div>
    );
}

export default App;
