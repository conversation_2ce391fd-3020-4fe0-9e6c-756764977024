import Dashboard from "./components/dashboard/Dashboard";
import AdminDashboard from "./frontend/components/dashboard/AdminDashboard";
import Plans from "./frontend/components/plans/Plans";
import CreateUser from "./frontend/components/users/CreateUser";
import EditUser from "./frontend/components/users/EditUser";
import User from "./frontend/components/users/User";
import UserDetail from "./frontend/components/users/UserDetail";
import CreatePlans from "./frontend/components/plans/CreatePlans";
import EditPlan from "./frontend/components/plans/EditPlan";
import Project from "./components/projects/Project";
import CreateProject from "./components/projects/CreateProject";
import EditProject from "./components/projects/EditProject";
import UpdateProfile from "./components/user-profile/UpdateProfile";
import QrCode from "./components/qr-codes/QrCode";
import CreateQrCode from "./components/qr-codes/CreateQrCode";
import EditQrCode from "./components/qr-codes/EditQrCode";
import AdminQrCode from "./frontend/components/QrCode/AdminQrCode";
import AdminLinks from "./frontend/components/links/AdminLinks";
import UpdateProfileAdmin from "./frontend/components/user-profile-admin/UpdateProfileAdmin";
import AdminProjects from "./frontend/components/project/AdminProjects";
import Links from "./components/links/Link";
import CreateLink from "./components/links/CreateLink";
import EditLink from "./components/links/EditLink";
import Currencies from "./frontend/components/currency/Currencies";
import WebsiteSettings from "./frontend/components/websiteSettings/WebsiteSettings";
import MainTab from "./frontend/components/websiteSettings/MainTab";
import CaptchaTab from "./frontend/components/websiteSettings/CaptchaTab";
import EmailNotificationSettings from "./frontend/components/websiteSettings/EmailNotificationSettings";
import PaypalTab from "./frontend/components/websiteSettings/PaypalTab";
import StripeSetting from "./frontend/components/websiteSettings/StripeSetting";
import CustomStyleSetting from "./frontend/components/websiteSettings/CustomStyleSetting";
import RazorpaySetting from "./frontend/components/websiteSettings/RazorpaySetting";
import AdsSetting from "./frontend/components/websiteSettings/AdsSetting";
import SmtpSettings from "./frontend/components/websiteSettings/SmtpSettings";
import SocialsSetting from "./frontend/components/websiteSettings/SocialsSetting";
import AnnouncementSetting from "./frontend/components/websiteSettings/AnnouncementSetting";
import SelectedPlan from "./components/subscription/subscriptionUpgrad/SelectedPlan";
import PlanTab from "./components/subscription/subscriptionUpgrad/PlanTab";
import Codes from "./frontend/components/couponCodes/Codes";
import CreateCode from "./frontend/components/couponCodes/CreateCode";
import EditCode from "./frontend/components/couponCodes/EditCode";
import AdminLinkAnalytics from "./frontend/components/links/adminLinkAnalytics/AdminLinkAnalytics";
import CashPayments from "./frontend/components/cash-payments/CashPayments";
import SubscribedPlans from "./frontend/components/subscribedPlans/SubscribedUserPlans";
import ManageSubscription from "./components/subscription/ManageSubscription";
import PlanDetail from "./frontend/components/plans/PlanDetail";
import CouponCodeDetail from "./frontend/components/couponCodes/CouponCodeDetail";
import ProjectDetails from "./components/projects/ProjectDetails";
import ResetLoginPassword from "./components/auth/ResetLoginPassword";
import LinkDetails from "./components/links/LinkDetails";
import QrCodeDetails from "./components/qr-codes/QrCodeDetails";
import AdminLinkCreate from "./frontend/components/links/CreateLink";
import CreateAdminQrCode from "./frontend/components/QrCode/CreateAdminQrCode";
import Transactions from "./frontend/components/transactions/Transactions";
import TransactionsDetails from "./frontend/components/transactions/TransactionsDetails";
import SocialLoginSetting from "./frontend/components/websiteSettings/SocialLoginSetting";
import FrontCms from "./frontend/components/frontCMS/FrontCms";
import FrontFeatures from "./frontend/components/frontCMS/FrontFeatures";
import FrontSubFeatures from "./frontend/components/frontCMS/FrontSubFeatures";
import TermsAndConditions from "./frontend/components/frontCMS/TermsAndConditions";
import FrontEnquiries from "./frontend/components/frontCMS/FrontEnquiries";
import FrontEnquiriesDetail from "./frontend/components/frontCMS/FrontEnquiriesDetail";
import Subscribers from "./frontend/components/subscribers/Subscribers";
import Language from "./frontend/components/languages/Language";
import EditLanguageData from "./frontend/components/languages/EditLanguageData";
import PrivacyPolicy from "./frontend/components/frontCMS/PrivacyPolicy";
import ForntPages from "./mainLayout/frontend/pages/ForntPages";
import AdminPages from "./frontend/components/adminPages/AdminPages";
import CreatePage from "./frontend/components/adminPages/CreatePage";
import EditPage from "./frontend/components/adminPages/EditPage";
import FrontQRTypes from "./frontend/components/frontCMS/FrontQRTypes";
import CreateGroup from "./frontend/components/project/CreateGroup";
import DigitalBusinessCard from "./components/digitalBusinessCard/DigitalBusinessCard";
import CreateBusinessCard from "./components/digitalBusinessCard/CreateBusinessCard";
import AdminDigitalBusinessCard from "./frontend/components/digitalBussinessCard/AdminDigitalBusinessCard";
import AdminBusinessCardForm from "./frontend/components/digitalBussinessCard/AdminBusinessCardForm";
import AdminCreateBusinessCard from "./frontend/components/digitalBussinessCard/AdminCreateBusinessCard";
import EditBusinessCard from "./components/digitalBusinessCard/EditBusinessCard";
import CashPaymentDetails from "./frontend/components/cash-payments/CashPaymentDetails";
import AdminBusinessAnalytics from "./frontend/components/digitalBussinessCard/AdminBusinessAnalytics";
import BussinessCardTabs from "./components/digitalBusinessCard/BussinessCardTabs";
import AdminEditBusinessCard from "./frontend/components/digitalBussinessCard/adminSocialLink/AdminEditBusinessCard";
import BusinessCardDetail from "./frontend/components/digitalBussinessCard/BusinessCardDetail";
import PagesDetail from "./frontend/components/adminPages/PagesDetail";

export const route = [
    {
        path: "dashboard",
        ele: <Dashboard />,
        permission: "",
    },
    {
        path: "admin/dashboard",
        ele: <AdminDashboard />,
        permission: "",
    },
    {
        path: "profile/edit",
        ele: <UpdateProfile />,
        permission: "",
    },
    {
        path: "digital-business-cards",
        ele: <DigitalBusinessCard />,
        permission: "",
    },
    {
        path: "digital-business-cards/create",
        ele: <CreateBusinessCard />,
        permission: "",
    },
    {
        path: "digital-business-cards/edit/:id",
        ele: <EditBusinessCard />,
        permission: "",
    },
    {
        path: "admin/profile/edit",
        ele: <UpdateProfileAdmin />,
        permission: "",
    },
    {
        path: "admin/users",
        ele: <User />,
        permission: "",
    },
    {
        path: "admin/users/create",
        ele: <CreateUser />,
        permission: "",
    },
    {
        path: "admin/users/edit/:id",
        ele: <EditUser />,
        permission: "",
    },
    {
        path: "admin/users/detail/:id",
        ele: <UserDetail />,
        permission: "",
    },
    {
        path: "admin/plans",
        ele: <Plans />,
        permission: "",
    },
    {
        path: "admin/plans/create",
        ele: <CreatePlans />,
        permission: "",
    },
    {
        path: "admin/plans/edit/:id",
        ele: <EditPlan />,
        permission: "",
    },
    {
        path: "admin/plans/detail/:id",
        ele: <PlanDetail />,
        permission: "",
    },
    {
        path: "collections",
        ele: <Project />,
        permission: "",
    },
    {
        path: "collections/create",
        ele: <CreateProject />,
        permission: "",
    },
    {
        path: "collections/edit/:id",
        ele: <EditProject />,
        permission: "",
    },
    {
        path: "collections/detail/:id",
        ele: <ProjectDetails />,
        permission: "",
    },
    {
        path: "qrcode",
        ele: <QrCode />,
        permission: "",
    },
    {
        path: "qrcode/create",
        ele: <CreateQrCode />,
        permission: "",
    },
    {
        path: "qrcode/edit/:id",
        ele: <EditQrCode />,
        permission: "",
    },
    {
        path: "qrcode/detail/:id",
        ele: <QrCodeDetails />,
        permission: "",
    },
    {
        path: "admin/qr-codes",
        ele: <AdminQrCode />,
        permission: "",
    },
    {
        path: "admin/minify-link",
        ele: <AdminLinks />,
        permission: "",
    },
    {
        path: "admin/collections",
        ele: <AdminProjects />,
        permission: "",
    },
    {
        path: "minify-link",
        ele: <Links />,
        permission: "",
    },
    {
        path: "minify-link/create",
        ele: <CreateLink />,
        permission: "",
    },
    {
        path: "minify-link/edit/:id",
        ele: <EditLink />,
        permission: "",
    },
    {
        path: "minify-link/detail/:id",
        ele: <LinkDetails />,
        permission: "",
    },
    {
        path: "admin/currencies",
        ele: <Currencies />,
        permission: "",
    },
    {
        path: "admin/website-setting",
        ele: <WebsiteSettings />,
        permission: "",
    },
    // {
    //     path: "admin/settings-payment-paypal",
    //     ele: <PaymentConfiguration />,
    //     permission: ""
    // },
    {
        path: "admin/settings-main",
        ele: <MainTab />,
        permission: "",
    },
    {
        path: "admin/settings-captcha",
        ele: <CaptchaTab />,
        permission: "",
    },
    {
        path: "admin/payment-paypal",
        ele: <PaypalTab />,
        permission: "",
    },
    {
        path: "admin/settings-emailNotiy",
        ele: <EmailNotificationSettings />,
        permission: "",
    },
    {
        path: "admin/payment-stripe",
        ele: <StripeSetting />,
        permission: "",
    },
    {
        path: "admin/settings-customStyle",
        ele: <CustomStyleSetting />,
        permission: "",
    },
    {
        path: "admin/payment-razorpay",
        ele: <RazorpaySetting />,
        permission: "",
    },
    {
        path: "admin/settings-ads",
        ele: <AdsSetting />,
        permission: "",
    },

    {
        path: "admin/settings-smtp",
        ele: <SmtpSettings />,
        permission: "",
    },
    {
        path: "admin/settings-socials",
        ele: <SocialsSetting />,
        permission: "",
    },
    {
        path: "admin/settings-social-login",
        ele: <SocialLoginSetting />,
        permission: "",
    },
    // {
    //     path: "admin/settings-announcement",
    //     ele: <AnnouncementSetting />,
    //     permission: ""
    // },
    {
        path: "selected-plan/:id",
        ele: <SelectedPlan />,
        permission: "",
    },
    {
        path: "manage-subscription",
        ele: <ManageSubscription />,
        permission: "",
    },
    {
        path: "upgrade-plan",
        ele: <PlanTab />,
        permission: "",
    },
    {
        path: "admin/codes",
        ele: <Codes />,
        permission: "",
    },
    {
        path: "admin/codes/create",
        ele: <CreateCode />,
        permission: "",
    },
    {
        path: "admin/codes/edit/:id",
        ele: <EditCode />,
        permission: "",
    },
    {
        path: "admin/codes/detail/:id",
        ele: <CouponCodeDetail />,
        permission: "",
    },
    {
        path: "admin/minify-link/:id/analytics",
        ele: <AdminLinkAnalytics />,
        permission: "",
    },
    {
        path: "minify-link/:id/analytics",
        ele: <AdminLinkAnalytics />,
        permission: "",
    },
    {
        path: "admin/digital-business-cards/:id/analytics",
        ele: <AdminBusinessAnalytics />,
        permission: "",
    },
    {
        path: "digital-business-cards/:id/analytics",
        ele: <AdminBusinessAnalytics />,
        permission: "",
    },
    {
        path: "admin/digital-business-cards/edit/:id",
        ele: <AdminEditBusinessCard />,
        permission: "",
    },
    {
        path: "admin/digital-business-cards/detail/:id",
        ele: <BusinessCardDetail />,
        permission: "",
    },
    {
        path: "digital-business-cards/detail/:id",
        ele: <BusinessCardDetail />,
        permission: "",
    },
    {
        path: "stripe-payment-success?session_id=*",
        ele: <PlanTab />,
        permission: "",
    },
    {
        path: "admin/cash-payments",
        ele: <CashPayments />,
        permission: "",
    },
    {
        path: "admin/cash-payments/:id",
        ele: <CashPaymentDetails />,
        permission: "",
    },
    {
        path: "admin/subscribers",
        ele: <Subscribers />,
        permission: "",
    },
    {
        path: "admin/subscribed-plans",
        ele: <SubscribedPlans />,
        permission: "",
    },
    {
        path: "admin/minify-link/create",
        ele: <AdminLinkCreate />,
        permission: "",
    },
    {
        path: "admin/collections/create",
        ele: <CreateGroup />,
        permission: "",
    },
    {
        path: "admin/qr-codes/create",
        ele: <CreateAdminQrCode />,
        permission: "",
    },
    {
        path: "admin/transactions",
        ele: <Transactions />,
        permission: "",
    },
    {
        path: "admin/transactions/:id",
        ele: <TransactionsDetails />,
        permission: "",
    },
    {
        path: "admin/front-hero",
        ele: <FrontCms />,
        permission: "",
    },
    {
        path: "admin/front-features",
        ele: <FrontFeatures />,
        permission: "",
    },
    {
        path: "admin/front-qrcodes-types",
        ele: <FrontQRTypes />,
        permission: "",
    },
    {
        path: "admin/front-subfeatures",
        ele: <FrontSubFeatures />,
        permission: "",
    },
    {
        path: "admin/front-terms-conditions",
        ele: <TermsAndConditions />,
        permission: "",
    },
    {
        path: "admin/front-privacy-policy",
        ele: <PrivacyPolicy />,
        permission: "",
    },
    {
        path: "admin/front-enquiries",
        ele: <FrontEnquiries />,
        permission: "",
    },
    {
        path: "admin/front-enquiries/:id",
        ele: <FrontEnquiriesDetail />,
        permission: "",
    },
    {
        path: "admin/languages",
        ele: <Language />,
        permission: "",
    },
    {
        path: "admin/languages/:id",
        ele: <EditLanguageData />,
        permission: "",
    },
    // {
    //     path: "pages/:name",
    //     ele: <ForntPages />,
    //     permission: "",
    // },
    // {
    //     path: "admin/custom-pages",
    //     ele: <AdminPages />,
    //     permission: "",
    // },
    // {
    //     path: "admin/custom-pages/create",
    //     ele: <CreatePage />,
    //     permission: "",
    // },
    // {
    //     path: "admin/custom-pages/edit/:id",
    //     ele: <EditPage />,
    //     permission: "",
    // },
    // {
    //     path: "admin/custom-pages/detail/:id",
    //     ele: <PagesDetail />,
    //     permission: "",
    // },
    {
        path: "admin/plans/detail/:id",
        ele: <PlanDetail />,
        permission: "",
    },
    {
        path: "admin/digital-business-cards",
        ele: <AdminDigitalBusinessCard />,
        permission: "",
    },
    {
        path: "admin/digital-business-cards/create",
        ele: <AdminCreateBusinessCard />,
        permission: "",
    },
    // {
    //     path: "reset-password",
    //     ele: <ResetLoginPassword />,
    //     permission: ''
    // }
];
