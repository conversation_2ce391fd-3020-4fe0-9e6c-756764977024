import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import EditSubscribedUserPlan from './EditSubscribedUserPlan';
import { fetchSubsctibedUserPlan, updatePlanStatus } from '../../../store/action/adminActions/subscribedUserPlanAction';
import { fetchCurrencies } from '../../../store/action/adminActions/currencyAction';
import { Link } from 'react-router-dom';


const SubscribedUserPlans = (props) => {
    const { isLoading, fetchSubsctibedUserPlan, subscribedUserPlan, fetchCurrencies, currencies, frontSettings, totalRecord, updatePlanStatus } = props;


    const [toggle, setToggle] = useState(false);
    const [subscribedPlan, setSubscribedPlan] = useState();
    const [currencySymbol, setCurrencySymbol] = useState("")
    const [planStatus, setPlanStatus] = useState(false);
    const handleClose = (item = null) => {
        setToggle(!toggle);
        setSubscribedPlan({
            ...item,
            end_date: item.endDate
        });
    };

    useEffect(() => {
        fetchCurrencies()
        // fetchSubsctibedUserPlan()
    }, [])

    useEffect(() => {
        if (currencies.length > 0 && frontSettings) {
            const symbole = currencies.filter((d) => parseInt(frontSettings?.currency) === d.id)
            if (symbole.length === 1) {
                setCurrencySymbol(symbole[0]?.attributes?.symbol)
            }
        }
    }, [currencies, frontSettings])

    const itemsValue = subscribedUserPlan.length >= 0 && subscribedUserPlan.map(item => ({
        user_image: item?.user?.image_url,
        user_name: item?.user?.name,
        user_email: item?.user?.email,
        user_id: item?.user?.id,
        plan_name: item?.plan?.name,
        plan_id: item?.plan?.id,
        plan_price: currencySymbol + " " + item?.subscription?.plan?.price,
        payable_ammount: currencySymbol + " " + item?.price_of_plan,
        start_date: getFormattedDate(item?.start_date),
        end_date: getFormattedDate(item?.end_date),
        status: item?.status,
        id: item?.id,
        endDate: item?.end_date
    }));

    const onChange = (filter) => {
        fetchSubsctibedUserPlan(filter, true);
    };
    const onCheckedEmail = (e, id, userid) => {
        setPlanStatus(true)
        updatePlanStatus(id, setPlanStatus, userid)
    };
    const columns = [
        {
            name: getFormattedMessage('users.table.user.column.title'),
            selector: row => row?.name,
            sortField: 'name',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`}>
                            {row.user_image ?
                                <img src={row.user_image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName(row.user_name)}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row?.user_id}`} className='text-decoration-none'>{row?.user_name}</Link>
                        <div>{row?.user_email}</div>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.plan.name.title'),
            selector: row => row.plan_name,
            sortField: 'plan_name',
            sortable: false,
        },
        {
            name: getFormattedMessage('globally.plan.start.date.title'),
            selector: row => row.start_date,
            sortField: 'start_date',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div>{row.start_date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('globally.plan.end.date.title'),
            selector: row => row.end_date,
            sortField: 'end_date',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div>{row.end_date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('dashboard.recentSales.status.label'),
            selector: row => row.status,
            sortField: 'status',
            sortable: false,
            cell: row => {
                return (
                    <div className="col-md-4 d-flex align-items-center mt-4">
                        <label className="form-check form-switch form-switch-sm cursor-pointer">
                            <input autoComplete="off" name="paypal" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                checked={row.status === true ? true : false}
                                onChange={(e) => onCheckedEmail(e, row.id, row.user_id)}
                                // disabled={row.status === true ? true : false}
                            />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => {
                return <ActionButton item={row} goToEditProduct={handleClose} isEditMode={true} isDeleteMode={false} />
            }
        }
    ];


    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('subscribed-plan.title')} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} isResetBtn={false}  isEmailStatus={planStatus}
                isLoading={isLoading} totalRows={totalRecord} isShowFilterField={true} isPlanStatus={true}/>
            <EditSubscribedUserPlan handleClose={handleClose} title={getFormattedMessage("edit.subscription.plan.title")} show={toggle} singleSubscribedPlan={subscribedPlan} />
        </MasterLayout>
    )
};


const mapStateToProps = (state) => {
    const { isLoading, subscribedUserPlan, currencies, frontSettings, totalRecord } = state;
    return { isLoading, subscribedUserPlan, currencies, frontSettings, totalRecord }
};
export default connect(mapStateToProps, { fetchSubsctibedUserPlan, fetchCurrencies, updatePlanStatus })(SubscribedUserPlans);
