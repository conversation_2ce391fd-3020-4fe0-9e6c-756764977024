import React, { useState, createRef } from 'react';
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { Modal } from 'react-bootstrap-v5';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactDatePicker from '../../../shared/datepicker/ReactDatePicker';
import { getFormattedMessage } from '../../../shared/sharedMethod';

const EditSubscribedPlan = (props) => {
    const { singleSubscribedPlan, handleClose, show, title } = props;
    const [formValue, setFormValue] = useState({
        end_date: singleSubscribedPlan ? singleSubscribedPlan.end_date : new Date,
    });


    // const handleValidation = () => {
    //     let errorss = {};
    //     let isValid = false;
    //     if (!formValue['name'].trim()) {
    //         errorss['name'] = getFormattedMessage("currency.modal.input.name.validate.label");
    //     } else {
    //         isValid = true;
    //     }
    //     setErrors(errorss);
    //     return isValid;
    // };

    const onChangeStartDate = (date) => {
        setFormValue(inputs => ({ ...inputs, end_date: date }))
    };

    const onSubmit = (event) => {
        event.preventDefault();
        // const valid = handleValidation();
        // if (singleSubscribedPlan && valid) {
        //     if (!disabled) {
        //         editCurrency(singleSubscribedPlan.id, formValue, handleClose);
        //         clearField(false);
        //     }
        // } else {
        //     if (valid) {
        //         setFormValue(formValue);
        //         addCurrencyData(formValue);
        //         clearField(false);
        //     }
        // }

    };

    const clearField = () => {
        setFormValue({
            end_date: new Date
        });
        handleClose(false);
    };

    return (
        <Modal show={show}
            onHide={clearField}
            keyboard={true}
        >
            <Form onKeyPress={(e) => {
                if (e.key === 'Enter') {
                    onSubmit(e)
                }
            }}>
                <Modal.Header closeButton>
                    <Modal.Title>{title}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <label className='form-label'>
                        {getFormattedMessage('globally.plan.end.date.title')}:<span className="required" />
                    </label>
                    <ReactDatePicker
                        onChangeDate={onChangeStartDate}
                        newStartDate={formValue.end_date}
                        isShowTimeSelect={false}
                    />
                </Modal.Body>
            </Form>
            <ModelFooter onEditRecord={singleSubscribedPlan} onSubmit={onSubmit}
                clearField={clearField} />
        </Modal>
    )
};

export default connect(null, {})(EditSubscribedPlan);
