import React, { useState } from 'react';
import { connect } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import EditSubscribedPlan from './EditSubscribedPlan';

const CashPayments = (props) => {
    const { isLoading } = props;

    const [toggle, setToggle] = useState(false);
    const [subscribedPlan, setSubscribedPlan] = useState();

    const handleClose = (item = null) => {
        setToggle(!toggle);
        setSubscribedPlan(item);
    };

    // const itemsValue = project_admin.length >= 0 && project_admin.map(project => ({
    //     start_date: getFormattedDate(new Date, "d-m-y"),
    //     end_date: getFormattedDate(new Date, "d-m-y"),
    //     // time: moment(new Date).format('LT'),
    //     name: "USER 1",
    //     plan_name: "Hello",
    //     plan_price: 1000,
    //     payable_ammount: 700,
    //     status: "active",
    //     user_name: project.attributes.user_name,
    //     user_email: project.attributes.user_email,
    //     user_id: project.attributes.user_id,
    //     id: project.id
    // }));

    const itemsValue = [{
        start_date: getFormattedDate(new Date, "d-m-y"),
        end_date: getFormattedDate(new Date, "d-m-y"),
        // time: moment(new Date).format('LT'),
        name: "USER 1",
        plan_name: "Hello",
        status: "active",
        // user_name: project.attributes.user_name,
        // user_email: project.attributes.user_email,
        // user_id: project.attributes.user_id,
        id: 1
    }]

    const onChange = (filter) => {
        // fetchProjects(filter, true);
    };

    const columns = [
        {
            name: getFormattedMessage('users.table.user.column.title'),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
        },
        {
            name: getFormattedMessage('globally.plan.name.title'),
            selector: row => row.plan_name,
            sortField: 'plan_name',
            sortable: true,
        },
        {
            name: getFormattedMessage('globally.plan.start.date.title'),
            selector: row => row.start_date,
            sortField: 'start_date',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div>{row.start_date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('globally.plan.end.date.title'),
            selector: row => row.end_date,
            sortField: 'end_date',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div>{row.end_date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('dashboard.recentSales.status.label'),
            selector: row => row.status,
            sortField: 'status',
            sortable: true,
            cell: row => {
                return (
                    <div className="col-md-4 d-flex align-items-center mt-4">
                        <label className="form-check form-switch form-switch-sm cursor-pointer">
                            <input autoComplete="off" name="paypal" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                            // checked={row.email_verified_at !== null ? true : false}
                            // onChange={(e) => onCheckedEmail(e, row.id)}
                            // disabled={row.email_verified_at !== null ? true : false}
                            />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => {
                return <ActionButton item={row} goToEditProduct={handleClose} isEditMode={true} isDeleteMode={false} />
            }
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('subscribed-plan.title')} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                isLoading={isLoading} />
            <EditSubscribedPlan handleClose={handleClose} title={getFormattedMessage("edit.subscription.plan.title")} show={toggle} singleSubscribedPlan={subscribedPlan} />
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { isLoading } = state;
    return { isLoading }
};
export default connect(mapStateToProps, {})(CashPayments);
