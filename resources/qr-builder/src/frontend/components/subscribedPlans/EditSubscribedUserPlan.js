import React, { useState, createRef, useEffect } from 'react';
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { Modal } from 'react-bootstrap-v5';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactDatePicker from '../../../shared/datepicker/ReactDatePicker';
import { getFormattedDate, getFormattedMessage } from '../../../shared/sharedMethod';
import moment from 'moment';
import { EditSubscribedUserPlanEndDate } from '../../../store/action/adminActions/subscribedUserPlanAction';


const EditSubscribedPlan = (props) => {
    const { singleSubscribedPlan, handleClose, show, title, EditSubscribedUserPlanEndDate } = props;
    const [formValue, setFormValue] = useState({
        end_date: singleSubscribedPlan ? new Date(singleSubscribedPlan?.end_date) : new Date,
    });

    useEffect(() => {
        if (singleSubscribedPlan?.end_date) {
            setFormValue({
                end_date: singleSubscribedPlan ? new Date(singleSubscribedPlan?.end_date) : new Date,
            })
        }
    }, [singleSubscribedPlan])

    const onChangeStartDate = (date) => {
        setFormValue(inputs => ({ ...inputs, end_date: date }))
    };

    const onSubmit = (event) => {
        event.preventDefault();
        if (singleSubscribedPlan?.end_date) {
            EditSubscribedUserPlanEndDate({
                subscription_id: singleSubscribedPlan.id,
                end_date: moment(formValue.end_date).format("YYYY-MM-DD")
            })
            handleClose(false)
        }
    };

    const clearField = () => {
        setFormValue({
            end_date: new Date
        });
        handleClose(false);
    };

    return (
        <Modal show={show}
            onHide={clearField}
            keyboard={true}
        >
            <Form onKeyPress={(e) => {
                if (e.key === 'Enter') {
                    onSubmit(e)
                }
            }}>
                <Modal.Header closeButton>
                    <Modal.Title>{title}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className='subscription-plan-date-picker'>
                        <label className='form-label'>
                            {getFormattedMessage('globally.plan.end.date.title')}:<span className="required" />
                        </label>
                        <ReactDatePicker
                            onChangeDate={onChangeStartDate}
                            newStartDate={formValue.end_date}
                            isShowTimeSelect={false}
                            minDate={new Date(singleSubscribedPlan?.end_date)}
                            selectYear={true}
                        />
                    </div>
                </Modal.Body>
            </Form>
            <ModelFooter onEditRecord={singleSubscribedPlan} onSubmit={onSubmit}
                clearField={clearField} />
        </Modal>
    )
};


export default connect(null, { EditSubscribedUserPlanEndDate })(EditSubscribedPlan);
