import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { formatAmount, getAvatarName, getFormattedDate, getFormattedOptions } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { approveCashPayment, fetchCashPayments } from '../../../store/action/adminActions/cashPaymentActions';
import { fetchAllCurrency } from '../../../store/action/adminActions/currencyAction';
import { cashPaymentStatusMethodOptions } from '../../../constants';
import ReactSelect from '../../../shared/select/reactSelect';
import moment from 'moment';

const CashPayments = (props) => {
    const { cashPayments, fetchCashPayments, totalRecord, isLoading, fetchAllCurrency, currencies, approveCashPayment } = props;

    useEffect(() => {
        fetchAllCurrency()
    }, [])

    const cashPaymentStatusMethodOption = getFormattedOptions(cashPaymentStatusMethodOptions)
    const cashPaymentStatusMethodOptionValue = cashPaymentStatusMethodOption?.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const onCashPaymentChange = (obj, row) => {
        const data = {
            id: row.id,
            status: obj.value
        }
        if (obj.value === 1) {
            approveCashPayment(data)
        } else {
            approveCashPayment(data)
        }
    }

    const currenciesType = currencies?.map((d) => {
        return {
            id: d.id,
            name: d.attributes.symbol
        }
    })
    const currenciesOption = getFormattedOptions(currenciesType)
    const currenciesTypeDefaultValue = currenciesOption?.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const itemsValue = cashPayments.length >= 0 && cashPayments?.map(item => {
        const findCurrencies = currenciesTypeDefaultValue?.filter((items) => { return (items.value === item?.subscription?.plan?.currency_id) })
        return {
            created_at: getFormattedDate(item.date),
            time: moment(item.created_at).format('LT'),
            user_image: item?.subscription?.user?.image_url,
            user_name: item?.subscription?.user?.name,
            user_email: item?.subscription?.user?.email,
            user_id: item?.subscription?.user?.id,
            plan_name: item?.subscription?.plan?.name,
            plan_id: item?.subscription?.plan?.id,
            plan_price: formatAmount(item?.subscription?.plan?.price),
            payable_ammount: formatAmount(item?.price_of_plan),
            start_date: getFormattedDate(item?.subscription?.start_date),
            end_date: getFormattedDate(item?.subscription?.end_date),
            is_manual_payment: item?.is_manual_payment,
            status: item?.status,
            currency_id: findCurrencies[0]?.label,
            payment_date: getFormattedDate(item?.subscription?.created_at) + " " + moment(item?.subscription?.created_at).format("LT"),
            id: item?.id,
        }
    });

    const onChange = (filter) => {
        fetchCashPayments(filter, true);
    };


    const goToDetailScreen = (id) => {
        window.location.href = '#/app/admin/cash-payments/' + id;
    };

    const columns = [
        {
            name: getFormattedMessage('globally.react-table.column.created-date.label'),
            selector: row => row.created_at,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div className='mb-1'>{row.time}</div>
                        <div>{row.created_at}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('users.table.user.column.title'),
            selector: row => row?.user_image,
            sortField: 'user_image',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`}>
                            {row.user_image ?
                                <img src={row.user_image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName(row.user_name)}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row?.user_id}`} className='text-decoration-none'>{row?.user_name}</Link>
                        <div>{row?.user_email}</div>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.plan.name.title'),
            selector: row => row?.plan_name,
            sortField: 'plan_name',
            sortable: false,
        },
        // {
        //     name: getFormattedMessage('globally.plan.price.title'),
        //     sortField: 'price_of_plan',
        //     sortable: true,
        //     cell: row => {
        //         return <div>
        //             <span>{row?.currency_id} </span>
        //             <span>{row?.plan_price}</span>
        //         </div>
        //     }
        // },
        {
            name: getFormattedMessage('new.plan.payable.amount.title'),
            sortField: 'price_of_plan',
            sortable: true,
            cell: row => {
                return <div>
                    <span>{row?.currency_id} </span>
                    <span>{row?.payable_ammount}</span>
                </div>
            }
        },
        // {
        //     name: getFormattedMessage('globally.plan.start.date.title'),
        //     selector: row => row?.start_date,
        //     sortField: 'created_at',
        //     sortable: true,
        //     cell: row => {
        //         return (
        //             <span className='badge bg-light-info'>
        //                 <div>{row?.start_date}</div>
        //             </span>
        //         )
        //     }
        // },
        // {
        //     name: getFormattedMessage('globally.plan.end.date.title'),
        //     selector: row => row?.end_date,
        //     sortField: 'created_at',
        //     sortable: true,
        //     cell: row => {
        //         return (
        //             <span className='badge bg-light-info'>
        //                 <div>{row?.end_date}</div>
        //             </span>
        //         )
        //     }
        // },
        {
            name: getFormattedMessage('dashboard.recentSales.status.label'),
            selector: row => row?.status,
            sortField: 'status',
            sortable: false,
            cell: row => {
                return (
                    row.is_manual_payment === 1 ?
                        <span className='badge bg-light-success'>
                            <div>{getFormattedMessage('globally.approved.label')}</div>
                        </span>
                        : row.is_manual_payment === 2 ?
                            <span className='badge bg-light-danger'>
                                <div>{getFormattedMessage("globally.reject.label")}</div>
                            </span>
                            : <ReactSelect
                                isRequired
                                defaultValue={cashPaymentStatusMethodOptionValue[0]}
                                multiLanguageOption={cashPaymentStatusMethodOption}
                                onChange={(e) => onCashPaymentChange(e, row)}
                            />
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton item={row} isViewIcon={true} isEditMode={false} isDeleteMode={false} goToDetailScreen={goToDetailScreen} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('cash-payments.title')} />
            <div className='cash-payment-table'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                    totalRows={totalRecord} isLoading={isLoading} />
            </div>
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { project_admin, totalRecord, isLoading, cashPayments, currencies } = state;
    return { project_admin, totalRecord, isLoading, cashPayments, currencies }
};
export default connect(mapStateToProps, { fetchCashPayments, fetchAllCurrency, approveCashPayment })(CashPayments);
