import React, { useEffect } from "react";
import { connect } from "react-redux";
import { Card, Table } from "react-bootstrap";
import MasterLayout from "../MasterLayout";
import TabTitle from "../../../shared/tab-title/TabTitle";
import {
    formatAmount,
    getFormattedDate,
    getFormattedOptions,
    placeholderText,
} from "../../../shared/sharedMethod";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import { useParams } from "react-router-dom";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import moment from "moment";
import { fetchAllCurrency } from "../../../store/action/adminActions/currencyAction";
import HeaderTitle from "../../../components/header/HeaderTitle";
import RoundLoader from "../../../shared/components/loaders/RoundLoader";
import { fetchCashPayment } from "../../../store/action/adminActions/cashPaymentActions";

const CashPaymentDetails = (props) => {
    const {
        currencies,
        fetchAllCurrency,
        isLoading,
        cashPaymentDetails,
        fetchCashPayment,
    } = props;
    const { id } = useParams();
    useEffect(() => {
        fetchCashPayment(id);
        fetchAllCurrency();
    }, []);

    const currenciesType = currencies.map((d) => {
        return {
            id: d.id,
            name: d.attributes.symbol,
        };
    });
    const currenciesOption = getFormattedOptions(currenciesType);
    const currenciesTypeDefaultValue = currenciesOption.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    const findCurrencies = currenciesTypeDefaultValue.filter((items) => {
        return (
            items.value === cashPaymentDetails?.subscription?.plan?.currency_id
        );
    });

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("cashpayment.details.title")} />
            <HeaderTitle
                title={getFormattedMessage("cashpayment.details.title")}
                to={"/app/admin/cash-payments"}
            />
            {
                <>
                    <div className="pt-5">
                        <Card>
                            <Card.Body>
                                {isLoading ? (
                                    <RoundLoader />
                                ) : (
                                    <Table responsive>
                                        <tbody>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "users.table.user-name.column.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {cashPaymentDetails &&
                                                        cashPaymentDetails
                                                            ?.subscription?.user
                                                            ?.name}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.input.email.placeholder.label"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {cashPaymentDetails &&
                                                        cashPaymentDetails
                                                            ?.subscription?.user
                                                            ?.email}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.react-table.column.payment-date.label"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    <span className="badge bg-light-info">
                                                        {cashPaymentDetails &&
                                                            getFormattedDate(
                                                                cashPaymentDetails
                                                                    ?.subscription
                                                                    ?.created_at
                                                            ) +
                                                                " " +
                                                                moment(
                                                                    cashPaymentDetails
                                                                        ?.subscription
                                                                        ?.created_at
                                                                ).format("LT")}
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.plan.name.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {cashPaymentDetails &&
                                                        cashPaymentDetails
                                                            ?.subscription?.plan
                                                            ?.name}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.plan.price.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {cashPaymentDetails &&
                                                        findCurrencies[0]
                                                            ?.label +
                                                            " " +
                                                            formatAmount(
                                                                cashPaymentDetails
                                                                    ?.subscription
                                                                    ?.plan
                                                                    ?.price
                                                            )}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "new.plan.payable.amount.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {cashPaymentDetails &&
                                                        findCurrencies[0]
                                                            ?.label +
                                                            " " +
                                                            formatAmount(
                                                                cashPaymentDetails?.price_of_plan
                                                            )}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.plan.start.date.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    <span className="badge bg-light-info">
                                                        <div>
                                                            {cashPaymentDetails &&
                                                                getFormattedDate(
                                                                    cashPaymentDetails
                                                                        ?.subscription
                                                                        ?.start_date
                                                                )}
                                                        </div>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.plan.end.date.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    <span className="badge bg-light-info">
                                                        <div>
                                                            {cashPaymentDetails &&
                                                                getFormattedDate(
                                                                    cashPaymentDetails
                                                                        ?.subscription
                                                                        ?.end_date
                                                                )}
                                                        </div>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "dashboard.recentSales.status.label"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {cashPaymentDetails &&
                                                    cashPaymentDetails.is_manual_payment ===
                                                        1 ? (
                                                        <span className="badge bg-light-success">
                                                            <div>
                                                                {getFormattedMessage(
                                                                    "globally.approved.label"
                                                                )}
                                                            </div>
                                                        </span>
                                                    ) : cashPaymentDetails.is_manual_payment ===
                                                      2 ? (
                                                        <span className="badge bg-light-danger">
                                                            <div>
                                                                {getFormattedMessage(
                                                                    "globally.reject.label"
                                                                )}
                                                            </div>
                                                        </span>
                                                    ) : (
                                                        <span className="badge bg-light-primary">
                                                            <div>
                                                                {getFormattedMessage(
                                                                    "globally.pending.label"
                                                                )}
                                                            </div>
                                                        </span>
                                                    )}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </Table>
                                )}
                            </Card.Body>
                        </Card>
                    </div>
                </>
            }
        </MasterLayout>
    );
};

const mapStateToProps = (state) => {
    const { isLoading, currencies, frontSettings, cashPaymentDetails } = state;
    return { isLoading, currencies, frontSettings, cashPaymentDetails };
};

export default connect(mapStateToProps, { fetchAllCurrency, fetchCashPayment })(
    CashPaymentDetails
);
