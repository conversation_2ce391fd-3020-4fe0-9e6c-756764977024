import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import DeletePlan from './DeletePlan';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { formatAmount, getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { fetchPlans, makeDefaultPlan } from '../../../store/action/plansAction';

const Plans = (props) => {
    const { plans, fetchPlans, totalRecord, isLoading, makeDefaultPlan } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);

    const navigate = useNavigate()

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };


    const itemsValue = plans?.length >= 0 && plans?.map(Plan => ({
        // date: getFormattedDate(Plan.attributes.created_at, "d-m-y"),
        // time: moment(Plan.attributes.created_at).format('LT'),
        name: Plan?.attributes?.name,
        order: Plan?.attributes?.order,
        is_default: Plan?.attributes?.is_default,
        price: Plan?.attributes?.price,
        trial_days: Plan?.attributes?.trial_days,
        currency_symbol: Plan?.attributes?.currency_symbol,
        total_user: Plan?.attributes?.total_user,
        freqency_type: Plan?.attributes?.type,
        id: Plan?.id
    }));

    const onChange = (filter) => {
        fetchPlans(filter, true);
    };

    const goToEdit = (item) => {
        const id = item.id;
        window.location.href = '#/app/admin/plans/edit/' + id;
    };

    const onChnageStatus = (id) => {
        makeDefaultPlan(id, navigate)
    }

    const columns = [
        {
            name: getFormattedMessage('react-data-table.name.column.title'),
            selector: row => row?.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/plans/detail/${row?.id}`} className='text-decoration-none'>{row?.name}</Link>
                    </div>
                </div>
            }
        },
        // {
        //     name: getFormattedMessage('globally.input.order.lable'),
        //     selector: row => row?.order,
        //     sortField: 'order',
        //     sortable: false,
        // },
        {
            name: getFormattedMessage('globally.input.price.label'),
            selector: row => row?.price,
            sortField: 'price',
            sortable: true,
            cell: row => {
                return <div className='w-50'>
                    {row?.currency_symbol} {formatAmount(Number(row?.price))}
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.input.trial-days.lable'),
            selector: row => row?.trial_days,
            sortField: 'trial_days',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    {row?.trial_days ? row?.trial_days + ` ${placeholderText("days.title")}` : "N/A"}
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.heading.frequency.title'),
            selector: row => row?.freqency_type,
            sortField: 'freqency_type',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    {
                        row?.freqency_type === 1
                            ?
                            getFormattedMessage("globally.heading.month.label")
                            :
                            getFormattedMessage("globally.heading.year.label")
                    }
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.heading.active-plans.label'),
            selector: row => row?.total_user,
            sortField: 'total_user',
            sortable: false,
            cell: row => {
                return <div className='badge bg-light-info'>
                    {row?.total_user}
                </div>
            }
        },
        {
            name: getFormattedMessage('react-data-table.make-default.label'),
            sortField: 'is_default',
            sortable: false,
            cell: row => {
                return (
                    row?.is_default === false
                        ?
                        <div className="col-md-4 d-flex align-items-center mt-4">
                            <label className="form-check form-switch form-switch-sm cursor-pointer">
                                <input autoComplete="off" name="phone" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                    value={row.status}
                                    onChange={(e) => onChnageStatus(row.id)}
                                />
                                <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                            </label>
                        </div>
                        :
                        <span className='badge bg-light-success'>
                            {getFormattedMessage("globally.react-table.column.default-plan.label")}
                        </span>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton isDeleteMode={row?.total_user >= 1 ? false : true && row?.is_default === true ? false : true} item={row} goToEditProduct={goToEdit} isEditMode={true}
                onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('plans.title')} />
            <div className='plan_table'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                    ButtonValue={getFormattedMessage('plans.create.title')}
                    to='#/app/admin/plans/create' totalRows={totalRecord} isLoading={isLoading} />
            </div>
            <DeletePlan onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { plans, totalRecord, isLoading, allConfigData } = state;
    return { plans, totalRecord, isLoading, allConfigData }
};
export default connect(mapStateToProps, { fetchPlans, makeDefaultPlan })(Plans);
