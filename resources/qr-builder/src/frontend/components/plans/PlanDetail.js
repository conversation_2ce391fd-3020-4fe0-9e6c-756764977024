import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Badge, Card, Table } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate, placeholderText } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import { fetchPlan } from "../../../store/action/plansAction"
import Spinner from "../../../shared/components/loaders/Spinner";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { typeOptions } from '../../../constants';
import HeaderTitle from "../../../components/header/HeaderTitle";
import RoundLoader from '../../../shared/components/loaders/RoundLoader';
import moment from 'moment';

const PlanDetail = (props) => {
    const { isLoading, fetchPlan, plans } = props;
    const { id } = useParams();

    const variants = ["success", "primary", "dark", "secondary", "info", "warning", "danger"]

    const qrTypes = plans[0]?.attributes.qr_code_types.split(",")
    const selectedType = qrTypes?.map((qrt) => {
        return typeOptions.filter((to) => to.id === parseInt(qrt))[0]?.name
    })

    useEffect(() => {
        fetchPlan(id);
    }, []);

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('plan-details.title')} />
            {<>
                <HeaderTitle title={getFormattedMessage('plan-details.title')} to='/app/admin/plans' editLink={`/app/admin/plans/edit/${id}`} />
                <div className='pt-5'>
                    <Card>
                        {/* <Card.Header as='h5'>{getFormattedMessage('plan.details.title')}</Card.Header> */}
                        <Card.Body className=''>
                            {isLoading ? <RoundLoader /> : <Table responsive>
                                <tbody>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.plan.name.title')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.name}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.order.lable')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.order}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.price.label')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.currency_symbol} {plans[0]?.attributes.price}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.trial-days.lable')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.trial_days || "N/A"}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.heading.frequency.title')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.type === 1 ? "Month" : "Year"}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.links-imit.lable')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.links_limit}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.projects-imit.lable')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.projects_limit}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.qr-codes-imit.lable')}</td>
                                        <td className='py-4'>{plans[0]?.attributes.qr_code_limit}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('react-data-table.qr-code-types.label')}</td>
                                        <td className='py-4'>{
                                            selectedType?.map((d, i) => {
                                                return <Badge key={i + 1} bg={variants[Math.floor(Math.random() * variants.length)]} className={i === 0 ? "m-1 ms-0" : "m-1"}>
                                                    {getFormattedMessage(d)}</Badge>
                                            })
                                        }</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.created-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(plans[0]?.attributes?.created_at, "d-m-y")} {moment(plans[0]?.attributes?.created_at).format('LT')}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.updated-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(plans[0]?.attributes?.updated_at, "d-m-y")} {moment(plans[0]?.attributes?.updated_at).format('LT')}
                                        </td>
                                    </tr>
                                </tbody>
                            </Table>}
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </MasterLayout>
    )
};

const mapStateToProps = state => {
    const { isLoading, plans } = state;
    return { isLoading, plans }
};

export default connect(mapStateToProps, { fetchPlan })(PlanDetail);
