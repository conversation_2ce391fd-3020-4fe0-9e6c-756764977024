import React, { useEffect } from "react";
import { connect } from "react-redux";
import { fetchPlan } from "../../../store/action/plansAction";
import { useParams } from "react-router-dom";
import MasterLayout from "../MasterLayout";
import HeaderTitle from "../../../components/header/HeaderTitle";
import {
    getFormattedMessage,
    placeholderText,
} from "../../../shared/sharedMethod";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import PlansForm from "./PlansForm";
import { fetchCurrencies } from "../../../store/action/adminActions/currencyAction";
import { Filters } from "../../../constants";
import TabTitle from "../../../shared/tab-title/TabTitle";

const EditPlan = (props) => {
    const { fetchPlan, plans, fetchCurrencies } = props;
    const { id } = useParams();

    useEffect(() => {
        fetchPlan(id);
    }, []);

    useEffect(() => {
        fetchCurrencies(Filters.OBJ, true);
    }, []);

    const itemsValue =
        plans &&
        plans.length === 1 &&
        plans.map((plan) => ({
            name: plan.attributes.name,
            order: plan.attributes.order,
            trial_days: plan.attributes.trial_days,
            type: plan.attributes.type,
            status: plan.attributes.status,
            plan_id: plan.attributes.plan_id,
            qr_code_limit: plan.attributes.qr_code_limit,
            links_limit: plan.attributes.links_limit,
            projects_limit: plan.attributes.projects_limit,
            // password_protection_enabled: plan.attributes.password_protection_enabled,
            qr_code_types: plan.attributes.qr_code_types,
            id: plan.id,
            currency_id: {
                value: plan.attributes.currency_id,
                label:
                    plan.attributes.currency_symbol +
                    " " +
                    plan.attributes.currency_name,
            },
            price: plan.attributes.price,
            allow_create_business_card:
                plan.attributes.allow_create_business_card,
            business_card_limit: plan.attributes.business_card_limit,
            description: plan.attributes.description,
        }));

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("plan.edit.title")} />
            <HeaderTitle
                title={getFormattedMessage("plan.edit.title")}
                to="/app/admin/plans"
            />
            {plans.length === 1 && (
                <PlansForm singlePlan={itemsValue} id={id} />
            )}
        </MasterLayout>
    );
};

const mapStateToProps = (state) => {
    const { plans } = state;
    return { plans };
};

export default connect(mapStateToProps, { fetchPlan, fetchCurrencies })(
    EditPlan
);
