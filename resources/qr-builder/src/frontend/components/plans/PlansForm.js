import React, { useEffect, useState } from "react";
import Form from "react-bootstrap/Form";
import { connect, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
    getFormattedMessage,
    getFormattedOptions,
    numValidate,
    decimalValidate,
    placeholderText,
} from "../../../shared/sharedMethod";
import { editPlan } from "../../../store/action/plansAction";
import ModelFooter from "../../../shared/components/modelFooter";
import ReactSelect from "../../../shared/select/reactSelect";
import HeaderTitle from "../../../components/header/HeaderTitle";
import { plansFrequencyOptions } from "../../../constants";
import moment from "moment";
import { fetchAllCurrency } from "../../../store/action/adminActions/currencyAction";

const PlansForm = (props) => {
    const { addPlansData, id, singlePlan, currencies, fetchAllCurrency } =
        props;
    const Dispatch = useDispatch();
    const navigate = useNavigate();

    useEffect(() => {
        fetchAllCurrency();
    }, []);

    const [planValue, setPlanValue] = useState({
        date: new Date(),
        name: singlePlan ? singlePlan[0]?.name : "",
        description: singlePlan ? singlePlan[0]?.description : "",
        order: singlePlan ? singlePlan[0]?.order : 0,
        trial_days: singlePlan ? singlePlan[0]?.trial_days : 0,
        price: singlePlan ? singlePlan[0]?.price : 0,
        status: singlePlan
            ? singlePlan[0]?.status === 0
                ? { value: 0, label: "Disabled" }
                : {
                      value: 1,
                      label: getFormattedMessage("globally.active.label"),
                  }
            : {
                  value: 0,
                  label: getFormattedMessage("globally.disabled.label"),
              },
        frequency: singlePlan
            ? singlePlan[0]?.type === 1
                ? {
                      value: 1,
                      label: getFormattedMessage(
                          "globally.heading.month.label"
                      ),
                  }
                : {
                      value: 2,
                      label: getFormattedMessage("globally.heading.year.label"),
                  }
            : {
                  value: 1,
                  label: getFormattedMessage("globally.heading.month.label"),
              },
        currency_id: singlePlan ? singlePlan[0]?.currency_id : "",
        qr_codes_imit: singlePlan ? singlePlan[0]?.qr_code_limit : 0,
        links_imit: singlePlan ? singlePlan[0]?.links_limit : 0,
        projects_imit: singlePlan ? singlePlan[0]?.projects_limit : 0,
        password_protection: 0,
        text: 1,
        url: 2,
        phone: 3,
        sms: 4,
        email: 5,
        whatsapp: 6,
        facetime: 7,
        location: 8,
        wifi: 9,
        event: 10,
        crypto: 11,
        vcard: 12,
        paypal: 13,
        qr_code_types: singlePlan
            ? singlePlan[0]?.qr_code_types?.split(",")
            : [],
        allow_tocreate_business_card: singlePlan
            ? singlePlan[0]?.allow_create_business_card
            : 0,
        bussineess_card_imit: singlePlan
            ? singlePlan[0]?.business_card_limit
            : 0,
    });

    const [errors, setErrors] = useState({
        name: "",
        description: "",
        currency_id: "",
        order: "",
        trial_days: "",
        status: "",
        frequency: "",
        qr_codes_imit: "",
        links_imit: "",
        projects_imit: "",
        password_protection: "",
        text: "",
        phone: "",
        email: "",
        facetime: "",
        wifi: "",
        crypto: "",
        paypal: "",
        url: "",
        sms: "",
        whatsapp: "",
        location: "",
        event: "",
        vcard: "",
        price: "",
    });

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (!planValue.name || planValue.name.trim()?.length === 0) {
            errorss.name = getFormattedMessage(
                "user.input.first-name.validate.label"
            );
        } else if (planValue.price <= 0) {
            errorss.price = getFormattedMessage(
                "please.enter.price.greater.than.1.error.message"
            );
        } else if (planValue.qr_codes_imit === "") {
            errorss.qr_codes_imit = getFormattedMessage(
                "please.enter.QR.Code.limit.error.message"
            );
        } else if (planValue.links_imit === "") {
            errorss.links_imit = getFormattedMessage(
                "please.enter.links.limit.error.message"
            );
        } else if (planValue.projects_imit === "") {
            errorss.projects_imit = getFormattedMessage(
                "please.enter.projects.limit.error.message"
            );
        } else if (
            !planValue.description ||
            planValue.description.trim()?.length === 0
        ) {
            errorss.description = getFormattedMessage(
                "Please.enter.description.validate.label"
            );
        } else if (
            planValue.allow_tocreate_business_card === 1 &&
            planValue.bussineess_card_imit === ""
        ) {
            errorss.bussineess_card_imit = getFormattedMessage(
                "please.enter.business-card.limit.error.message"
            );
        } else if (planValue.qr_code_types?.length <= 0) {
            errorss.qr_code_types = getFormattedMessage(
                "globally.qr.code.types.required.error"
            );
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };
    const disabled =
        singlePlan &&
        singlePlan[0]?.name === planValue.name &&
        singlePlan[0]?.description === planValue.description &&
        singlePlan[0]?.order === planValue.order &&
        singlePlan[0]?.trial_days === planValue.trial_days &&
        singlePlan[0]?.price === planValue.price &&
        singlePlan[0]?.currency_id.value === planValue.currency_id.value &&
        singlePlan[0]?.type === planValue.frequency.value &&
        singlePlan[0]?.qr_code_limit === planValue.qr_codes_imit &&
        singlePlan[0]?.links_limit === planValue.links_imit &&
        singlePlan[0]?.projects_limit === planValue.projects_imit &&
        singlePlan[0].qr_code_types.split(",").length ===
            planValue.qr_code_types.length &&
        singlePlan[0]?.allow_create_business_card ===
            planValue.allow_tocreate_business_card &&
        singlePlan[0]?.business_card_limit === planValue.bussineess_card_imit;

    const onChangeInput = (e) => {
        e.preventDefault();
        if (
            e.target.name === "qr_codes_imit" ||
            e.target.name === "links_imit" ||
            e.target.name === "projects_imit" ||
            e.target.name === "bussineess_card_imit"
        ) {
            if (e.target.value === "") {
                setPlanValue((inputs) => ({ ...inputs, [e.target.name]: "" }));
            } else {
                setPlanValue((inputs) => ({
                    ...inputs,
                    [e.target.name]: e.target.value,
                }));
            }
        } else {
            setPlanValue((inputs) => ({
                ...inputs,
                [e.target.name]: e.target.value,
            }));
        }
        setErrors("");
    };

    const plansFrequencyMethodOption = getFormattedOptions(
        plansFrequencyOptions
    );
    const plansFrequencyDefaultValue = plansFrequencyMethodOption.map(
        (option) => {
            return {
                value: option.id,
                label: option.name,
            };
        }
    );

    const currenciesType = currencies.map((d) => {
        return {
            id: d.id,
            name: d.attributes.symbol + " " + d.attributes.name,
        };
    });

    const currenciesOption = getFormattedOptions(currenciesType);
    const currenciesTypeDefaultValue = currenciesOption.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    useEffect(() => {
        if (currenciesTypeDefaultValue[0]) {
            if (!planValue.currency_id.value) {
                setPlanValue((planValue) => ({
                    ...planValue,
                    currency_id: currenciesTypeDefaultValue[0],
                }));
            }
        }
    }, [currenciesTypeDefaultValue]);

    const onFrequencyChange = (obj) => {
        setPlanValue((inputs) => ({ ...inputs, frequency: obj }));
        setErrors("");
    };

    const onCurrencyChange = (obj) => {
        setPlanValue((inputs) => ({ ...inputs, currency_id: obj }));
        setErrors("");
    };

    const onPasswordProtection = (e) => {
        const check = e.target.checked;
        if (check) {
            setPlanValue((inputs) => ({ ...inputs, password_protection: 1 }));
        } else {
            setPlanValue((inputs) => ({ ...inputs, password_protection: 0 }));
        }
        setErrors("");
    };

    const onAllowtocreateBussinessCardChange = (e) => {
        const check = e.target.checked;
        if (check) {
            setPlanValue((inputs) => ({
                ...inputs,
                allow_tocreate_business_card: 1,
            }));
        } else {
            setPlanValue((inputs) => ({
                ...inputs,
                allow_tocreate_business_card: 0,
            }));
        }
        setErrors("");
    };

    const onChecked = (e) => {
        const check = e.target.checked;
        if (check) {
            if (e.target.name === "all_types") {
                setPlanValue((inputs) => ({
                    ...inputs,
                    qr_code_types: [
                        "1",
                        "2",
                        "3",
                        "4",
                        "5",
                        "6",
                        "7",
                        "8",
                        "9",
                        "10",
                        "11",
                        "12",
                        "13",
                    ],
                }));
            } else {
                setPlanValue((inputs) => ({
                    ...inputs,
                    qr_code_types: [...inputs.qr_code_types, e.target.value],
                }));
            }
        } else {
            if (e.target.name === "all_types") {
                setPlanValue((inputs) => ({ ...inputs, qr_code_types: [] }));
            } else {
                planValue.qr_code_types.splice(
                    planValue.qr_code_types.indexOf(e.target.value),
                    1
                );
                setPlanValue((inputs) => ({
                    ...inputs,
                    qr_code_types: planValue.qr_code_types,
                }));
            }
        }
        setErrors("");
    };

    const prepareFormData = (prepareData) => {
        const formValue = {
            price: prepareData.price,
            currency_id: prepareData.currency_id.value,
            date: moment(prepareData.date).toDate(),
            name: prepareData.name,
            order: prepareData.order,
            trial_days: prepareData.trial_days,
            type: prepareData.frequency.value,
            status: prepareData.status.value
                ? prepareData.status.value
                : prepareData.status.value,
            qr_code_limit: prepareData.qr_codes_imit,
            links_limit: prepareData.links_imit,
            projects_limit: prepareData.projects_imit,
            // password_protection_enabled: prepareData.password_protection,
            qr_code_types: prepareData.qr_code_types.toString(),
            allow_create_business_card:
                prepareData.allow_tocreate_business_card,
            business_card_limit: prepareData.bussineess_card_imit,
            description: prepareData.description,
        };
        return formValue;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation();
        if (singlePlan && valid) {
            Dispatch(editPlan(id, prepareFormData(planValue), navigate));
        } else {
            if (valid) {
                setPlanValue(planValue);
                addPlansData(prepareFormData(planValue));
            }
        }
    };

    return (
        <div className="card">
            <div className="card-body">
                <Form>
                    <div className="row my-5">
                        <div className="col-md-6 mb-3">
                            <label
                                htmlFor="exampleInputEmail1"
                                className="form-label"
                            >
                                {getFormattedMessage(
                                    "globally.plan.name.title"
                                )}
                                :<span className="required" />
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={planValue.name || ""}
                                className="form-control"
                                autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText(
                                    "globally.plan.name.title"
                                )}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["name"] ? errors["name"] : null}
                            </span>
                        </div>
                        <div className="col-md-6  mb-3">
                            <ReactSelect
                                title={getFormattedMessage(
                                    "globally.heading.currency.title"
                                )}
                                name="currency"
                                value={planValue.currency_id}
                                errors={errors["currency_id"]}
                                defaultValue={currenciesTypeDefaultValue[0]}
                                multiLanguageOption={currenciesOption}
                                onChange={onCurrencyChange}
                            />
                        </div>

                        <div className="col-md-6 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.plan.price.title"
                                )}
                                :
                            </label>
                            <span className="required" />
                            <input
                                type="text"
                                name="price"
                                value={planValue.price || ""}
                                className="form-control"
                                pattern="[0-9]"
                                min={1}
                                placeholder={placeholderText(
                                    "globally.plan.price.title"
                                )}
                                onKeyPress={(event) => decimalValidate(event)}
                                onChange={(e) => onChangeInput(e)}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["price"] ? errors["price"] : null}
                            </span>
                        </div>
                        <div className="col-md-6  mb-3">
                            <ReactSelect
                                title={getFormattedMessage(
                                    "globally.heading.frequency.title"
                                )}
                                name="frequency"
                                value={planValue.frequency}
                                errors={errors["frequency"]}
                                defaultValue={plansFrequencyDefaultValue[0]}
                                multiLanguageOption={plansFrequencyMethodOption}
                                onChange={onFrequencyChange}
                            />
                        </div>
                        {/* <div className='col-md-6 mb-3'>
                            <label className='form-label'>
                                {getFormattedMessage("globally.input.order.lable")}:
                            </label>
                            <span className='required' />
                            <input autoComplete="off" type='number' name='order' className='form-control'
                                onChange={(e) => onChangeInput(e)}
                                pattern='[0-9]' min={0}
                                placeholder={placeholderText("user.input.order.placeholder.label")}
                                onKeyPress={(event) => numValidate(event)}
                                value={planValue.order || ""} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['order'] ? errors['order'] : null}</span>
                        </div> */}
                        <div className="col-md-6 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.trial-days.lable"
                                )}
                                :
                            </label>
                            <input
                                type="text"
                                name="trial_days"
                                className="form-control"
                                onChange={(e) => onChangeInput(e)}
                                pattern="[0-9]"
                                min={0}
                                placeholder={placeholderText(
                                    "user.input.trial-days.placeholder.label"
                                )}
                                onKeyPress={(event) => numValidate(event)}
                                value={planValue.trial_days}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["trial_days"]
                                    ? errors["trial_days"]
                                    : null}
                            </span>
                        </div>
                        <div className="col-md-6 mb-3 position-relative">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.description.lable"
                                )}
                                :{" "}
                            </label>
                            <span className="required" />
                            <textarea
                                rows={3}
                                type="text"
                                maxLength="200"
                                name="description"
                                value={planValue.description}
                                placeholder={placeholderText(
                                    "enter-description.placeholder"
                                )}
                                className="form-control"
                                autoComplete="off"
                                onChange={(e) => onChangeInput(e)}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["description"]
                                    ? errors["description"]
                                    : null}
                            </span>
                        </div>
                    </div>
                    <div className="row mt-5 pt-5">
                        <HeaderTitle
                            title={getFormattedMessage("plans.settings")}
                        />
                        <div className="col-md-4 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.qr-codes-imit.lable"
                                )}
                                :
                            </label>
                            <span className="required" />
                            <input
                                type="text"
                                name="qr_codes_imit"
                                className="form-control"
                                onChange={(e) => onChangeInput(e)}
                                pattern="[0-9]"
                                min={0}
                                placeholder={placeholderText(
                                    "user.input.qr-code-limit.placeholder.label"
                                )}
                                onKeyPress={(event) => numValidate(event)}
                                value={planValue.qr_codes_imit}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["qr_codes_imit"]
                                    ? errors["qr_codes_imit"]
                                    : null}
                            </span>
                        </div>
                        <div className="col-md-4 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.links-imit.lable"
                                )}
                                :
                            </label>
                            <span className="required" />
                            <input
                                type="text"
                                name="links_imit"
                                className="form-control"
                                onChange={(e) => onChangeInput(e)}
                                pattern="[0-9]"
                                min={0}
                                placeholder={placeholderText(
                                    "user.input.link-limit.placeholder.label"
                                )}
                                onKeyPress={(event) => numValidate(event)}
                                value={planValue.links_imit}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["links_imit"]
                                    ? errors["links_imit"]
                                    : null}
                            </span>
                        </div>
                        <div className="col-md-4 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.projects-imit.lable"
                                )}
                                :
                            </label>
                            <span className="required" />
                            <input
                                type="text"
                                name="projects_imit"
                                className="form-control"
                                onChange={(e) => onChangeInput(e)}
                                pattern="[0-9]"
                                min={0}
                                placeholder={placeholderText(
                                    "user.input.project-limit.placeholder.label"
                                )}
                                onKeyPress={(event) => numValidate(event)}
                                value={planValue.projects_imit}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["projects_imit"]
                                    ? errors["projects_imit"]
                                    : null}
                            </span>
                        </div>
                        <div className="col-md-12">
                            <label className="form-check form-switch form-switch-sm cursor-pointer d-inline-block w-auto">
                                <input
                                    autoComplete="off"
                                    name="allow_tocreate_business_card"
                                    data-id="704"
                                    className="form-check-input admin-status cursor-pointer"
                                    checked={
                                        planValue.allow_tocreate_business_card ===
                                        1
                                            ? true
                                            : false
                                    }
                                    type="checkbox"
                                    value={
                                        planValue.allow_tocreate_business_card
                                    }
                                    onChange={(e) =>
                                        onAllowtocreateBussinessCardChange(e)
                                    }
                                />
                                <>
                                    {getFormattedMessage(
                                        "globally.allow-to-create-business-card.title"
                                    )}
                                    {/* <figure className='figure-caption'>
                                            {getFormattedMessage("enable.access.to.set.a.password.protection.for.links")}
                                        </figure> */}
                                </>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </label>
                        </div>
                        {/* <div className="col-md-6 d-flex align-items-center mt-4">
                                <label className="form-check form-switch form-switch-sm cursor-pointer">
                                    <input autoComplete="off" name="password_protection" data-id="704" className="form-check-input admin-status cursor-pointer" checked={planValue.password_protection === 1 ? true : false} type="checkbox" value={planValue.password_protection}
                                        onChange={(e) => onPasswordProtection(e)} />
                                    <>
                                        {getFormattedMessage("globally.input.password-protection.lable")}
                                        <figure className='figure-caption'>
                                            {getFormattedMessage("enable.access.to.set.a.password.protection.for.links")}
                                        </figure>
                                    </>
                                    <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                </label>
                            </div> */}
                        {planValue.allow_tocreate_business_card === 1 && (
                            <div className="mt-3 col-md-4 col-12">
                                <label className="form-label">
                                    {getFormattedMessage(
                                        "globally.input.business-card-imit.lable"
                                    )}
                                    :
                                </label>
                                <span className="required" />
                                <input
                                    type="text"
                                    name="bussineess_card_imit"
                                    className="form-control"
                                    onChange={(e) => onChangeInput(e)}
                                    pattern="[0-9]"
                                    min={0}
                                    placeholder={placeholderText(
                                        "user.input.project-limit.placeholder.label"
                                    )}
                                    onKeyPress={(event) => numValidate(event)}
                                    value={planValue.bussineess_card_imit}
                                />
                                <span className="text-danger d-block fw-400 fs-small mt-2">
                                    {errors["bussineess_card_imit"]
                                        ? errors["bussineess_card_imit"]
                                        : null}
                                </span>
                            </div>
                        )}
                    </div>
                    <div className="row mt-5 pt-5">
                        <div className="row align-items-center">
                            <div className="col-8">
                                {/*<HeaderTitle title={getFormattedMessage('plans.qr.codes.types')} required/>*/}
                                <span className="mb-0 h1">
                                    {getFormattedMessage(
                                        "plans.qr.codes.types"
                                    )}
                                </span>
                                <span className={"required"}></span>
                                {/* <figure className='figure-caption fs-4'>
                                    {getFormattedMessage("choose.which.QR.Codes.types.you.want.to.give.access.to")}<span className='required' />
                                </figure> */}
                            </div>
                            <div className="d-flex align-items-center col-2">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="all_types"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        value={planValue.all_types}
                                        checked={[
                                            "1",
                                            "2",
                                            "3",
                                            "4",
                                            "5",
                                            "6",
                                            "7",
                                            "8",
                                            "9",
                                            "10",
                                            "11",
                                            "12",
                                            "13",
                                        ].every((item) => {
                                            return planValue.qr_code_types.includes(
                                                item
                                            );
                                        })}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "plan.input.all-type.title"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                        </div>
                        <div className="row">
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="text"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.text.toString()
                                        )}
                                        value={planValue.text}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.text.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="phone"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.phone.toString()
                                        )}
                                        value={planValue.phone}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.phone.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="email"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.email.toString()
                                        )}
                                        value={planValue.email}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.email.label"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="facetime"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.facetime.toString()
                                        )}
                                        value={planValue.facetime}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.facetime.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="wifi"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.wifi.toString()
                                        )}
                                        value={planValue.wifi}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.wifi.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="crypto"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.crypto.toString()
                                        )}
                                        value={planValue.crypto}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.crypto.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="paypal"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.paypal.toString()
                                        )}
                                        value={planValue.paypal}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.paypal.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="url"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.url.toString()
                                        )}
                                        value={planValue.url}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.url.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="sms"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.sms.toString()
                                        )}
                                        value={planValue.sms}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.sms.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="whatsapp"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.whatsapp.toString()
                                        )}
                                        value={planValue.whatsapp}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.whatsapp.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="location"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.location.toString()
                                        )}
                                        value={planValue.location}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.location.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="event"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.event.toString()
                                        )}
                                        value={planValue.event}
                                        onChange={(event) => onChecked(event)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.event.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                            <div className="col-md-4 d-flex align-items-center mt-4">
                                <label className="form-label mb-0 cursor-pointer d-inline-block">
                                    <input
                                        autoComplete="off"
                                        name="vcard"
                                        data-id="704"
                                        className="form-check-input admin-status cursor-pointer"
                                        type="checkbox"
                                        checked={planValue.qr_code_types?.includes(
                                            planValue.vcard.toString()
                                        )}
                                        value={planValue.vcard}
                                        onChange={(e) => onChecked(e)}
                                    />
                                    {getFormattedMessage(
                                        "globally.input.vcard.lable"
                                    )}
                                </label>
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </div>
                        </div>
                        <span className="text-danger d-block fw-400 fs-small mt-2">
                            {errors["qr_code_types"]
                                ? errors["qr_code_types"]
                                : null}
                        </span>
                    </div>
                    <ModelFooter
                        onEditRecord={singlePlan}
                        editDisabled={disabled}
                        addDisabled={!planValue.name}
                        link="/app/admin/plans"
                        onSubmit={onSubmit}
                    />
                </Form>
            </div>
        </div>
    );
};

const mapStateToProps = (state) => {
    const { currencies } = state;
    return { currencies };
};

export default connect(mapStateToProps, { fetchAllCurrency })(PlansForm);
