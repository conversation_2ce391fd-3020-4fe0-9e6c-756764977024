import React from 'react';
import { connect, useSelector } from 'react-redux';
import { addPlan } from '../../../store/action/plansAction';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { Filters } from '../../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import PlansForm from './PlansForm';
import { Helmet } from 'react-helmet';

const CreatePlans = (props) => {
    const { addPlan } = props;
    const navigate = useNavigate();
    const {frontSettings} = useSelector(state => state)
    const addPlansData = (formValue) => {
        addPlan(formValue, navigate, Filters.OBJ);
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText('plans.create-plan.title') + ' | ' + frontSettings?.title}/>
            <HeaderTitle title={getFormattedMessage('plans.create-plan.title')} to='/app/admin/plans' />
            <PlansForm addPlansData={addPlansData} />
        </MasterLayout>
    );
}

export default connect(null, { addPlan })(CreatePlans);
