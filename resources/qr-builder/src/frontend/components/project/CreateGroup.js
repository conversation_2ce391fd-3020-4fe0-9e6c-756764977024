import React from 'react';
import { connect, useSelector } from 'react-redux';
import AdminGroupForm from './AdminGroupForm';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import { Helmet } from 'react-helmet';
import { adminAddGroup } from '../../../store/action/adminActions/projectsAction';

const CreateGroup = ( props ) => {
    const { adminAddGroup } = props;
    const navigate = useNavigate();
    const { frontSettings } = useSelector( state => state )
    const addGroupData = ( formValue ) => {
        adminAddGroup( formValue, navigate );
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText( 'project.create.title' ) + ' | ' + frontSettings?.title} />
            <HeaderTitle title={getFormattedMessage( 'project.create.title' )} to='/app/admin/collections' />
            <AdminGroupForm addGroupData={addGroupData} />
        </MasterLayout>
    );
}

export default connect( null, { adminAddGroup } )( CreateGroup );
