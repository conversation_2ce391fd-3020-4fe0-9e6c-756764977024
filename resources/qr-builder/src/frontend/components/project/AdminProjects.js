import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { fetchProjects } from '../../../store/action/adminActions/projectsAction';
import DeleteAdminproject from './DeleteAdminproject';

const AdminProjects = ( props ) => {
    const { project_admin, fetchProjects, totalRecord, isLoading, allConfigData } = props;
    const [ deleteModel, setDeleteModel ] = useState( false );
    const [ isDelete, setIsDelete ] = useState( null );

    const navigate = useNavigate()

    const onClickDeleteModel = ( isDelete = null ) => {
        setDeleteModel( !deleteModel );
        setIsDelete( isDelete );
    };

    const itemsValue = project_admin.length >= 0 && project_admin.map( project => ( {
        date: getFormattedDate( project.attributes.created_at, "d-m-y" ),
        time: moment( project.attributes.created_at ).format( 'LT' ),
        name: project.attributes.name,
        color: project.attributes.color,
        user_name: project.attributes.user_name,
        user_email: project.attributes.user_email,
        user_id: project.attributes.user_id,
        user_image: project.attributes.user_image,
        link_count: project.attributes.link_count,
        qr_code_count: project.attributes.qr_code_count,
        id: project.id
    } ) );

    const onChange = ( filter ) => {
        fetchProjects( filter, true );
    };

    const onCLickQrCodeRedirect = ( id ) => {
        navigate( "/app/admin/qr-codes" )
    }

    const onCLickLinkRedirect = ( id ) => {
        navigate( "/app/admin/minify-link" )
    }

    const columns = [
        {
            name: getFormattedMessage( 'users.table.user-name.column.title' ),
            selector: row => row.name,
            sortField: 'name',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`}>
                            {row.user_image ?
                                <img src={row.user_image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName( row.user_name )}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`} className='text-decoration-none'>{row.user_name}</Link>
                        <div>{row.user_email}</div>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'project-name.title' ),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        <div>{row.name}</div>
                    </div>
                </div>
            }
        },
        // {
        //     name: getFormattedMessage('globally.input.color.lable'),
        //     selector: row => row.color,
        //     sortField: 'color',
        //     sortable: true,
        //     cell: row => {
        //         return <span className='badge' style={{ background: row.color, "color": row.color === "#ffffff" ? "#000000" : "#ffffff" }}>
        //             {row.color.substr(1)}
        //         </span>
        //     }
        // },
        {
            name: getFormattedMessage( "globally.qrcode.title" ),
            selector: row => row.qr_code_count,
            sortable: false,
            cell: row => {
                return <div className='px-3'><span className='badge bg-light-success cursor-pointer' onClick={() => onCLickQrCodeRedirect( row.id )}>
                    {row.qr_code_count}
                </span></div>
            }
        },
        {
            name: getFormattedMessage( "globally.links.title" ),
            selector: row => row.link_count,
            sortable: false,
            cell: row => {
                return <div className='px-7'><span className='badge bg-light-warning cursor-pointer' onClick={() => onCLickLinkRedirect( row.id )}>
                    {row.link_count}
                </span></div>
            }
        },
        {
            name: getFormattedMessage( 'globally.react-table.column.created-date.label' ),
            selector: row => row.date,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div className='mb-1'>{row.time}</div>
                        <div>{row.date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage( 'react-data-table.action.column.label' ),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton item={row} isEditMode={false}
                onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( 'projects.title' )} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                totalRows={totalRecord} isLoading={isLoading} ButtonValue={getFormattedMessage( 'project.create.title' )} to='#/app/admin/collections/create' />
            <DeleteAdminproject onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )
};

const mapStateToProps = ( state ) => {
    const { project_admin, totalRecord, isLoading, allConfigData } = state;
    return { project_admin, totalRecord, isLoading, allConfigData }
};
export default connect( mapStateToProps, { fetchProjects } )( AdminProjects );
