import React, { useEffect, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactColorPicker from '../../../shared/colorpocker/ReactColorPicker';
import UserDropdown from "./UserDropdown";


const AdminGroupForm = ( props ) => {
    const { addGroupData, id, singleGroup } = props;

    const [ groupValue, setGroupValue ] = useState( {
        name: '',
        tenant_id: '',
        color: ''
    } );
    const [ errors, setErrors ] = useState( {
        tenant_id: '',
        name: '',
        color: ''
    } );

    const disabled = singleGroup && groupValue.name === singleGroup[ 0 ].name

    useEffect( () => {
        if ( singleGroup?.length ) {
            setGroupValue( {
                name: singleGroup ? singleGroup[ 0 ].name : '',
                color: singleGroup ? singleGroup[ 0 ].color : ''
            } )
        }
    }, [ singleGroup ] )

    const onUserChange = ( obj ) => {
        setGroupValue( inputs => ( { ...inputs, tenant_id: obj } ) )
        setErrors( '' )
    };

    const handleCallback = ( color ) => {
        setGroupValue( previousState => {
            return { ...previousState, color: color.hex }
        } );
    };
    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if ( !groupValue[ 'name' ] || groupValue[ 'name' ]?.trim()?.length === 0 ) {
            errorss[ 'name' ] = getFormattedMessage( 'user.input.first-name.validate.label' );
        } else if ( !groupValue[ 'tenant_id' ] ) {
            errorss[ 'tenant_id' ] = getFormattedMessage( "user.input.validate.label" )
        } else {
            isValid = true;
        }
        setErrors( errorss );
        return isValid;
    };

    const onChangeInput = ( e ) => {
        e.preventDefault();
        setGroupValue( inputs => ( { ...inputs, [ e.target.name ]: e.target.value } ) )
        setErrors( '' );
    };

    const prepareFormData = ( data, dataURL ) => {
        const formData = new FormData();
        formData.append( 'name', data.name );
        formData.append( 'tenant_id', data.tenant_id.value )
        formData.append( 'color', data.color );
        return formData;
    };


    const onSubmit = ( event ) => {
        event.preventDefault();
        const valid = handleValidation();
        if ( valid ) {
            setGroupValue( groupValue );
            addGroupData( prepareFormData( groupValue ) );
        }
    };

    return (
        <div className='card'>
            <div className='card-body'>
                <Form>
                    <div className='row'>
                        <div className='col-md-12 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage( "users.table.user.column.title" )}:<span className="required" />
                            </label>
                            <UserDropdown onUserChange={onUserChange} disabledKey={"Type"} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors[ 'tenant_id' ] ? errors[ 'tenant_id' ] : null}
                            </span>
                        </div>
                        <div className='col-md-12 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage( "project-name.title" )}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={groupValue.name || ""}
                                className='form-control' autoFocus={true}
                                onChange={( e ) => onChangeInput( e )}
                                placeholder={placeholderText( "project-name.title" )}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors[ 'name' ] ? errors[ 'name' ] : null}
                            </span>
                        </div>
                        <div className='col-md-1 mb-3'>
                            <label className='form-label'>
                                {getFormattedMessage( "globally.input.color.lable" )}:
                            </label>
                            <div style={{ "width": "40px" }}>
                                <ReactColorPicker onChangeColor={handleCallback} className={'40px'} selectedColor={groupValue.color} />
                            </div>
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors[ 'email' ] ? errors[ 'email' ] : null}</span>
                        </div>
                        <ModelFooter onEditRecord={singleGroup} onSubmit={onSubmit} editDisabled={disabled}
                            link='/app/admin/collections' addDisabled={!groupValue.name} />
                    </div>
                </Form>
            </div>
        </div>
    )
};

const mapStateToProps = ( state ) => {
    const { roles } = state;
    return { roles }
};

export default connect( mapStateToProps, {} )( AdminGroupForm );

