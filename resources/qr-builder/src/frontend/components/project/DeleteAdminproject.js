import React from 'react';
import { connect } from 'react-redux';
import { deleteAdminProject } from '../../../store/action/projectAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';

const DeleteAdminProject = (props) => {
    const { deleteAdminProject, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deleteAdminProject(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage('project.title')} />}
        </div>
    )
};

export default connect(null, { deleteAdminProject })(DeleteAdminProject);
