import React, { useEffect, useState, useRef } from "react";
import Form from "react-bootstrap/Form";
import { connect, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
    getFormattedMessage,
    placeholderText,
    slugify,
} from "../../../shared/sharedMethod";
import ModelFooter from "../../../shared/components/modelFooter";
import { fetchAllCurrency } from "../../../store/action/adminActions/currencyAction";
import { editPage } from "../../../store/action/adminActions/pagesAction";
import ReactQuill from "react-quill";
import { escape, unescape } from "lodash";

const PagesForm = (props) => {
    const { addPageData, id, singlePage } = props;
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const editorRef = useRef(null);
    const [imagePreviewUrl, setImagePreviewUrl] = useState(null);
    const [pageValue, setPageValue] = useState({
        title: "",
        meta_title: "",
        meta_description: "",
        visibility: false,
        description: "",
    });

    useEffect(() => {
        if (singlePage?.length > 0) {
            setPageValue({
                title: singlePage[0]?.title,
                meta_title: singlePage[0]?.meta_title,
                meta_description: singlePage[0]?.meta_description,
                visibility: singlePage[0]?.visibility,
                description: unescape(singlePage[0]?.description),
            });
        }
    }, [singlePage]);

    const [errors, setErrors] = useState({});

    const formats = [
        "header",
        "font",
        "italic",
        "underline",
        "strike",
        "blockquote",
        "list",
        "bullet",
        "indent",
        "align",
        "link",
        "background",
        "color",
        "image",
        "bold",
        "clean",
    ];

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        const description =
            editorRef?.current?.getEditor()?.getText()?.trim().length === 0;
        if (!pageValue.title || pageValue.title.trim().length === 0) {
            errorss.title = getFormattedMessage(
                "globally.input.title.validate.label"
            );
        } else if (
            !pageValue.meta_title ||
            pageValue.meta_title.trim().length === 0
        ) {
            errorss.meta_title = getFormattedMessage(
                "globally.input.meta-title.validate.label"
            );
        } else if (
            !pageValue.meta_description ||
            pageValue.meta_description.trim().length === 0
        ) {
            errorss.meta_description = getFormattedMessage(
                "globally.input.meta-description.validate.label"
            );
        } else if (description) {
            errorss.description = getFormattedMessage(
                "Please.enter.description.validate.label"
            );
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const disabled =
        singlePage &&
        singlePage[0]?.meta_description === pageValue.meta_description &&
        singlePage[0]?.meta_title === pageValue.meta_title &&
        singlePage[0]?.title === pageValue.title &&
        pageValue?.description === unescape(singlePage[0]?.description) &&
        singlePage[0]?.visibility === pageValue?.visibility;

    const onChangeInput = (e) => {
        e.preventDefault();
        setPageValue((inputs) => ({
            ...inputs,
            [e.target.name]: e.target.value,
        }));
        setErrors("");
    };

    const onChecked = (e) => {
        const check = e.target.checked;
        if (check) {
            setPageValue((inputs) => ({ ...inputs, [e.target.name]: true }));
        } else {
            setPageValue((inputs) => ({ ...inputs, [e.target.name]: false }));
        }
        setErrors("");
    };

    const handleImageChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === "image/jpeg" || file.type === "image/png") {
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setImagePreviewUrl(fileReader.result);
                    editorRef.current &&
                        editorRef.current.setContent(
                            editorRef.current.getContent() +
                                `<img src="${fileReader.result}" alt="uploaded image" />`
                        );
                };
                fileReader.readAsDataURL(file);
                setErrors("");
            }
        }
    };

    const prepareFormData = (prepareData) => {
        const formValue = {
            title: prepareData.title,
            slug: slugify(prepareData.title),
            meta_title: prepareData.meta_title,
            meta_description: prepareData.meta_description,
            description: escape(prepareData.description),
            visibility: prepareData.visibility === true ? 1 : 0,
        };
        return formValue;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation();

        if (singlePage && valid) {
            dispatch(editPage(id, prepareFormData(pageValue), navigate));
        } else {
            if (valid) {
                setPageValue(pageValue);
                addPageData(prepareFormData(pageValue));
            }
        }
    };

    const handleChangeDescription = (content) => {
        setPageValue((inputs) => ({ ...inputs, description: content }));
    };

    return (
        <div className="card">
            <div className="card-body">
                <Form>
                    <div className="row my-5">
                        <div className="col-md-12 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.title.label"
                                )}
                                :
                            </label>
                            <span className="required" />
                            <input
                                type="text"
                                name="title"
                                value={pageValue.title || ""}
                                className="form-control"
                                placeholder={placeholderText(
                                    "globally.input.title.label"
                                )}
                                onChange={(e) => onChangeInput(e)}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["title"] ? errors["title"] : null}
                            </span>
                        </div>
                        <div className="col-md-12 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.slug.title"
                                )}
                                :<span className="required" />
                            </label>
                            <input
                                type="text"
                                name="slug"
                                className="form-control"
                                onChange={(e) => onChangeInput(e)}
                                disabled
                                placeholder={placeholderText(
                                    "globally.input.slug.title"
                                )}
                                value={slugify(pageValue.title)}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["slug"] ? errors["slug"] : null}
                            </span>
                        </div>
                        <div className="col-md-6 mb-3">
                            <label
                                htmlFor="exampleInputEmail1"
                                className="form-label"
                            >
                                {getFormattedMessage(
                                    "globally.input.meta-title.title"
                                )}
                                :<span className="required" />
                            </label>
                            <input
                                type="text"
                                name="meta_title"
                                value={pageValue.meta_title || ""}
                                className="form-control"
                                autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText(
                                    "globally.input.meta-title.title"
                                )}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["meta_title"]
                                    ? errors["meta_title"]
                                    : null}
                            </span>
                        </div>
                        <div className="col-md-6 mb-3">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.meta_description.title"
                                )}
                                :
                            </label>
                            <span className="required" />
                            <input
                                type="text"
                                name="meta_description"
                                value={pageValue.meta_description || ""}
                                className="form-control"
                                pattern="[0-9]"
                                min={1}
                                placeholder={placeholderText(
                                    "globally.input.meta_description.title"
                                )}
                                onChange={(e) => onChangeInput(e)}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["meta_description"]
                                    ? errors["meta_description"]
                                    : null}
                            </span>
                        </div>

                        <div className="col-md-12 row align-items-center mb-5">
                            <label className="form-label col-xl-1 col-lg-2 col-md-2 col-sm-3 col-6 mb-0 page-view-switch">
                                {getFormattedMessage(
                                    "globally.input.visibility.title"
                                )}
                                :
                            </label>
                            <label className="form-check form-switch form-switch-sm cursor-pointer col-1 mb-0">
                                <input
                                    autoComplete="off"
                                    name="visibility"
                                    data-id="704"
                                    className="form-check-input admin-status cursor-pointer"
                                    type="checkbox"
                                    checked={pageValue.visibility}
                                    value={pageValue.visibility}
                                    onChange={(e) => onChecked(e)}
                                />
                                <span
                                    className="switch-slider"
                                    data-checked="✓"
                                    data-unchecked="✕"
                                ></span>
                            </label>
                        </div>
                        {/* <div className='mb-4'>
                            <ImagePicker
                                isCreate={isCreate} avtarName={"No Image Selected"}
                                imageTitle={"header.profile-menu.profile.label"} imagePreviewUrl={imagePreviewUrl} handleImageChange={handleImageChanges}
                            />
                        </div> */}

                        <div className="col-12 mb-lg-0 mb-5 terms-box">
                            <label className="form-label">
                                {getFormattedMessage(
                                    "globally.input.description.lable"
                                )}
                                :<span className="required" />
                            </label>
                            <ReactQuill
                                theme="snow"
                                ref={editorRef}
                                value={pageValue.description}
                                onChange={handleChangeDescription}
                                formats={formats}
                                modules={{
                                    toolbar: {
                                        imageResize: {
                                            displaySize: true,
                                        },
                                        container: [
                                            [{ font: [] }],
                                            [
                                                {
                                                    header: [
                                                        1,
                                                        2,
                                                        3,
                                                        4,
                                                        5,
                                                        6,
                                                        false,
                                                    ],
                                                },
                                            ],
                                            [
                                                "bold",
                                                "italic",
                                                "underline",
                                                "strike",
                                            ],
                                            [{ color: [] }, { background: [] }],
                                            ["blockquote"],
                                            [
                                                { list: "ordered" },
                                                { list: "bullet" },
                                            ],
                                            [
                                                { indent: "-1" },
                                                { indent: "+1" },
                                                { align: [] },
                                            ],
                                            ["link", "image"],
                                            ["clean"],
                                        ],
                                    },
                                }}
                            />
                            <span className="text-danger d-block fw-400 fs-small mt-2">
                                {errors["description"]
                                    ? errors["description"]
                                    : null}
                            </span>
                        </div>

                        {/* <div className='col-md-6 d-flex justify-content-between align-items-center mb-3'>
                        <label className='form-label'>
                            {getFormattedMessage("globally.input.location.lable")}:<span className='required' />
                        </label>
                        <label className="form-label">
                            <input type="radio" name="radio" className='form-check-input' />
                            {getFormattedMessage("globally.input.main-menu.title")}
                        </label>

                        <label className="form-label">
                            <input type="radio" name="radio" className='form-check-input' />
                            {getFormattedMessage("globally.input.dont-add-menu.title")}
                        </label>
                    </div> */}

                        {/* <div className="col-md-12 row align-items-center mb-5">
                        <label className='form-label col-2 mb-0'>
                            {getFormattedMessage("globally.input.show-breadcrumb.title")}:
                        </label>
                        <label className="form-check form-switch form-switch-sm cursor-pointer col-1 mb-0">
                            <input autoComplete="off" name="text" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox" checked={pageValue.qr_code_types?.includes(pageValue.text.toString())} value={pageValue.text}
                                onChange={(e) => onChecked(e)} />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                    <div className="col-md-12 row align-items-center mb-5">
                        <label className='form-label col-2 mb-0'>
                            {getFormattedMessage("globally.input.show-right.title")}:
                        </label>
                        <label className="form-check form-switch form-switch-sm cursor-pointer col-1 mb-0">
                            <input autoComplete="off" name="text" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox" checked={pageValue.qr_code_types?.includes(pageValue.text.toString())} value={pageValue.text}
                                onChange={(e) => onChecked(e)} />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                    <div className="col-md-12 row align-items-center mb-5">
                        <label className='form-label col-2 mb-0'>
                            {getFormattedMessage("globally.input.show-title.title")}:
                        </label>
                        <label className="form-check form-switch form-switch-sm cursor-pointer col-1 mb-0">
                            <input autoComplete="off" name="text" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox" checked={pageValue.qr_code_types?.includes(pageValue.text.toString())} value={pageValue.text}
                                onChange={(e) => onChecked(e)} />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                    <div className="col-md-12 row align-items-center mb-3">
                        <label className='form-label col-2 mb-0'>
                            {getFormattedMessage("globally.input.show-only-to-registered-users.title")}:
                        </label>
                        <label className="form-check form-switch form-switch-sm cursor-pointer col-1 mb-0">
                            <input autoComplete="off" name="text" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox" checked={pageValue.qr_code_types?.includes(pageValue.text.toString())} value={pageValue.text}
                                onChange={(e) => onChecked(e)} />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div> */}
                    </div>
                    <ModelFooter
                        onEditRecord={singlePage}
                        addDisabled={!pageValue.title}
                        editDisabled={disabled}
                        link="/app/admin/custom-pages"
                        onSubmit={onSubmit}
                    />
                </Form>
            </div>
        </div>
    );
};

const mapStateToProps = (state) => {
    const {} = state;
    return {};
};

export default connect(mapStateToProps, { fetchAllCurrency })(PagesForm);
