import React from 'react';
import { connect } from 'react-redux';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { deletePage } from '../../../store/action/adminActions/pagesAction';

const DeletePage = (props) => {
    const { deletePage, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deletePage(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage("settings.select.page.label")} />}
        </div>
    )

};

export default connect(null, { deletePage })(DeletePage);
