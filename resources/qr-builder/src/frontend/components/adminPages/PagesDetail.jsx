import React, { useEffect } from "react";
import { connect, useSelector } from "react-redux";
import { Card, Table } from "react-bootstrap";
import MasterLayout from "../MasterLayout";
import TabTitle from "../../../shared/tab-title/TabTitle";
import {
    getFormattedDate,
    placeholderText,
} from "../../../shared/sharedMethod";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import { useParams } from "react-router-dom";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import HeaderTitle from "../../../components/header/HeaderTitle";
import RoundLoader from "../../../shared/components/loaders/RoundLoader";
import { useDispatch } from "react-redux";
import { fetchPage } from "../../../store/action/adminActions/pagesAction";
import moment from "moment";

const PagesDetail = (props) => {
    const { isLoading } = props;
    const { id } = useParams();
    const { pages } = useSelector((state) => state);
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(fetchPage(id));
    }, []);

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("page.detail.title")} />
            {
                <>
                    <HeaderTitle
                        title={getFormattedMessage("page.detail.title")}
                        to="/app/admin/custom-pages"
                    />
                    <div className="pt-5">
                        <Card>
                            {/* <Card.Header as='h5'>{getFormattedMessage('plan.details.title')}</Card.Header> */}
                            <Card.Body className="">
                                {isLoading ? (
                                    <RoundLoader />
                                ) : (
                                    <Table responsive>
                                        <tbody>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.input.title.label"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {
                                                        pages[0]?.attributes
                                                            ?.title
                                                    }
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.input.slug.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {pages[0]?.attributes?.slug}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.input.meta-title.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {
                                                        pages[0]?.attributes
                                                            ?.meta_title
                                                    }
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.input.meta_description.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {
                                                        pages[0]?.attributes
                                                            ?.meta_description
                                                    }
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.input.visibility.title"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {pages[0]?.attributes
                                                        ?.visibility === 1 ? (
                                                        <span className="badge bg-light-success">
                                                            <div>
                                                                {getFormattedMessage(
                                                                    "globally.active.label"
                                                                )}
                                                            </div>
                                                        </span>
                                                    ) : (
                                                        <span className="badge bg-light-danger">
                                                            <div>
                                                                {getFormattedMessage(
                                                                    "globally.deactive.label"
                                                                )}
                                                            </div>
                                                        </span>
                                                    )}
                                                </td>
                                            </tr>
                                            {/* <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.description.lable')}</td>
                                        <td className='py-4' id="desc_td">{unescape(pages[0]?.attributes?.description)?.replace(/<[^>]*>/g, '')?.substring(0, 20) + '...'}</td>
                                    </tr> */}
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.react-table.column.created-date.label"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {getFormattedDate(
                                                        pages[0]?.attributes
                                                            ?.created_at,
                                                        "d-m-y"
                                                    )}{" "}
                                                    {moment(
                                                        pages[0]?.attributes
                                                            ?.created_at
                                                    ).format("LT")}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className="py-4">
                                                    {getFormattedMessage(
                                                        "globally.react-table.column.updated-date.label"
                                                    )}
                                                </td>
                                                <td className="py-4">
                                                    {getFormattedDate(
                                                        pages[0]?.attributes
                                                            ?.updated_at,
                                                        "d-m-y"
                                                    )}{" "}
                                                    {moment(
                                                        pages[0]?.attributes
                                                            ?.updated_at
                                                    ).format("LT")}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </Table>
                                )}
                            </Card.Body>
                        </Card>
                    </div>
                </>
            }
        </MasterLayout>
    );
};

const mapStateToProps = (state) => {
    const { isLoading, plans } = state;
    return { isLoading, plans };
};

export default connect(mapStateToProps, {})(PagesDetail);
