import React, { useState } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import MasterLayout from "../MasterLayout";
import ReactDataTable from "../../../shared/table/ReactDataTable";
import DeletePage from "./DeletePage";
import TabTitle from "../../../shared/tab-title/TabTitle";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import { placeholderText } from "../../../shared/sharedMethod";
import ActionButton from "../../../shared/action-buttons/ActionButton";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import {
    editPage,
    fetchPages,
} from "../../../store/action/adminActions/pagesAction";

const AdminPages = (props) => {
    const { totalRecord } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);

    const dispatch = useDispatch();
    const { pages, isLoading } = useSelector((state) => state);
    const navigate = useNavigate();

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    const itemsValue =
        pages?.length >= 0 &&
        pages?.map((page) => ({
            title: page?.attributes?.title,
            description: page?.attributes?.description,
            meta_title: page?.attributes?.meta_title,
            meta_description: page?.attributes?.meta_description,
            visibility: page?.attributes?.visibility === 1 ? true : false,
            id: page?.id,
        }));

    const onChange = (filter) => {
        dispatch(fetchPages(filter, true));
    };

    const goToEdit = (item) => {
        const id = item.id;
        window.location.href = "#/app/admin/custom-pages/edit/" + id;
    };

    const onCheckedStatus = (e, data) => {
        const pageData = {
            title: data.title,
            slug: data.title?.replace(/ /g, "-"),
            meta_title: data.meta_title,
            meta_description: data.meta_description,
            description: data.description,
            visibility: data.visibility === false ? 1 : 0,
        };
        dispatch(editPage(data.id, pageData, navigate, true));
    };

    const goToDetailScreen = (id) => {
        window.location.href = "#/app/admin/custom-pages/detail/" + id;
    };

    const columns = [
        {
            name: getFormattedMessage("globally.input.title.label"),
            selector: (row) => row?.title,
            sortField: "title",
            sortable: true,
        },
        {
            name: getFormattedMessage("globally.input.meta-title.title"),
            selector: (row) => row?.meta_title,
            sortField: "meta_title",
            sortable: true,
        },
        {
            name: getFormattedMessage("globally.input.meta_description.title"),
            selector: (row) => row?.meta_description,
            sortField: "meta_description",
            sortable: true,
        },
        {
            name: getFormattedMessage("globally.input.visibility.title"),
            selector: (row) => row.visibility,
            cell: (row) => {
                return (
                    <div className="col-md-4 d-flex align-items-center mt-4 ">
                        <label className="form-check form-switch form-switch-sm cursor-pointer">
                            <input
                                autoComplete="off"
                                name="paypal"
                                data-id="704"
                                className="form-check-input admin-status cursor-pointer"
                                type="checkbox"
                                checked={row.visibility}
                                onChange={(e) => onCheckedStatus(e, row)}
                            />
                            <span
                                className="switch-slider"
                                data-checked="✓"
                                data-unchecked="✕"
                            ></span>
                        </label>
                    </div>
                );
            },
        },
        {
            name: getFormattedMessage("react-data-table.action.column.label"),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: (row) => (
                <ActionButton
                    isDeleteMode={true}
                    item={row}
                    goToEditProduct={goToEdit}
                    isEditMode={true}
                    onClickDeleteModel={onClickDeleteModel}
                    goToDetailScreen={goToDetailScreen}
                    isViewIcon={true}
                />
            ),
        },
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("settings.select.pages.label")} />
            <ReactDataTable
                columns={columns}
                items={itemsValue}
                onChange={onChange}
                ButtonValue={getFormattedMessage("page.create.title")}
                to="#/app/admin/custom-pages/create"
                totalRows={totalRecord}
                isLoading={isLoading}
            />
            <DeletePage
                onClickDeleteModel={onClickDeleteModel}
                deleteModel={deleteModel}
                onDelete={isDelete}
            />
        </MasterLayout>
    );
};

const mapStateToProps = (state) => {
    const { totalRecord, allConfigData } = state;
    return { totalRecord, allConfigData };
};
export default connect(mapStateToProps, {})(AdminPages);
