import React, { useEffect, useState } from "react";
import { connect, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import MasterLayout from "../MasterLayout";
import HeaderTitle from "../../../components/header/HeaderTitle";
import {
    getFormattedMessage,
    placeholderText,
} from "../../../shared/sharedMethod";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import PagesForm from "./PagesForm";
import { fetchPage } from "../../../store/action/adminActions/pagesAction";

const EditPage = (props) => {
    const { fetchPage } = props;
    const { id } = useParams();
    const [isEdit, setIsEdit] = useState(false);
    const { pages } = useSelector((state) => state);

    useEffect(() => {
        fetchPage(id);
        setIsEdit(true);
    }, []);

    const itemsValue =
        pages?.length >= 0 &&
        pages?.map((page) => ({
            title: page?.attributes?.title,
            meta_title: page?.attributes?.meta_title,
            meta_description: page?.attributes?.meta_description,
            description: page?.attributes?.description,
            slug: page?.attributes?.slug,
            visibility: page?.attributes?.visibility === 1 ? true : false,
            id: page?.id,
        }));

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("page.edit.title")} />
            <HeaderTitle
                title={getFormattedMessage("page.edit.title")}
                to="/app/admin/custom-pages"
            />
            {pages.length === 1 && (
                <PagesForm singlePage={itemsValue} id={id} isEdit={isEdit} />
            )}
        </MasterLayout>
    );
};

const mapStateToProps = (state) => {
    const {} = state;
    return {};
};

export default connect(mapStateToProps, { fetchPage })(EditPage);
