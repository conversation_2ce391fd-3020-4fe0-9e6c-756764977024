import React from "react";
import { connect, useSelector } from "react-redux";
import MasterLayout from "../MasterLayout";
import HeaderTitle from "../../../components/header/HeaderTitle";
import { useNavigate } from "react-router-dom";
import {
    getFormattedMessage,
    placeholderText,
} from "../../../shared/sharedMethod";
import PagesForm from "./PagesForm";
import { Helmet } from "react-helmet";
import { addPageAction } from "../../../store/action/adminActions/pagesAction";

const CreatePage = (props) => {
    const { addPageAction } = props;
    const navigate = useNavigate();
    const { frontSettings } = useSelector((state) => state);
    const addPageData = (formValue) => {
        addPageAction(formValue, navigate);
    };

    return (
        <MasterLayout>
            <Helmet
                title={
                    placeholderText("page.create.title") +
                    " | " +
                    frontSettings?.title
                }
            />
            <HeaderTitle
                title={getFormattedMessage("page.create.title")}
                to="/app/admin/custom-pages"
            />
            <PagesForm addPageData={addPageData} />
        </MasterLayout>
    );
};

export default connect(null, { addPageAction })(CreatePage);
