import React, { useState } from 'react';
import { connect } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar';
import FrontTabs from './frontTabs';
import { fetchFrontFeatures } from '../../../store/action/adminActions/frontcmsActions';
import EditFrontFeatures from './EditFrontFeatures';

const FrontFeatures = (props) => {
    const { fetchFrontFeatures, frontCms } = props;
    const [toggle, setToggle] = useState(false);
    const [feature, setFeature] = useState({})

    const handleClose = (item = null) => {
        setToggle(!toggle);
        setFeature(item)
    };

    const onChange = (filter) => {
        fetchFrontFeatures(filter, true)
    }


    const itemsValue = frontCms?.features && frontCms?.features?.length > 0 && frontCms?.features?.map(item => ({
        title: item?.attributes?.title,
        image: item?.attributes?.image,
        description: item?.attributes?.description.substring(0, 20) + '...',
        item: item?.attributes,
        id: item?.id
    }));

    const columns = [
        {
            name: getFormattedMessage('globally.react-table.column.image.label'),
            selector: row => row.image,
            sortField: 'image',
            sortable: false,
            cell: row => {
                return <div className='me-2'>
                    {row.image ?
                        <img src={row.image} height='50' width='50' alt='User Image'
                            className='image image-circle image-mini image-effect' />
                        :
                        <span className='custom-user-avatar fs-5'>
                            {getAvatarName(row.title)}
                        </span>
                    }
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.input.name.label'),
            selector: row => row.title,
            sortable: true,
            sortField: 'title',
        },
        {
            name: getFormattedMessage('globally.input.description.lable'),
            selector: row => row.description,
            sortable: true,
            sortField: 'description',
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => {
                return <ActionButton item={row} goToEditProduct={handleClose} isEditMode={true}
                    isDeleteMode={false}
                />
            }
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('front.features.title')} />
            <FrontTabs page={"features"} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} />
            <EditFrontFeatures title="edit.feature.title" handleClose={handleClose} show={toggle} singleFeature={feature} />
        </MasterLayout>
    )

};

const mapStateToProps = (state) => {
    const { totalRecord, isLoading, frontCms } = state;
    return { totalRecord, isLoading, frontCms }
};

export default connect(mapStateToProps, { fetchFrontFeatures })(FrontFeatures);
