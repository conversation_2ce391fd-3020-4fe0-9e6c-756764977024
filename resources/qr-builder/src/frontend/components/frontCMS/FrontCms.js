import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ImagePicker from "../../../shared/image-picker/ImagePicker";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import MasterLayout from "../MasterLayout";
import FrontTabs from "./frontTabs";
import { fetchFrontCms, updateFrontCms } from "../../../store/action/adminActions/frontcmsActions";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faQuestionCircle } from "@fortawesome/free-solid-svg-icons";
import { Tooltip as ReactTooltip } from "react-tooltip";


const FrontCms = ( props ) => {


    const { settings, fetchFrontCms, frontSettings, frontCms } = props

    const navigate = useNavigate()
    const dispatch = useDispatch()

    const [ bannerPreviewUrl, setBannerPreviewUrl ] = useState( localStorage.getItem( "website_logo" ) || null );
    const [ selectBanner, setSelectBanner ] = useState( null );

    useEffect( () => {
        fetchFrontCms()
    }, [] )

    const [ errors, setErrors ] = useState( {
        title: "",
        plan_expire_notification: ""
    } )

    const [ frontCmsDetails, setFrontCmsDetails ] = useState( {
        title: frontCms?.cms ? frontCms?.cms[ 0 ]?.title : "",
        banner: frontCms?.cms ? frontCms?.cms[ 0 ]?.image_url : "",
        description: frontCms?.cms ? frontCms?.cms[ 0 ]?.description : "",
    } )

    useEffect( () => {
        if ( frontCms ) {
            setFrontCmsDetails( {
                title: frontCms?.cms ? frontCms?.cms[ 0 ]?.title : "",
                banner: frontCms?.cms ? frontCms?.cms[ 0 ]?.image_url : "",
                description: frontCms?.cms ? frontCms?.cms[ 0 ]?.description : "",
            } )
        }
    }, [ frontCms ] )

    const onChangeInput = ( e ) => {
        setFrontCmsDetails( inputs => ( { ...inputs, [ e.target.name ]: e.target?.value } ) )
        setErrors( '' );
    }

    const handleBannerChanges = ( e ) => {
        e.preventDefault();
        if ( e.target.files.length > 0 ) {
            const file = e.target.files[ 0 ];
            if ( file.type === "image/png"
                || file.type === "image/jpg"
                || file.type === "image/svg+xml"
                || file.type === "image/jpeg"
                || file.type === "image/webp"
                || file.type === "image/gif"
                || file.type === "image/tiff" ) {
                setSelectBanner( file );
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setBannerPreviewUrl( fileReader.result );
                    setFrontCmsDetails( inputs => ( { ...inputs, banner: fileReader.result } ) )
                };
                fileReader.readAsDataURL( file );
            }
        }
    };

    const disabled = frontCms?.cms && frontCms?.cms[ 0 ]?.title === frontCmsDetails.title
        && frontCms?.cms && frontCms?.cms[ 0 ]?.image_url === frontCmsDetails.banner
        && frontCms?.cms && frontCms?.cms[ 0 ]?.description === frontCmsDetails.description



    const handleValidation = () => {
        let errorss = {};
        let isValid = false;

        if ( !frontCmsDetails.title || frontCmsDetails.title?.trim()?.length === 0 ) {
            errorss.title = getFormattedMessage( "globally.input.title.validate.label" )
        } else if ( !frontCmsDetails.description || frontCmsDetails.description?.trim()?.length === 0 ) {
            errorss.description = getFormattedMessage( "Please.enter.description.validate.label" )
        } else {
            isValid = true
        }
        setErrors( errorss );
        return isValid;
    }

    const prepareFormData = ( prepareData ) => {
        const formData = new FormData();
        formData.append( 'title', prepareData?.title );
        formData.append( 'id', "1" );
        formData.append( 'description', prepareData?.description );
        if ( selectBanner ) {
            formData.append( 'image', selectBanner );
        }

        return formData;
    };

    const onSubmit = ( event ) => {
        event.preventDefault();
        // dispatch(addToast(
        //     { text: 'This action is not allowed in demo.', type: toastType.ERROR }));
        const valid = handleValidation();
        if ( valid ) {
            dispatch( updateFrontCms( prepareFormData( frontCmsDetails ), navigate ) );
        }
    }

    const onClickCancelButton = () => {
        setFrontCmsDetails( {
            title: frontCms?.cms ? frontCms?.cms[ 0 ]?.title : "",
            banner: frontCms?.cms ? frontCms?.cms[ 0 ]?.image_url : "",
            description: frontCms?.cms ? frontCms?.cms[ 0 ]?.description : "",
        } )


        if ( frontSettings?.favicon ) {
            const logo = frontSettings?.favicon
            let link = document.querySelector( "link[rel~='icon']" );
            if ( !link ) {
                link = document.createElement( 'link' );
                link.rel = 'icon';
                document.getElementsByTagName( 'head' )[ 0 ].appendChild( link );
            }
            link.href = logo;
        }
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText( "admin.frontcms.hero-section.title" )} />
                {/* <WebsiteSettings page={'main'} /> */}
                <FrontTabs page={"hero"} />
                <div className='card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-12 mb-3 position-relative'>
                                    <ImagePicker
                                        user={frontCmsDetails.banner}
                                        isCreate={true}
                                        avtarName={"No Banner Selected"}
                                        imageTitle={"globally.input.hero-image.label"}
                                        imagePreviewUrl={frontCmsDetails.banner}
                                        handleImageChange={handleBannerChanges}
                                        isRequired
                                        tooltipContent={placeholderText( "The.image.must.be.of.pixel.612.x.315" )}
                                        tooltipId={"my-element"}
                                        tooltip={true}
                                    />
                                    <ReactTooltip anchorId="my-element" />
                                </div>
                                <div className='col-12 pt-3 mb-3 position-relative'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage( "admin.frontcms.hero-section-title.title" )}:<span className="required" />
                                    </label>
                                    <span id="my-element3" className="ms-1 form-label" data-tooltip-content={placeholderText( "you.can.enter.Max.100.characters-with-meta.in.The.title" )}>
                                        <FontAwesomeIcon icon={faQuestionCircle} />
                                    </span>
                                    <ReactTooltip anchorId="my-element3" />
                                    <input
                                        type='text'
                                        name='title'
                                        maxLength="100"
                                        value={frontCmsDetails.title}
                                        placeholder={placeholderText( "globally.input.hero-section-title.placeholder" )}
                                        className='form-control'
                                        autoFocus={true}
                                        onChange={( e ) => onChangeInput( e )}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors[ 'title' ] ? errors[ 'title' ] : null}
                                    </span>
                                </div>
                                <div className='col-12 mb-3 position-relative'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage( "admin.frontcms.hero-section-sub-title.title" )}:<span className="required" />
                                    </label>
                                    <span id="my-element1" className="ms-1 form-label" data-tooltip-content={placeholderText( "you.can.enter.Max.100.characters-with-meta.in.The.description.tooltip" )}>
                                        <FontAwesomeIcon icon={faQuestionCircle} />
                                    </span>
                                    <ReactTooltip anchorId="my-element1" />
                                    <input
                                        type='text'
                                        name='description'
                                        maxLength="100"
                                        value={frontCmsDetails.description}
                                        placeholder={placeholderText( "globally.input.hero-section-sub-title.placeholder" )}
                                        className='form-control'
                                        autoFocus={true}
                                        onChange={( e ) => onChangeInput( e )}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors[ 'description' ] ? errors[ 'description' ] : null}
                                    </span>
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={settings}
                                        onSubmit={onSubmit}
                                        editDisabled={disabled}
                                        onClickCancelButton={onClickCancelButton}
                                        link='/app/admin/front-hero'
                                        addDisabled={!frontCmsDetails.title}
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};


const mapStateToProps = ( state ) => {
    const { settings, frontSettings, frontCms } = state;
    return { settings, frontSettings, frontCms }
};


// export default connect(mapStateToProps, { fetchFrontCms })(FrontCms);

export default connect( mapStateToProps, { fetchFrontCms } )( FrontCms );
