import React, { useState } from 'react';
import { connect } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar';
import FrontTabs from './frontTabs';
import { fetchFrontQrCodeTypes } from '../../../store/action/adminActions/frontcmsActions';
import EditFrontQRTypes from './EditFrontQRTypes';
import { environment } from '../../../config/environment';

const FrontQRTypes = (props) => {
    const { fetchFrontQrCodeTypes, frontCms, totalRecord, isLoading } = props;
    const [toggle, setToggle] = useState(false);
    const [qrCodeType, setQrCodeType] = useState({})

    const handleClose = (item = null) => {
        setToggle(!toggle);
        setQrCodeType(item)
    };

    const onChange = (filter) => {
        fetchFrontQrCodeTypes(filter, true)
    }

    const qrcodeTypesDetails = [
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/1.svg',
            name: "globally.input.text.lable",
            discription: getFormattedMessage("globally.text.heading"),
            foot: getFormattedMessage("globally.text.title"),
            typeNo: 1
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/7.svg',
            name: "globally.input.url.lable",
            discription: getFormattedMessage("globally.url.heading"),
            foot: getFormattedMessage("globally.url.title"),
            typeNo: 2
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/2.svg',
            name: "globally.input.phone.lable",
            discription: getFormattedMessage("globally.phone.heading"),
            foot: getFormattedMessage("globally.phone.title"),
            typeNo: 3
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/3.svg',
            name: "user.input.email.label",
            discription: getFormattedMessage("globally.email.heading"),
            foot: getFormattedMessage("globally.email.title"),
            typeNo: 4
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/10.svg',
            name: "globally.input.location.lable",
            discription: getFormattedMessage("globally.location.heading"),
            foot: getFormattedMessage("globally.location.title"),
            typeNo: 5
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/5.svg',
            name: "globally.input.wifi.lable",
            discription: getFormattedMessage("globally.wifi.heading"),
            foot: getFormattedMessage("globally.wifi.title"),
            typeNo: 6
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/4.svg',
            name: "globally.input.facetime.lable",
            discription: getFormattedMessage("globally.facetime.heading"),
            foot: getFormattedMessage("globally.facetime.title"),
            typeNo: 7
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/11.svg',
            name: "globally.input.event.lable",
            discription: getFormattedMessage("globally.event.heading"),
            foot: getFormattedMessage("globally.event.title"),
            typeNo: 8
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/9.svg',
            name: "globally.input.whatsapp.lable",
            discription: getFormattedMessage("globally.whatsapp.heading"),
            foot: getFormattedMessage("globally.whatsapp.title"),
            typeNo: 9
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/13.svg',
            name: "globally.input.crypto.lable",
            discription: getFormattedMessage("globally.crypto.heading"),
            foot: getFormattedMessage("globally.crypto.title"),
            typeNo: 10
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/12.svg',
            name: "globally.input.vcard.lable",
            discription: getFormattedMessage("globally.vcard.heading"),
            foot: getFormattedMessage("globally.vcard.title"),
            typeNo: 11
        },
        {
            image: environment.URL + '/default-images/frontEnd-images/QrCodeTyleImages/6.svg',
            name: "globally.input.paypal.lable",
            discription: getFormattedMessage("globally.paypal.heading"),
            foot: getFormattedMessage("globally.paypal.title"),
            typeNo: 12
        },
    ]


    const itemsGetValue = frontCms && frontCms?.qrCodeTypes?.length > 0 && qrcodeTypesDetails?.map((data) => {
        const itemsGet = frontCms?.qrCodeTypes?.filter((items) => data.typeNo === items?.id)
        if (itemsGet.length >= 1) {
            if (data.typeNo === itemsGet[0]?.id) {
                return ({
                    title: data.name,
                    name: itemsGet[0]?.attributes?.title,
                    image: itemsGet[0]?.attributes?.image,
                    description: itemsGet[0]?.attributes?.description,
                    content: itemsGet[0]?.attributes?.description,
                    item: itemsGet[0]?.attributes,
                    id: itemsGet[0]?.id
                })
            }
        }
    })

    const itemsValue = itemsGetValue && itemsGetValue.length > 0 && itemsGetValue.filter((item) => item !== undefined)



    const columns = [
        {
            name: getFormattedMessage('globally.react-table.column.image.label'),
            selector: row => row.image,
            sortField: 'image',
            sortable: false,
            cell: row => {
                return <div className='me-2'>
                    {row.image ?
                        <img src={row.image} height='50' width='50' alt='User Image'
                            className='image image-circle image-mini image-effect' />
                        :
                        <span className='custom-user-avatar fs-5'>
                            {getAvatarName(row.name)}
                        </span>
                    }
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.input.name.label'),
            selector: row => getFormattedMessage(row.title),
            sortable: false,
            sortField: 'title',
        },
        {
            name: getFormattedMessage("globally.content.title"),
            selector: row => row.description,
            sortable: false,
            sortField: 'description',
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => {
                return <ActionButton item={row} goToEditProduct={handleClose} isEditMode={true}
                    isDeleteMode={false}
                />
            }
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("plans.qr.codes.types")} />
            <FrontTabs page={"qrcodes-types"} />
            <div className='front_qr_types_table'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} totalRows={totalRecord} isLoading={isLoading} />
            </div>
            <EditFrontQRTypes title="globally.editqr-types.content.title" handleClose={handleClose} show={toggle} singleQrTypeContent={qrCodeType} />
        </MasterLayout>
    )

};

const mapStateToProps = (state) => {
    const { totalRecord, isLoading, frontCms } = state;
    return { totalRecord, isLoading, frontCms }
};

export default connect(mapStateToProps, { fetchFrontQrCodeTypes })(FrontQRTypes);
