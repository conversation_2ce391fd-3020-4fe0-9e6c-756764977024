import React, { useState, createRef, useEffect } from 'react';
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { Modal } from 'react-bootstrap-v5';
import { getFormattedMessage, placeholderText } from "../../../shared/sharedMethod";
import ModelFooter from '../../../shared/components/modelFooter';
import { updateFrontFeatures, updateFrontSubFeatures } from '../../../store/action/adminActions/frontcmsActions';
import ImagePicker from '../../../shared/image-picker/ImagePicker';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faQuestionCircle } from '@fortawesome/free-solid-svg-icons';
import { Tooltip as ReactTooltip } from "react-tooltip";
import { addToast } from '../../../store/action/toastAction';
import { toastType } from '../../../constants';

const EditFrontSubFeaturesForm = (props) => {
    const { updateFrontSubFeatures, singleSubFeature, handleClose, show, title } = props;
    const innerRef = createRef();
    const [formValue, setFormValue] = useState({
        title: singleSubFeature?.item?.title ? singleSubFeature.item?.title : '',
        description: singleSubFeature?.item?.description ? singleSubFeature?.item?.description : '',
        image: singleSubFeature?.item?.image_url ? singleSubFeature?.item?.image_url : "",
        sub_titles: singleSubFeature?.item?.sub_titles ? singleSubFeature?.item?.sub_titles : [],
        id: singleSubFeature?.id
    });
    const [selectBanner, setSelectBanner] = useState(null);
    const dispatch = useDispatch();

    useEffect(() => {
        setFormValue({
            title: singleSubFeature?.item?.title ? singleSubFeature.item?.title : '',
            description: singleSubFeature?.item?.description ? singleSubFeature?.item?.description : '',
            image: singleSubFeature?.item?.image_url ? singleSubFeature?.item?.image_url : "",
            id: singleSubFeature?.id,
            sub_titles: singleSubFeature?.item?.sub_titles?.length > 0 ? singleSubFeature?.item?.sub_titles : [],
        })
    }, [singleSubFeature])

    const [errors, setErrors] = useState({
        title: '',
        sub_titles: ""
    });

    const disabled = singleSubFeature && singleSubFeature?.item && singleSubFeature.item?.title === formValue.title
        && singleSubFeature && singleSubFeature?.item && singleSubFeature.item?.description === formValue.description
        && singleSubFeature && singleSubFeature?.item && singleSubFeature.item?.image_url === formValue.image

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (!formValue['title'] || formValue.title.trim()?.length === 0) {
            errorss['title'] = getFormattedMessage("globally.input.title.validate.label");
        } else if ((formValue['title'] || formValue['title']?.length > 50)) {
            errorss['title'] = getFormattedMessage("globally.input.name.validate.label");
        } else if (!formValue['description'] || formValue.description.trim()?.length === 0) {
            errorss['description'] = getFormattedMessage("Please.enter.description.validate.label");
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const handleBannerChanges = (e) => {
        e.preventDefault();
        if (e.target.files?.length > 0) {
            const file = e.target.files[0];
            if (file.type === "image/png"
                || file.type === "image/jpg"
                || file.type === "image/jpeg"
                || file.type === "image/webp"
                || file.type === "image/gif"
                || file.type === "image/tiff") {
                setSelectBanner(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setFormValue(inputs => ({ ...inputs, image: fileReader.result }))
                };
                fileReader.readAsDataURL(file);
            }
        }
    };

    const onChangeInput = (e) => {
        e.preventDefault();
        setFormValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    };

    const prepareFormData = (prepareData) => {
        const formData = new FormData();
        formData.append('title', prepareData.title);
        formData.append('id', prepareData.id);
        formData.append('description', prepareData?.description);
        if (selectBanner) {
            formData.append('image', selectBanner);
        }

        return formData;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation();
        // dispatch(addToast(
        //     { text: 'This action is not allowed in demo.', type: toastType.ERROR }));
        if (singleSubFeature && valid) {
            if (!disabled) {
                setFormValue(formValue)
                updateFrontSubFeatures(prepareFormData(formValue), handleClose);
                clearField(false);
            }
        }
    };

    const clearField = () => {
        setFormValue({
            title: singleSubFeature?.title ? singleSubFeature.title : '',
            description: singleSubFeature?.description ? singleSubFeature.description : '',
        });
        setErrors('');
        handleClose();
    };

    return (
        <Modal show={show}
            onHide={clearField}
            keyboard={true}
            onShow={() => setTimeout(() => {
                innerRef.current.focus();
            }, 1)}
        >
            <Form onKeyPress={(e) => {
                if (e.key === 'Enter') {
                    onSubmit(e)
                }
            }}>
                <Modal.Header closeButton>
                    <Modal.Title>{title}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className='row'>
                        <div className='col-12 mb-3 position-relative'>
                            <ImagePicker
                                user={formValue.image}
                                isCreate={true}
                                avtarName={"No Image"}
                                imageTitle={'globally.react-table.column.image.label'}
                                imagePreviewUrl={formValue.image}
                                handleImageChange={handleBannerChanges}
                                isRequired
                                tooltipContent={placeholderText("The.image.must.be.of.pixel.220.x.172")}
                                tooltipId={"my-element1"}
                                tooltip={true}
                            />
                            <ReactTooltip anchorId="my-element1" />
                        </div>
                        <div className='col-md-12 mb-3 pt-3 position-relative'>
                            <label
                                className='form-label'>{getFormattedMessage("globally.input.title.label")}: </label>
                            <span className='required' />
                            <span id="my-element2" className="ms-1 form-label" data-tooltip-content={placeholderText("you.can.enter.Max.50.characters.in.The.title")}>
                                <FontAwesomeIcon icon={faQuestionCircle} />
                            </span>
                            <ReactTooltip anchorId="my-element2" />
                            <input type='text' name='title' value={formValue.title}
                                maxLength="50"
                                placeholder={placeholderText("enter-title.placeholder")}
                                className='form-control' autoComplete='off'
                                onChange={(e) => onChangeInput(e)} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['title'] ? errors['title'] : null}</span>
                        </div>
                        <div className='col-md-12  mb-5 position-relative'>
                            <label
                                className='form-label'>{getFormattedMessage("globally.input.description.lable")}: </label>
                            <span className='required' />
                            <span id="my-element3" className="ms-1 form-label" data-tooltip-content={placeholderText("you.can.enter.Max.200.characters.in.The.description")}>
                                <FontAwesomeIcon icon={faQuestionCircle} />
                            </span>
                            <ReactTooltip anchorId="my-element3" />
                            <textarea rows={4} type='text' maxLength="200" name='description' value={formValue.description}
                                placeholder={placeholderText("enter-description.placeholder")}
                                className='form-control' autoComplete='off'
                                onChange={(e) => onChangeInput(e)}
                            />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['description'] ? errors['description'] : null}</span>
                        </div>
                    </div>
                </Modal.Body>
            </Form>
            <ModelFooter onEditRecord={singleSubFeature} onSubmit={onSubmit} editDisabled={disabled}
                clearField={clearField} addDisabled={disabled} />
        </Modal>
    )

};

export default connect(null, { updateFrontSubFeatures })(EditFrontSubFeaturesForm);
