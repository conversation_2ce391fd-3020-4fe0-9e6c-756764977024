import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar';
import FrontTabs from './frontTabs';
import { fetchFrontFeatures, fetchTermsAndCondition, updateTermsAndCondition } from '../../../store/action/adminActions/frontcmsActions';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Card } from 'react-bootstrap';
import ModelFooter from '../../../shared/components/modelFooter';
import { addToast } from '../../../store/action/toastAction';
import { toastType } from '../../../constants';

const TermsAndConditions = (props) => {
    const { totalRecord, isLoading, fetchFrontFeatures, frontCms } = props;
    const dispatch = useDispatch();
    const [terms, setTerms] = useState({
        terms: '',
        privacy: ""
    })

    const [errors, setErrors] = useState({
        terms: '',
        privacy: ""
    })

    useEffect(() => {
        dispatch(fetchTermsAndCondition())
    }, [])


    useEffect(() => {
        if (frontCms?.allTremCondition) {
            setTerms({
                terms: frontCms?.allTremCondition?.term_conditions ? frontCms?.allTremCondition?.term_conditions : '',
                privacy: frontCms?.allTremCondition?.privacy_policy ? frontCms?.allTremCondition?.privacy_policy : ''
            })
        }

    }, [frontCms])

    const handleTermsChange = (content) => {
        setTerms(inputs => ({ ...inputs, terms: content }))
    }

    const handlePrivacyChange = (content) => {
        setTerms(inputs => ({ ...inputs, privacy: content }))
    }


    const disabled = frontCms && frontCms?.allTremCondition && frontCms?.allTremCondition?.term_conditions === terms.terms
        && frontCms && frontCms?.allTremCondition && frontCms?.allTremCondition?.privacy_policy === terms.privacy

    const formats = [
        "header",
        "bold",
        "italic",
        "underline",
        "strike",
        "blockquote",
        "list",
        "bullet",
        "indent",
        "link",
    ];

    const onSubmit = (event) => {
        event.preventDefault();
        // dispatch(addToast(
        //     { text: 'This action is not allowed in demo.', type: toastType.ERROR }));
        var support = (function () {
            if (!window.DOMParser) return false;
            var parser = new DOMParser();
            try {
                parser.parseFromString('x', 'text/html');
            } catch (err) {
                return false;
            }
            return true;
        })();
        var textToHTML = function (str) {
            if (support) {
                var parser = new DOMParser();
                var doc = parser.parseFromString(str, 'text/html');
                return doc.body.innerText
            }
        };
        const privPoli = textToHTML(terms.privacy)
        if (privPoli === "" || privPoli.trim().length === 0) {
            setErrors({ privacy: getFormattedMessage("Please.enter.privacy.policy.validate.label") })
        } else {
            dispatch(updateTermsAndCondition({
                term_conditions: frontCms?.allTremCondition?.term_conditions ? frontCms?.allTremCondition?.term_conditions : 'Terms & Conditions',
                privacy_policy: terms.privacy
            }, 2))
            setErrors({
                terms: "",
                privacy: ""
            })
        }
    }

    const clearField = () => {
        setTerms({
            terms: frontCms?.allTremCondition?.term_conditions ? frontCms?.allTremCondition?.term_conditions : '',
            privacy: frontCms?.allTremCondition?.privacy_policy ? frontCms?.allTremCondition?.privacy_policy : ''
        });
        setErrors('');
    };


    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("admin.privacy-policy.title")} />
            <FrontTabs page={"privacy-policy"} />
            <Card>
                <Card.Body>
                    <div className='col-12 terms-box'>
                        <label
                            className='form-label'>{getFormattedMessage('admin.privacy-policy.title')}:<span className="required" /> </label>
                        <ReactQuill theme="snow" formats={formats} value={terms.privacy}
                            onChange={handlePrivacyChange} />
                        <span
                            className='text-danger d-block fw-400 fs-small mt-2'>{errors['privacy'] ? errors['privacy'] : null}</span>
                    </div>
                    <div className='col-12 pt-sm-0 pt-3'>
                        <ModelFooter
                            onEditRecord={frontCms}
                            onSubmit={onSubmit}
                            editDisabled={disabled}
                            onClickCancelButton={clearField}
                            link='/app/admin/front-terms-conditions'
                            addDisabled={!terms.terms}
                        />
                    </div>
                </Card.Body>
            </Card>
        </MasterLayout>
    )

};

const mapStateToProps = (state) => {
    const { totalRecord, isLoading, frontCms } = state;
    return { totalRecord, isLoading, frontCms }
};

export default connect(mapStateToProps, { fetchFrontFeatures })(TermsAndConditions);
