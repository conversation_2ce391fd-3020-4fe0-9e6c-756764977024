import React, { useState } from 'react';
import { connect } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate, getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar';
import FrontTabs from './frontTabs';
import { fetchFrontEnquiries } from '../../../store/action/adminActions/frontcmsActions';
import DeleteFrontEnquiries from './DeleteFrontEnquiries';
import moment from 'moment';

const FrontEnquiries = (props) => {
    const { totalRecord, isLoading, fetchFrontEnquiries, frontCms } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);

    const itemsValue = frontCms?.allEnquiries && frontCms?.allEnquiries?.length > 0 && frontCms?.allEnquiries?.map(item => ({
        name: item?.name,
        email: item?.email,
        subject: item?.subject,
        message: item?.message.length > 15 ? item?.message.split('').splice(0, 15).join("") + "..." : item?.message.split(' ').splice(0, 4).join(" ") + "...",
        view: item.view === 0 ? 0 : 1,
        date: getFormattedDate(item?.created_at),
        time: moment(item?.created_at).format('LT'),
        id: item?.id
    }));

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    const goToDetail = (id) => {
        window.location.href = '#/app/admin/front-enquiries/' + id;
    };

    const onChange = (filter) => {
        fetchFrontEnquiries(filter, true);
    };


    const columns = [
        {
            name: getFormattedMessage('globally.input.name.label'),
            selector: row => row.name,
            sortable: true,
            sortField: 'name',
        },
        {
            name: getFormattedMessage('globally.input.email.placeholder.label'),
            selector: row => row.email,
            sortable: true,
            sortField: 'email',
        },
        {
            name: getFormattedMessage('dashboard.recentSales.status.label'),
            selector: row => row?.view,
            sortField: 'view',
            sortable: true,
            cell: row => {
                return row.view === 0 ? <div className='badge bg-light-danger'>
                    {getFormattedMessage("unread.label")}
                </div> : <div className='badge bg-light-success'>
                    {getFormattedMessage("read.label")}
                </div>
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton isDeleteMode={true} item={row} isEditMode={false} goToDetailScreen={goToDetail} isViewIcon={true} onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('front.enquiries.title')} />
            <FrontTabs page={"enquiries"} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} totalRows={totalRecord} isLoading={isLoading} />
            <DeleteFrontEnquiries onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )

};

const mapStateToProps = (state) => {
    const { totalRecord, isLoading, frontCms } = state;
    return { totalRecord, isLoading, frontCms }
};

export default connect(mapStateToProps, { fetchFrontEnquiries })(FrontEnquiries);
