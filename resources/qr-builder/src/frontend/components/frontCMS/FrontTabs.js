import React, { useEffect, useState } from 'react'
import { Tab, Tabs } from 'react-bootstrap'
import { connect } from 'react-redux';
import { getFormattedMessage } from "../../../shared/sharedMethod"

const FrontTabs = (props) => {
    const { allConfigData, page } = props;
    const [key, setKey] = useState(page);

    useEffect(() => {
        setKey(page)
    }, [page])

    useEffect(() => {
        window.location.href = '#/app/admin/front-' + key;
    }, [key])


    return (
        <>
            <Tabs defaultActiveKey={'hero'} activeKey={page} id='uncontrolled-tab-example' onSelect={(k) => setKey(k)}
                className='mt-7 mb-5'>
                <Tab eventKey='hero' title={getFormattedMessage("admin.frontcms.hero-section.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                <Tab eventKey='features' title={getFormattedMessage("admin.feature.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                <Tab eventKey='qrcodes-types' title={getFormattedMessage("plans.qr.codes.types")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                {/* <Tab eventKey='subfeatures' title={getFormattedMessage("admin.sub-feature.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab> */}
                <Tab eventKey='terms-conditions' title={getFormattedMessage("admin.terms-condition.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                <Tab eventKey='privacy-policy' title={getFormattedMessage("admin.privacy-policy.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                <Tab eventKey='enquiries' title={getFormattedMessage("admin.enquiries.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
            </Tabs>

        </>
    )

}
const mapStateToProps = (state) => {
    const { allConfigData } = state;
    return { allConfigData }
};

export default connect(mapStateToProps, {})(FrontTabs);
