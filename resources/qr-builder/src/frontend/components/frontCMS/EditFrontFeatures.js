import React, { useState, createRef, useEffect } from 'react';
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { Modal } from 'react-bootstrap-v5';
import { getFormattedMessage, placeholderText } from "../../../shared/sharedMethod";
import ModelFooter from '../../../shared/components/modelFooter';
import { updateFrontFeatures } from '../../../store/action/adminActions/frontcmsActions';
import ImagePicker from '../../../shared/image-picker/ImagePicker';
import { Tooltip as ReactTooltip } from "react-tooltip";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faQuestionCircle } from '@fortawesome/free-solid-svg-icons';
import { addToast } from '../../../store/action/toastAction';
import { dateFormat, toastType } from '../../../constants';

const EditFrontFeaturesForm = (props) => {
    const { singleFeature, handleClose, show, title, updateFrontFeatures } = props;

    const [formValue, setFormValue] = useState({
        title: singleFeature?.item?.title ? singleFeature.item?.title : '',
        description: singleFeature?.item?.description ? singleFeature?.item?.description : '',
        image: singleFeature?.item?.image ? singleFeature?.item?.image : "",
        sub_titles: [],
        id: singleFeature?.id
    });
    const [subTitlesObj, setSubTitlesObj] = useState([])
    const [selectBanner, setSelectBanner] = useState(null);
    const [subTitle, setSubTitle] = useState('');
    const dispatch = useDispatch();

    useEffect(() => {
        let preperArray = []
        for (let index = 6; index > 0; --index) {
            if (singleFeature?.item?.sub_titles !== null) {
                if (singleFeature?.item?.sub_titles[index - 1]) {
                    preperArray.push(singleFeature?.item?.sub_titles[index - 1])
                } else {
                    preperArray.push("")
                }
            } else {
                preperArray.push("")
            }
        }
        setFormValue({
            title: singleFeature?.item?.title ? singleFeature.item?.title : '',
            description: singleFeature?.item?.description ? singleFeature?.item?.description : '',
            image: singleFeature?.item?.image ? singleFeature?.item?.image : "",
            id: singleFeature?.id,
            sub_titles: preperArray ? preperArray.reverse() : [],
        })
    }, [singleFeature])

    const [errors, setErrors] = useState({
        title: '',
        sub_titles: ""
    });

    let disabled = singleFeature && singleFeature.item && singleFeature?.item?.title === formValue.title
        && singleFeature && singleFeature.item && singleFeature?.item?.description === formValue.description
        && singleFeature && singleFeature.item && singleFeature?.item?.image === formValue.image
        && singleFeature && singleFeature.item && formValue?.sub_titles[0] === subTitlesObj[0]?.sub_title1 && formValue?.sub_titles[1] === subTitlesObj[1]?.sub_title2 && formValue?.sub_titles[2] === subTitlesObj[2]?.sub_title3 && formValue?.sub_titles[3] === subTitlesObj[3]?.sub_title4 && formValue?.sub_titles[4] === subTitlesObj[4]?.sub_title5 && formValue?.sub_titles[5] === subTitlesObj[5]?.sub_title6

    const findDuplicates = (arr) => {
        let sorted_arr = arr.slice().sort();
        let results = [];
        for (let i = 0; i < sorted_arr.length - 1; i++) {
            if (sorted_arr[i] !== "") {
                if (sorted_arr[i + 1] === sorted_arr[i]) {
                    results.push(sorted_arr[i]);
                }
            }
        }
        return results;
    }

    let subTitleData = []
    subTitlesObj?.map((std, i) => {
        const objKey = Object.keys(std)[0]
        subTitleData = [...subTitleData, std[objKey]]
    })
    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (!formValue['title'] || formValue.title.trim().length === 0) {
            errorss['title'] = getFormattedMessage("globally.input.title.validate.label");
        } else if (formValue.title.length > 50) {
            errorss['title'] = getFormattedMessage("globally.input.max-titel.validate.label");
        } else if ((formValue['title'] && formValue['title']?.length > 50)) {
            errorss['title'] = getFormattedMessage("globally.input.name.validate.label");
        } else if (!formValue['description'] || formValue.description.trim()?.length === 0) {
            errorss['description'] = getFormattedMessage("Please.enter.description.validate.label");
        } else if (formValue?.sub_titles[0]?.trim()?.length === 0 &&
            formValue?.sub_titles[1]?.trim()?.length === 0 &&
            formValue?.sub_titles[2]?.trim()?.length === 0 &&
            formValue?.sub_titles[3]?.trim()?.length === 0 &&
            formValue?.sub_titles[4]?.trim()?.length === 0 &&
            formValue?.sub_titles[5]?.trim()?.length === 0) {
            errorss['sub_titles'] = getFormattedMessage("Please.enter.sub.title.validate.label");
        } else if (findDuplicates(formValue?.sub_titles)?.length > 0) {
            isValid = false
        } else {
            isValid = true;
        }

        setErrors(errorss);
        return isValid;
    };

    const handleBannerChanges = (e) => {
        e.preventDefault();
        if (e.target.files?.length > 0) {
            const file = e.target.files[0];
            if (file.type === "image/png"
                || file.type === "image/jpg"
                || file.type === "image/svg+xml"
                || file.type === "image/jpeg"
                || file.type === "image/webp"
                || file.type === "image/gif"
                || file.type === "image/tiff") {
                setSelectBanner(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setFormValue(inputs => ({ ...inputs, image: fileReader.result }))
                };
                fileReader.readAsDataURL(file);
            }
        }
    };

    const onChangeInput = (e, index) => {
        e.preventDefault();
        const { name, value } = e.target;
        if (e.target.name?.includes("sub_title")) {
            let data = []
            formValue.sub_titles.map((items, indexs) => {
                if (indexs === index) {
                    data.push(value)
                } else {
                    data.push(items)
                }
            })
            setFormValue(updata => ({
                ...updata,
                sub_titles: data
            }))
        } else {
            setFormValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        }
        setErrors({});
    };

    const handleAddSubtitle = (e) => {
        if (e.key === "Enter") {
            if (subTitle !== "" || subTitle.trim().length !== 0) {
                if (formValue.sub_titles?.length < 6) {
                    let exist = false
                    formValue.sub_titles?.map(d => {
                        if (d.toLowerCase() === subTitle.toLowerCase()) {
                            exist = true
                            setErrors({ sub_titles: getFormattedMessage("You.have.already.added.this.feature.validate.label") })
                        }
                    })

                    if (!exist) {
                        setFormValue(inputs => ({ ...inputs, sub_titles: [...inputs.sub_titles, subTitle] }))
                        setSubTitle("")
                        setErrors({})
                    }
                } else {
                    setErrors({ sub_titles: getFormattedMessage("Only.6.sub.titles.are.available.to.add.validate.label") })
                }
            } else {
                setErrors({ sub_titles: getFormattedMessage("Please.enter.text.in.Sub.Title.validate.label") })
            }
        }
    }

    const prepareFormData = (prepareData) => {
        const formData = new FormData();
        formData.append('title', prepareData.title);
        formData.append('id', prepareData.id);
        // formData.append('sub_title', prepareData?.sub_titles);
        formData.append('description', prepareData?.description);
        if (selectBanner) {
            formData.append('image', selectBanner);
        }
        return formData;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        var filtered = formValue.sub_titles.filter(function (el) {
            return (el != null && el !== "");
        });

        if (event.key !== "Enter") {
            const valid = handleValidation();
            // dispatch(addToast(
            //     { text: 'This action is not allowed in demo.', type: toastType.ERROR }));
            if (singleFeature && valid) {
                setFormValue(formValue)
                updateFrontFeatures(prepareFormData(formValue), handleClose, filtered);
                clearField(false);
            }
        }
    };

    const clearField = () => {
        setFormValue({
            title: singleFeature?.item?.title ? singleFeature.item?.title : '',
            description: singleFeature?.item?.description ? singleFeature?.item?.description : '',
            image: singleFeature?.item?.image ? singleFeature?.item?.image : "",
            sub_titles: singleFeature?.item?.sub_titles ? singleFeature?.item?.sub_titles : [],
            id: singleFeature?.id
        });
        setErrors({});
        handleClose();
    };

    const handleDeleteSubtitle = (e, data) => {
        e.preventDefault()
        const deleted = formValue?.sub_titles?.filter((d) => d !== data)
        setFormValue(inputs => ({ ...inputs, sub_titles: deleted }))
    }

    return (
        <Modal show={show}
            onHide={clearField}
            size='lg'
        >
            <Form onKeyPress={(e) => {
                if (e.key === 'Enter') {
                    onSubmit(e)
                }
            }}>
                <Modal.Header closeButton className='front-features-header'>
                    <Modal.Title>{getFormattedMessage(title)}</Modal.Title>
                </Modal.Header>
                <Modal.Body className='pb-0 pt-0'>
                    <div className='row'>
                        <div className='col-md-3 col-12 mb-3 position-relative'>
                            <ImagePicker
                                user={formValue.image}
                                isCreate={true}
                                avtarName={"No Image"}
                                imageTitle={'globally.react-table.column.image.label'}
                                imagePreviewUrl={formValue.image}
                                handleImageChange={handleBannerChanges}
                                isRequired
                                tooltipContent={placeholderText("The.image.must.be.of.pixel.612.x.315")}
                                tooltipId={"my-element1"}
                                tooltip={true}
                            />
                            <ReactTooltip anchorId="my-element1" />
                        </div>
                        <div className='col-md-9 col-12'>
                            <div className='col-md-12 col-12 mb-3 position-relative'>
                                <label
                                    className='form-label'>{getFormattedMessage("globally.input.title.label")}:  </label>
                                <span className='required' />
                                <span id="my-element2" className="ms-1 form-label" data-tooltip-content={placeholderText("you.can.enter.Max.50.characters.in.The.title")}>
                                    <FontAwesomeIcon icon={faQuestionCircle} />
                                </span>
                                <ReactTooltip anchorId="my-element2" />
                                <input type='text' name='title' value={formValue.title}
                                    maxLength="50"
                                    placeholder={placeholderText("enter-title.placeholder")}
                                    className='form-control' autoComplete='off'
                                    onChange={(e) => onChangeInput(e)} />
                                <span
                                    className='text-danger d-block fw-400 fs-small'>{errors['title'] ? errors['title'] : null}</span>
                            </div>
                            <div className='col-md-12 mb-5 position-relative'>
                                <label
                                    className='form-label'>{getFormattedMessage("globally.input.description.lable")}: </label>
                                <span className='required' />
                                <span id="my-element3" className="ms-1 form-label" data-tooltip-content={placeholderText("you.can.enter.Max.200.characters.in.The.description")}>
                                    <FontAwesomeIcon icon={faQuestionCircle} />
                                </span>
                                <ReactTooltip anchorId="my-element3" />
                                <textarea rows={3} type='text' maxLength="200" name='description' value={formValue.description}
                                    placeholder={placeholderText("enter-description.placeholder")}
                                    className='form-control' autoComplete='off'
                                    onChange={(e) => onChangeInput(e)}
                                />
                                <span
                                    className='text-danger d-block fw-400 fs-small mt-2'>{errors['description'] ? errors['description'] : null}</span>
                            </div>
                        </div>
                        <div className='col-md-12 col-12 position-relative'>
                            <label
                                className='form-label'>{getFormattedMessage("sub-title.title")}: </label>
                            <span className='required' />
                            <span id="my-element5" className="ms-1 form-label" data-tooltip-content={placeholderText("you.can.enter.Max.90.characters.in.The.subTitle")}>
                                <FontAwesomeIcon icon={faQuestionCircle} />
                            </span>
                            <ReactTooltip anchorId="my-element5" />
                        </div>
                        <div className='col-md-12 mb-5'>
                            {
                                formValue?.sub_titles?.length > 0 &&
                                formValue?.sub_titles?.map((std, i) => {
                                    // const objKey = Object.keys(std)[0]
                                    return <div className="input-group mb-2" key={i + 1} >
                                        <div className="input-group-prepend">
                                            <div className="input-group-text">{i + 1}.</div>
                                        </div>
                                        <input type="text" maxLength="90" name={`sub_title${i}`} className="form-control" id="inlineFormInputGroup" value={std} placeholder={`${placeholderText("enter.sub.title.placeholder")}${i + 1}`} onChange={(e) => onChangeInput(e, i)} />
                                    </div>
                                })
                            }
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['sub_titles'] ? errors['sub_titles'] : null}
                            </span>
                            {findDuplicates(formValue?.sub_titles)?.length > 0
                                &&
                                <span className='text-danger d-block fw-400 fs-small mt-2'>{getFormattedMessage("you.have.used.this.message")} "{findDuplicates(formValue?.sub_titles)[0]}"  {getFormattedMessage("subtitle.more.than.once.message")}</span>}
                        </div>

                    </div>
                </Modal.Body>
            </Form>
            <ModelFooter onEditRecord={singleFeature} onSubmit={onSubmit}
                clearField={clearField} editDisabled={disabled} />
        </Modal >
    )

};

export default connect(null, { updateFrontFeatures })(EditFrontFeaturesForm);
