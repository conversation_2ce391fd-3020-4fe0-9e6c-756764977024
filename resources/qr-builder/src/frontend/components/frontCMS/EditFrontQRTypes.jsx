import React, { useState, useEffect } from 'react';
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { Modal } from 'react-bootstrap-v5';
import { getFormattedMessage, placeholderText } from "../../../shared/sharedMethod";
import ModelFooter from '../../../shared/components/modelFooter';
import { updateFrontQrCodeType } from '../../../store/action/adminActions/frontcmsActions';
import ImagePicker from '../../../shared/image-picker/ImagePicker';
import { Tooltip as ReactTooltip } from "react-tooltip";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faQuestionCircle } from '@fortawesome/free-solid-svg-icons';

const EditFrontQRTypes = (props) => {
    const { singleQrTypeContent, handleClose, show, title, updateFrontQrCodeType } = props;

    const [formValue, setFormValue] = useState({
        name: singleQrTypeContent?.title ? singleQrTypeContent?.title : '',
        content: singleQrTypeContent?.description ? singleQrTypeContent?.description : '',
        id: singleQrTypeContent?.id,
        title: singleQrTypeContent?.name ? singleQrTypeContent?.name : '',
    });
    const [selectBanner, setSelectBanner] = useState(null);
    const dispatch = useDispatch();

    useEffect(() => {
        setFormValue({
            name: singleQrTypeContent?.title ? singleQrTypeContent?.title : '',
            content: singleQrTypeContent?.content ? singleQrTypeContent?.content : '',
            id: singleQrTypeContent?.id,
            title: singleQrTypeContent?.name ? singleQrTypeContent?.name : '',
        })
    }, [singleQrTypeContent])

    const [errors, setErrors] = useState({
        name: '',
        content: ""
    });

    let disabled = singleQrTypeContent && singleQrTypeContent?.content === formValue?.content

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (!formValue['content'] || formValue.content.trim().length === 0) {
            errorss['content'] = getFormattedMessage("globally.input.content.validate.label");
        } else {
            isValid = true;
        }

        setErrors(errorss);
        return isValid;
    };

    const handleBannerChanges = (e) => {
        e.preventDefault();
        if (e.target.files?.length > 0) {
            const file = e.target.files[0];
            if (file.type === "image/png"
                || file.type === "image/jpg"
                || file.type === "image/svg+xml"
                || file.type === "image/jpeg"
                || file.type === "image/webp"
                || file.type === "image/gif"
                || file.type === "image/tiff") {
                setSelectBanner(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setFormValue(inputs => ({ ...inputs, image: fileReader.result }))
                };
                fileReader.readAsDataURL(file);
            }
        }
    };

    const onChangeInput = (e, index) => {
        e.preventDefault();
        setFormValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors({});
    };

    const prepareFormData = (prepareData) => {
        const formData = new FormData();
        formData.append('title', prepareData.title);
        formData.append('id', prepareData.id);
        formData.append('description', prepareData?.content);
        return formData;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation()
        if (singleQrTypeContent && valid) {
            setFormValue(formValue)
            updateFrontQrCodeType(prepareFormData(formValue), handleClose);
        }
    };

    const clearField = () => {
        setFormValue({
            name: singleQrTypeContent?.name ? singleQrTypeContent.name : '',
            content: singleQrTypeContent?.content ? singleQrTypeContent?.content : '',
            id: singleQrTypeContent?.id
        });
        setErrors({});
        handleClose();
    };

    return (
        <Modal show={show}
            onHide={clearField}
            size='lg'
        >
            <Form onKeyPress={(e) => {
                if (e.key === 'Enter') {
                    onSubmit(e)
                }
            }}>
                <Modal.Header closeButton className='front-features-header'>
                    <Modal.Title>{getFormattedMessage(title)}</Modal.Title>
                </Modal.Header>
                <Modal.Body className='pb-0 pt-0'>
                    <div className='row'>
                        {/* <div className='col-md-3 col-12 mb-3 position-relative'>
                            <ImagePicker
                                user={formValue.image}
                                isCreate={true}
                                avtarName={"No Image"}
                                imageTitle={'globally.react-table.column.image.label'}
                                imagePreviewUrl={formValue.image}
                                handleImageChange={handleBannerChanges}
                                isRequired
                                tooltipContent={placeholderText("The.image.must.be.of.pixel.612.x.315")}
                                tooltipId={"my-element1"}
                                tooltip={true}
                            />
                            <ReactTooltip anchorId="my-element1" />
                        </div> */}
                        <div className='col-12'>
                            <div className='col-12 mb-3 position-relative'>
                                <label
                                    className='form-label'>{getFormattedMessage("react-data-table.qr-code-name.label")}:  </label>
                                <span className='required' />
                                <input type='text' name='name' value={formValue.name === '' ? '' : placeholderText(formValue.name)}
                                    maxLength="50"
                                    placeholder={placeholderText("enter-title.placeholder")}
                                    className='form-control' autoComplete='off'
                                    onChange={(e) => onChangeInput(e)} disabled />
                            </div>
                            <div className='col-12 mb-5 position-relative'>
                                <label
                                    className='form-label'>{getFormattedMessage("globally.content.title")}: </label>
                                <span className='required' />
                                <span id="my-element3" className="ms-1 form-label" data-tooltip-content={placeholderText("you.can.enter.Max.100.characters.in.The.content")}>
                                    <FontAwesomeIcon icon={faQuestionCircle} />
                                </span>
                                <ReactTooltip anchorId="my-element3" />
                                <textarea rows={3} type='text' maxLength="100" name='content' value={formValue.content}
                                    placeholder={placeholderText("globally.enter-content.placeholder")}
                                    className='form-control' autoComplete='off'
                                    onChange={(e) => onChangeInput(e)}
                                />
                                <span
                                    className='text-danger d-block fw-400 fs-small mt-2'>{errors['content'] ? errors['content'] : null}</span>
                            </div>
                        </div>
                    </div>
                </Modal.Body>
            </Form>
            <ModelFooter onEditRecord={singleQrTypeContent} onSubmit={onSubmit}
                clearField={clearField} editDisabled={disabled} />
        </Modal >
    )

};

export default connect(null, { updateFrontQrCodeType })(EditFrontQRTypes);
