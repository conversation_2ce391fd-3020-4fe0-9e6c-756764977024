import React, { useState } from 'react';
import { connect } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar';
import FrontTabs from './frontTabs';
import { fetchFrontSubFeatures } from "../../../store/action/adminActions/frontcmsActions";
import EditFrontSubFeatures from './EditFrontSubFeatures';

const FrontSubFeatures = (props) => {
    const { fetchFrontSubFeatures, frontCms } = props;
    const [toggle, setToggle] = useState(false);
    const [subFeature, setSubFeature] = useState({})

    const handleClose = (item = null) => {
        setToggle(!toggle);
        setSubFeature(item)
    };


    const onChange = (filter) => {
        fetchFrontSubFeatures(filter, true);
    };

    const itemsValue = frontCms?.subFeatures && frontCms?.subFeatures?.length > 0 && frontCms?.subFeatures?.map(item => ({
        title: item?.title,
        image: item?.image_url,
        description: item?.description.split(" ").splice(0, 4).join(" ") + "...",
        id: item.id,
        item
    }));


    const columns = [
        {
            name: getFormattedMessage('globally.react-table.column.image.label'),
            selector: row => row.image,
            sortField: 'image',
            sortable: false,
            cell: row => {
                return <div className='me-2'>
                    {row.image ?
                        <img src={row.image} height='50' width='50' alt='User Image'
                            className='image image-circle image-mini image-effect' />
                        :
                        <span className='custom-user-avatar fs-5'>
                            {getAvatarName(row.title)}
                        </span>
                    }
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.input.name.label'),
            selector: row => row.title,
            sortable: true,
            sortField: 'title',
        },
        {
            name: getFormattedMessage('globally.input.description.lable'),
            selector: row => row.description,
            sortable: true,
            sortField: 'description',
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => {
                return <ActionButton item={row} goToEditProduct={handleClose} isEditMode={true}
                    isDeleteMode={false} />
            }
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('front.sub-features.title')} />
            <FrontTabs page={"subfeatures"} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} />
            <EditFrontSubFeatures title={getFormattedMessage("Edit.Sub.Feature")} handleClose={handleClose} show={toggle} singleSubFeature={subFeature} />
        </MasterLayout>
    )

};

const mapStateToProps = (state) => {
    const { totalRecord, isLoading, frontCms } = state;
    return { totalRecord, isLoading, frontCms }
};

export default connect(mapStateToProps, { fetchFrontSubFeatures })(FrontSubFeatures);
