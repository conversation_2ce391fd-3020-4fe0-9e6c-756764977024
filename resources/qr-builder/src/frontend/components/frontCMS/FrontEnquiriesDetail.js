import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Card } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate, placeholderText } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import Spinner from "../../../shared/components/loaders/Spinner";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import HeaderTitle from "../../../components/header/HeaderTitle";
import { fetchFrontEnquirie } from '../../../store/action/adminActions/frontcmsActions';
import moment from 'moment';
import RoundLoader from '../../../shared/components/loaders/RoundLoader';

const FrontEnquiriesDetail = (props) => {
    const { isLoading, fetchFrontEnquirie, frontCms } = props;
    const { id } = useParams();

    useEffect(() => {
        fetchFrontEnquirie(id);
    }, []);

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('front.enquiries.details.title')} />
            {<>
                <HeaderTitle title={getFormattedMessage('front.enquiries.details.title')} to='/app/admin/front-enquiries' />
                <div className='pt-5'>
                    <Card>
                        {/* <Card.Header as='h5'>{getFormattedMessage('plan.details.title')}</Card.Header> */}
                        <Card.Body className=''>
                            {
                                isLoading ? <RoundLoader /> :
                                    <div className="row">
                                        <div className="col-md-6 d-flex flex-column mb-md-10 mb-5">
                                            <label for="name" className="pb-2 fs-4 text-gray-600">{getFormattedMessage("react-data-table.name.column.title")}:</label>
                                            <span className="fs-4 text-gray-800">{frontCms?.allEnquiries?.name}</span>
                                        </div>
                                        <div className="col-md-6 d-flex flex-column mb-md-10 mb-5">
                                            <label for="name" className="pb-2 fs-4 text-gray-600">{getFormattedMessage("globally.input.email.label")}:</label>
                                            <span className="fs-4 text-gray-800">{frontCms?.allEnquiries?.email}</span>
                                        </div>
                                        <div className="col-md-6 d-flex flex-column mb-md-10 mb-5">
                                            <label for="name" className="pb-2 fs-4 text-gray-600">{getFormattedMessage("globally.input.subject.lable")}:</label>
                                            <span className="fs-4 text-gray-800">{frontCms?.allEnquiries?.subject}</span>
                                        </div>

                                        <div className="col-md-6 d-flex flex-column mb-md-10 mb-5">
                                            <label for="name" className="pb-2 fs-4 text-gray-600">{getFormattedMessage('globally.react-table.column.created-date.label')}:</label>
                                            <span className="fs-4 text-gray-800" title="23 Aug 2022">{getFormattedDate(frontCms?.allEnquiries?.created_at)} {moment(frontCms?.allEnquiries?.created_at).format("LT")}</span>
                                        </div>
                                        <div className="col-md-6 d-flex flex-column mb-md-10 mb-5">
                                            <label for="name" className="pb-2 fs-4 text-gray-600">{getFormattedMessage("globally.input.message.lable")}:</label>
                                            <span className="fs-4 text-gray-800">{frontCms?.allEnquiries?.message}</span>
                                        </div>
                                    </div>}
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </MasterLayout>
    )
};

const mapStateToProps = state => {
    const { isLoading, frontCms } = state;
    return { isLoading, frontCms }
};

export default connect(mapStateToProps, { fetchFrontEnquirie })(FrontEnquiriesDetail);
