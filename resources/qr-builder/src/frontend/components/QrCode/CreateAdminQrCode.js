import React from 'react';
import { connect, useSelector } from 'react-redux';
import MasterLayout from '../MasterLayout';
import HeaderTitle from "../../../components/header/HeaderTitle";
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import QrCodeForm from "./QrCodeForm"
import { Helmet } from 'react-helmet';

const CreateAdminQrCode = (props) => {
    const { addUser } = props;
    const {frontSettings} = useSelector(state => state)
    const addUserData = (formValue) => {
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText('qrcode.create.title') + ' | ' + frontSettings?.title}/>
            <HeaderTitle title={getFormattedMessage('qrcode.create.title')} to='/app/admin/qr-codes' />
            <QrCodeForm addUserData={addUserData} />
        </MasterLayout>
    );
}

export default connect(null, {})(CreateAdminQrCode);
