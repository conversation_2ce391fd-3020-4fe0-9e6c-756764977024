import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import DeleteUser from '../users/DeleteUser';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedDate, getFormattedOptions } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import DeleteAdminQrCode from './DeleteAdminQrCode';
import Dropdown from "react-bootstrap/Dropdown";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faDownload } from "@fortawesome/free-solid-svg-icons";
import { downloadExtentionOptions, typeOptions } from "../../../constants";
import { environment } from '../../../config/environment';
import ImageModal from '../../../shared/imageModal/ImageModal';
import { fetchAdminQrcodes } from '../../../store/action/adminActions/qrCodeAction';

const AdminQrCode = (props) => {
    const { qrcodes_admin, fetchAdminQrcodes, totalRecord, isLoading, allConfigData } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);
    const [showImageSlider, setShowImageSlider] = useState({
        display: false,
        src: '',
        name: ""
    })

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    const itemsValue = qrcodes_admin.length >= 0 && qrcodes_admin.map(qrcode => ({
        date: getFormattedDate(qrcode?.attributes?.created_at, "d-m-y"),
        time: moment(qrcode?.attributes?.created_at).format('LT'),
        name: qrcode.attributes.name,
        user_name: qrcode.attributes.user_name,
        email: qrcode.attributes.email,
        image: qrcode.attributes?.image,
        role_name: qrcode.attributes.role_name,
        id: qrcode.id,
        type_name: placeholderText(typeOptions.filter(d => d.id === qrcode?.attributes?.type)[0].name),
        user_email: qrcode.attributes.user_email,
        user_id: qrcode.attributes.user_id,
        user_image: qrcode.attributes.user_image,
    }));

    const onChange = (filter) => {
        fetchAdminQrcodes(filter, true);
    };

    const closeImageSlider = () => {
        setShowImageSlider({
            display: false,
            src: '',
            name: ""
        })
    }

    const downloadExtentionTypeOptions = getFormattedOptions(downloadExtentionOptions)
    const onDownloadClick = (e, url, name, ext) => {
        e.preventDefault();
        saveAs(url, `${name}.${ext}`);
    }

    const columns = [
        {
            name: getFormattedMessage('qrcode.lable'),
            cell: row => {
                return (
                    <div className='d-flex justify-content-center align-items-center'>
                        <div className='me-2'>
                            <img src={row?.image} height='50' width='50' alt='QR Code Image'
                                className='image image-mini image-effect cursor-pointer' onClick={() => setShowImageSlider({
                                    display: true,
                                    src: row?.image,
                                    name: row?.name
                                })} />
                        </div>
                        <Dropdown className="table-dropdown qrcodeDownload">
                            <Dropdown.Toggle className='text-primary hide-arrow bg-transparent border-0 p-0' id="dropdown-basic">
                                <FontAwesomeIcon icon={faDownload} className={'fs-3'} />
                            </Dropdown.Toggle>
                            <Dropdown.Menu className="w-100">
                                {
                                    downloadExtentionTypeOptions?.map((d) => {
                                        return <Dropdown.Item key={d.id} onClick={e => onDownloadClick(e, row.image, row.name, d.name)}>{d.name.toUpperCase()}</Dropdown.Item>
                                    })
                                }
                            </Dropdown.Menu>
                        </Dropdown>
                    </div>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.name.column.title'),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        <div className='text-decoration-none'>{row.name}</div>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage('react-data-table.qr-code-types.label'),
            selector: row => row.type_name,
            sortField: 'type_name',
            sortable: false,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        {row.type_name}
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('users.table.user-name.column.title'),
            selector: row => row.name,
            sortField: 'name',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`}>
                            {row.user_image ?
                                <img src={row.user_image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName(row.user_name)}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`} className='text-decoration-none'>{row.user_name}</Link>
                        <div>{row.user_email}</div>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.react-table.column.created-date.label'),
            selector: row => row.date,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div className='mb-1'>{row.time}</div>
                        <div>{row.date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton item={row} isEditMode={false}
                onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('qrcode.title')} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} ButtonValue={getFormattedMessage('qrcode.create.title')}
                to='#/app/admin/qr-codes/create'
                totalRows={totalRecord} isLoading={isLoading} />
            <DeleteAdminQrCode onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
            <ImageModal display={showImageSlider} closeSlider={closeImageSlider} onDownloadClick={onDownloadClick} />
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { qrcodes_admin, totalRecord, isLoading, allConfigData } = state;
    return { qrcodes_admin, totalRecord, isLoading, allConfigData }
};
export default connect(mapStateToProps, { fetchAdminQrcodes })(AdminQrCode);
