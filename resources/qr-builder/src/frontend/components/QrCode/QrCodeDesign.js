import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Tab, Tabs } from 'react-bootstrap-v5';
import QrCodeStyleOption from './QrCodeStyleOption';
import QrCodeColorOption from './QrCodeColorOption';
import QrCodeStyle from './qrCodeStyle/QrCodeStyle';
import QrCodeSizeOption from './QrCodeSizeOption';
import { getFormattedMessage } from '../../../shared/sharedMethod';


const QrCodeDesign = (props) => {
    const {
        qrDetails,
        setKey,
        setShowColor,
        showColor,
        setQrdetails,
        handleForgroundColor,
        gradientColor1,
        gradientColor2,
        forgroundGradientStyleTypeDefaultValue,
        forgroundGradientStyleTypeOptions,
        onForgroundGradientStyleChange,
        handleForgroundColor1,
        handleForgroundColor2,
        handleBackgroundColorChange,
        setIsTooltipActive,
        handleBGTransparencyChange,
        isTooltipActive,
        setIsLoading,
        customeEyeColorTypeDefaultValue,
        customeEyeColorTypeOptions,
        onChangeCustomeEyeColor,
        eyeInnerColorChnage,
        eyeOuterColorChange,
        errors,
        isLoading,
        setShowOptions,
        showOptions,
        onChangeInput,
        errorCorrectionTypeDefaultValue,
        errorCorrectionTypeOptions,
        onErrorCorrectionChange,
        enableEyesColor,
        setEnableEyesColor,
        onSubmit,
        isDark, enableColorHorizontal, setEnableColorHorizontal,
        enableColorRadial, setEnableColorRadial,
        defaulytColorType, setDefaultColorType
    } = props;
    const [keys, setKeys] = useState('Style');

    useEffect(() => {
        setIsLoading(true)
    }, [])
    return (
        <>
            <div className='qrcodeDesign d-flex w-100'>
                <div className='d-flex me-3 w-100 flex-lg-row flex-column'>
                    <Tabs defaultActiveKey='Style' id='uncontrolled-tab-example' onSelect={(k) => setKeys(k)}
                        className='mt-7 mb-5 hadow'>
                        <Tab eventKey='Style' title={getFormattedMessage("globally.heading.style.title")}
                            tabClassName='position-relative'>
                            <div className='w-100 mx-auto'>
                                {keys === 'Style' &&
                                    <QrCodeStyleOption qrDetails={qrDetails} setKey={setKey} setIsLoading={setIsLoading}
                                        onSubmit={onSubmit}
                                        setShowColor={setShowColor} errors={errors} showColor={showColor}
                                        setQrdetails={setQrdetails}
                                        handleForgroundColor={handleForgroundColor}
                                        forgroundGradientStyleTypeDefaultValue={forgroundGradientStyleTypeDefaultValue}
                                        forgroundGradientStyleTypeOptions={forgroundGradientStyleTypeOptions}
                                        onForgroundGradientStyleChange={onForgroundGradientStyleChange}
                                        handleForgroundColor1={handleForgroundColor1}
                                        handleForgroundColor2={handleForgroundColor2}
                                        handleBackgroundColorChange={handleBackgroundColorChange}
                                        setIsTooltipActive={setIsTooltipActive}
                                        handleBGTransparencyChange={handleBGTransparencyChange}
                                        isTooltipActive={isTooltipActive}
                                        customeEyeColorTypeDefaultValue={customeEyeColorTypeDefaultValue}
                                        customeEyeColorTypeOptions={customeEyeColorTypeOptions}
                                        onChangeCustomeEyeColor={onChangeCustomeEyeColor}
                                        eyeInnerColorChnage={eyeInnerColorChnage}
                                        eyeOuterColorChange={eyeOuterColorChange} isDark={isDark}
                                    />
                                }
                            </div>
                        </Tab>
                        <Tab eventKey='Color' title={getFormattedMessage("globally.input.color.lable")}
                            tabClassName='position-relative'>
                            <div className='w-100 mx-auto'>
                                {keys === 'Color' &&
                                    <QrCodeColorOption qrDetails={qrDetails} setIsLoading={setIsLoading}
                                        enableEyesColor={enableEyesColor}
                                        setEnableEyesColor={setEnableEyesColor} onSubmit={onSubmit}
                                        setShowColor={setShowColor} errors={errors}
                                        gradientColor1={gradientColor1} gradientColor2={gradientColor2}
                                        showColor={showColor} setQrdetails={setQrdetails}
                                        handleForgroundColor={handleForgroundColor}
                                        forgroundGradientStyleTypeDefaultValue={forgroundGradientStyleTypeDefaultValue}
                                        forgroundGradientStyleTypeOptions={forgroundGradientStyleTypeOptions}
                                        onForgroundGradientStyleChange={onForgroundGradientStyleChange}
                                        handleForgroundColor1={handleForgroundColor1}
                                        handleForgroundColor2={handleForgroundColor2}
                                        handleBackgroundColorChange={handleBackgroundColorChange}
                                        setIsTooltipActive={setIsTooltipActive}
                                        handleBGTransparencyChange={handleBGTransparencyChange}
                                        isTooltipActive={isTooltipActive}
                                        customeEyeColorTypeDefaultValue={customeEyeColorTypeDefaultValue}
                                        customeEyeColorTypeOptions={customeEyeColorTypeOptions}
                                        onChangeCustomeEyeColor={onChangeCustomeEyeColor}
                                        eyeInnerColorChnage={eyeInnerColorChnage}
                                        eyeOuterColorChange={eyeOuterColorChange} setKey={setKey}
                                        enableColorHorizontal={enableColorHorizontal} setEnableColorHorizontal={setEnableColorHorizontal}
                                        enableColorRadial={enableColorRadial} setEnableColorRadial={setEnableColorRadial}
                                        defaulytColorType={defaulytColorType} setDefaultColorType={setDefaultColorType}
                                    />}
                            </div>
                        </Tab>
                        <Tab eventKey='Size' title={getFormattedMessage("globally.input.size.lable")}
                            tabClassName='position-relative '>
                            <div className='w-100 mx-auto'>
                                {keys === 'Size' &&
                                    <QrCodeSizeOption qrDetails={qrDetails} onSubmit={onSubmit} setKey={setKey}
                                        setIsLoading={setIsLoading} setShowOptions={setShowOptions}
                                        showOptions={showOptions} onChangeInput={onChangeInput}
                                        errors={errors}
                                        errorCorrectionTypeDefaultValue={errorCorrectionTypeDefaultValue}
                                        errorCorrectionTypeOptions={errorCorrectionTypeOptions}
                                        onErrorCorrectionChange={onErrorCorrectionChange} />}
                            </div>
                        </Tab>
                    </Tabs>
                </div>
                <div className='col-md-6 col-12 mt-md-0 mt-5 p-2 qr_code-card position-sticky top-10 card '>
                    <QrCodeStyle dotsOptionsType={qrDetails.style} qrUrl={qrDetails} data={qrDetails}
                        isLoading={isLoading} />
                </div>
            </div>
        </>
    )
}

export default QrCodeDesign
