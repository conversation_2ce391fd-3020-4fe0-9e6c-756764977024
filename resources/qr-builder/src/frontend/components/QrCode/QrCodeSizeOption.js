import { faQuestionCircle, faWrench } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useEffect } from 'react'
import { Button, InputGroup } from 'react-bootstrap-v5';
import { getFormattedMessage, numValidate, placeholderText } from '../../../shared/sharedMethod';
import { Tooltip as ReactTooltip } from 'react-tooltip'
import ReactSelect from '../../../shared/select/reactSelect';

const QrCodeSizeOption = (props) => {
    const { qrDetails, setShowOptions, showOptions, setKey, onChangeInput, errors, errorCorrectionTypeDefaultValue, errorCorrectionTypeOptions, onErrorCorrectionChange, setIsLoading, onSubmit } = props;
    useEffect(() => {
        setIsLoading(true)
    }, [])
    return (
        <div className='card ms-3'>
            <div className='card-body'>
                <div className='row'>
                    {/* Branding */}
                    <div className='col-12 mt-3'>
                        <div className={"d-flex justify-content-center flex-wrap"}>
                            {/* Size */}
                            <div className='col-12 mb-0'>
                                <InputGroup className='flex-nowrap mb-0 dropdown-side-btn'>
                                    <div className='col-12 pt-3 mb-0 position-relative'>
                                        <label htmlFor="exampleInputEmail1" className="form-label">
                                            {getFormattedMessage("globally.input.size.lable")}:
                                        </label> <span id="my-element1" className="form-label" data-tooltip-content={placeholderText("Minimum.100px.and.Maximum.400px")}>
                                            <FontAwesomeIcon icon={faQuestionCircle} />
                                        </span>
                                        <ReactTooltip anchorId="my-element1" />
                                        <div className="col-12 d-flex justify-content-center align-items-center">
                                            <div className="col-10">
                                                <input type='text' name='size' value={qrDetails.size}
                                                    className='form-control py-3' autoFocus={true}
                                                    onKeyPress={(event) => numValidate(event)}
                                                    placeholder={placeholderText("globally.placeholder.size.label")}
                                                    onChange={(e) => onChangeInput(e)} />
                                            </div>
                                            <div className="col-2 py-3 text-white px-display" >
                                                {getFormattedMessage("qrcode.btn.px.label")}
                                            </div>
                                        </div>
                                        <span className='text-danger d-block fw-400 fs-small'>
                                            {errors['size'] ? errors['size'] : null}
                                        </span>
                                    </div>
                                </InputGroup>
                            </div>

                            {/* Margin size */}
                            {/* <div className='d-flex mb-3 pt-3 justify-content-between flex-wrap w-100 qr_style_btn position-relative' >
                                <label htmlFor="exampleInputEmail1" className="form-label col-12">
                                    {getFormattedMessage("globally.input.margin-size.lable")}: <span id="my-element" className="form-label" data-tooltip-content={placeholderText("Minimum.10px.and.Maximum.50px")}>
                                        <FontAwesomeIcon icon={faQuestionCircle} />
                                    </span>
                                </label>
                                <ReactTooltip anchorId="my-element" />
                                <input type='text' name='margin_size' value={qrDetails.margin_size}
                                    className='form-control'
                                    placeholder={placeholderText("globally.placeholder.margin-size.label")}
                                    onKeyPress={(event) => numValidate(event)}
                                    onChange={(e) => onChangeInput(e)} />
                            </div> */}

                            {/* Error correction capability */}
                            {/* <div className="d-flex justify-content-between flex-wrap w-100 qr_style_btn">
                                <ReactSelect title={getFormattedMessage('globally.input.error-correction-capability.label')}
                                    value={qrDetails.error_correction} errors={errors['payment_type']}
                                    defaultValue={errorCorrectionTypeDefaultValue[0]}
                                    multiLanguageOption={errorCorrectionTypeOptions}
                                    onChange={onErrorCorrectionChange}
                                    isRequired
                                /> */}
                            {/* </div> */}
                        </div>
                    </div>
                </div>
                <div className='row'>
                    <div className='col-12 mt-3 d-flex justify-content-between'>
                        <Button variant="light" className='btn btn-secondary'
                            onClick={() => setKey('basic-details')}
                        > {getFormattedMessage("globally.back-btn")}
                        </Button>
                        <Button variant="light" className='btn btn-primary'
                            onClick={(event) => onSubmit(event)}
                        > {getFormattedMessage("globally.submit-btn")}
                        </Button>
                    </div>

                </div>
            </div>
        </div>
    )
}

export default QrCodeSizeOption
