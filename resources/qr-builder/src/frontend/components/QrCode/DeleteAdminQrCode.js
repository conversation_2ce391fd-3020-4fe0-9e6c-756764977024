import React from 'react';
import { connect } from 'react-redux';
import { deleteAdminQrcode } from '../../../store/action/qrCodeAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';

const DeleteAdminQrCode = (props) => {
    const { deleteAdminQrcode, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deleteAdminQrcode(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage('qrcode.lable')} />}
        </div>
    )
};

export default connect(null, { deleteAdminQrcode })(DeleteAdminQrCode);
