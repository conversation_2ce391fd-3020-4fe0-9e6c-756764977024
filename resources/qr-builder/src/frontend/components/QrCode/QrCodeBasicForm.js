import React from 'react'
import { getFormattedMessage, numValidate, placeholderText } from '../../../shared/sharedMethod'
import { Button, Dropdown, DropdownButton, Form, InputGroup } from 'react-bootstrap-v5'
import ReactSelect from '../../../shared/select/reactSelect'
import PasswordHandling from '../../../shared/password-handler/PasswordHandling'
import ReactDatePicker from '../../../shared/datepicker/ReactDatePicker'
import CountryList from 'country-list-with-dial-code-and-flag'

const QrCodeBasicForm = (props) => {
    const {
        qrDetails,
        onChangeInput,
        errors,
        projectTypeDefaultValue,
        projectTypeOptions,
        onProjectChange,
        countryCode,
        onChangeCountryCode,
        phoneNumber,
        setPhoneNumber,
        setQrdetails,
        wPhoneNumber,
        setWPhoneNumber,
        setErrors,
        encryptionTypeDefaultValue,
        encryptionTypeOptions,
        onEncChange,
        wifiHiddenTypeDefaultValue,
        wifiHiddenTypeOptions,
        onWifiHiddenChange,
        onChangeStartDate,
        onChangeEndDate,
        cryptoCoinTypeDefaultValue,
        cryptoCoinTypeOptions,
        onCryptoChange,
        phone,
        setPhone,
        onChangeDOB,
        payPalTypeDefaultValue,
        payPalTypeOptions,
        onPayPalTypeChange,
        currencyCodeTypeDefaultValue,
        currencyCodeTypeOptions,
        onCurrencyCodeTypeChange,
        setKey,
        getTitle,
        refactor, qrCodeType
    } = props
    const flagData = CountryList.getAll()

    const countryFlag = flagData?.filter(d => d?.dial_code === countryCode)

    return (
        <div className='card'>
            <div className='card-body'>
                <Form>
                    <div className='row'>
                        <div className='col-12 '>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("react-data-table.qr-code-name.label")}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={qrDetails.name}
                                className='form-control'
                                placeholder={placeholderText("globally.enter.qr.code.name.placeholder")}
                                autoFocus={true}
                                onChange={(e) => onChangeInput(e)} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['name'] ? errors['name'] : null}</span>
                        </div>
                        <div className='col-12 mb-3'>
                            <ReactSelect title={getFormattedMessage('globally.input.project.lable')}
                                value={qrDetails.project_id} errors={errors['payment_type']}
                                defaultValue={projectTypeDefaultValue[0]}
                                multiLanguageOption={projectTypeOptions}
                                onChange={onProjectChange}
                                isRequired
                            />
                        </div>
                        {
                            qrDetails.type.value === 1
                            &&
                            <div className='col-12 mb-3'>
                                <label htmlFor="exampleInputEmail1" className="form-label">
                                    {getFormattedMessage("globally.input.text-content.lable")}:<span
                                        className="required" />
                                </label>
                                <textarea type='text' name='text_content' value={qrDetails.text_content}
                                    className='form-control'
                                    placeholder={placeholderText("globally.input.text-content.placeholder")}
                                    onChange={(e) => onChangeInput(e)} />
                                <span
                                    className='text-danger d-block fw-400 fs-small mt-2'>{errors['text_content'] ? errors['text_content'] : null}</span>
                            </div>
                        }
                        {
                            qrDetails.type.value === 2
                            &&
                            <div className='col-12 mb-3'>
                                <label htmlFor="exampleInputEmail1" className="form-label">
                                    {getFormattedMessage("globally.input.url.lable")}:<span className="required" />
                                </label>
                                <input type='text' name='url' value={qrDetails.url}
                                    className='form-control'
                                    placeholder={placeholderText("globally.input.url.placeholder")}
                                    onChange={(e) => onChangeInput(e)} />
                                <span
                                    className='text-danger d-block fw-400 fs-small mt-2'>{errors['url'] ? errors['url'] : null}</span>
                            </div>
                        }
                        {
                            qrDetails.type.value === 3
                                ?
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.phone-number.lable")}:<span
                                            className="required" />
                                    </label>
                                    <InputGroup className="country_code_dropdown">
                                        <DropdownButton
                                            variant="primary"
                                            title={<span>
                                                <img className="thumbnail-image"
                                                    src={`https://flagcdn.com/16x12/${countryFlag[0]?.data?.code?.toLowerCase()}.png`}
                                                    srcSet={`https://flagcdn.com/32x24/${countryFlag[0]?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${countryFlag[0]?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12}
                                                    alt={countryFlag[0]?.data?.code}
                                                /> {countryCode}
                                            </span>}
                                            id="input-group-dropdown-1"
                                        >
                                            {
                                                flagData.map((d, i) => {
                                                    return <Dropdown.Item key={i + 1} onClick={() => onChangeCountryCode(d.data.dial_code, "phone_number")} value={d.data.dial_code}>
                                                        <img src={`https://flagcdn.com/16x12/${d?.data?.code?.toLowerCase()}.png`} srcSet={`https://flagcdn.com/32x24/${d?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${d?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12} alt="South Africa"
                                                        /> {d.data.dial_code}</Dropdown.Item>
                                                })
                                            }
                                        </DropdownButton>
                                        <Form.Control
                                            type='text' name='phone_number' value={phoneNumber}
                                            className='form-control'
                                            placeholder={placeholderText("globally.input.phone-number.placeholder.label")}
                                            onKeyPress={(event) => numValidate(event)}
                                            onChange={(e) => {
                                                setPhoneNumber(e.target.value.replace(/ /g, ""))
                                                setQrdetails(inputs => ({
                                                    ...inputs,
                                                    [e.target.name]: `${countryCode} ${e.target.value.replace(/ /g, "")}`
                                                }))
                                            }}
                                        />
                                    </InputGroup>
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['phone_number'] ? errors['phone_number'] : null}</span>
                                </div>
                                :
                                null
                        }
                        {
                            qrDetails.type.value === 4
                            &&
                            <>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.phone-number.lable")}:<span
                                            className="required" />
                                    </label>
                                    <InputGroup className="country_code_dropdown">
                                        <DropdownButton
                                            variant="primary"
                                            title={<span>
                                                <img className="thumbnail-image"
                                                    src={`https://flagcdn.com/16x12/${countryFlag[0]?.data?.code?.toLowerCase()}.png`}
                                                    srcSet={`https://flagcdn.com/32x24/${countryFlag[0]?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${countryFlag[0]?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12}
                                                    alt={countryFlag[0]?.data?.code}
                                                /> {countryCode}
                                            </span>}
                                            id="input-group-dropdown-1"
                                        >
                                            {
                                                flagData.map((d, i) => {
                                                    return <Dropdown.Item key={i + 1} onClick={() => onChangeCountryCode(d.data.dial_code, "phone_number")} value={d.data.dial_code}>
                                                        <img src={`https://flagcdn.com/16x12/${d?.data?.code?.toLowerCase()}.png`} srcSet={`https://flagcdn.com/32x24/${d?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${d?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12} alt="South Africa"
                                                        /> {d.data.dial_code}</Dropdown.Item>
                                                })
                                            }
                                        </DropdownButton>
                                        <Form.Control
                                            type='text' name='phone_number' value={phoneNumber}
                                            className='form-control'
                                            onKeyPress={(event) => numValidate(event)}
                                            placeholder={placeholderText("globally.input.phone-number.placeholder.label")}
                                            onChange={(e) => {
                                                setPhoneNumber(e.target.value.replace(/ /g, ""))
                                                setQrdetails(inputs => ({
                                                    ...inputs,
                                                    [e.target.name]: `${countryCode} ${e.target.value.replace(/ /g, "")}`
                                                }))
                                            }}
                                        />
                                    </InputGroup>
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['phone_number'] ? errors['phone_number'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.message.lable")}:
                                    </label>
                                    <textarea type='text' name='prefilled_message' value={qrDetails.prefilled_message}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.prefilled-message.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                </div>
                            </>
                        }
                        {
                            qrDetails.type.value === 5
                            &&
                            <>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.email.label")}:<span className="required" />
                                    </label>
                                    <input type='email' name='email' value={qrDetails.email}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.email.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['email'] ? errors['email'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.prefilled-subject.lable")}:
                                    </label>
                                    <input type='email' name='prefilled_subject' value={qrDetails.prefilled_subject}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.prefilled-subject.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.prefilled-message.lable")}:
                                    </label>
                                    <textarea type='text' name='prefilled_message' value={qrDetails.prefilled_message}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.prefilled-message.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                </div>
                            </>
                        }
                        {
                            qrDetails.type.value === 6
                            &&
                            <>
                                <div className='col-12'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.phone-number.lable")}:<span
                                            className="required" />
                                    </label>
                                    <InputGroup className="country_code_dropdown">
                                        <DropdownButton
                                            variant="primary"
                                            title={<span>
                                                <img className="thumbnail-image"
                                                    src={`https://flagcdn.com/16x12/${countryFlag[0]?.data?.code?.toLowerCase()}.png`}
                                                    srcSet={`https://flagcdn.com/32x24/${countryFlag[0]?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${countryFlag[0]?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12}
                                                    alt={countryFlag[0]?.data?.code}
                                                /> {countryCode}
                                            </span>}
                                            id="input-group-dropdown-1"
                                        >
                                            {
                                                flagData.map((d, i) => {
                                                    return <Dropdown.Item key={i + 1} onClick={() => onChangeCountryCode(d.data.dial_code, "wp_phone_number")} value={d.data.dial_code}>
                                                        <img src={`https://flagcdn.com/16x12/${d?.data?.code?.toLowerCase()}.png`} srcSet={`https://flagcdn.com/32x24/${d?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${d?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12} alt="South Africa"
                                                        /> {d.data.dial_code}</Dropdown.Item>
                                                })
                                            }
                                        </DropdownButton>
                                        <Form.Control
                                            type='text' name='wp_phone_number' value={wPhoneNumber}
                                            className='form-control'
                                            onKeyPress={(event) => numValidate(event)}
                                            placeholder={placeholderText("globally.input.phone-number.placeholder.label")}
                                            onChange={(e) => {
                                                setWPhoneNumber(e.target.value.replace(/ /g, ""))
                                                setQrdetails(inputs => ({
                                                    ...inputs,
                                                    [e.target.name]: `${countryCode} ${e.target.value.replace(/ /g, "")}`
                                                }))
                                                setErrors("")
                                            }}
                                        />

                                    </InputGroup>
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['wp_phone_number'] ? errors['wp_phone_number'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.whatsapp.message.lable")}:
                                    </label>
                                    <textarea type='text' name='prefilled_message' value={qrDetails.prefilled_message}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.prefilled-message.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                </div>
                            </>
                        }
                        {
                            qrDetails.type.value === 7
                            &&
                            <div className='col-12 mb-3'>
                                <label htmlFor="exampleInputEmail1" className="form-label">
                                    {getFormattedMessage("globally.input.phone-number.label")} or {getFormattedMessage("globally.input.email.label")}:<span
                                        className="required" />
                                </label>
                                <input type='text' name='phone_or_email' value={qrDetails.phone_or_email}
                                    className='form-control'
                                    placeholder={placeholderText("globally.input.phone-or-email.placeholder")}
                                    onChange={(e) => onChangeInput(e)} />
                                <span
                                    className='text-danger d-block fw-400 fs-small mt-2'>{errors['phone_or_email'] ? errors['phone_or_email'] : null}</span>
                            </div>
                        }
                        {
                            qrDetails.type.value === 8
                            &&
                            <>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.latitude.lable")}:<span
                                            className="required" />
                                    </label>
                                    <input type='number' name='latitude' value={qrDetails.latitude}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.latitude.placeholder')}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['latitude'] ? errors['latitude'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.longitude.lable")}:<span
                                            className="required" />
                                    </label>
                                    <input type='number' name='longitude' value={qrDetails.longitude}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.longitude.placeholder')}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['longitude'] ? errors['longitude'] : null}</span>
                                </div>
                            </>
                        }
                        {
                            qrDetails.type.value === 9
                            &&
                            <>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.wifi-ssid.lable")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='wifi_name' value={qrDetails.wifi_name}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.wifi-name.placeholder')}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['wifi_name'] ? errors['wifi_name'] : null}</span>
                                </div>
                                <ReactSelect title={getFormattedMessage('globally.input.encryption.lable')}
                                    name='encryption'
                                    value={qrDetails.encryption}
                                    errors={errors['type']}
                                    defaultValue={encryptionTypeDefaultValue[0]}
                                    multiLanguageOption={encryptionTypeOptions}
                                    onChange={onEncChange}
                                />
                                {
                                    qrDetails.encryption.value !== 3
                                    &&
                                    <div className='col-12 mb-md-0 mt-3'>
                                        <PasswordHandling onChangeInput={onChangeInput} name={"password"}
                                            passwordValue={qrDetails.password} IsRequired={true} />
                                        <span
                                            className='text-danger d-block fw-400 fs-small mt-2'>{errors['password'] ? errors['password'] : null}</span>
                                    </div>
                                }
                                <div className={`mb-3 ${qrDetails.encryption.value === 3 && "my-3"}`}>
                                    <ReactSelect title={getFormattedMessage('globally.input.wifi-hidden.lable')}
                                        value={qrDetails.wifi_is_hidden}
                                        defaultValue={wifiHiddenTypeDefaultValue[0]}
                                        multiLanguageOption={wifiHiddenTypeOptions}
                                        onChange={onWifiHiddenChange}
                                    />
                                </div>
                            </>
                        }
                        {
                            qrDetails.type.value === 10
                            &&
                            <>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.event-name.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='event_name' value={qrDetails.event_name}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.event-name.placeholder')}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['event_name'] ? errors['event_name'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.geo-location.lable")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='geo_location' value={qrDetails.geo_location}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.deo-location-name.placeholder')}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['geo_location'] ? errors['geo_location'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.event-url.lable")}:
                                    </label>
                                    <input type='text' name='event_url' value={qrDetails.event_url}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.event-url.placeholder')}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['event_url'] ? errors['event_url'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.notes.label")}:
                                    </label>
                                    <textarea type='text' name='notes' value={qrDetails.notes}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.notes.placeholder.label')}
                                        onChange={(e) => onChangeInput(e)} />
                                </div>
                                <div className='position-relative col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.start-date.lable")}:<span
                                            className="required" />
                                    </label>
                                    <ReactDatePicker
                                        onChangeDate={onChangeStartDate}
                                        newStartDate={qrDetails.starts_on || new Date}
                                        isShowTimeSelect={true}
                                        minDate={new Date}
                                        selectYear={true}
                                    />
                                </div>
                                <div className='position-relative col-12'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.ends-date.lable")}:<span
                                            className="required" />
                                    </label>
                                    <ReactDatePicker
                                        onChangeDate={onChangeEndDate}
                                        newStartDate={qrDetails.ends_on}
                                        isShowTimeSelect={true}
                                        minDate={qrDetails.starts_on}
                                        selectYear={true}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['ends_on'] ? errors['ends_on'] : null}</span>
                                </div>

                                {/* <ReactSelect
                                        title={getFormattedMessage('globally.input.timezone.lable')}
                                        defaultValue={qrDetails.timezone}
                                        errors={errors['brand_id']}
                                        data={timeZonArr}
                                        onChange={onTimeZoneChange}
                                        lassName='position-relative'
                                        value={qrDetails.timezone}
                                    /> */}
                            </>
                        }
                        {
                            qrDetails.type.value === 11
                            &&
                            <>
                                <ReactSelect title={getFormattedMessage('globally.input.coin.lable')}
                                    value={qrDetails.coin} errors={errors['payment_type']}
                                    defaultValue={cryptoCoinTypeDefaultValue[0]}
                                    multiLanguageOption={cryptoCoinTypeOptions}
                                    onChange={onCryptoChange}
                                />
                                <div className='col-12  mb-md-0 my-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.currency.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='address' value={qrDetails.address}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.crypto-currency-address.placeholder')}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['address'] ? errors['address'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.amount.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='amount' value={qrDetails.amount}
                                        className='form-control'
                                        placeholder={placeholderText('globally.input.amount.placeholder')}
                                        onKeyPress={(event) => numValidate(event)}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['amount'] ? errors['amount'] : null}</span>
                                </div>

                            </>
                        }
                        {
                            qrDetails.type.value === 12
                            &&
                            <>
                                <div className='col-md-6 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.first-name.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='first_name' value={qrDetails.first_name}
                                        className='form-control'
                                        placeholder={placeholderText("user.input.first-name.placeholder.label")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['first_name'] ? errors['first_name'] : null}</span>
                                </div>
                                <div className='col-md-6 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.last-name.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='last_name' value={qrDetails.last_name}
                                        className='form-control'
                                        placeholder={placeholderText("user.input.last-name.placeholder.label")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['last_name'] ? errors['last_name'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.email.label")}:<span className="required" />
                                    </label>
                                    <input type='email' name='email' value={qrDetails.email}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.email.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['email'] ? errors['email'] : null}</span>
                                </div>
                                <div className='col-12mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.phone-number.lable")}:<span
                                            className="required" />
                                    </label>
                                    <InputGroup className="country_code_dropdown">
                                        <DropdownButton
                                            variant="primary"
                                            title={<span>
                                                <img className="thumbnail-image"
                                                    src={`https://flagcdn.com/16x12/${countryFlag[0]?.data?.code?.toLowerCase()}.png`}
                                                    srcSet={`https://flagcdn.com/32x24/${countryFlag[0]?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${countryFlag[0]?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12}
                                                    alt={countryFlag[0]?.data?.code}
                                                /> {countryCode}
                                            </span>}
                                            id="input-group-dropdown-1"
                                        >
                                            {
                                                flagData.map((d, i) => {
                                                    return <Dropdown.Item key={i + 1} onClick={() => onChangeCountryCode(d.data.dial_code, "phone")} value={d.data.dial_code}>
                                                        <img src={`https://flagcdn.com/16x12/${d?.data?.code?.toLowerCase()}.png`} srcSet={`https://flagcdn.com/32x24/${d?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${d?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12} alt="South Africa"
                                                        /> {d.data.dial_code}</Dropdown.Item>
                                                })
                                            }
                                        </DropdownButton>
                                        <Form.Control
                                            type='text' name='phone' value={phone}
                                            className='form-control'
                                            onKeyPress={(event) => numValidate(event)}
                                            placeholder={placeholderText("globally.input.phone-number.placeholder.label")}
                                            onChange={(e) => {
                                                setPhone(e.target.value.replace(/ /g, ""))
                                                setQrdetails(inputs => ({
                                                    ...inputs,
                                                    [e.target.name]: `${countryCode} ${e.target.value.replace(/ /g, "")}`
                                                }))
                                                setErrors("")
                                            }}
                                        />
                                    </InputGroup>
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['phone'] ? errors['phone'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 my-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("qrcode.website-url.title")}:
                                    </label>
                                    <input type='text' name='website_url' value={qrDetails.website_url}
                                        className='form-control'
                                        placeholder={placeholderText("globally.placeholder.website-url.label")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['website_url'] ? errors['website_url'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.company.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='company' value={qrDetails.company}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.company.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['company'] ? errors['company'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.job-title.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='job_title' value={qrDetails.job_title}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.job.title.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['job_title'] ? errors['job_title'] : null}</span>
                                </div>
                                <div className='col-12 mb-3 subscription-plan-date-picker'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.birth-date.label")}:<span
                                            className="required" />
                                    </label>
                                    <ReactDatePicker
                                        onChangeDate={onChangeDOB}
                                        newStartDate={qrDetails.birthday}
                                        maxDate={new Date(new Date().getTime() - 24 * 60 * 60 * 1000)}
                                        selectYear={true}
                                    />
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("qrcode.paypal.vcard.title")} {getFormattedMessage("globally.input.address.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='street_address' value={qrDetails.street_address}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.street.address.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['street_address'] ? errors['street_address'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.country.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='country' value={qrDetails.country}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.country.placeholder.label")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['country'] ? errors['country'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.state.label")}:<span className="required" />
                                    </label>
                                    <input type='text' name='region' value={qrDetails.region}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.region.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['region'] ? errors['region'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.city.label")}:<span className="required" />
                                    </label>
                                    <input type='text' name='city' value={qrDetails.city}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.city.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['city'] ? errors['city'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.zip.label")}:<span className="required" />
                                    </label>
                                    <input type='text' name='zip' value={qrDetails.zip}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.zip.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['zip'] ? errors['zip'] : null}</span>
                                </div>
                            </>
                        }
                        {
                            qrDetails.type.value === 13
                            &&
                            <>
                                <ReactSelect title={getFormattedMessage('globally.type.label')}
                                    value={qrDetails.paypal_type} errors={errors['payment_type']}
                                    defaultValue={payPalTypeDefaultValue[0]}
                                    multiLanguageOption={payPalTypeOptions}
                                    onChange={onPayPalTypeChange}
                                />
                                <div className='col-12 mb-md-0 my-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        PayPal {getFormattedMessage("globally.input.email.label")}:<span
                                            className="required" />
                                    </label>
                                    <input type='email' name='paypal_email' value={qrDetails.paypal_email}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.paypal.email.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['paypal_email'] ? errors['paypal_email'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage(getTitle)}:<span className="required" />
                                    </label>
                                    <input type='text' name='product_title' value={qrDetails.product_title}
                                        className='form-control'
                                        placeholder={getTitle === "globally.product.code.title" ? placeholderText("globally.input.product.code.placeholder") : placeholderText("globally.input.product.name.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['product_title'] ? errors['product_title'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <ReactSelect title={getFormattedMessage('globally.input.currency.code.label')}
                                        value={qrDetails.currency_code} errors={errors['currency_code']}
                                        defaultValue={currencyCodeTypeDefaultValue[0]}
                                        multiLanguageOption={currencyCodeTypeOptions}
                                        onChange={onCurrencyCodeTypeChange}
                                    />
                                </div>
                                <div className='col-12 mb-md-0 my-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.price.label")}:<span className="required" />
                                    </label>
                                    <input type='text' name='price' value={qrDetails.price}
                                        className='form-control'
                                        placeholder={placeholderText("user.input.price.placeholder.label")}
                                        onKeyPress={(event) => numValidate(event)}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['price'] ? errors['price'] : null}</span>
                                </div>
                                <div className='col-12 mb-md-0 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage('qrcode.paypal.thank-you-url.title')} {getFormattedMessage("globally.input.url.lable")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='thanks_url' value={qrDetails.thanks_url}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.thank-url.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['thanks_url'] ? errors['thanks_url'] : null}</span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage('qrcode.paypal.cancel-url.title')} {getFormattedMessage("globally.input.url.lable")}:<span
                                            className="required" />
                                    </label>
                                    <input type='text' name='cancel_url' value={qrDetails.cancel_url}
                                        className='form-control'
                                        placeholder={placeholderText("globally.input.cancel-url.placeholder")}
                                        onChange={(e) => onChangeInput(e)} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>{errors['cancel_url'] ? errors['cancel_url'] : null}</span>
                                </div>
                            </>
                        }
                    </div>
                    <div className='row'>
                        <div className='col-12 d-flex justify-content-between'>
                            <Button variant="light" className='btn btn-secondary'
                                onClick={() => {
                                    setKey('Type')
                                    setQrdetails(refactor(qrDetails, qrCodeType))
                                }}
                            > {getFormattedMessage("globally.back-btn")}
                            </Button>
                            <Button variant="light" className='btn btn-primary'
                                onClick={() => setKey('design')}
                            > {getFormattedMessage("globally.next-btn")}
                            </Button>
                        </div>

                    </div>
                </Form>
            </div>

        </div>
    )
}

export default QrCodeBasicForm
