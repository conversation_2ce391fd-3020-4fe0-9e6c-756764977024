import React from "react";
import MasterLayout from "../MasterLayout";
import TabTitle from "../../../shared/tab-title/TabTitle";
import { placeholderText } from "../../../shared/sharedMethod";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import Widget from "./Widget";
import RecentUsers from "./RecentUsers";
import ThisWeekEarning from "./ThisWeekEarning";
import TopVisitedURL from "./TopVisitedURL";

const Dashboard = () => {
    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText("dashboard.title")} />
            <Widget />
            <div className="row w-100 charts_style m-0">
                <div className="col-xl-8 col-12 mb-xl-0 mb-4 ps-0 pe-xl-2 pe-0">
                    <ThisWeekEarning />
                </div>
                <div className="col-xl-4 col-12 m-0 pe-0 ps-xl-2 ps-0">
                    <TopVisitedURL />
                </div>
            </div>
            <RecentUsers />
        </MasterLayout>
    );
};

export default Dashboard;
