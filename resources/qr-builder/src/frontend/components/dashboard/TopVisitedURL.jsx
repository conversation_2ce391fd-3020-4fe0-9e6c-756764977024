import React, { useEffect } from "react";
import ReactECharts from "echarts-for-react";
import { useSelector, useDispatch } from "react-redux";
import { Card } from "react-bootstrap";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import { getTopVisitedLinkAdminAction } from "../../../store/action/adminActions/dashboardAction";

const TopVisitedURL = () => {
    const { theme, topVisitedLinkAdmin } = useSelector((state) => state);
    const dispatch = useDispatch();
    const monthNames = [
        getFormattedMessage("month.name.jan.title"),
        getFormattedMessage("month.name.feb.title"),
        getFormattedMessage("month.name.mar.title"),
        getFormattedMessage("month.name.apr.title"),
        getFormattedMessage("month.name.may.title"),
        getFormattedMessage("month.name.jun.title"),
        getFormattedMessage("month.name.jul.title"),
        getFormattedMessage("month.name.aug.title"),
        getFormattedMessage("month.name.sep.title"),
        getFormattedMessage("month.name.act.title"),
        getFormattedMessage("month.name.nov.title"),
        getFormattedMessage("month.name.dec.title"),
    ];

    const data =
        topVisitedLinkAdmin?.length > 0
            ? topVisitedLinkAdmin?.map((link) => {
                  return {
                      name: link?.name,
                      value: link?.count,
                  };
              })
            : [];

    const option = {
        title: {
            show: false,
        },
        tooltip: {
            trigger: "item",
        },
        legend: {
            show: false,
        },
        series: [
            {
                name: "",
                type: "pie",
                radius: "90%",
                data: data,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: "rgba(0, 0, 0, 0.5)",
                    },
                },
                label: {
                    show: false,
                },
            },
        ],
    };

    useEffect(() => {
        dispatch(getTopVisitedLinkAdminAction());
    }, []);

    return (
        <>
            <Card className="shadow h-100">
                <Card.Header className="pb-0 px-10">
                    <h5 className="mb-0 mx-auto">
                        {getFormattedMessage(
                            "dashboard.5mostvisitedlinksof.title"
                        )}{" "}
                        <strong>({monthNames[new Date().getMonth()]})</strong>
                    </h5>
                </Card.Header>
                <Card.Body>
                    <ReactECharts
                        option={option}
                        height={100}
                        theme={theme === true ? "dark" : ""}
                    />
                </Card.Body>
            </Card>
        </>
    );
};

export default TopVisitedURL;
