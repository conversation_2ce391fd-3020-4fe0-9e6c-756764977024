import React, { useEffect, useState } from "react";
import { Col, Row } from "react-bootstrap";
import { connect, useDispatch } from "react-redux";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faUsers,
    faQrcode,
    faExternalLink,
    faDiagramProject,
    faCoins,
    faMoneyBill1Wave,
    faUserTag,
    faUserCheck,
} from "@fortawesome/free-solid-svg-icons";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import { fetchDashboardDetails } from "../../../store/action/adminActions/dashboardAction";
import Widget from "../../../shared/Widget/Widget";
import { useNavigate } from "react-router-dom";
import { Filters, Tokens } from "../../../constants";
import { fetchAllCurrency } from "../../../store/action/adminActions/currencyAction";
import { cssHandling } from "../../../cssHandling/cssHandling";

const widget = (props) => {
    const { frontSettings, currencies, adminDashboard, fetchDashboardDetails } =
        props;
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [currencySym, setCurrencySym] = useState("");
    const updatedLanguage = localStorage.getItem(Tokens.UPDATED_LANGUAGE);
    useEffect(() => {
        fetchDashboardDetails(Filters.OBJ);
        dispatch(fetchAllCurrency());
    }, []);

    useEffect(() => {
        cssHandling(updatedLanguage);

        return () => cssHandling(updatedLanguage);
    }, [adminDashboard]);

    const onClick = (redirect) => {
        navigate(`/${redirect}`);
    };

    useEffect(() => {
        if (frontSettings?.currency) {
            if (currencies?.length > 0) {
                setCurrencySym(
                    currencies?.filter(
                        (d) => Number(frontSettings?.currency) === d.id
                    )[0]?.attributes?.symbol
                );
            }
        }
    }, [currencies, frontSettings]);

    return (
        <Row className="g-4">
            <Col className="col-12 mb-4">
                <Row>
                    <Widget
                        title={getFormattedMessage("users.title")}
                        onClick={() => onClick("app/admin/users")}
                        className={`bg-warning cursor-pointer gradient-style1`}
                        iconClass="bg-yellow-300"
                        icon={
                            <FontAwesomeIcon
                                icon={faUsers}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={adminDashboard && adminDashboard?.users}
                    />

                    <Widget
                        title={getFormattedMessage("qrcode.title")}
                        onClick={() => onClick("app/admin/qr-codes")}
                        className={`bg-primary cursor-pointer gradient-style2`}
                        iconClass="widget-light-primary"
                        icon={
                            <FontAwesomeIcon
                                icon={faQrcode}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={adminDashboard && adminDashboard?.qr_codes}
                    />

                    <Widget
                        title={getFormattedMessage("links.title")}
                        onClick={() => onClick("app/admin/minify-link")}
                        className="widget-bg-purple cursor-pointer gradient-style3"
                        iconClass="bg-purple-700"
                        icon={
                            <FontAwesomeIcon
                                icon={faExternalLink}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={adminDashboard && adminDashboard?.links}
                    />
                    <Widget
                        title={getFormattedMessage("projects.title")}
                        onClick={() => onClick("app/admin/collections")}
                        className="widget-bg-pink cursor-pointer gradient-style4"
                        iconClass="bg-pink-700"
                        icon={
                            <FontAwesomeIcon
                                icon={faDiagramProject}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={adminDashboard && adminDashboard?.projects}
                    />
                    <Widget
                        title={getFormattedMessage(
                            "globally.total-earning.lable"
                        )}
                        onClick={() => onClick("app/admin/transactions")}
                        className="widget-bg-pink cursor-pointer gradient-style5"
                        iconClass="bg-pink-700"
                        icon={
                            <FontAwesomeIcon
                                icon={faCoins}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={
                            adminDashboard &&
                            adminDashboard?.total_earning?.toFixed(2)
                        }
                        isAmount={true}
                        currencySym={currencySym}
                    />
                    <Widget
                        title={getFormattedMessage(
                            "globally.today-earning.lable"
                        )}
                        onClick={() => onClick("app/admin/transactions")}
                        className="widget-bg-pink cursor-pointer gradient-style6"
                        iconClass="bg-pink-700"
                        icon={
                            <FontAwesomeIcon
                                icon={faMoneyBill1Wave}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={
                            adminDashboard &&
                            adminDashboard?.today_earning?.toFixed(2)
                        }
                        isAmount={true}
                        currencySym={currencySym}
                    />
                    <Widget
                        title={getFormattedMessage(
                            "globally.total-subscriptions.lable"
                        )}
                        onClick={() => onClick("app/admin/subscribed-plans")}
                        className="widget-bg-pink cursor-pointer gradient-style7"
                        iconClass="bg-pink-700"
                        icon={
                            <FontAwesomeIcon
                                icon={faUserTag}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={
                            adminDashboard &&
                            adminDashboard?.total_subscriptions
                        }
                    />
                    <Widget
                        title={getFormattedMessage(
                            "globally.active-subscriptions.lable"
                        )}
                        onClick={() => onClick("app/admin/subscribed-plans")}
                        className="widget-bg-pink cursor-pointer gradient-style8"
                        iconClass="bg-pink-700"
                        icon={
                            <FontAwesomeIcon
                                icon={faUserCheck}
                                className="fs-1-xl text-white"
                            />
                        }
                        value={
                            adminDashboard &&
                            adminDashboard?.active_subscriptions
                        }
                    />
                </Row>
            </Col>
        </Row>
    );
};
const mapStateToProps = (state) => {
    const { totalRecord, adminDashboard, frontSettings, currencies } = state;
    return { totalRecord, adminDashboard, frontSettings, currencies };
};

export default connect(mapStateToProps, { fetchDashboardDetails })(widget);
