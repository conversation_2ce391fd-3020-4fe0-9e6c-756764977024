import React, { useEffect, useState } from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    BarElement,
    RadialLinearScale,
    ArcElement
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';
import { connect, useSelector } from 'react-redux';
import { formatAmount, placeholderText } from '../../../shared/sharedMethod';

const LineChart = (props) => {
    const { currency, linkData, isLineChart = true, chartData } = props

    const { theme } = useSelector(state => state)

    ChartJS.register(
        CategoryScale,
        LinearScale,
        PointElement,
        LineElement,
        BarElement,
        RadialLinearScale,
        ArcElement,
        Title,
        Tooltip,
        Legend
    );

    const valueFormatter = (tooltipItems) => {
        const value = (tooltipItems?.dataset?.data[tooltipItems.dataIndex])
        const label = tooltipItems?.dataset?.label
        return `${label}: ${currency} ${formatAmount(value)} `
    };

    const yFormatter = (yValue) => {
        const value = `${currency} ${yValue <= 1 ? yValue * 10 : yValue}`
        return value
    };

    const options = {
        responsive: true,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: (tooltipItems, data) => valueFormatter(tooltipItems)
                }
            },
        },
        scales: {
            y: {
                ticks: {
                    callback: (value, index, values) => yFormatter(value),
                    color: theme === true ? "#b7c3e3" : "#000000"
                },
                title: {
                    display: true,
                    text: placeholderText("globally.input.amount.label"),
                    align: 'center',
                    color: theme === true ? "#b7c3e3" : "#000000"
                },
                grid: {
                    color: "rgba(0, 0, 0, 0)",
                }
            },
            x: {
                ticks: {
                    color: theme === true ? "#b7c3e3" : "#000000"
                },
                grid: {
                    color: "rgba(0, 0, 0, 0)",
                }
            }
        },
    };

    const labels = chartData ? chartData.dates?.map(d => d?.split("-")?.reverse()?.join("-")) : '';
    const data = {
        labels,
        datasets: [
            {
                label: `${placeholderText("dashboard.Earning.title")}`,
                data: chartData.earning,
                fill: true,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(255, 159, 64, 0.5)',
                    'rgba(255, 205, 86, 0.5)',
                    'rgba(75, 192, 192, 0.5)',
                    'rgba(54, 162, 235, 0.5)',
                    'rgba(153, 102, 255, 0.5)',
                    'rgba(255, 0, 223, 0.5)'
                ],
                borderColor: [
                    'rgb(255, 99, 132)',
                    'rgb(255, 159, 64)',
                    'rgb(255, 205, 86)',
                    'rgb(75, 192, 192)',
                    'rgb(54, 162, 235)',
                    'rgb(153, 102, 255)',
                    'rgb(255, 0, 223)'
                ],
                borderWidth: 1
            },
        ],
    };

    return <>
        {isLineChart === false &&
            <Bar options={options} data={data} height={100} />}
        {isLineChart === true &&
            <Line options={options} data={data} height={100} />}
    </>
}

const mapStateToProps = (state) => {
    const { weekSalePurchase, yearTopProduct } = state;
    return { weekSalePurchase, yearTopProduct }
};

export default connect(mapStateToProps, {})(LineChart);

