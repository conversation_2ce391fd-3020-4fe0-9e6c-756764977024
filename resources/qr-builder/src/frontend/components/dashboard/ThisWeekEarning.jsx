import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from "chart.js";
import { Card, NavDropdown } from "react-bootstrap";
import { Row } from "react-bootstrap-v5";
import moment from "moment";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBars } from "@fortawesome/free-solid-svg-icons";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import LineChart from "./LineChart";
import { useDispatch, useSelector } from "react-redux";
import { getWeeklyEarningAction } from "../../../store/action/adminActions/dashboardAction";
import { fetchAllCurrency } from "../../../store/action/adminActions/currencyAction";

const ThisWeekEarning = () => {
    const [isLineChart, isSetLineChart] = useState(false);
    const [currencySym, setCurrencySym] = useState("");
    const dispatch = useDispatch();
    const { thisWeekEarning, frontSettings, currencies } = useSelector(
        (state) => state
    );

    useEffect(() => {
        dispatch(getWeeklyEarningAction());
        dispatch(fetchAllCurrency());
    }, []);

    useEffect(() => {
        if (frontSettings?.currency) {
            if (currencies?.length > 0) {
                setCurrencySym(
                    currencies?.filter(
                        (d) => Number(frontSettings?.currency) === d.id
                    )[0]?.attributes?.symbol
                );
            }
        }
    }, [currencies, frontSettings]);

    return (
        <>
            <Card className="shadow h-100">
                <Card.Header className="pb-0 px-10">
                    <h5 className="mb-0">
                        {getFormattedMessage(
                            "dashboard.ThisWeekEarnings.title"
                        )}
                    </h5>
                    <div className="mb-2 chart-dropdown">
                        <NavDropdown title={<FontAwesomeIcon icon={faBars} />}>
                            <NavDropdown.Item
                                className={`${
                                    isLineChart === true ? "" : "text-primary"
                                } fs-6`}
                                onClick={() => isSetLineChart(false)}
                            >
                                {getFormattedMessage("bar.title")}
                            </NavDropdown.Item>
                            <NavDropdown.Item
                                className={`${
                                    isLineChart === true ? "text-primary" : ""
                                } fs-6`}
                                onClick={() => isSetLineChart(true)}
                            >
                                {getFormattedMessage("line.title")}
                            </NavDropdown.Item>
                        </NavDropdown>
                    </div>
                </Card.Header>
                <Card.Body className="d-flex align-items-center">
                    <LineChart
                        isLineChart={isLineChart}
                        chartData={thisWeekEarning}
                        currency={currencySym}
                    />
                </Card.Body>
            </Card>
        </>
    );
};

export default ThisWeekEarning;
