import React from 'react';
import { Card, Row, Table } from 'react-bootstrap';
import { connect } from 'react-redux';
import { getAvatarName, getFormattedMessage } from '../../../shared/sharedMethod';
import { fetchUsers } from '../../../store/action/userAction';
import { Link } from "react-router-dom";

const RecentUsers = (props) => {
    const { adminDashboard } = props;

    return (
        <div className='pt-6'>
            <Row className='g-4'>
                <div className='col-xxl-12 col-12'>
                    <Card className='shadow'>
                        <Card.Header className='pb-0 px-10'>
                            <h5 className="mb-0">{getFormattedMessage("dashboard.recentUser.title")}</h5>
                        </Card.Header>
                        <Card.Body className='pt-7 pb-2'>
                            <Table responsive>
                                <thead>
                                    <tr>
                                        <th>{getFormattedMessage("users.table.user-name.column.title")}</th>
                                        <th>{getFormattedMessage("globally.plan.name.title")}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {adminDashboard && adminDashboard?.latest_users?.map((user, index) => {
                                        const image = user.image ? user.image : null;
                                        return index < 5 ? (
                                            <tr key={index}>
                                                <td>
                                                    <div className='d-flex align-items-center'>
                                                        <div className='me-2'>
                                                            <Link to={`/app/admin/users/detail/${user.id}`}>
                                                                {image ?
                                                                    <img src={image} height='50' width='50' alt='User Image'
                                                                        className='image image-circle image-mini image-effect' /> :
                                                                    <span className='custom-user-avatar fs-5'>{getAvatarName(user.name)}</span>
                                                                }
                                                            </Link>
                                                        </div>
                                                        <div className='d-flex flex-column'>
                                                            <Link to={`/app/admin/users/detail/${user.id}`}
                                                                className='text-decoration-none'>{user.name}</Link>
                                                            <span>{user.email}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <p className={"badge bg-light-success m-0"}>{user?.plan}</p>
                                                </td>
                                            </tr>
                                        ) : null
                                    })}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </div>
            </Row>
        </div>
    )
};

const mapStateToProps = (state) => {
    const { users, adminDashboard } = state;
    return { users, adminDashboard }
};


export default connect(mapStateToProps, { fetchUsers })(RecentUsers);
