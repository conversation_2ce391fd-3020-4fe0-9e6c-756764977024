import React from 'react';
import { connect, useSelector } from 'react-redux';
import { addPlan } from '../../../store/action/plansAction';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { Filters } from '../../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import CodesForm from './CodesForm';
import { Helmet } from 'react-helmet';

const CreateCode = (props) => {
    const { addPlan } = props;
    const navigate = useNavigate();
    const {frontSettings} = useSelector(state => state)
    const addPlansData = (formValue) => {
        addPlan(formValue, navigate, Filters.OBJ);
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText('code.create.title') + ' | ' + frontSettings?.title}/>
            <HeaderTitle title={getFormattedMessage('code.create.title')} to='/app/admin/codes' />
            <CodesForm addPlansData={addPlansData} />
        </MasterLayout>
    );
}

export default connect(null, { addPlan })(CreateCode);
