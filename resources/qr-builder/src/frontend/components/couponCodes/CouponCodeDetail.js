import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Card, Table } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate, placeholderText } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import { fetchCode } from "../../../store/action/adminActions/coupenCodesActions"
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { fetchCurrencies } from '../../../store/action/adminActions/currencyAction';
import HeaderTitle from "../../../components/header/HeaderTitle";
import RoundLoader from '../../../shared/components/loaders/RoundLoader';
import moment from 'moment';

const CouponCodeDetail = (props) => {
    const { isLoading, fetchCode, couponCodes, frontSettings, currencies, fetchCurrencies } = props;
    const { id } = useParams();
    const [currencySymbol, setCurrencySymbol] = useState("")

    useEffect(() => {
        fetchCode(id);
        fetchCurrencies()
    }, []);

    useEffect(() => {
        if (currencies.length > 0) {
            const cur = currencies.filter((d) => d.id === parseInt(frontSettings.currency))

            cur.length === 1 && setCurrencySymbol(cur[0]?.attributes.symbol)

        }
    }, [frontSettings, currencies])

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('coupon-code.details.title')} />
            {<>
                <HeaderTitle title={getFormattedMessage('coupon-code.details.title')} to='/app/admin/codes' editLink={`/app/admin/codes/edit/${id}`} />
                <div className='pt-5'>
                    <Card>
                        <Card.Body>
                            {isLoading ? <RoundLoader /> : <Table responsive>
                                <tbody>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('admin.codes.name.title')}</td>
                                        <td className='py-4'>{couponCodes[0]?.attributes.name}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.code.label')}</td>
                                        <td className='py-4'>{couponCodes[0]?.attributes.code}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.type.label')}</td>
                                        <td className='py-4'>{couponCodes[0]?.attributes.type === 1 ? placeholderText("globally.input.percentage.label") : placeholderText("globally.input.fixed.label")}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.discount.label')}</td>
                                        <td className='py-4'>{
                                            couponCodes[0]?.attributes.discount
                                                ?
                                                couponCodes[0]?.attributes.type === 1
                                                    ?
                                                    couponCodes[0]?.attributes.discount + "%"
                                                    :
                                                    currencySymbol + couponCodes[0]?.attributes.discount
                                                :
                                                "N/A"
                                        }</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.quantity.label')}</td>
                                        <td className='py-4'>{couponCodes[0]?.attributes.how_many_can_use || "N/A"}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.created-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(couponCodes[0]?.attributes?.created_at, "d-m-y")} {moment(couponCodes[0]?.attributes?.created_at).format('LT')}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.updated-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(couponCodes[0]?.attributes?.updated_at, "d-m-y")} {moment(couponCodes[0]?.attributes?.updated_at).format('LT')}
                                        </td>
                                    </tr>
                                </tbody>
                            </Table>}
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </MasterLayout>
    )
};

const mapStateToProps = state => {
    const { isLoading, couponCodes, frontSettings, currencies } = state;
    return { isLoading, couponCodes, frontSettings, currencies }
};

export default connect(mapStateToProps, { fetchCode, fetchCurrencies })(CouponCodeDetail);
