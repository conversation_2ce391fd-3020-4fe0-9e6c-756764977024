import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar';
import CodesForm from "./CodesForm";
import { fetchCode } from '../../../store/action/adminActions/coupenCodesActions';
import TabTitle from '../../../shared/tab-title/TabTitle';

const EditCode = (props) => {
    const { fetchCode, couponCodes } = props;
    const { id } = useParams();
    const [isEdit, setIsEdit] = useState(false);

    useEffect(() => {
        fetchCode(id);
    }, []);


    const itemsValue = couponCodes && couponCodes.length === 1 && couponCodes.map(couponCode => ({
        name: couponCode.attributes.name,
        code: couponCode.attributes.code,
        type: couponCode.attributes.type,
        discount: couponCode.attributes.discount,
        quantity: couponCode.attributes.how_many_can_use,
        used: couponCode.attributes.used,
        id: couponCode.id,
    }));

    return (
        <MasterLayout>
            <TabTitle title={placeholderText('plan.coupon-code.title')} />
            <TopProgressBar />
            <HeaderTitle title={getFormattedMessage('plan.coupon-code.title')} to='/app/admin/codes' />
            {couponCodes.length === 1 && <CodesForm singleCode={itemsValue[0]} id={id} isEdit={isEdit} setIsEdit={setIsEdit} />}
        </MasterLayout>
    );
}

const mapStateToProps = (state) => {
    const { couponCodes } = state;
    return { couponCodes }
};

export default connect(mapStateToProps, { fetchCode })(EditCode);
