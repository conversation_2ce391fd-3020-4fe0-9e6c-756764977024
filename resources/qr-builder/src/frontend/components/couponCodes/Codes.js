import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import DeleteCode from './DeleteCode';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { formatAmount, getFormattedDate } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { fetchCodes } from '../../../store/action/adminActions/coupenCodesActions';
import { fetchCurrencies } from '../../../store/action/adminActions/currencyAction';

const Codes = (props) => {
    const { totalRecord, isLoading, couponCodes, fetchCodes, frontSettings, fetchCurrencies, currencies } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);
    const [currencySymbol, setCurrencySymbol] = useState("")

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    useEffect(() => {
        // fetchCodes(Filters.OBJ, true);
        fetchCurrencies()
    }, [])

    useEffect(() => {
        if (currencies.length > 0) {
            const cur = currencies.filter((d) => d.id === parseInt(frontSettings.currency))

            cur.length === 1 && setCurrencySymbol(cur[0]?.attributes.symbol)
        }
    }, [frontSettings, currencies])

    const itemsValue = couponCodes.length >= 0 && couponCodes.map(couponCode => ({
        date: getFormattedDate(couponCode.attributes.created_at, "d-m-y"),
        time: moment(couponCode.attributes.created_at).format('LT'),
        name: couponCode.attributes.name,
        type: couponCode.attributes.type === 1 ? placeholderText("globally.input.percentage.label") : placeholderText("globally.input.fixed.label"),
        code: couponCode.attributes.code,
        discount: couponCode.attributes.type === 1 ? couponCode.attributes.discount + "%" : currencySymbol + " " + formatAmount(couponCode.attributes.discount),
        quantity: couponCode.attributes.how_many_can_use,
        used: couponCode.attributes.used,
        id: couponCode.id
    }));

    const onChange = (filter) => {
        fetchCodes(filter, true);
    };

    const goToEdit = (item) => {
        const id = item.id;
        window.location.href = '#/app/admin/codes/edit/' + id;
    };

    const columns = [
        {
            name: getFormattedMessage('react-data-table.name.column.title'),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/codes/detail/${row.id}`} className='text-decoration-none'>{row.name}</Link>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.input.type.lable'),
            selector: row => row.type,
            sortField: 'type',
            sortable: true,
        },
        {
            name: getFormattedMessage('globally.input.code.label'),
            sortField: 'code',
            sortable: true,
            cell: row => {
                return <span className='badge bg-light-primary'>
                    <span className='text-wrap'>{row.code}</span>
                </span>
            }
        },
        {
            name: getFormattedMessage('globally.input.discount.label'),
            selector: row => row.discount,
            sortField: 'discount',
            sortable: true,
            cell: row => <>
                {
                    row.discount ?
                        <div>
                            {row.discount}
                        </div>
                        :
                        <div>
                            N/A
                        </div>
                }
            </>
        },
        {
            name: getFormattedMessage('globally.input.customer-imit.lable'),
            selector: row => row.quantity,
            sortField: 'how_many_can_use',
            sortable: true,
            cell: row => <>
                {
                    row.quantity ?
                        <div>
                            {row.quantity}
                        </div>
                        :
                        <div>
                            N/A
                        </div>
                }
            </>
        },
        {
            name: getFormattedMessage('globally.plan.used.title'),
            selector: row => row.quantity,
            sortField: 'many',
            sortable: false,
            cell: row => <div>{row.used}</div>
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton item={row} goToEditProduct={goToEdit} isEditMode={true}
                onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('codes.title')} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                ButtonValue={getFormattedMessage('code.create.title')}
                to='#/app/admin/codes/create' totalRows={totalRecord} isLoading={isLoading} />
            <DeleteCode onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { isLoading, couponCodes, frontSettings, currencies, totalRecord } = state;
    return { isLoading, couponCodes, frontSettings, currencies, totalRecord }
};
export default connect(mapStateToProps, { fetchCodes, fetchCurrencies })(Codes);
