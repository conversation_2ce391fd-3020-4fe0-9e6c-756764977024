import React, { useEffect, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, getFormattedOptions, numValidate, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactSelect from '../../../shared/select/reactSelect';
import { couponCodeTypeOptions } from '../../../constants';
import { addCode, editCode, fetchCode } from '../../../store/action/adminActions/coupenCodesActions';

const CodesForm = (props) => {
    const { id, singleCode, isEdit, editCode, addCode, setIsEdit } = props;
    const navigate = useNavigate();
    const [codesValue, setCodesValue] = useState({
        name: singleCode?.name || "",
        type: singleCode ?
            singleCode?.type === 1 ?
                { value: 1, label: "Percentage" } :
                { value: 2, label: "Fixed" } :
            { value: 1, label: "Percentage" },
        code: singleCode?.code || "",
        discount: singleCode?.discount || "",
        quantity: singleCode?.quantity || "",
        used: false,
    });

    useEffect(() => {
        setCodesValue({
            name: singleCode?.name || "",
            type: singleCode ?
                singleCode?.type === 1 ?
                    { value: 1, label: getFormattedMessage("globally.input.percentage.label") } :
                    { value: 2, label: getFormattedMessage("globally.input.fixed.label") } :
                { value: 1, label: getFormattedMessage("globally.input.percentage.label") },
            code: singleCode?.code || "",
            discount: singleCode?.discount >= 0 ? singleCode?.discount : "",
            quantity: singleCode?.quantity || "",
            used: singleCode && singleCode.used === 0 ? false : true
        })
        singleCode && setIsEdit(true)
    }, [singleCode])

    const disabled = singleCode && singleCode?.name === codesValue.name &&
        singleCode?.type === codesValue.type.value &&
        singleCode?.code === codesValue.code &&
        singleCode?.discount === codesValue.discount &&
        singleCode?.quantity === codesValue.quantity

    const [errors, setErrors] = useState({
        name: "",
        type: "",
        code: "",
        discount: "",
        quantity: ""
    });

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        const numReg = /^\d+$/
        if (!codesValue.name || codesValue.name.trim().length === 0) {
            errorss.name = getFormattedMessage("globally.input.name.validate.label")
        } else if ((!codesValue.code) || codesValue.code.trim().length === 0) {
            errorss.code = getFormattedMessage("please.enter.the.code.error.message")
        } else if (!codesValue.discount && codesValue.type.value === 1) {
            errorss.discount = getFormattedMessage("please.enter.the.percentage.error.message")
        } else if (!numReg.test(codesValue.discount) && codesValue.type.value === 1) {
            errorss.discount = getFormattedMessage("please.enter.the.percentage.error.message")
        } else if (!codesValue.discount && codesValue.type.value === 2) {
            errorss.discount = getFormattedMessage("globally.input.amount.validate.label")
        } else if (!numReg.test(codesValue.discount) && codesValue.type.value === 2) {
            errorss.discount = getFormattedMessage("globally.input.amount.validate.label")
        } else if (codesValue.discount && codesValue.type.value === 1 && ((parseInt(codesValue.discount) < 0) || (parseInt(codesValue.discount) > 100))) {
            errorss.discount = getFormattedMessage("please.enter.percentage.between.0.to.100.error.message")
        } else if ((codesValue.quantity < 1) || !codesValue.quantity) {
            errorss.quantity = getFormattedMessage("please.enter.the.quantity.error.message")
        } else if (!numReg.test(codesValue.quantity)) {
            errorss.quantity = getFormattedMessage("please.enter.the.quantity.error.message")
        } else {
            isValid = true
        }
        setErrors(errorss);
        return isValid;
    }

    const onChangeInput = (e) => {
        e.preventDefault();
        setCodesValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('')
    };

    const onCodeInput = (e) => {
        e.preventDefault();
        setCodesValue(inputs => ({ ...inputs, [e.target.name]: e.target.value.trim() }))
        setErrors('')
    };

    const couponCodeType = getFormattedOptions(couponCodeTypeOptions)
    const couponCodeTypeDefaultValue = couponCodeType.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const onCodeTypeChange = (obj) => {
        setCodesValue(inputs => ({ ...inputs, type: obj }));
        setErrors('')
    }

    const prepareFormData = (prepareData) => {
        const formValue = {
            name: prepareData.name,
            type: prepareData.type.value,
            code: prepareData.code,
            discount: prepareData.discount,
            how_many_can_use: prepareData.quantity
        }
        return formValue
    };

    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation();

        if (valid && isEdit) {
            editCode(id, prepareFormData(codesValue), navigate)
        }
        else {
            if (valid) {
                setCodesValue(codesValue);
                addCode(prepareFormData(codesValue), navigate)
            }
        }
    };

    return (
        <div className='card'>
            <div className='card-body'>
                <Form>
                    <div className='row my-5'>
                        <div className='col-md-12 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("globally.coupon.code.name.title")}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={codesValue.name}
                                className='form-control' autoFocus={true}
                                placeholder={placeholderText("globally.enter.coupon.code.name.placeholder")}
                                onChange={(e) => onChangeInput(e)} />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['name'] ? errors['name'] : null}
                            </span>
                        </div>
                        <div className='col-md-12 mb-4'>
                            <ReactSelect title={getFormattedMessage('globally.coupon.code.type.title')}
                                name='type'
                                value={codesValue.type}
                                errors={errors['currency']}
                                defaultValue={couponCodeTypeDefaultValue[0]}
                                multiLanguageOption={couponCodeType}
                                onChange={onCodeTypeChange}
                                disabled={isEdit}
                            />
                        </div>
                        <div className='col-md-12 mb-3'>
                            <label className='form-label'>
                                {getFormattedMessage("globally.input.code.label")}:
                            </label>
                            <span className='required' />
                            <input autoComplete="off" type='text' name='code' className='form-control'
                                placeholder={placeholderText("globally.input.coupon-code.placeholder.label")}
                                onChange={(e) => onCodeInput(e)}
                                disabled={isEdit}
                                value={codesValue.code} />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['code'] ? errors['code'] : null}
                            </span>
                        </div>
                        <div className='col-md-12 mb-3'>
                            <label className='form-label'>
                                {codesValue.type.value === 2 ? getFormattedMessage('globally.input.amount.label') : getFormattedMessage('globally.input.percentage.label')}:
                            </label><span className='required' />
                            <input autoComplete="off" type='text' name='discount' className='form-control'
                                placeholder={codesValue.type.value === 2 ? placeholderText("globally.input.amount.placeholder") : placeholderText("user.input.name.percentage.label")}
                                onChange={(e) => onChangeInput(e)}
                                onKeyPress={(event) => numValidate(event)}
                                value={codesValue.discount} />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['discount'] ? errors['discount'] : null}
                            </span>
                        </div>
                        <div className='col-md-12 mb-3'>
                            <label className='form-label'>
                                {getFormattedMessage("coupon.code.how-many.use?.title")}:
                            </label><span className='required' />
                            <input type='text' name='quantity' className='form-control'
                                placeholder={placeholderText("globally.input.how-many-people-can-use?.validate.label")}
                                onChange={(e) => onChangeInput(e)}
                                onKeyPress={(event) => numValidate(event)}
                                value={codesValue.quantity} />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['quantity'] ? errors['quantity'] : null}
                            </span>
                        </div>

                    </div>
                    <ModelFooter
                        onEditRecord={singleCode}
                        editDisabled={disabled} addDisabled={!codesValue.name}
                        link='/app/admin/codes' onSubmit={onSubmit} />
                </Form>
            </div>
        </div>
    )
};

const mapStateToProps = (state) => {
    const { couponCodes } = state;
    return { couponCodes }
};

export default connect(mapStateToProps, { addCode, fetchCode, editCode })(CodesForm);

