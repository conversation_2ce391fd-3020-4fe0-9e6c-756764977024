import React from 'react';
import { connect } from 'react-redux';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { deleteCode } from '../../../store/action/adminActions/coupenCodesActions';

const DeleteCode = (props) => {
    const { deleteCode, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deleteCode(onDelete.id);
        onClickDeleteModel(false);
    };


    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage('globally.input.code.label')} />}
        </div>
    )
};

export default connect(null, { deleteCode })(DeleteCode);
