import React from 'react';
import { connect } from 'react-redux';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import LanguageForm from './LanguageForm';

const EditLanguage = (props) => {
    const { handleClose, show, language, filterData } = props;

    return (
        <>
            {language &&
                <LanguageForm filterData={filterData} handleClose={handleClose} show={show} singleLanguage={language}
                    title={getFormattedMessage("edit-language.title")} />
            }
        </>
    )
};

export default connect(null)(EditLanguage);

