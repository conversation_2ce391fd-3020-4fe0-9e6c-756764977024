import React from 'react';
import { connect, useSelector } from 'react-redux';
import AdminLinkForm from './AdminLinkForm';
import { adminAddLink } from "../../../store/action/adminActions/linksAction";
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { Filters } from '../../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import { Helmet } from 'react-helmet';

const CreateLink = ( props ) => {
    const { adminAddLink } = props;
    const navigate = useNavigate();
    const { frontSettings } = useSelector( state => state )
    const addLinkData = ( formValue ) => {
        adminAddLink( formValue, navigate, Filters.OBJ );
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText( 'link.create.title' ) + ' | ' + frontSettings?.title} />
            <HeaderTitle title={getFormattedMessage( 'link.create.title' )} to='/app/admin/minify-link' />
            <AdminLinkForm addLinkData={addLinkData} />
        </MasterLayout>
    );
}

export default connect( null, { adminAddLink } )( CreateLink );
