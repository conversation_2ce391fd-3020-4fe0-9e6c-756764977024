import React, { useEffect, useRef, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { InputGroup } from 'react-bootstrap-v5';
import * as EmailValidator from 'email-validator';
import { editUser } from '../../../store/action/userAction';
import { getAvatarName, getFormattedMessage, placeholderText, numValidate, isValidHttpUrl } from '../../../shared/sharedMethod';
// import user from '../../assets/images/avatar.png';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactColorPicker from '../../../shared/colorpocker/ReactColorPicker';
import { editLink } from '../../../store/action/linkAction';
import { environment } from "../../../config/environment"
import UserDropdown from "./UserDropdown";
import QRCodeStyling from 'qr-code-styling';
const qrCode = new QRCodeStyling( {
    width: 400,
    height: 400,
    dotsOptions: {
        color: "#A561FF",
        type: "square",
    },
    imageOptions: {
        crossOrigin: "anonymous",
        margin: 20
    },
    margin: 10
} );

const AdminLinkForm = ( props ) => {
    const { addLinkData, id, singleLink } = props;
    const Dispatch = useDispatch()
    const navigate = useNavigate();
    const ref = useRef( null );

    const [ linkValue, setLinkValue ] = useState( {
        name: '',
        destination_url: '',
        url_alias: '',
        tenant_id: ''
    } );
    const [ errors, setErrors ] = useState( {
        destination_url: '',
        url_alias: '',
        tenant_id: '',
        name: '',
    } );

    const disabled = singleLink && linkValue.name === singleLink[ 0 ].name && singleLink[ 0 ].name === linkValue.destination_url
        && singleLink[ 0 ].color === linkValue.url_alias

    useEffect( () => {
        if ( singleLink?.length ) {
            setLinkValue( {
                name: singleLink ? singleLink[ 0 ].name : '',
                destination_url: singleLink ? singleLink[ 0 ].destination_url : '',
                url_alias: singleLink ? singleLink[ 0 ].url_alias : ''
            } )
        }
    }, [ singleLink ] )

    useEffect( () => {
        qrCode.update( {
            width: 400,
            height: 400,
            data: environment.URL + '/' + linkValue.url_alias,
            dotsOptions: {
                color: "#A561FF",
                gradient: null
            }
        } )
        qrCode.append( ref.current );
    }, [ linkValue.url_alias ] )

    const onUserChange = ( obj ) => {
        setLinkValue( inputs => ( { ...inputs, tenant_id: obj } ) )
        setErrors( '' )
    };

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if ( !linkValue[ 'name' ] || linkValue[ 'name' ]?.trim()?.length === 0 ) {
            errorss[ 'name' ] = getFormattedMessage( 'user.input.first-name.validate.label' );
        } else if ( !linkValue[ 'tenant_id' ] ) {
            errorss[ 'tenant_id' ] = getFormattedMessage( "user.input.validate.label" )
        } else if ( !linkValue[ 'destination_url' ] ) {
            errorss[ 'destination_url' ] = getFormattedMessage( 'destination.input.validate.label' )
        } else if ( isValidHttpUrl( linkValue.destination_url ) === false ) {
            errorss[ 'destination_url' ] = getFormattedMessage( "globally.input.URL.valid.validate.label" );
        } else if ( !linkValue[ 'url_alias' ] || linkValue[ 'url_alias' ]?.trim()?.length === 0 ) {
            errorss[ 'url_alias' ] = getFormattedMessage( 'alias.input.validate.label' )
        } else {
            isValid = true;
        }
        setErrors( errorss );
        return isValid;
    };

    const onChangeInput = ( e ) => {
        e.preventDefault();
        setLinkValue( inputs => ( { ...inputs, [ e.target.name ]: e.target.value } ) )
        setErrors( '' );
    };


    const onChangeAliasInput = ( e ) => {
        e.preventDefault();
        setLinkValue( inputs => ( { ...inputs, [ e.target.name ]: e.target.value } ) )
        setErrors( '' );
    }

    const prepareFormData = ( data, dataURL ) => {
        const formData = new FormData();
        formData.append( 'name', data.name );
        formData.append( 'destination_url', data.destination_url );
        formData.append( 'url_alias', data.url_alias );
        formData.append( 'tenant_id', data.tenant_id.value )
        formData.append( 'qr_image', dataURL );
        return formData;
    };


    const onSubmit = ( event ) => {
        event.preventDefault();
        const valid = handleValidation();
        const canvas = document.getElementById( 'canvas' );
        const dataURL = canvas?.firstElementChild?.toDataURL();
        if ( singleLink && valid ) {
            if ( !disabled ) {
                dataURL !== undefined && Dispatch( editLink( id, prepareFormData( linkValue, dataURL ), navigate ) );
            }
        } else {
            if ( valid ) {
                setLinkValue( linkValue );
                dataURL !== undefined && addLinkData( prepareFormData( linkValue, dataURL ) );
            }
        }
    };

    return (
        <div className='card'>
            <div className='card-body'>
                <Form>
                    <div className='row'>
                        <div className='col-md-12 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage( "globally.input.name.label" )}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={linkValue.name || ""}
                                className='form-control' autoFocus={true}
                                onChange={( e ) => onChangeInput( e )}
                                placeholder={placeholderText( "user.input.name.placeholder.label" )}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors[ 'name' ] ? errors[ 'name' ] : null}
                            </span>
                        </div>
                        <div className='col-md-12 mb-4'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage( "users.table.user.column.title" )}:<span className="required" />
                            </label>
                            <UserDropdown onUserChange={onUserChange} disabledKey={"Type"} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors[ 'tenant_id' ] ? errors[ 'tenant_id' ] : null}
                            </span>
                        </div>
                        <div className='col-md-12 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage( "globally.source.url.title" )}:<span className="required" />
                            </label>
                            <input type='url' name='destination_url' value={linkValue.destination_url}
                                placeholder={placeholderText( "globally.source.url.title" )}
                                className='form-control' autoFocus={true}
                                onChange={( e ) => onChangeInput( e )} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors[ 'destination_url' ] ? errors[ 'destination_url' ] : null}</span>
                        </div>
                        <div className='col-md-12 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage( "globally.shorten.name.title" )}:<span className="required" />
                            </label>
                            <InputGroup>
                                <InputGroup.Text>{environment.URL}</InputGroup.Text>
                                <input type='text' name='url_alias' value={linkValue.url_alias.replace( /\s/g, '' )}
                                    placeholder={placeholderText( "globally.shorten.name.title" )}
                                    className='form-control' autoFocus={true}
                                    onChange={( e ) => onChangeAliasInput( e )} />
                            </InputGroup>
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors[ 'url_alias' ] ? errors[ 'url_alias' ] : null}</span>
                        </div>
                        <div ref={ref} id="canvas" className='d-none' />
                        <ModelFooter onEditRecord={singleLink} onSubmit={onSubmit} editDisabled={disabled}
                            link='/app/admin/minify-link' addDisabled={!linkValue.name} />
                    </div>
                </Form>
            </div>
        </div>
    )
};

const mapStateToProps = ( state ) => {
    const { roles } = state;
    return { roles }
};

export default connect( mapStateToProps, {} )( AdminLinkForm );

