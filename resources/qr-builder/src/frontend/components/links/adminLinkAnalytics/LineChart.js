import React, { useEffect, useState } from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    BarElement,
    RadialLinearScale,
    ArcElement
} from 'chart.js';
import { Bar, Line, PolarArea } from 'react-chartjs-2';
import { connect, useSelector } from 'react-redux';
// import {weekSalePurchases} from '../../store/action/weeksalePurchaseAction';
// import {yearlyTopProduct} from '../../store/action/yearlyTopProductAction';
import { getFormattedMessage, placeholderText } from "../../../../shared/sharedMethod";

const LineChart = ( props ) => {
    const { weekSalePurchase, frontSetting, linkData, isLineChart = true } = props

    const [ labels, setLabels ] = useState( [] )
    const [ numData, setNumData ] = useState( [] )
    const [ darkTheme, setDarkTheme ] = useState( true )
    const { theme } = useSelector( state => state )

    ChartJS.register(
        CategoryScale,
        LinearScale,
        PointElement,
        LineElement,
        BarElement,
        RadialLinearScale,
        ArcElement,
        Title,
        Tooltip,
        Legend
    );

    useEffect( () => {
        if ( linkData?.weeklyLabels ) {
            setLabels( linkData.weeklyLabels )
            setNumData( linkData?.totalVisitorCount )
        }
    }, [ linkData ] )

    const valueFormatter = ( tooltipItems ) => {
        const value = ( tooltipItems?.dataset?.data[ tooltipItems.dataIndex ] )
        const label = tooltipItems?.dataset?.label
        const date = tooltipItems?.dataset?.weeklyLabels?.length > 1 ? tooltipItems?.dataset?.weeklyLabels[ tooltipItems.dataIndex ] : tooltipItems?.dataset?.weeklyLabels[ 0 ]
        return value + " " + label + " (" + date + ")"
    };

    const yFormatter = ( yValue ) => {
        const value = yValue
        return value
    };

    const options = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    color: theme === true ? "#b7c3e3" : "#000000"
                }
            },
            tooltip: {
                callbacks: {
                    label: ( tooltipItems ) => valueFormatter( tooltipItems ),
                }

            },
        },
        scales: {
            r: {
                grid: {
                    color: theme === true ? "#b7c3e3" : "#000000"
                }
            }
        }
    };

    const data = {
        labels: [ placeholderText( "visitor.title" ) ],
        datasets: [
            {
                label: placeholderText( "visitor.title" ),
                weeklyLabels: linkData?.weeklyLabels ? linkData?.weeklyLabels : [],
                data: numData,
                backgroundColor: [
                    '#a561ff47',
                ],
                borderWidth: 1,
            },
        ],
    };

    return <>
        {/* {isLineChart === false &&
            <Bar options={options} data={data} height={100} />}
        {isLineChart === true &&
            <Line options={options} data={data} height={100} />} */}
        <PolarArea data={data} options={options} theme={"dark"} />
    </>
}

const mapStateToProps = ( state ) => {
    const { weekSalePurchase, yearTopProduct } = state;
    return { weekSalePurchase, yearTopProduct }
};

export default connect( mapStateToProps, {} )( LineChart );

