import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { getFormattedMessage, placeholderText } from "../../../../../shared/sharedMethod";
import { Card, ProgressBar, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const LanguageAnalyticsTab = (props) => {
    const { weekSalePurchase, frontSetting, linkData, changeTab } = props
    const [languageDetails, setLanguageDetails] = useState([])
    const variants = ["success", "primary", "dark", "secondary", "info", "warning", "danger"]

    let languageNames = new Intl.DisplayNames(['en'], { type: 'language' });
    useEffect(() => {
        if (linkData) {
            let languageData = [];
            if(linkData.language){
            [linkData.language]?.map((d) => {
                for (let key in d) {
                    languageData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: languageNames?.of(key.length > 1 ? key : "en"),
                        color: variants[Math.floor(Math.random() * variants.length)]
                    })
                }
            })
        }
            setLanguageDetails(languageData)
        }
    }, [linkData])

    return <>
        {languageDetails.length >= 1 &&
            //<Card>
            <Card.Body>
                <Card>
                    <Card.Body className='border rounded'>
                        <Card.Title className='mb-5 '>{getFormattedMessage("settings.select.language.label")}s</Card.Title>
                        {languageDetails.map((d, i) => {
                            return <React.Fragment key={i + 1}>
                                <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                    <span>
                                        {d.name.charAt(0).toUpperCase() + d.name.slice(1) || "India"}
                                    </span>
                                    <span>
                                        {d.per}% {d.count}
                                    </span>
                                </Card.Subtitle>
                                <>
                                    <ProgressBar variant={d.color} now={d.per} />
                                </>
                            </React.Fragment>
                        })
                        }
                        {/* <Button variant='Link'
                            className="text-muted p-0 my-2 text-decoration-underline"
                            onClick={() => changeTab("language")}
                        >View more</Button> */}
                    </Card.Body>
                </Card>
            </Card.Body>
            //</></Card>
        }
        {languageDetails.length === 0 &&
            <Card>
                <Card.Body className='border text-center'>
                    <h1>{getFormattedMessage("no-data-available.title")}</h1>
                </Card.Body>
            </Card>}
    </>
}

const mapStateToProps = (state) => {
    const { weekSalePurchase, yearTopProduct } = state;
    return { weekSalePurchase, yearTopProduct }
};

export default connect(mapStateToProps, {})(LanguageAnalyticsTab);

