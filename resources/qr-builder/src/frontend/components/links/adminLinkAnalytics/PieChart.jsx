import React from 'react'
import ReactECharts from 'echarts-for-react';
import { useEffect } from 'react';
import { useState } from 'react';
import { getFormattedMessage } from '../../../../shared/sharedMethod';
import { useSelector } from 'react-redux';
import RoundLoader from '../../../../shared/components/loaders/RoundLoader';

const PieChart = ({ data, keyName }) => {

    const [chartData, setChartData] = useState([])
    const { theme } = useSelector(state => state)
    const [isLoding, setIsLoding] = useState(false)

    useEffect(() => {
        let tempData = chartData
        if (_.isEmpty(data) === false) {
            setIsLoding(false)
            if (data?.noRecord === undefined && Object?.keys(data)?.length > 0) {
                if (keyName === "overview") {
                    if (_.isEmpty(data["browser"]) === false) {
                        if (Object?.keys(data["browser"])?.length > 0) {
                            tempData = []
                            const brObj = { name: "browser", value: Object?.keys(data["browser"])?.length }
                            const countryObj = { name: "country", value: Object?.keys(data["country"])?.length }
                            const deviceObj = { name: "device", value: Object?.keys(data["device"])?.length }
                            const languageObj = { name: "language", value: Object?.keys(data["language"])?.length }
                            const osObj = { name: "operating_system", value: Object?.keys(data["operating_system"])?.length }
                            if (!tempData.some(obj => obj.name === brObj.name)) {
                                tempData.push(brObj)
                            }
                            if (!tempData.some(obj => obj.name === countryObj.name)) {
                                tempData.push(countryObj)
                            }
                            if (!tempData.some(obj => obj.name === deviceObj.name)) {
                                tempData.push(deviceObj)
                            }
                            if (!tempData.some(obj => obj.name === languageObj.name)) {
                                tempData.push(languageObj)
                            }
                            if (!tempData.some(obj => obj.name === osObj.name)) {
                                tempData.push(osObj)
                            }
                        }
                    }
                }

                if (keyName === "country") {
                    tempData = []
                    for (let key in data?.country) {
                        if (!tempData.some(obj => obj.name === key)) {
                            tempData.push({ name: key === "" ? "India" : key, value: data?.country[key]?.count })
                        }
                    }
                }

                if (keyName === "device") {
                    tempData = []
                    for (let key in data?.device) {
                        if (!tempData.some(obj => obj.name === key)) {
                            tempData.push({ name: key, value: data?.device[key]?.count })
                        }
                    }
                }

                if (keyName === "os") {
                    tempData = []
                    for (let key in data?.operating_system) {
                        if (!tempData.some(obj => obj.name === key)) {
                            tempData.push({ name: key, value: data?.operating_system[key]?.count })
                        }
                    }
                }

                let languageNames = new Intl.DisplayNames(['en'], { type: 'language' });
                if (keyName === "language") {
                    tempData = []
                    for (let key in data?.language) {
                        if (!tempData.some(obj => obj.name === key)) {
                            tempData.push({ name: languageNames?.of(key.length > 1 ? key : "en"), value: data?.language[key]?.count })
                        }
                    }
                }

                if (keyName === "browser") {
                    tempData = []
                    for (let key in data?.browser) {
                        if (!tempData.some(obj => obj.name === key)) {
                            tempData.push({ name: key, value: data?.browser[key]?.count })
                        }
                    }
                }
            }
        } else {
            setIsLoding(true)
        }

        if (data?.noRecord !== undefined) {
            tempData = []
        }

        setChartData(tempData)
    }, [data, keyName])

    const option = {
        title: {
            show: false
        },
        tooltip: {
            trigger: 'item',
        },
        legend: {
            show: false
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: '80%',
                data: chartData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                    }
                },
                label: {
                    show: false,
                },
            }
        ]
    };

    return (
        <>
            {isLoding ? <RoundLoader /> :
                data?.noRecord === undefined ? <ReactECharts
                    option={option}
                    style={{ height: 400 }}
                    theme={theme === true ? "dark" : ""}
                />
                    : <h2 className='text-center'>{getFormattedMessage("no-data-available.title")}</h2>
            }
        </>
    )
}

export default PieChart
