import React, { useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedDate } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { fetchLinks } from '../../../store/action/adminActions/linksAction';
import DeleteAdminLink from './DeleteAdminLink';
import { faExternalLink, faPaste } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { environment } from '../../../config/environment';
import { faChartLine } from '@fortawesome/free-solid-svg-icons';
import { addToast } from '../../../store/action/toastAction';
import { toastType } from '../../../constants';
import ImageModal from '../../../shared/imageModal/ImageModal';
import { saveAs } from 'file-saver';

const AdminLinks = ( props ) => {
    const { links_admin, fetchLinks, totalRecord, isLoading, allConfigData } = props;
    const [ deleteModel, setDeleteModel ] = useState( false );
    const [ isDelete, setIsDelete ] = useState( null );
    const dispatch = useDispatch()
    const DarkMod = localStorage.getItem( 'isDarkMod' );
    const [ showImageSlider, setShowImageSlider ] = useState( {
        display: false,
        src: '',
        name: ""
    } )

    const onClickDeleteModel = ( isDelete = null ) => {
        setDeleteModel( !deleteModel );
        setIsDelete( isDelete );
    };

    const closeImageSlider = () => {
        setShowImageSlider( {
            display: false,
            src: '',
            name: ""
        } )
    }

    const onDownloadClick = ( e, url, name, ext ) => {
        e.preventDefault();
        saveAs( url, `${name}.${ext}` );
    }

    const goToAnalytics = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/admin/minify-link/' + id + '/analytics';
    };

    const itemsValue = links_admin.length >= 0 && links_admin.map( link => ( {
        date: getFormattedDate( link.attributes.created_at, "d-m-y" ),
        time: moment( link.attributes.created_at ).format( 'LT' ),
        name: link.attributes.url_alias,
        to: link.attributes.destination_url,
        url: `${environment.URL}/${link.attributes.url_alias}`,
        user_name: link.attributes.user_name,
        user_email: link.attributes.user_email,
        user_id: link.attributes.user_id,
        link_name: link.attributes.name,
        user_image: link.attributes.user_image,
        qrcode_image: link.attributes.qrcode_image,
        id: link.id
    } ) );

    const onChange = ( filter ) => {
        fetchLinks( filter, true );
    };

    const copyClickBoard = ( data ) => {
        const unsecuredCopyToClipboard = () => {
            const textArea = document.createElement( "textarea" );
            textArea.value = data.url;
            document.body.appendChild( textArea );
            textArea.focus();
            textArea.select();
            try {
                document.execCommand( 'copy' );
                dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
            } catch ( err ) {
                dispatch( addToast( { text: `Failed to copy text to clipboard: ${err}`, type: toastType.ERROR } ) )
            }
            document.body.removeChild( textArea );
        }

        if ( window.isSecureContext && navigator.clipboard ) {
            navigator.clipboard.writeText( data.url );
            dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
        } else {
            unsecuredCopyToClipboard();
        }
    }

    const columns = [
        {
            name: getFormattedMessage( "qrcode.lable" ),
            selector: row => row.qrcode_image,
            sortField: 'qrcode_image',
            sortable: false,
            cell: row => {
                return <div className='me-2' >
                    <img src={row?.qrcode_image} height='50' width='50' alt='QR Code Image'
                        className='image image-mini image-effect cursor-pointer' onClick={() => setShowImageSlider( {
                            display: true,
                            src: row?.qrcode_image,
                            name: row?.link_name
                        } )} />
                </div>
            }
        },
        {
            name: getFormattedMessage( 'globally.input.name.label' ),
            selector: row => row.link_name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        {row.link_name}
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'link.table.plan.column.title' ),
            selector: row => row.name,
            sortField: 'destination_url',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center w-100'>
                    <div className=''>
                        <div>
                            <a href={row.to} target={"_blank"} className={`${DarkMod === 'true' ? 'text-muted' : 'text-dark'} text-decoration-none`}>{row.to}</a>
                        </div>
                        <div className='text-primary pe-3'>
                            <FontAwesomeIcon className={"me-1"} width={10} icon={faExternalLink} />
                            <a href={row.url} target={"_blank"} className='text-decoration-none text-primary'>{row.url}</a>
                            <span className='text-blue ms-2 fs-3 cursor-pointer' title={placeholderText( 'globally.copy-link.tool-tip.message' )} onClick={( e ) => { copyClickBoard( row ) }}>
                                <FontAwesomeIcon icon={faPaste} className='text-primary' />
                            </span>
                        </div>
                    </div>
                </div>
            }
        },
        // {
        //     name: getFormattedMessage('globally.react-table.column.stats.label'),
        //     selector: row => row.name,
        //     sortField: 'stats',
        //     sortable: false,
        //     cell: row => {
        //         return <div className='d-flex cursor-pointer'>
        //             <div className='text-info'><FontAwesomeIcon icon={faChartLine} className="fs-2" onClick={() => goToAnalytics(row)} /></div>
        //         </div>
        //     }
        // },
        {
            name: getFormattedMessage( 'users.table.user.column.title' ),
            selector: row => row.user_name,
            sortField: 'name',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`}>
                            {row.user_image ?
                                <img src={row.user_image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName( row.user_name )}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`} className='text-decoration-none'>{row.user_name}</Link>
                        <div>{row.user_email}</div>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'globally.react-table.column.created-date.label' ),
            selector: row => row.date,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        <div className='mb-1'>{row.time}</div>
                        <div>{row.date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage( 'react-data-table.action.column.label' ),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row =>
                <ActionButton item={row} isAnalytics={true} goToAnalytics={goToAnalytics} isCopyBtn={false} onCopybtn={copyClickBoard} isEditMode={false}
                    onClickDeleteModel={onClickDeleteModel} />

        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( 'links.title' )} />
            <div className='link-table'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} ButtonValue={getFormattedMessage( 'links.create.title' )}
                    to='#/app/admin/minify-link/create' totalRows={totalRecord} isLoading={isLoading} />
            </div>
            <DeleteAdminLink onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
            <ImageModal display={showImageSlider} closeSlider={closeImageSlider} onDownloadClick={onDownloadClick} />
        </MasterLayout>
    )
};

const mapStateToProps = ( state ) => {
    const { links_admin, totalRecord, isLoading, allConfigData } = state;
    return { links_admin, totalRecord, isLoading, allConfigData }
};
export default connect( mapStateToProps, { fetchLinks } )( AdminLinks );
