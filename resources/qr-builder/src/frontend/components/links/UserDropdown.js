import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { fetchAllUsers } from '../../../store/action/userAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import ReactSelect from "../../../shared/select/reactSelect";

const UserDropdown = (props) => {
    const { defaultUser, selectedCustomerOption, fetchAllUsers, users, onUserChange, disabledKey } = props;

    useEffect(() => {
        fetchAllUsers();
    }, []);

    return (
        <ReactSelect
            placeholder={getFormattedMessage("dropdown.choose-user.placeholder")}
            defaultValue={defaultUser}
            value={selectedCustomerOption}
            onChange={onUserChange}
            data={users}
            noOptionsMessage={() => getFormattedMessage('no-option.label')}
            isRequired
            disabled={disabledKey === "Type" ? false : true}
        />
    )
};

const mapStateToProps = (state) => {
    const { users } = state;
    return { users }
};
export default connect(mapStateToProps, { fetchAllUsers })(UserDropdown);
