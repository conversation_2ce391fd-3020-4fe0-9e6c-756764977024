import React from 'react';
import { connect } from 'react-redux';
import { deleteAdminLink } from '../../../store/action/linkAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';

const DeleteAdminLink = (props) => {
    const { deleteAdminLink, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deleteAdminLink(onDelete.id);
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage('link.table.plan.column.title')} />}
        </div>
    )
};

export default connect(null, { deleteAdminLink })(DeleteAdminLink);
