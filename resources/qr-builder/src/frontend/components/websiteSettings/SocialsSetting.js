import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import ModelFooter from '../../../shared/components/modelFooter';
import { fetchSocialSetting, updateSocialsSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsitesocialSetting from "./WebsiteSettings";
import { getFormattedMessage, placeholderText } from "../../../shared/sharedMethod";
import { InputGroup } from "react-bootstrap-v5";
import { addToast } from "../../../store/action/toastAction";
import { toastType } from "../../../constants";


const SocialsSetting = (props) => {
    const { socialSetting, updateSocialsSetting, fetchSocialSetting } = props
    const dispatch = useDispatch()

    const [socialsValue, setSocialsValue] = useState({
        youtube_link: "",
        facebook_link: "",
        twitter_link: "",
        instagram_link: "",
        tiktok_link: "",
        linked_in_link: ""
    })

    useEffect(() => {
        fetchSocialSetting()
    }, [])

    useEffect(() => {
        if (socialSetting) {
            setSocialsValue({
                youtube_link: socialSetting ? socialSetting.youtube_link : "",
                facebook_link: socialSetting ? socialSetting.facebook_link : "",
                twitter_link: socialSetting ? socialSetting.twitter_link : "",
                instagram_link: socialSetting ? socialSetting.instagram_link : "",
                linked_in_link: socialSetting ? socialSetting.linked_in_link : "",
                tiktok_link: socialSetting ? socialSetting.tiktok_link : ""
            })
        }
    }, [socialSetting])

    const disabled = socialSetting &&
        socialSetting.youtube_link === socialsValue.youtube_link &&
        socialSetting.facebook_link === socialsValue.facebook_link &&
        socialSetting.twitter_link === socialsValue.twitter_link &&
        socialSetting.instagram_link === socialsValue.instagram_link &&
        socialSetting.tiktok_link === socialsValue.tiktok_link &&
        socialSetting.linked_in_link === socialsValue.linked_in_link

    const onChangeInput = (e) => {
        e.preventDefault();
        const { value } = e.target;
        setSocialsValue(inputs => ({ ...inputs, [e.target.name]: value.length === 0 ? "" : value }))
    };

    const prepareFormData = (data) => {
        const formValue = {
            youtube_link: data ? data.youtube_link : '',
            facebook_link: data ? data.facebook_link : '',
            twitter_link: data ? data.twitter_link : '',
            instagram_link: data ? data.instagram_link : '',
            tiktok_link: data ? data.tiktok_link : '',
            linked_in_link: data ? data.linked_in_link : '',
        }
        return formValue
    }


    const onSubmit = (e) => {
        e.preventDefault();
        // dispatch(addToast(
        //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
        setSocialsValue({
            youtube_link: socialSetting ? socialSetting.youtube_link : "",
            facebook_link: socialSetting ? socialSetting.facebook_link : "",
            twitter_link: socialSetting ? socialSetting.twitter_link : "",
            instagram_link: socialSetting ? socialSetting.instagram_link : "",
            linked_in_link: socialSetting ? socialSetting.linked_in_link : "",
            tiktok_link: socialSetting ? socialSetting.tiktok_link : ""
        })
        setSocialsValue(socialsValue);
        updateSocialsSetting(prepareFormData(socialsValue));
        fetchSocialSetting()
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('settings.social.title')} />
                <WebsitesocialSetting page={'socials'} />
                <div className='card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-md-6 col-12 mb-3'>
                                    <label
                                        className='form-label'><span className="me-2"><svg xmlns="http://www.w3.org/2000/svg" height={20} width={20} viewBox="0 0 576 512"><path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z" /></svg></span>{getFormattedMessage("admin.setting.social.youtube.label")}: </label>
                                    <InputGroup>
                                        <InputGroup.Text>{'https://youtube.com/@'}</InputGroup.Text>
                                        <input type='text' name='youtube_link'
                                            className='form-control'
                                            placeholder={placeholderText("username.title")}
                                            onChange={(e) => onChangeInput(e)}
                                            value={socialsValue && socialsValue.youtube_link} />
                                    </InputGroup>
                                </div>
                                <div className='col-md-6 col-12 mb-3'>
                                    <label
                                        className='form-label'><span className="me-2"><svg xmlns="http://www.w3.org/2000/svg" height={20} width={20} viewBox="0 0 512 512"><path d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z" /></svg></span>{getFormattedMessage("admin.setting.social.facebook.label")}: </label>
                                    <InputGroup>
                                        <InputGroup.Text>{'https://facebook.com/'}</InputGroup.Text>
                                        <input type='text' name='facebook_link'
                                            className='form-control'
                                            onChange={(e) => onChangeInput(e)}
                                            placeholder={placeholderText("username.title")}
                                            value={socialsValue && socialsValue.facebook_link} />
                                    </InputGroup>
                                </div>
                                <div className='col-md-6 col-12 mb-3'>
                                    <label
                                        className='form-label'><span className="me-2">  <svg xmlns="http://www.w3.org/2000/svg" height={20} width={20} viewBox="0 0 448 512"><path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" /></svg></span>{getFormattedMessage("admin.setting.social.instagram.label")}: </label>
                                    <InputGroup>
                                        <InputGroup.Text>{'https://instagram.com/'}</InputGroup.Text>
                                        <input type='text' name='instagram_link'
                                            className='form-control'
                                            placeholder={placeholderText("username.title")}
                                            onChange={(e) => onChangeInput(e)}
                                            value={socialsValue && socialsValue.instagram_link} />
                                    </InputGroup>
                                </div>
                                <div className='col-md-6 col-12 mb-3'>
                                    <label
                                        className='form-label'><span className="me-2"> <svg xmlns="http://www.w3.org/2000/svg" height={20} width={20} viewBox="0 0 448 512"><path d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z" /></svg></span>{getFormattedMessage("admin.setting.social.linkedin.label")}: </label>
                                    <InputGroup>
                                        <InputGroup.Text>{'https://linkedin.com/in/'}</InputGroup.Text>
                                        <input type='text' name='linked_in_link'
                                            className='form-control'
                                            placeholder={placeholderText("username.title")}
                                            onChange={(e) => onChangeInput(e)}
                                            value={socialsValue && socialsValue.linked_in_link} />
                                    </InputGroup>
                                </div>

                                <div className='col-md-6 col-12 mb-3'>
                                    <label
                                        className='form-label'><span className="me-2"><svg xmlns="http://www.w3.org/2000/svg" height={20} width={20} viewBox="0 0 448 512"><path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z" /></svg></span>{getFormattedMessage("admin.setting.social.tiktok.label")}: </label>
                                    <InputGroup>
                                        <InputGroup.Text>{'https://tiktok.com/@'}</InputGroup.Text>
                                        <input type='text' name='tiktok_link'
                                            className='form-control'
                                            placeholder={placeholderText("username.title")}
                                            onChange={(e) => onChangeInput(e)}
                                            value={socialsValue && socialsValue.tiktok_link} />
                                    </InputGroup>
                                </div>
                                <div className='col-md-6 col-12 mb-3'>
                                    <label
                                        className='form-label'><span className="me-2"><svg xmlns="http://www.w3.org/2000/svg" height={20} width={20} viewBox="0 0 448 512"><path d="M400 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zm-48.9 158.8c.2 2.8.2 5.7.2 8.5 0 86.7-66 186.6-186.6 186.6-37.2 0-71.7-10.8-100.7-29.4 5.3.6 10.4.8 15.8.8 30.7 0 58.9-10.4 81.4-28-28.8-.6-53-19.5-61.3-45.5 10.1 1.5 19.2 1.5 29.6-1.2-30-6.1-52.5-32.5-52.5-64.4v-.8c8.7 4.9 18.9 7.9 29.6 8.3a65.447 65.447 0 0 1-29.2-54.6c0-12.2 3.2-23.4 8.9-33.1 32.3 39.8 80.8 65.8 135.2 68.6-9.3-44.5 24-80.6 64-80.6 18.9 0 35.9 7.9 47.9 20.7 14.8-2.8 29-8.3 41.6-15.8-4.9 15.2-15.2 28-28.8 36.1 13.2-1.4 26-5.1 37.8-10.2-8.9 13.1-20.1 24.7-32.9 34z" /></svg></span>{getFormattedMessage("admin.setting.social.twitter.label")}: </label>
                                    <InputGroup>
                                        <InputGroup.Text>{'https://twitter.com/'}</InputGroup.Text>
                                        <input type='text' name='twitter_link'
                                            className='form-control'
                                            placeholder={placeholderText("username.title")}
                                            onChange={(e) => onChangeInput(e)}
                                            value={socialsValue && socialsValue.twitter_link} />
                                    </InputGroup>
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={socialSetting}
                                        editDisabled={disabled}
                                        addDisabled={disabled}
                                        onSubmit={onSubmit}
                                        link='/app/admin/settings-main'
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { socialSetting } = state;
    return { socialSetting }
};

export default connect(mapStateToProps, { updateSocialsSetting, fetchSocialSetting })(SocialsSetting);
