import React, { useEffect, useRef, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactSelect from '../../../shared/select/reactSelect';
import { enabledPaypalPaymentsOptions, paypalModeOptions } from "../../../constants";
import { updatePaypalSetting, fetchPaypalSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import PaymentConfiguration from "./PaymentConfiguration";

const PaypalTab = (props) => {

    const { updatePaypalSetting, fetchPaypalSetting, settings } = props

    useEffect(() => {
        fetchPaypalSetting()
    }, [])

    const [paypalValue, setPaypalValue] = useState({
        enable_paypal_payments: settings && settings?.enable_paypal_payments ?
            settings?.enable_paypal_payments === '0' ? { value: 0, label: placeholderText('globally.input.no.lable') } : { value: 1, label: placeholderText('globally.input.yes.lable') }
            : { value: 0, label: placeholderText('globally.input.no.lable') },

        paypal_payment_mode: settings && settings?.paypal_payment_mode ? settings?.paypal_payment_mode === '0' ? { value: 0, label: placeholderText("setting.sandbox.lable") } : { value: 1, label: placeholderText("setting.live.lable") } : { value: 0, label: placeholderText("setting.sandbox.lable") },

        paypal_client_id: settings && settings?.paypal_client_id ? settings?.paypal_client_id : '',
        paypal_secret: settings && settings?.paypal_secret ? settings?.paypal_secret : ''
    })

    useEffect(() => {
        if (settings) {
            setPaypalValue({
                enable_paypal_payments: settings && settings?.enable_paypal_payments ?
                    settings?.enable_paypal_payments === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') }
                    : { value: 0, label: getFormattedMessage('globally.input.no.lable') },

                paypal_payment_mode: settings && settings?.paypal_payment_mode ? settings?.paypal_payment_mode === '0' ? { value: 0, label: getFormattedMessage("setting.sandbox.lable") } : { value: 1, label: getFormattedMessage("setting.live.lable") } : { value: 0, label: getFormattedMessage("setting.sandbox.lable") },

                paypal_client_id: settings && settings?.paypal_client_id ? settings?.paypal_client_id : '',
                paypal_secret: settings && settings?.paypal_secret ? settings?.paypal_secret : ''
            })
        }
    }, [settings])

    const disabled = settings &&
        Number(settings.enable_paypal_payments) === paypalValue.enable_paypal_payments.value &&
        Number(settings.paypal_payment_mode) === paypalValue.paypal_payment_mode.value &&
        settings.paypal_client_id === paypalValue.paypal_client_id &&
        settings.paypal_secret === paypalValue.paypal_secret

    const [errors, setErrors] = useState({
        paypal_client_id: '',
        paypal_secret: ''
    })

    const enabledPaypalPaymentsOption = getFormattedOptions(enabledPaypalPaymentsOptions)
    const enabledPaypalPaymentsDefaultValue = enabledPaypalPaymentsOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const onChangeInput = (e) => {
        setPaypalValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    }

    // const onPaypalKeyChange = (obj) => {
    //     setPaypalValue(inputs => ({ ...inputs, enable_paypal_payments: obj }));
    //     setErrors('');
    // }

    // const paypalModeOption = getFormattedOptions(paypalModeOptions)
    // const paypalModeDefaultValue = paypalModeOption.map((option) => {
    //     return {
    //         value: option.id,
    //         label: option.name
    //     }
    // })

    // const onPaypalModeChange = (obj) => {
    //     setPaypalValue(inputs => ({ ...inputs, paypal_payment_mode: obj }));
    //     setErrors('');
    // }

    const onCheckedMode = (e) => {
        const check = e.target.checked
        if (check) {
            setPaypalValue(inputs => ({ ...inputs, paypal_payment_mode: { value: 1, label: getFormattedMessage("setting.live.lable") } }));
        } else {
            setPaypalValue(inputs => ({ ...inputs, paypal_payment_mode: { value: 0, label: getFormattedMessage("setting.sandbox.lable") } }));
        }
        setErrors('');
    };

    const onCheckedStatus = (e) => {
        const check = e.target.checked
        if (check) {
            setPaypalValue(inputs => ({ ...inputs, enable_paypal_payments: { value: 1, label: getFormattedMessage('globally.input.yes.lable') } }));
        } else {
            setPaypalValue(inputs => ({ ...inputs, enable_paypal_payments: { value: 0, label: getFormattedMessage('globally.input.no.lable') } }));
        }
        setErrors('');
    };

    const prepareFormData = (data) => {
        const formValue = {
            enable_paypal_payments: data ? data.enable_paypal_payments.value : '',
            paypal_payment_mode: data ? data.paypal_payment_mode.value : '',
            paypal_client_id: data ? data.paypal_client_id : '',
            paypal_secret: data ? data.paypal_secret : ''
        }
        return formValue
    }

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (paypalValue.enable_paypal_payments.value === 1 || paypalValue.enable_paypal_payments.value === "1") {
            if (paypalValue.paypal_client_id.trim().length === 0 || !paypalValue.paypal_client_id) {
                errorss.paypal_client_id = getFormattedMessage("Please.Enter.Client.ID.validate.label")
            } else if (paypalValue.paypal_secret.trim().length === 0 || !paypalValue.paypal_secret) {
                errorss.paypal_secret = getFormattedMessage("Please.enter.secret.validate.label")
            } else {
                isValid = true
            }
        } else {
            isValid = true
        }
        setErrors(errorss);
        return isValid;
    }

    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation()
        if (valid) {
            updatePaypalSetting(prepareFormData(paypalValue));
        }

    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText("globally.paypal.payment.title")} />
                <PaymentConfiguration page={'Paypal'} />

                <div className='card setting-card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-6 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("globally.input.client-id.label")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='paypal_client_id'
                                        placeholder={placeholderText("settings.login.client-id.placeholder")}
                                        value={paypalValue.paypal_client_id}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['paypal_client_id'] ? errors['paypal_client_id'] : null}
                                    </span>
                                </div>
                                <div className='col-6 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.paypal.settings.secret.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='paypal_secret'
                                        placeholder={placeholderText("admin.paypal.settings.secret.placeholder")}
                                        value={paypalValue.paypal_secret}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['paypal_secret'] ? errors['paypal_secret'] : null}
                                    </span>
                                </div>
                                <div className="col-md-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="enable_paypal_payments" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={paypalValue?.enable_paypal_payments?.value === 0 ? false : true}
                                            value={paypalValue.enable_paypal_payments?.value}
                                            onChange={(event) => onCheckedStatus(event)} />
                                        <>
                                            {getFormattedMessage("globally.input.status.lable")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                {/* <ReactSelect title={getFormattedMessage('settings.enable.paypal-payment.label')}
                                    name='enable_paypal_payments'
                                    value={paypalValue.enable_paypal_payments}
                                    errors={errors['currency']}
                                    isRequired
                                    defaultValue={enabledPaypalPaymentsDefaultValue[0]}
                                    multiLanguageOption={enabledPaypalPaymentsOption}
                                    onChange={onPaypalKeyChange}
                                /> */}
                                <div className="col-md-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="paypal_payment_mode" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={paypalValue?.paypal_payment_mode?.value === 0 ? false : true}
                                            value={paypalValue.paypal_payment_mode?.value}
                                            onChange={(event) => onCheckedMode(event)} />
                                        <>
                                            {getFormattedMessage("settings.paypal.is-live.label")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                {/* <ReactSelect title={getFormattedMessage('settings.paypal.mode.label')}
                                    name='paypal_payment_mode'
                                    value={paypalValue.paypal_payment_mode}
                                    errors={errors['currency']}
                                    isRequired
                                    defaultValue={paypalModeDefaultValue[0]}
                                    multiLanguageOption={paypalModeOption}
                                    onChange={onPaypalModeChange}
                                /> */}
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={settings}
                                        editDisabled={disabled}
                                        // onEditRecord={singleQrCode}
                                        onSubmit={onSubmit}
                                        // editDisabled={!qrDetails.name}
                                        link='/app/admin/settings-main'
                                    // addDisabled={!paypalValue.paypal_client_id}
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { settings } = state;
    return { settings }
};

export default connect(mapStateToProps, { updatePaypalSetting, fetchPaypalSetting })(PaypalTab);
