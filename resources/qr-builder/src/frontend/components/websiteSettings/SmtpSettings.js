import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import { fetchSmtpSetting, updateSmtpSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import { smtpSettingsencryptionOptions, toastType } from "../../../constants";
import ReactSelect from "../../../shared/select/reactSelect";
import { addToast } from "../../../store/action/toastAction";


const SmtpSetting = (props) => {

    const { fetchSmtpSetting, smtpMailSetting, updateSmtpSetting } = props
    const dispatch = useDispatch()

    useEffect(() => {
        fetchSmtpSetting()
    }, [])

    const smtpSettingsencryptionTypeOption = getFormattedOptions(smtpSettingsencryptionOptions)
    const smtpSettingsencryptionTypeDefaultValue = smtpSettingsencryptionTypeOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const [errors, setErrors] = useState({
        from_name: "",
        from_email: "",
        host: "",
        port: "",
        authentication: "",
        email_username: "",
        email_password: ""
    })

    const [smtpValue, setSmtpValue] = useState({
        from_name: smtpMailSetting.from_name || "",
        from_email: smtpMailSetting.from_email || "",
        host: smtpMailSetting.host || "",
        encryption: { value: 0, label: getFormattedMessage("globally.TLS.title") },
        port: smtpMailSetting.port || "",
        authentication: parseInt(smtpMailSetting.authentication) === 0 ? 0 : 1 || 1,
        email_username: smtpMailSetting.email_username || "",
        email_password: smtpMailSetting.email_password || ""
    })

    useEffect(() => {
        setSmtpValue({
            from_name: smtpMailSetting && smtpMailSetting.from_name || "",
            from_email: smtpMailSetting && smtpMailSetting.from_email || "",
            host: smtpMailSetting && smtpMailSetting.host || "",
            encryption: smtpMailSetting && smtpMailSetting.encryption !== "" ? smtpSettingsencryptionTypeDefaultValue.filter((d) => d?.label === smtpMailSetting.encryption)[0] : { value: 0, label: getFormattedMessage('globally.TLS.title') },
            port: smtpMailSetting && smtpMailSetting.port || "",
            authentication: smtpMailSetting && parseInt(smtpMailSetting.authentication) === 0 ? 0 : 1 || 1,
            email_username: smtpMailSetting && smtpMailSetting.email_username || "",
            email_password: smtpMailSetting && smtpMailSetting.email_password || ""
        })
    }, [smtpMailSetting])

    const disabled = smtpMailSetting &&
        smtpMailSetting.from_name === smtpValue.from_name &&
        smtpMailSetting.from_email === smtpValue.from_email &&
        smtpMailSetting.host === smtpValue.host &&
        (smtpMailSetting.encryption !== "" ? smtpMailSetting.encryption === smtpValue?.encryption?.label : false) &&
        smtpMailSetting.port === smtpValue.port &&
        smtpMailSetting.email_username === smtpValue.email_username &&
        smtpMailSetting.email_password === smtpValue.email_password

    const onChangeInput = (e) => {
        const { value } = e.target;
        setSmtpValue(inputs => ({ ...inputs, [e.target.name]: value }))
        setErrors('');
    };

    const onEncChange = (obj) => {
        setSmtpValue(inputs => ({ ...inputs, encryption: obj }));
        setErrors('');
    }
    const onChecked = (e) => {
        const check = e.target.checked
        if (check) {
            setSmtpValue(inputs => ({ ...inputs, [e.target.name]: 1 }))
        } else {
            setSmtpValue(inputs => ({ ...inputs, [e.target.name]: 0 }))
        }
        setErrors('');
    };

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        let emailPattern = /^\w+@[a-zA-Z_]+?\.[a-zA-Z]{2,3}$/;
        if (!smtpValue.from_name || smtpValue.from_name?.trim()?.length === 0) {
            errorss.from_name = getFormattedMessage("Please.Enter.The.From.Name.validate.label")
        } else if (!smtpValue.from_email) {
            errorss.from_email = getFormattedMessage("Please.Enter.The.From.Email.validate.label")
        } else if ((emailPattern.test(smtpValue.from_email)) === false) {
            errorss.from_email = getFormattedMessage("user.input.email.valid.validate.label")
        } else if (!smtpValue.host || smtpValue.host?.trim()?.length === 0) {
            errorss.host = getFormattedMessage("Please.Enter.The.Host.validate.label")
        } else if (!smtpValue.port || smtpValue.port?.trim()?.length === 0) {
            errorss.port = getFormattedMessage("Please.Enter.The.Port.validate.label")
        } else if (!smtpValue.email_username || smtpValue.email_username?.trim()?.length === 0) {
            errorss.email_username = getFormattedMessage("Please.Enter.Username.validate.label")
        } else if (!smtpValue.email_password || smtpValue.email_password?.trim()?.length === 0) {
            errorss.email_password = getFormattedMessage("user.input.password.validate.label")
        } else {
            isValid = true
        }
        setErrors(errorss);
        return isValid;
    }

    const prepareFormData = (data) => {
        const formValue = {
            from_name: data.from_name,
            from_email: data.from_email,
            host: data.host,
            encryption: data?.encryption?.label,
            port: data.port,
            authentication: data.authentication,
            email_username: data.email_username,
            email_password: data.email_password
        }
        return formValue
    }

    const onSubmit = (e) => {
        e.preventDefault()
        const valid = handleValidation();
        if (valid) {
            updateSmtpSetting(prepareFormData(smtpValue));
            // dispatch(addToast(
            //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
            setSmtpValue({
                from_name: smtpMailSetting && smtpMailSetting.from_name || "",
                from_email: smtpMailSetting && smtpMailSetting.from_email || "",
                host: smtpMailSetting && smtpMailSetting.host || "",
                encryption: smtpMailSetting && smtpMailSetting.encryption !== "" ? smtpSettingsencryptionTypeDefaultValue.filter((d) => d?.label === smtpMailSetting.encryption)[0] : { value: 0, label: getFormattedMessage('globally.TLS.title') },
                port: smtpMailSetting && smtpMailSetting.port || "",
                authentication: smtpMailSetting && parseInt(smtpMailSetting.authentication) === 0 ? 0 : 1 || 1,
                email_username: smtpMailSetting && smtpMailSetting.email_username || "",
                email_password: smtpMailSetting && smtpMailSetting.email_password || ""
            })
        }

    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('settings.smtp.title')} />
                <WebsiteSettings page={'smtp'} />

                <div className='card setting-card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("host.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='host'
                                        placeholder={placeholderText("enter.host.placeholder")}
                                        value={smtpValue.host}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['host'] ? errors['host'] : null}
                                    </span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("users.table.user-name.column.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='email_username'
                                        placeholder={placeholderText("enter.username.placeholder")}
                                        value={smtpValue.email_username}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['email_username'] ? errors['email_username'] : null}
                                    </span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("user.input.password.label")}:<span className="required" />
                                    </label>
                                    <input
                                        type='password'
                                        name='email_password'
                                        placeholder={placeholderText("user.input.password.placeholder.label")}
                                        value={smtpValue.email_password}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['email_password'] ? errors['email_password'] : null}
                                    </span>
                                </div>
                                <div className='col-sm-4 col-12 mb-3'>
                                    <ReactSelect
                                        title={getFormattedMessage("globally.input.encryption.lable")}
                                        name='encryption'
                                        value={smtpValue.encryption}
                                        // errors={errors['type']}
                                        defaultValue={smtpSettingsencryptionTypeDefaultValue[0]}
                                        multiLanguageOption={smtpSettingsencryptionTypeOption}
                                        onChange={onEncChange}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['encryption'] ? errors['encryption'] : null}
                                    </span>
                                </div>
                                <div className='col-sm-8 col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("port.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='port'
                                        placeholder={placeholderText("enter.port.placeholder")}
                                        value={smtpValue.port}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['port'] ? errors['port'] : null}
                                    </span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("from.name.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='from_name'
                                        value={smtpValue.from_name}
                                        placeholder={placeholderText("enter.from.name.placeholder")}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['from_name'] ? errors['from_name'] : null}
                                    </span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("from.email.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='email'
                                        name='from_email'
                                        placeholder={placeholderText("enter.from.email.placeholder")}
                                        value={smtpValue.from_email}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['from_email'] ? errors['from_email'] : null}
                                    </span>
                                </div>

                                {/*<div className='col-12 my-3'>*/}
                                {/*    <label className="form-check form-switch form-switch-sm">*/}
                                {/*        <input*/}
                                {/*            autoComplete="off" name="authentication" data-id="704" className="form-check-input admin-status" type="checkbox"*/}
                                {/*            checked={smtpValue.authentication === 0 ? false : true}*/}
                                {/*            value={smtpValue.authentication}*/}
                                {/*            onChange={(event) => onChecked(event)} />*/}
                                {/*        <>*/}
                                {/*            {'Authentication'}*/}
                                {/*        </>*/}
                                {/*        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>*/}
                                {/*    </label>*/}
                                {/*</div>*/}

                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={smtpMailSetting}
                                        editDisabled={disabled}
                                        onSubmit={onSubmit}
                                        link='/app/admin/settings-main'
                                        addDisabled={!smtpValue.from_name}
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { settings, callSetting, adsSetting, smtpMailSetting } = state;
    return { settings, callSetting, adsSetting, smtpMailSetting }
};

export default connect(mapStateToProps, { updateSmtpSetting, fetchSmtpSetting })(SmtpSetting);
