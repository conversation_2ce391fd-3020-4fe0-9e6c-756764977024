import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import ModelFooter from '../../../shared/components/modelFooter';
import { fetchSocialFacebookLoginSetting, fetchSocialGoogleLoginSetting, updateSocialsFacebookLoginSetting, updateSocialsGoogleLoginSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsitesocialSetting from "./WebsiteSettings";
import { getFormattedMessage, placeholderText } from "../../../shared/sharedMethod";
import { addToast } from "../../../store/action/toastAction";
import { toastType } from "../../../constants";


const SocialLoginSetting = (props) => {
    const { fetchSocialGoogleLoginSetting, socialLogins, fetchSocialFacebookLoginSetting, updateSocialsFacebookLoginSetting, updateSocialsGoogleLoginSetting } = props
    const [socialsValue, setSocialsValue] = useState({
        enable_facebook_login: socialLogins?.enable_facebook_login ? parseInt(socialLogins?.enable_facebook_login) : 0,
        facebook_app_id: socialLogins?.facebook_app_id ? socialLogins?.facebook_app_id : null,
        enable_google_login: socialLogins?.enable_google_login ? parseInt(socialLogins?.enable_google_login) : 0,
        google_client_id: socialLogins?.google_client_id ? socialLogins?.google_client_id : null,
    })
    const [error, setError] = useState({
        facebook_app_id: "",
        google_client_id: "",
    })

    useEffect(() => {
        fetchSocialGoogleLoginSetting()
        // fetchSocialFacebookLoginSetting()
    }, [])

    useEffect(() => {
        if (socialLogins) {
            setSocialsValue({
                enable_facebook_login: socialLogins?.enable_facebook_login ? parseInt(socialLogins?.enable_facebook_login) : 0,
                facebook_app_id: socialLogins?.facebook_app_id ? socialLogins?.facebook_app_id : null,
                enable_google_login: socialLogins?.enable_google_login ? parseInt(socialLogins?.enable_google_login) : 0,
                google_client_id: socialLogins?.google_client_id ? socialLogins?.google_client_id : null,
            })
        }
    }, [socialLogins])

    const onChangeInput = (e) => {
        e.preventDefault();
        const { value } = e.target;
        setSocialsValue(inputs => ({ ...inputs, [e.target.name]: value.length === 0 ? null : value }))
    };

    const disabled = socialLogins &&
        socialLogins.google_client_id === socialsValue.google_client_id &&
        Number(socialLogins.enable_google_login) === socialsValue.enable_google_login


    const handleValidation = () => {
        let errorss = {};
        let valid = false
        // if (socialsValue.enable_facebook_login === 1 && socialsValue.enable_google_login === 1) {
        //     if (!socialsValue.facebook_app_id) {
        //         errorss.facebook_app_id = getFormattedMessage("Please.Enter.App.ID.validate.label")
        //     } else if (!socialsValue.google_client_id) {
        //         errorss.google_client_id = getFormattedMessage("Please.Enter.Client.ID.validate.label")
        //     } else {
        //         valid = true
        //     }
        // } else
        // if (socialsValue.enable_facebook_login === 1) {
        //     if (!socialsValue.facebook_app_id) {
        //         errorss.facebook_app_id = getFormattedMessage("Please.Enter.App.ID.validate.label")
        //     } else {
        //         valid = true
        //     }
        // } else
        if (socialsValue.enable_google_login === 1) {
            if (!socialsValue.google_client_id || socialsValue.google_client_id.trim().length === 0) {
                errorss.google_client_id = getFormattedMessage("Please.Enter.Client.ID.validate.label")
            } else {
                valid = true
            }
        } else {
            valid = true
        }

        setError(errorss)
        return valid
    }

    const onSubmit = (e) => {
        e.preventDefault();
        const valid = handleValidation()
        if (valid) {
            // updateSocialsFacebookLoginSetting({
            //     enable_facebook_login: socialsValue.enable_facebook_login,
            //     facebook_app_id: socialsValue.facebook_app_id,
            // })
            // dispatch(addToast(
            //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
            // setSocialsValue({
            //     // enable_facebook_login: socialLogins?.enable_facebook_login ? parseInt(socialLogins?.enable_facebook_login) : 0,
            //     // facebook_app_id: socialLogins?.facebook_app_id ? socialLogins?.facebook_app_id : null,
            //     enable_google_login: socialLogins?.enable_google_login ? parseInt(socialLogins?.enable_google_login) : 0,
            //     google_client_id: socialLogins?.google_client_id ? socialLogins?.google_client_id : null,
            // })
            updateSocialsGoogleLoginSetting({
                enable_google_login: socialsValue.enable_google_login,
                google_client_id: socialsValue.google_client_id
            })
        }
    }

    const onChecked = (e) => {
        const check = e.target.checked
        if (check) {
            setSocialsValue(inputs => ({ ...inputs, [e.target.name]: 1 }))
        } else {
            setSocialsValue(inputs => ({ ...inputs, [e.target.name]: 0 }))
        }
    };

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('settings.social.login.title')} />
                <WebsitesocialSetting page={'social-login'} />
                <Form>
                    {/* <div className='card mb-5'>
                        <div className='card-body'>
                            <div className='row'>
                                <div className='col-12 mb-4'>
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input autoComplete="off" name="enable_facebook_login" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={socialsValue.enable_facebook_login === 1 ? true : false}
                                            value={socialsValue.enable_facebook_login}
                                            onChange={(e) => onChecked(e)} />
                                        <>
                                            {getFormattedMessage("settings.enable.facebook-login.label")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                <div className='col-12 mb-4'>
                                    <label className='form-label'>
                                        {getFormattedMessage("settings.login.app-id.label")}:
                                    </label>
                                    <span className='required' />
                                    <input type='text' name='facebook_app_id' className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                        placeholder={placeholderText("settings.login.app-id.placeholder")}
                                        value={socialsValue.facebook_app_id || ""} />
                                    <span className='text-danger d-block fw-400 fs-small mt-2'>
                                        {error['facebook_app_id'] ? error['facebook_app_id'] : null}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div> */}
                    <div className='card mb-5 setting-card'>
                        <div className='card-body'>
                            <div className='row'>
                                <div className='mb-4 w-auto'>
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input autoComplete="off" name="enable_google_login" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={socialsValue.enable_google_login === 1 ? true : false}
                                            value={socialsValue.enable_google_login}
                                            onChange={(e) => onChecked(e)} />
                                        <>
                                            {getFormattedMessage("settings.enable.google-login.label")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                <div className='col-12 mb-4'>
                                    <label className='form-label'>
                                        {getFormattedMessage("settings.login.client-id.label")}:
                                    </label>
                                    <span className='required' />
                                    <input type='text' name='google_client_id' className='form-control'
                                        placeholder={placeholderText("settings.login.client-id.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                        value={socialsValue.google_client_id || ""} />
                                    <span className='text-danger d-block fw-400 fs-small mt-2'>
                                        {error['google_client_id'] ? error['google_client_id'] : null}
                                    </span>
                                </div>
                            </div>
                            <div className="d-flex justify-content-start">
                                <ModelFooter
                                    onEditRecord={socialLogins}
                                    editDisabled={disabled}
                                    addDisabled={!socialsValue.google_client_id || !socialsValue.facebook_app_id}
                                    className={"mt-0"}
                                    onSubmit={onSubmit}
                                    link='/app/admin/settings-main'
                                />
                            </div>
                        </div>
                    </div>
                </Form>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { socialLogins } = state;
    return { socialLogins }
};

export default connect(mapStateToProps, { fetchSocialGoogleLoginSetting, updateSocialsFacebookLoginSetting, fetchSocialFacebookLoginSetting, updateSocialsGoogleLoginSetting })(SocialLoginSetting);
