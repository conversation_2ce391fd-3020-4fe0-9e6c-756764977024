import React, { useEffect, useRef, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactSelect from '../../../shared/select/reactSelect';
import { commonYesOrNoOptions, enabledPaypalPaymentsOptions, paypalModeOptions } from "../../../constants";
import { fetchStripeSetting, updateStripeSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import PaymentConfiguration from "./PaymentConfiguration";

const StripeSetting = (props) => {

    const { fetchStripeSetting, updateStripeSetting, stripe } = props

    useEffect(() => {
        fetchStripeSetting()
    }, [])

    const [stripeValue, setStripeValue] = useState({
        enable_stripe_payments: { value: 0, label: getFormattedMessage('globally.input.no.lable') },
        stripe_Key: '',
        stripe_secret: '',
        stripe_payment_mode: { value: 0, label: getFormattedMessage('setting.live.lable') },
    })

    useEffect(() => {
        if (stripe) {
            setStripeValue({
                enable_stripe_payments: stripe && stripe?.enable_stripe_payments ? stripe?.enable_stripe_payments === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
                stripe_Key: stripe && stripe?.stripe_Key ? stripe?.stripe_Key : '',
                stripe_secret: stripe && stripe?.stripe_secret ? stripe?.stripe_secret : '',
                stripe_payment_mode: stripe && stripe?.stripe_payment_mode ? stripe?.stripe_payment_mode === '0' ? { value: 0, label: getFormattedMessage("setting.sandbox.lable") } : { value: 1, label: getFormattedMessage('setting.live.lable') } : { value: 0, label: getFormattedMessage("setting.sandbox.lable") },
            })
        }
    }, [stripe])


    const disabled = stripe &&
        Number(stripe.enable_stripe_payments) === stripeValue.enable_stripe_payments.value &&
        stripe.stripe_Key === stripeValue.stripe_Key &&
        stripe.stripe_secret === stripeValue.stripe_secret &&
        Number(stripe.stripe_payment_mode) === stripeValue.stripe_payment_mode.value


    const [errors, setErrors] = useState({
        stripe_Key: "",
    })

    // const enabledStripeOption = getFormattedOptions(commonYesOrNoOptions)
    // const enabledStripePaymentsDefaultValue = enabledStripeOption.map((option) => {
    //     return {
    //         value: option.id,
    //         label: option.name
    //     }
    // })

    // const paypalModeOption = getFormattedOptions(paypalModeOptions)
    // const paypalModeDefaultValue = paypalModeOption.map((option) => {
    //     return {
    //         value: option.id,
    //         label: option.name
    //     }
    // })

    // const onStripeKeyChange = (obj) => {
    //     setStripeValue(inputs => ({ ...inputs, enable_stripe_payments: obj }));
    // }

    const onChangeInput = (e) => {
        setStripeValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
    }

    // const onPaypalModeChange = (obj) => {
    //     setStripeValue(inputs => ({ ...inputs, stripe_payment_mode: obj }));
    // }

    const onCheckedMode = (e) => {
        const check = e.target.checked
        if (check) {
            setStripeValue(inputs => ({ ...inputs, stripe_payment_mode: { value: 1, label: getFormattedMessage("setting.live.lable") } }));
        } else {
            setStripeValue(inputs => ({ ...inputs, stripe_payment_mode: { value: 0, label: getFormattedMessage("setting.sandbox.lable") } }));
        }
        setErrors('');
    };

    const onCheckedStatus = (e) => {
        const check = e.target.checked
        if (check) {
            setStripeValue(inputs => ({ ...inputs, enable_stripe_payments: { value: 1, label: getFormattedMessage('globally.input.yes.lable') } }));
        } else {
            setStripeValue(inputs => ({ ...inputs, enable_stripe_payments: { value: 0, label: getFormattedMessage('globally.input.no.lable') } }));
        }
        setErrors('');
    };

    const prepareFormData = (data) => {
        const formValue = {
            enable_stripe_payments: data ? data.enable_stripe_payments.value : '',
            stripe_Key: data ? data.stripe_Key : '',
            stripe_secret: data ? data.stripe_secret : '',
            stripe_payment_mode: data ? data.stripe_payment_mode.value : ""
        }
        return formValue
    }

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (stripeValue.enable_stripe_payments.value === 1 || stripeValue.enable_stripe_payments.value === "1") {
            if (stripeValue.stripe_Key.trim().length === 0 || !stripeValue.stripe_Key) {
                errorss.stripe_Key = getFormattedMessage("Please.enter.stripe.key.validate.label")
            } else if (stripeValue.stripe_secret.trim().length === 0 || !stripeValue.stripe_secret) {
                errorss.stripe_secret = getFormattedMessage("Please.enter.stripe.secret.validate.label")
            } else {
                isValid = true
            }
        } else {
            isValid = true
        }
        setErrors(errorss);
        return isValid;
    }

    const onSubmit = (event) => {
        event.preventDefault();
        setStripeValue(stripeValue);
        const valid = handleValidation()
        if (valid) {
            updateStripeSetting(prepareFormData(stripeValue));
        }
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText("globally.stripe.payment.title")} />
                <PaymentConfiguration page={'stripe'} />

                <div className='card setting-card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-6 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.stripe.settings.key.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='stripe_Key'
                                        value={stripeValue.stripe_Key}
                                        className='form-control'
                                        autoFocus={true}
                                        placeholder={placeholderText("admin.stripe.settings.key.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['stripe_Key'] ? errors['stripe_Key'] : null}
                                    </span>
                                </div>
                                <div className='col-6 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.stripe.settings.secret.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='stripe_secret'
                                        value={stripeValue.stripe_secret}
                                        className='form-control'
                                        autoFocus={true}
                                        placeholder={placeholderText("admin.stripe.settings.secret.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['stripe_secret'] ? errors['stripe_secret'] : null}
                                    </span>
                                </div>
                                <div className="col-md-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="enable_paypal_payments" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={stripeValue.enable_stripe_payments?.value === 0 ? false : true}
                                            value={stripeValue.enable_stripe_payments?.value}
                                            onChange={(event) => onCheckedStatus(event)} />
                                        <>
                                            {getFormattedMessage("globally.input.status.lable")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                {/* <div className='col-6 mb-3'>
                                    <ReactSelect
                                        title={getFormattedMessage('settings.enable.stripe-payment.label')}
                                        name='enable_stripe_payments'
                                        value={stripeValue.enable_stripe_payments}
                                        isRequired
                                        defaultValue={enabledStripePaymentsDefaultValue[0]}
                                        multiLanguageOption={enabledStripeOption}
                                        onChange={onStripeKeyChange}
                                    />
                                </div> */}
                                <div className="col-md-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="enable_paypal_payments" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={stripeValue.stripe_payment_mode?.value === 0 ? false : true}
                                            value={stripeValue.stripe_payment_mode?.value}
                                            onChange={(event) => onCheckedMode(event)} />
                                        <>
                                            {getFormattedMessage("settings.paypal.is-live.label")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                {/* <div className='col-6 mb-3'>
                                    <ReactSelect title={getFormattedMessage('settings.paypal.mode.label')}
                                        name='mode'
                                        value={stripeValue.stripe_payment_mode}
                                        // errors={errors['currency']}
                                        isRequired
                                        defaultValue={paypalModeDefaultValue[0]}
                                        multiLanguageOption={paypalModeOption}
                                        onChange={onPaypalModeChange}
                                    />
                                </div> */}
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={stripe}
                                        editDisabled={disabled}
                                        onSubmit={onSubmit}
                                        link='/app/admin/settings-main'
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { stripe } = state;
    return { stripe }
};

export default connect(mapStateToProps, { updateStripeSetting, fetchStripeSetting })(StripeSetting);
