import React, { useEffect, useState } from 'react'
import { Tab, Tabs } from 'react-bootstrap'
import { connect } from 'react-redux';
import { getFormattedMessage } from "../../../shared/sharedMethod"


const WebsiteSettings = (props) => {
    const { allConfigData, page } = props;
    const [key, setKey] = useState(page);

    useEffect(() => {
        setKey(page)
    }, [page])

    useEffect(() => {
        window.location.href = '#/app/admin/settings-' + key;
    }, [key])


    return (
        <>
            <Tabs defaultActiveKey={'main'} activeKey={page} id='uncontrolled-tab-example' onSelect={(k) => setKey(k)}
                className='mt-7 mb-0 responsive-setting-tab setting-tab'>
                <Tab eventKey='main' title={getFormattedMessage('admin.settings.main-settings.title')}
                    tabClassName='position-relative mb-0 ms-0'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                <Tab eventKey='captcha' title={getFormattedMessage("captcha.title")}
                    tabClassName='position-relative mb-0 mx-2'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                {/* <Tab eventKey='emailNotiy' title={getFormattedMessage("admin.emailsetting.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab> */}
                {/* <Tab eventKey='socials' title={getFormattedMessage("socials.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab> */}
                <Tab eventKey='social-login' title={getFormattedMessage("admin.social-login.settings.title")}
                    tabClassName='position-relative mb-0 me-2'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                {/* <Tab eventKey='ads' title={getFormattedMessage("admin.ads.settings.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab> */}
                <Tab eventKey='smtp' title={getFormattedMessage("settings.smtp.title")}
                    tabClassName='position-relative mb-0 me-0'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                {/* <Tab eventKey='customStyle' title={getFormattedMessage("admin.custom-style.settings.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab> */}
                {/* <Tab eventKey='announcement' title={getFormattedMessage("Announcement.title")}
                    tabClassName='position-relative mb-3 me-7'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab> */}
            </Tabs>

        </>
    )
}
const mapStateToProps = (state) => {
    const { allConfigData } = state;
    return { allConfigData }
};

export default connect(mapStateToProps, {})(WebsiteSettings);
