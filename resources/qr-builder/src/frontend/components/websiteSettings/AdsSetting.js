import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import { fetchAdsSetting, updateAdsSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import PaymentConfiguration from "./PaymentConfiguration";
import { addToast } from "../../../store/action/toastAction";
import { toastType } from "../../../constants";


const AdsSetting = (props) => {

    const { settings, callSetting, updateAdsSetting, adsSetting } = props

    const dispatch = useDispatch()

    useEffect(() => {
        dispatch(fetchAdsSetting())
    }, [settings, callSetting])

    const [adsValue, setAdsValue] = useState({
        ads_header: adsSetting.ads_header || "",
        ads_footer: adsSetting.ads_footer || "",
    })

    useEffect(() => {
        setAdsValue({
            ads_header: adsSetting.ads_header || "",
            ads_footer: adsSetting.ads_footer || "",
        })
    }, [adsSetting])


    const disabled = adsSetting &&
        adsSetting.ads_header === adsValue.ads_header &&
        adsSetting.ads_footer === adsValue.ads_footer

    const onChangeInput = (e) => {
        const { value } = e.target;
        setAdsValue(inputs => ({ ...inputs, [e.target.name]: value }))
    };

    const prepareFormData = (data) => {
        const formValue = {
            ads_header: data.ads_header,
            ads_footer: data.ads_footer,
        }
        return formValue
    }

    const onSubmit = (e) => {
        e.preventDefault()
        setCaptchaValue(captchaValue);
        // dispatch(addToast(
        //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
        setAdsValue({
            ads_header: adsSetting.ads_header || "",
            ads_footer: adsSetting.ads_footer || "",
        })
        updateAdsSetting(prepareFormData(adsValue));
        dispatch(fetchAdsSetting())
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('admin.ads.settings.title')} />
                <WebsiteSettings page={'ads'} />
                <div className='card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-12 mb-3'>
                                    <label
                                        className='form-label'>{getFormattedMessage("settings.ads.app-header.label")}: </label>
                                    <textarea className='form-control' name='ads_header' rows={2}
                                        placeholder={placeholderText("settings.ads.app-header.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                        value={adsValue.ads_header} />
                                </div>
                                <div className='col-12 my-3'>
                                    <label
                                        className='form-label'>{getFormattedMessage("settings.ads.app-footer.label")}: </label>
                                    <textarea className='form-control' name='ads_footer' rows={2}
                                        placeholder={placeholderText("settings.ads.app-footer.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                        value={adsValue.ads_footer} />
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={adsSetting}
                                        editDisabled={disabled}
                                        onSubmit={onSubmit}
                                        link='/app/admin/settings-main'
                                        addDisabled={!adsValue.ads_header && !adsValue.ads_footer}
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { settings, callSetting, adsSetting } = state;
    return { settings, callSetting, adsSetting }
};

export default connect(mapStateToProps, { updateAdsSetting })(AdsSetting);
