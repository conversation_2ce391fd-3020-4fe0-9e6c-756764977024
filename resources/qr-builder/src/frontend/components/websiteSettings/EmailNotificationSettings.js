import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import ModelFooter from '../../../shared/components/modelFooter';
import { fetchEmailNotificationSetting, updateEmailNotificationSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import { getFormattedMessage, placeholderText } from "../../../shared/sharedMethod";
import { ReactMultiEmailInput } from 'react-multi-email-input';
import 'react-multi-email-input/dist/style.css'
import { addToast } from "../../../store/action/toastAction";
import { toastType } from "../../../constants";

const EmailNotificationSettings = (props) => {
    const { emailsetting, updateEmailNotificationSetting, fetchEmailNotificationSetting } = props
    const dispatch = useDispatch()

    const [isdisabled, setdisabled] = useState({
        new_user: 0,
        delete_user: 0,
        new_payment: 0,
        contact_page_emails: 0
    })


    const [emailNotiyValue, setEmailNotiyValue] = useState({
        new_user: false,
        delete_user: false,
        new_payment: false,
        contact_page_emails: false
    })
    const [emailList, setEmailList] = useState([])
    const [errors, setErrors] = useState({
        emailList: ""
    })

    useEffect(() => {
        fetchEmailNotificationSetting()
    }, [])

    useEffect(() => {
        if (emailsetting?.new_user) {
            setdisabled({
                new_user: emailsetting ? Number(emailsetting.new_user) : 0,
                delete_user: emailsetting ? Number(emailsetting.delete_user) : 0,
                new_payment: emailsetting ? Number(emailsetting.new_payment) : 0,
                contact_page_emails: emailsetting ? Number(emailsetting.contact_page_emails) : 0,
            })
            setEmailNotiyValue({
                // emails_to_be_notified: emailsetting ? emailsetting.emails_to_be_notified : '',
                new_user: emailsetting && emailsetting.new_user ? Number(emailsetting.new_user) === 0 ? false : true : false,
                delete_user: emailsetting && emailsetting.delete_user ? Number(emailsetting.delete_user) === 0 ? false : true : false,
                new_payment: emailsetting && emailsetting.new_payment ? Number(emailsetting.new_payment) === 0 ? false : true : false,
                // new_custom_domain: emailsetting ? Number(emailsetting.new_custom_domain) : 0,
                contact_page_emails: emailsetting && emailsetting.contact_page_emails ? Number(emailsetting.contact_page_emails) === 0 ? false : true : false,
            })
            if (emailsetting?.emails_to_be_notified) {
                setEmailList(emailsetting && emailsetting?.emails_to_be_notified && emailsetting?.emails_to_be_notified?.split(","))
            }
        }
    }, [emailsetting])


    const disabled = emailsetting &&
        Number(emailsetting?.new_user) === isdisabled.new_user &&
        Number(emailsetting?.delete_user) === isdisabled.delete_user &&
        Number(emailsetting?.new_payment) === isdisabled.new_payment &&
        Number(emailsetting?.contact_page_emails) === isdisabled.contact_page_emails
        && (emailsetting?.emails_to_be_notified !== "" ? (emailsetting?.emails_to_be_notified?.split(',').length) === (emailList.length === undefined ? emailList.length + 1 : emailList.length) : emailList?.length === 0)

    const onChecked = (e) => {
        const check = e.target.checked
        if (check) {
            setEmailNotiyValue(inputs => ({ ...inputs, [e.target.name]: true }))
            setdisabled(inputs => ({ ...inputs, [e.target.name]: 1 }))
        } else {
            setEmailNotiyValue(inputs => ({ ...inputs, [e.target.name]: false }))
            setdisabled(inputs => ({ ...inputs, [e.target.name]: 0 }))
        }
        setErrors('');
    };

    const prepareFormData = (data) => {
        const formValue = {
            emails_to_be_notified: emailList.toString(),
            new_user: data ? data.new_user === false ? 0 : 1 : 0,
            delete_user: data ? data.delete_user === false ? 0 : 1 : 0,
            new_payment: data ? data.new_payment === false ? 0 : 1 : 0,
            // new_custom_domain: data ? data.new_custom_domain : 0,
            contact_page_emails: data ? data.contact_page_emails === false ? 0 : 1 : 0,
        }
        return formValue
    }

    const handleValidation = () => {
        let valid = false
        let errorss = {};
        if (emailList.length === 0) {
            errorss["emailList"] = getFormattedMessage("globally.input.email.validate.label")
        } else {
            valid = true
        }
        setErrors(errorss)
        return valid
    }

    const onSubmit = (event) => {
        event.preventDefault()
        setEmailNotiyValue(emailNotiyValue);
        const valid = handleValidation()
        if (valid) {
            updateEmailNotificationSetting(prepareFormData(emailNotiyValue));
            // dispatch(addToast(
            //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
            setEmailNotiyValue({
                // emails_to_be_notified: emailsetting ? emailsetting.emails_to_be_notified : '',
                new_user: emailsetting && emailsetting.new_user ? Number(emailsetting.new_user) === 0 ? false : true : false,
                delete_user: emailsetting && emailsetting.delete_user ? Number(emailsetting.delete_user) === 0 ? false : true : false,
                new_payment: emailsetting && emailsetting.new_payment ? Number(emailsetting.new_payment) === 0 ? false : true : false,
                // new_custom_domain: emailsetting ? Number(emailsetting.new_custom_domain) : 0,
                contact_page_emails: emailsetting && emailsetting.contact_page_emails ? Number(emailsetting.contact_page_emails) === 0 ? false : true : false,
            })
            setEmailList([])
        }
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('admin.email.setting.title')} />
                <WebsiteSettings page={'emailNotiy'} />

                <div className='card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-12 mb-3'>
                                    <label
                                        className='form-label'>{getFormattedMessage("emails.to.be.notified.label")}:<span className="required" />
                                    </label>
                                    <ReactMultiEmailInput
                                        className="p-5"
                                        placeholder={placeholderText("emails.to.be.notified.placeholder")}
                                        emails={emailList}
                                        setEmails={setEmailList}
                                    />
                                    <figure className='figure-caption mt-1'>
                                        {getFormattedMessage("emails.that.will.receive.a.notification.when.one.of.the.actions.from.below.are.performed.add.valid.email.addresses.separated.by.a.comma")}
                                    </figure>
                                    <span className="text-danger">
                                        {errors.emailList && errors.emailList}
                                    </span>
                                </div>
                                <div className='col-6 mb-3'>
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="new_user" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={emailNotiyValue.new_user}
                                            value={emailNotiyValue.new_user}
                                            onChange={(event) => onChecked(event)} />
                                        <>
                                            {getFormattedMessage("new.user.title")}
                                            <figure className='figure-caption'>
                                                {getFormattedMessage("receive.an.email.when.a.new.users.registers.to.the.website")}
                                            </figure>
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                <div className='col-6 mb-3'>
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="delete_user" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={emailNotiyValue.delete_user}
                                            value={emailNotiyValue.delete_user}
                                            onChange={(event) => onChecked(event)} />
                                        <>
                                            {getFormattedMessage("delete.user.title")}
                                            <figure className='figure-caption'>
                                                {getFormattedMessage("receive.an.email.when.any.user.deletes.their.account")}
                                            </figure>
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                <div className='col-6 mb-3'>
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="new_payment" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={emailNotiyValue.new_payment}
                                            value={emailNotiyValue.new_payment}
                                            onChange={(event) => onChecked(event)} />
                                        <>
                                            {getFormattedMessage("new.payment.title")}
                                            <figure className='figure-caption'>
                                                {getFormattedMessage("receive.an.email.when.a.new.payment.is.successfully.processed")}
                                            </figure>
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                {/* <div className='col-6 mb-3'>
                                    <label className="form-check form-switch form-switch-sm">
                                        <input
                                            autoComplete="off" name="new_custom_domain" data-id="704" className="form-check-input admin-status" type="checkbox"
                                            checked={emailNotiyValue.new_custom_domain === 0 ? false : true}
                                            value={emailNotiyValue.new_custom_domain}
                                            onChange={(event) => onChecked(event)} />
                                        <>
                                            {'New Custom Domain'}
                                            <figure className='figure-caption'>
                                                Receive an email when a new custom domain is pending approval.
                                            </figure>
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div> */}
                                <div className='col-6 mb-3'>
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="contact_page_emails" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={emailNotiyValue.contact_page_emails}
                                            value={emailNotiyValue.contact_page_emails}
                                            onChange={(event) => onChecked(event)} />
                                        <>
                                            {getFormattedMessage("contact.page.emails.title")}
                                            <figure className='figure-caption'>
                                                {getFormattedMessage("enable.the.contact.system")}
                                            </figure>
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={emailsetting}
                                        onSubmit={onSubmit}
                                        editDisabled={disabled}
                                        link='/app/admin/settings-main'
                                        addDisabled={!emailList}
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { emailsetting } = state;
    return { emailsetting }
};

export default connect(mapStateToProps, { updateEmailNotificationSetting, fetchEmailNotificationSetting })(EmailNotificationSettings);

