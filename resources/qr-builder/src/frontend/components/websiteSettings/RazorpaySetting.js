import React, { useEffect, useRef, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactSelect from '../../../shared/select/reactSelect';
import { commonYesOrNoOptions, enabledPaypalPaymentsOptions, paypalModeOptions } from "../../../constants";
import { fetchRazorpaySetting, updateRazorpaySetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import PaymentConfiguration from "./PaymentConfiguration";

const RazorpaySetting = (props) => {

    const { fetchRazorpaySetting, updateRazorpaySetting, razorpay } = props

    useEffect(() => {
        fetchRazorpaySetting()
    }, [])

    const [razorpayValue, setRazorpayValue] = useState({
        enable_razorpay_payments: { value: 0, label: getFormattedMessage('globally.input.no.lable') },
        razorpay_key: '',
        razorpay_secret: '',
        razorpay_payment_mode: { value: 0, label: getFormattedMessage('setting.live.lable') },
    })

    useEffect(() => {
        if (razorpay) {
            setRazorpayValue({
                enable_razorpay_payments: razorpay && razorpay?.enable_razorpay_payments ? razorpay?.enable_razorpay_payments === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
                razorpay_key: razorpay && razorpay?.razorpay_key ? razorpay.razorpay_key : '',
                razorpay_secret: razorpay && razorpay?.razorpay_secret ? razorpay.razorpay_secret : '',
                razorpay_payment_mode: razorpay && razorpay?.razorpay_payment_mode ? razorpay?.razorpay_payment_mode === '0' ? { value: 0, label: getFormattedMessage("setting.sandbox.lable") } : { value: 1, label: getFormattedMessage("setting.live.lable") } : { value: 0, label: getFormattedMessage("setting.sandbox.lable") },
            })
        }
    }, [razorpay])

    const disabled = razorpay &&
        Number(razorpay.enable_razorpay_payments) === razorpayValue.enable_razorpay_payments.value &&
        razorpay.razorpay_key === razorpayValue.razorpay_key &&
        razorpay.razorpay_secret === razorpayValue.razorpay_secret &&
        Number(razorpay.razorpay_payment_mode) === razorpayValue.razorpay_payment_mode.value

    const [errors, setErrors] = useState({
        razorpay_key: "",
    })

    // const enabledRazorPayOption = getFormattedOptions(commonYesOrNoOptions)
    // const enabledRazorPayPaymentsDefaultValue = enabledRazorPayOption.map((option) => {
    //     return {
    //         value: option.id,
    //         label: option.name
    //     }
    // })
    // const paypalModeOption = getFormattedOptions(paypalModeOptions)
    // const paypalModeDefaultValue = paypalModeOption.map((option) => {
    //     return {
    //         value: option.id,
    //         label: option.name
    //     }
    // })

    // const onPaypalModeChange = (obj) => {
    //     setRazorpayValue(inputs => ({ ...inputs, razorpay_payment_mode: obj }));
    //     setErrors('');
    // }


    // const onStripeKeyChange = (obj) => {
    //     setRazorpayValue(inputs => ({ ...inputs, enable_razorpay_payments: obj }));
    //     setErrors('');
    // }

    const onChangeInput = (e) => {
        setRazorpayValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    }

    const onCheckedMode = (e) => {
        const check = e.target.checked
        if (check) {
            setRazorpayValue(inputs => ({ ...inputs, razorpay_payment_mode: { value: 1, label: getFormattedMessage("setting.live.lable") } }));
        } else {
            setRazorpayValue(inputs => ({ ...inputs, razorpay_payment_mode: { value: 0, label: getFormattedMessage("setting.sandbox.lable") } }));
        }
        setErrors('');
    };

    const onCheckedStatus = (e) => {
        const check = e.target.checked
        if (check) {
            setRazorpayValue(inputs => ({ ...inputs, enable_razorpay_payments: { value: 1, label: getFormattedMessage('globally.input.yes.lable') } }));
        } else {
            setRazorpayValue(inputs => ({ ...inputs, enable_razorpay_payments: { value: 0, label: getFormattedMessage('globally.input.no.lable') } }));
        }
        setErrors('');
    };

    const prepareFormData = (data) => {
        const formValue = {
            enable_razorpay_payments: data ? data.enable_razorpay_payments.value : '',
            razorpay_key: data ? data.razorpay_key : '',
            razorpay_secret: data ? data.razorpay_secret : '',
            razorpay_payment_mode: data.razorpay_payment_mode.value
        }
        return formValue
    }

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        if (razorpayValue.enable_razorpay_payments.value === 1 || razorpayValue.enable_razorpay_payments.value === "1") {
            if (razorpayValue.razorpay_key.trim().length === 0 || !razorpayValue.razorpay_key) {
                errorss.razorpay_key = getFormattedMessage("Please.enter.razorpay.key.validate.label")
            } else if (razorpayValue.razorpay_secret.trim().length === 0 || !razorpayValue.razorpay_secret) {
                errorss.razorpay_secret = getFormattedMessage("Please.enter.razorpay.secret.validate.label")
            } else {
                isValid = true
            }
        } else {
            isValid = true
        }
        setErrors(errorss);
        return isValid;
    }

    const onSubmit = (event) => {
        event.preventDefault();
        setRazorpayValue(razorpayValue);
        const valid = handleValidation()
        if (valid) {
            updateRazorpaySetting(prepareFormData(razorpayValue));
        }
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText("globally.razorpay.payment.title")} />
                <PaymentConfiguration page={'razorpay'} />

                <div className='card setting-card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-6 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.razorpay.settings.key.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='razorpay_key'
                                        value={razorpayValue.razorpay_key}
                                        className='form-control'
                                        placeholder={placeholderText("admin.razorpay.settings.key.placeholder")}
                                        autoFocus={true}
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['razorpay_key'] ? errors['razorpay_key'] : null}
                                    </span>
                                </div>
                                <div className='col-6 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.razorpay.settings.secret.title")}:<span className="required" />
                                    </label>
                                    <input
                                        type='text'
                                        name='razorpay_secret'
                                        value={razorpayValue.razorpay_secret}
                                        className='form-control'
                                        autoFocus={true}
                                        placeholder={placeholderText("admin.razorpay.settings.secret.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['razorpay_secret'] ? errors['razorpay_secret'] : null}
                                    </span>
                                </div>
                                <div className="col-md-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="enable_paypal_payments" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={razorpayValue.enable_razorpay_payments?.value === 0 ? false : true}
                                            value={razorpayValue.enable_razorpay_payments?.value}
                                            onChange={(event) => onCheckedStatus(event)} />
                                        <>
                                            {getFormattedMessage("globally.input.status.lable")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                {/* <div className='col-6 mb-3'>
                                    <ReactSelect
                                        title={getFormattedMessage('settings.enable.razorpay-payment.label')}
                                        name='enable_razorpay_payments'
                                        value={razorpayValue.enable_razorpay_payments}
                                        isRequired
                                        defaultValue={enabledRazorPayPaymentsDefaultValue[0]}
                                        multiLanguageOption={enabledRazorPayOption}
                                        onChange={onStripeKeyChange}
                                    />
                                </div> */}
                                <div className="col-md-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="enable_paypal_payments" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={razorpayValue?.razorpay_payment_mode?.value === 0 ? false : true}
                                            value={razorpayValue.razorpay_payment_mode?.value}
                                            onChange={(event) => onCheckedMode(event)} />
                                        <>
                                            {getFormattedMessage("settings.paypal.is-live.label")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                {/* <div className='col-6 mb-3'>
                                    <ReactSelect title={getFormattedMessage('settings.paypal.mode.label')}
                                        name='mode'
                                        value={razorpayValue.razorpay_payment_mode}
                                        // errors={errors['currency']}
                                        isRequired
                                        defaultValue={paypalModeDefaultValue[0]}
                                        multiLanguageOption={paypalModeOption}
                                        onChange={onPaypalModeChange}
                                    />
                                </div> */}
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={razorpay}
                                        editDisabled={disabled}
                                        onSubmit={onSubmit}
                                        link='/app/admin/settings-main'
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { razorpay } = state;
    return { razorpay }
};

export default connect(mapStateToProps, { fetchRazorpaySetting, updateRazorpaySetting })(RazorpaySetting);
