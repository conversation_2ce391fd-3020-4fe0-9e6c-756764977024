import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactSelect from '../../../shared/select/reactSelect';
import { captchaContactPageOptions, captchaLostPasswordOptions, captchaProviderOptions, captchaResendActivationOptions, toastType } from "../../../constants";
import { fetchCaptchaSetting, updateCaptchaSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import { addToast } from "../../../store/action/toastAction";


const CaptchaTab = (props) => {

    const { settings, updateCaptchaSetting, fetchCaptchaSetting } = props
    const dispatch = useDispatch()

    useEffect(() => {
        fetchCaptchaSetting()
    }, [])

    const [captchaValue, setCaptchaValue] = useState({
        captcha_key: { value: 0, label: "Basic captcha (self-hosted)" },
        captcha_on_the_lost_password: { value: 0, label: getFormattedMessage('globally.input.no.lable') },
        captcha_on_register_login: { value: 0, label: getFormattedMessage('globally.input.no.lable') },
        captcha_on_contact_page: { value: 0, label: getFormattedMessage('globally.input.no.lable') },
        captcha_secret_key: settings?.captcha_secret_key ? settings?.captcha_secret_key : "",
        captcha_site_key: settings?.captcha_site_key ? settings?.captcha_site_key : "",
    })

    useEffect(() => {
        if (settings?.captcha_site_key) {
            const captcha_key_obj = captchaProviderOptions.filter((item) => item.id === Number(settings?.captcha_key))
            setCaptchaValue({
                captcha_key: captcha_key_obj ? { value: captcha_key_obj[0].id, label: captcha_key_obj[0].name } : { value: 0, label: "Basic captcha (self-hosted)" },
                // captcha_on_the_lost_password: settings ? settings?.captcha_on_the_lost_password === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
                // captcha_on_register_login: settings ? settings?.captcha_on_register_login === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
                // captcha_on_contact_page: settings ? settings?.captcha_on_contact_page === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
                captcha_secret_key: settings?.captcha_secret_key ? settings?.captcha_secret_key : "",
                captcha_site_key: settings?.captcha_site_key ? settings?.captcha_site_key : "",
            })
        }
    }, [settings])

    const disabled = settings &&
        Number(settings.captcha_key) === captchaValue.captcha_key.value &&
        // Number(settings.captcha_on_the_lost_password) === captchaValue.captcha_on_the_lost_password.value &&
        // Number(settings.captcha_on_register_login) === captchaValue.captcha_on_register_login.value &&
        // Number(settings.captcha_on_contact_page) === captchaValue.captcha_on_contact_page.value &&
        settings.captcha_secret_key === captchaValue.captcha_secret_key &&
        settings.captcha_site_key === captchaValue.captcha_site_key

    const captchaProviderOption = getFormattedOptions(captchaProviderOptions)
    const captchaProviderTypeDefaultValue = captchaProviderOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const captchaLostPasswordOption = getFormattedOptions(captchaLostPasswordOptions)
    const captchaLostPasswordTypeDefaultValue = captchaLostPasswordOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })


    const captchaResendActivationOption = getFormattedOptions(captchaResendActivationOptions)
    const captchaResendActivationTypeDefaultValue = captchaResendActivationOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })


    const captchaContactPageOption = getFormattedOptions(captchaContactPageOptions)
    const captchaContactPageTypeDefaultValue = captchaContactPageOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const onCaptchaContactChange = (obj) => {
        setCaptchaValue(inputs => ({ ...inputs, captcha_on_contact_page: obj }))
    }

    const onCaptchaResendActiveChange = (obj) => {
        setCaptchaValue(inputs => ({ ...inputs, captcha_on_register_login: obj }))
    }

    const onCaptchaLostPasswordChange = (obj) => {
        setCaptchaValue(inputs => ({ ...inputs, captcha_on_the_lost_password: obj }))
    }

    const onCaptchaKeyChange = (obj) => {
        setCaptchaValue(inputs => ({ ...inputs, captcha_key: obj }));
    }

    const onChangeInput = (e) => {
        setCaptchaValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
    }

    const onCheckedStatus = (e) => {
        const check = e.target.checked
        if (check) {
            setCaptchaValue(inputs => ({ ...inputs, captcha_key: { value: 1, label: getFormattedMessage('globally.input.yes.lable') } }));
        } else {
            setCaptchaValue(inputs => ({ ...inputs, captcha_key: { value: 0, label: getFormattedMessage('globally.input.no.lable') } }));
        }
    };

    const prepareFormData = (data) => {
        const formValue = {
            captcha_key: data ? data.captcha_key.value : '0',
            captcha_on_the_lost_password: data ? data.captcha_key.value : '0',
            captcha_on_register_login: data ? data.captcha_key.value : '0',
            captcha_on_contact_page: data ? data.captcha_key.value : '0',
            captcha_secret_key: data.captcha_secret_key,
            captcha_site_key: data.captcha_site_key,
        }
        return formValue
    }

    const onSubmit = (e) => {
        e.preventDefault()
        setCaptchaValue(captchaValue);
        updateCaptchaSetting(prepareFormData(captchaValue));
        // dispatch(addToast(
        //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
        const captcha_key_obj = captchaProviderOptions.filter((item) => item.id === Number(settings?.captcha_key))
        setCaptchaValue({
            captcha_key: captcha_key_obj ? { value: captcha_key_obj[0].id, label: captcha_key_obj[0].name } : { value: 0, label: "Basic captcha (self-hosted)" },
            // captcha_on_the_lost_password: settings ? settings?.captcha_on_the_lost_password === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
            // captcha_on_register_login: settings ? settings?.captcha_on_register_login === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
            // captcha_on_contact_page: settings ? settings?.captcha_on_contact_page === '0' ? { value: 0, label: getFormattedMessage('globally.input.no.lable') } : { value: 1, label: getFormattedMessage('globally.input.yes.lable') } : { value: 0, label: getFormattedMessage('globally.input.no.lable') },
            captcha_secret_key: settings?.captcha_secret_key ? settings?.captcha_secret_key : "",
            captcha_site_key: settings?.captcha_site_key ? settings?.captcha_site_key : ""
        })
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('admin.captcha.settings.title')} />
                <WebsiteSettings page={'captcha'} />

                <div className='card setting-card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                {/* <div className='col-6 mb-3'>
                                    <ReactSelect title={'Captcha provider'}
                                        name='captcha_key'
                                        value={captchaValue.captcha_key}
                                        // errors={errors['currency']}
                                        isRequired
                                        defaultValue={captchaProviderTypeDefaultValue[0]}
                                        multiLanguageOption={captchaProviderOption}
                                        onChange={onCaptchaKeyChange}
                                    />
                                </div> */}
                                <div className='mb-4 w-auto'>
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="enable_paypal_payments" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={captchaValue?.captcha_key?.value === 0 ? false : true}
                                            value={captchaValue.captcha_key?.value}
                                            onChange={(event) => onCheckedStatus(event)} />
                                        <>
                                            {getFormattedMessage("settings.enable.captcha.label")}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.captcha.settings.key.title")}:
                                    </label>
                                    <input
                                        type='text'
                                        name='captcha_site_key'
                                        value={captchaValue.captcha_site_key}
                                        placeholder={placeholderText("admin.captcha.settings.key.placeholder")}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.stripe.settings.secret.title")}:
                                    </label>
                                    <input
                                        type='text'
                                        name='captcha_secret_key'
                                        placeholder={placeholderText("admin.stripe.settings.secret.placeholder")}
                                        value={captchaValue.captcha_secret_key}
                                        className='form-control'
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={settings}
                                        onSubmit={onSubmit}
                                        editDisabled={disabled}
                                        link='/app/admin/settings-main'
                                        addDisabled={!captchaValue.captcha_key}
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { settings } = state;
    return { settings }
};

export default connect(mapStateToProps, { updateCaptchaSetting, fetchCaptchaSetting })(CaptchaTab);
