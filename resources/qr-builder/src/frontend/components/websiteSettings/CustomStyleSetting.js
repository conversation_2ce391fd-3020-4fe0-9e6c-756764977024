import React, { useEffect, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import { updateCustomStyleSetting, fetchCustomStyleSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import { addToast } from "../../../store/action/toastAction";
import { toastType } from "../../../constants";

const CustomStyle = (props) => {

    const { updateCustomStyleSetting, fetchCustomStyleSetting, customStyle } = props
    const dispatch = useDispatch()

    useEffect(() => {
        fetchCustomStyleSetting()
    }, [])

    const [customValue, setCustomValue] = useState({
        custom_js: '',
        custom_css: '',
    })

    useEffect(() => {
        if (customStyle.custom_js) {
            setCustomValue({
                custom_js: customStyle ? customStyle.custom_js : '',
                custom_css: customStyle ? customStyle.custom_css : ''
            })
        }
    }, [customStyle])

    const disabled = customStyle &&
        customStyle.custom_js === customValue.custom_js &&
        customStyle.custom_css === customValue.custom_css

    const [errors, setErrors] = useState({
        custom_js: "",
    })

    const onChangeInput = (e) => {
        setCustomValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
    }

    const prepareFormData = (data) => {
        const formValue = {
            custom_js: data ? data.custom_js : '',
            custom_css: data ? data.custom_css : ''
        }
        return formValue
    }

    const onSubmit = (event) => {
        event.preventDefault();
        setCustomValue(customValue);
        updateCustomStyleSetting(prepareFormData(customValue));
        // dispatch(addToast(
        //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
        setCustomValue({
            custom_js: customStyle ? customStyle.custom_js : '',
            custom_css: customStyle ? customStyle.custom_css : ''
        })
    }

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText('settings.custom.style.title')} />
                <WebsiteSettings page={'customStyle'} />

                <div className='card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.custom.js.style.title")}:
                                    </label>
                                    <textarea className='form-control' name='custom_js' rows={3}
                                        placeholder={placeholderText("admin.custom.js.style.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                        value={customValue.custom_js} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['custom_js'] ? errors['custom_js'] : null}
                                    </span>
                                </div>
                                <div className='col-12 mb-3'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage("admin.custom.css.style.title")}:
                                    </label>
                                    <textarea className='form-control' name='custom_css' rows={3}
                                        placeholder={placeholderText("admin.custom.css.style.placeholder")}
                                        onChange={(e) => onChangeInput(e)}
                                        value={customValue.custom_css} />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors['custom_css'] ? errors['custom_css'] : null}
                                    </span>
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={customStyle}
                                        editDisabled={disabled}
                                        onSubmit={onSubmit}
                                        link='/app/admin/settings-main'
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = (state) => {
    const { customStyle } = state;
    return { customStyle }
};

export default connect(mapStateToProps, { updateCustomStyleSetting, fetchCustomStyleSetting })(CustomStyle);
