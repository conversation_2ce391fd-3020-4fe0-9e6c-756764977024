import React, { useEffect, useState } from "react";
import Form from "react-bootstrap/Form";
import { connect, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
    getFormattedMessage,
    getFormattedOptions,
    numValidate,
    placeholderText,
} from "../../../shared/sharedMethod";
import ModelFooter from "../../../shared/components/modelFooter";
import ReactSelect from "../../../shared/select/reactSelect";
import { InputGroup } from "react-bootstrap-v5";
import { fetchAllCurrency } from "../../../store/action/adminActions/currencyAction";
import ImagePicker from "../../../shared/image-picker/ImagePicker";
import {
    fetchCacheClear,
    updateMainSetting,
    fetchMainSetting,
} from "../../../store/action/adminActions/settingsActions";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import MasterLayout from "../MasterLayout";
import WebsiteSettings from "./WebsiteSettings";
import { Dropdown, DropdownButton } from "react-bootstrap";
import { Tooltip as ReactTooltip } from "react-tooltip";
import * as EmailValidator from "email-validator";
import {
    updateEmailNotificationSetting,
    fetchEmailNotificationSetting,
} from "../../../store/action/adminActions/settingsActions";
import { parsePhoneNumberFromString } from "libphonenumber-js";
import countryData from "country-telephone-data";
import CountryList from "country-list-with-dial-code-and-flag";

const MainTab = (props) => {
    const {
        fetchAllCurrency,
        currencies,
        fetchCacheClear,
        updateMainSetting,
        fetchMainSetting,
        settings,
        frontSettings,
        emailsetting,
        updateEmailNotificationSetting,
        fetchEmailNotificationSetting,
        emailNotiy,
    } = props;
    const flagData = CountryList.getAll();
    const navigate = useNavigate();
    const [isdisabled, setdisabled] = useState({
        new_user: 0,
        delete_user: 0,
        new_payment: 0,
        contact_page_emails: 0,
    });
    const [emailNotiyValue, setEmailNotiyValue] = useState({
        new_user: false,
        delete_user: false,
        new_payment: false,
        contact_page_emails: false,
    });
    const [emailList, setEmailList] = useState([]);
    const [errors, setErrors] = useState({
        emailList: "",
        website_title: "",
        plan_expire_notification: "",
    });

    const [faviconPreviewUrl, setFaviconPreviewUrl] = useState(
        localStorage.getItem("website_favicon") || null
    );
    const [selectfavIcon, setSelectFavicon] = useState(null);

    const [logoPreviewUrl, setLogoPreviewUrl] = useState(
        localStorage.getItem("website_logo") || null
    );
    const [selectLogo, setSelectLogo] = useState(null);
    const [countryCode, setCountryCode] = useState("+91");
    const [phoneNumber, setPhoneNumber] = useState("");

    const countryFlag = flagData?.filter((d) => d?.dial_code === countryCode);

    useEffect(() => {
        fetchEmailNotificationSetting();
    }, []);
    useEffect(() => {
        fetchMainSetting();
    }, []);

    useEffect(() => {
        if (emailsetting?.new_user) {
            setdisabled({
                new_user: emailsetting ? Number(emailsetting.new_user) : 0,
                delete_user: emailsetting
                    ? Number(emailsetting.delete_user)
                    : 0,
                new_payment: emailsetting
                    ? Number(emailsetting.new_payment)
                    : 0,
                contact_page_emails: emailsetting
                    ? Number(emailsetting.contact_page_emails)
                    : 0,
            });
            setEmailNotiyValue({
                // emails_to_be_notified: emailsetting ? emailsetting.emails_to_be_notified : '',
                new_user:
                    emailsetting && emailsetting.new_user
                        ? Number(emailsetting.new_user) === 0
                            ? false
                            : true
                        : false,
                delete_user:
                    emailsetting && emailsetting.delete_user
                        ? Number(emailsetting.delete_user) === 0
                            ? false
                            : true
                        : false,
                new_payment:
                    emailsetting && emailsetting.new_payment
                        ? Number(emailsetting.new_payment) === 0
                            ? false
                            : true
                        : false,
                // new_custom_domain: emailsetting ? Number(emailsetting.new_custom_domain) : 0,
                contact_page_emails:
                    emailsetting && emailsetting.contact_page_emails
                        ? Number(emailsetting.contact_page_emails) === 0
                            ? false
                            : true
                        : false,
            });
            if (emailsetting?.emails_to_be_notified) {
                setEmailList(
                    emailsetting &&
                        emailsetting?.emails_to_be_notified &&
                        emailsetting?.emails_to_be_notified?.split(",")
                );
            }
        }
    }, [emailsetting]);

    useEffect(() => {
        fetchAllCurrency();
    }, []);

    // const languageTypeOptions = getFormattedOptions(languageOptions)
    // const languageTypeDefaultValue = languageTypeOptions.map((option) => {
    //     return {
    //         value: option.id,
    //         label: option.name
    //     }
    // })

    const currenciesType = currencies.map((d) => {
        return {
            id: d.id,
            name: d.attributes.symbol + " " + d.attributes.name,
        };
    });

    currenciesType.unshift({
        id: 0,
        name: placeholderText("settings.select.currency.placeholder"),
    });

    const currenciesOption = getFormattedOptions(currenciesType);
    const currenciesTypeDefaultValue = currenciesOption.map((option) => {
        return {
            value: option.id,
            label: option.name,
        };
    });

    const selectedCurrency =
        currenciesTypeDefaultValue.length > 1
            ? currenciesTypeDefaultValue.filter(
                  (d) => d.label === settings?.currency
              )
            : {
                  value: 0,
                  label: placeholderText(
                      "settings.select.currency.placeholder"
                  ),
              };

    const [mainTabDetails, setMainTabDetails] = useState({
        website_title: settings?.title ? settings?.title : "QR Builder",
        logo: settings?.logo
            ? settings?.logo
            : localStorage.getItem("website_logo") || "",
        favicon: settings?.favicon
            ? settings?.favicon
            : localStorage.getItem("website_favicon") || "",
        enable_new_users_registration:
            settings?.enable_new_users_registration === "0" ? 0 : 1 || 0,
        email_confirmation: settings?.email_confirmation === "0" ? 0 : 1 || 0,
        currency: selectedCurrency[0]
            ? selectedCurrency[0]
            : {
                  value: 0,
                  label: placeholderText(
                      "settings.select.currency.placeholder"
                  ),
              },
        plan_expire_notification: "",
        email: settings?.email ? settings?.email : "",
        phone: settings?.phone ? settings?.phone : "",
        address: settings?.address ? settings?.address : "",
        // default_language: { value: "en", label: "English" },
    });

    useEffect(() => {
        const findCurrencies = currenciesTypeDefaultValue?.filter(
            (items) => items?.value === Number(settings.currency)
        );
        setMainTabDetails({
            website_title: settings?.title ? settings?.title : "QR Builder",
            logo: settings.logo
                ? settings.logo
                : frontSettings?.logo
                ? frontSettings?.logo
                : localStorage.getItem("website_favicon"),
            favicon: settings.favicon
                ? settings.favicon
                : frontSettings?.favicon
                ? frontSettings?.favicon
                : localStorage.getItem("website_logo"),
            enable_new_users_registration:
                settings?.enable_new_users_registration === "0" ? 0 : 1 || 0,
            email_confirmation:
                settings?.email_confirmation === "0" ? 0 : 1 || 0,
            currency: findCurrencies
                ? findCurrencies[0]
                : {
                      value: 0,
                      label: placeholderText(
                          "settings.select.currency.placeholder"
                      ),
                  },
            plan_expire_notification: settings
                ? settings.plan_expire_notification
                : "",
            email: settings?.email ? settings?.email : "",
            address: settings?.address ? settings?.address : "",
            phone: settings?.phone ? settings?.phone : "",
        });

        setCountryCode(
            settings?.phone ? settings?.phone?.split(" ")[0] : "+91"
        );
        setPhoneNumber(settings?.phone ? settings?.phone?.split(" ")[1] : "");
        if (frontSettings?.favicon) {
            const logo = frontSettings?.favicon;
            let link = document.querySelector("link[rel~='icon']");
            if (!link) {
                link = document.createElement("link");
                link.rel = "icon";
                document.getElementsByTagName("head")[0].appendChild(link);
            }
            link.href = logo;
        }
    }, [settings, currencies, frontSettings]);

    const onChangeInput = (e) => {
        setMainTabDetails((inputs) => ({
            ...inputs,
            [e.target.name]: e.target?.value,
        }));
        setErrors("");
    };

    // const onLanguageChange = (e) => {
    //     setMainTabDetails(inputs => ({ ...inputs, default_language: e }))
    //     setErrors('');
    // }

    const onCurrencyChange = (obj) => {
        setMainTabDetails((inputs) => ({ ...inputs, currency: obj }));
        setErrors("");
    };

    const handleFaviconChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (
                file.type === "image/png" ||
                file.type === "image/jpg" ||
                file.type === "image/jpeg" ||
                file.type === "image/webp" ||
                file.type === "image/gif" ||
                file.type === "image/tiff"
            ) {
                setSelectFavicon(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setFaviconPreviewUrl(fileReader.result);
                    setMainTabDetails((inputs) => ({
                        ...inputs,
                        favicon: fileReader.result,
                    }));
                };
                fileReader.readAsDataURL(file);
            }
        }
    };

    const handleLogoChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (
                file.type === "image/png" ||
                file.type === "image/jpg" ||
                file.type === "image/jpeg" ||
                file.type === "image/webp" ||
                file.type === "image/gif" ||
                file.type === "image/tiff"
            ) {
                setSelectLogo(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setLogoPreviewUrl(fileReader.result);
                    setMainTabDetails((inputs) => ({
                        ...inputs,
                        logo: fileReader.result,
                    }));
                };
                fileReader.readAsDataURL(file);
            }
        }
    };

    const onChecked = (e) => {
        const check = e.target.checked;
        if (check) {
            setMainTabDetails((inputs) => ({ ...inputs, [e.target.name]: 1 }));
        } else {
            setMainTabDetails((inputs) => ({ ...inputs, [e.target.name]: 0 }));
        }
        setErrors("");
    };

    const onCacheClear = (event) => {
        event.preventDefault();
        fetchCacheClear();
    };

    const disabled =
        settings &&
        settings.title === mainTabDetails.website_title &&
        Number(settings.enable_new_users_registration) ===
            mainTabDetails.enable_new_users_registration &&
        Number(settings.currency) === mainTabDetails?.currency?.value &&
        settings.plan_expire_notification ===
            mainTabDetails.plan_expire_notification &&
        settings.logo === mainTabDetails.logo &&
        settings.favicon === mainTabDetails.favicon &&
        settings.email === mainTabDetails.email &&
        settings.phone === mainTabDetails.phone &&
        settings.address === mainTabDetails.address;

    const disabledEm =
        emailsetting &&
        Number(emailsetting?.new_user) === isdisabled.new_user &&
        Number(emailsetting?.delete_user) === isdisabled.delete_user &&
        Number(emailsetting?.new_payment) === isdisabled.new_payment &&
        Number(emailsetting?.contact_page_emails) ===
            isdisabled.contact_page_emails &&
        (emailsetting?.emails_to_be_notified !== ""
            ? emailsetting?.emails_to_be_notified?.split(",").length ===
              (emailList.length === undefined
                  ? emailList.length + 1
                  : emailList.length)
            : emailList?.length === 0);

    const onCheckedEm = (e) => {
        const check = e.target.checked;
        if (check) {
            setEmailNotiyValue((inputs) => ({
                ...inputs,
                [e.target.name]: true,
            }));
            setdisabled((inputs) => ({ ...inputs, [e.target.name]: 1 }));
        } else {
            setEmailNotiyValue((inputs) => ({
                ...inputs,
                [e.target.name]: false,
            }));
            setdisabled((inputs) => ({ ...inputs, [e.target.name]: 0 }));
        }
        setErrors("");
    };

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        const countryName = countryData.allCountries.find(
            (c) => `+${c?.dialCode}` === countryCode
        );
        const parsedNumber = parsePhoneNumberFromString(
            mainTabDetails?.phone?.split(" ")?.join(""),
            countryName?.iso2
        )?.isValid();
        if (
            !mainTabDetails.website_title ||
            mainTabDetails.website_title.trim().length === 0
        ) {
            errorss.website_title = getFormattedMessage(
                "Please.Enter.The.Website.Name.validate.label"
            );
        } else if (mainTabDetails.email?.trim()?.length === 0) {
            errorss.email = getFormattedMessage(
                "globally.input.email.validate.label"
            );
        } else if (!EmailValidator.validate(mainTabDetails.email?.trim())) {
            errorss.email = getFormattedMessage(
                "Please enter valid email address"
            );
        } else if (mainTabDetails.currency?.value === 0) {
            errorss.currency = getFormattedMessage(
                "Please.Select.Currency.validate.label"
            );
        } else if (mainTabDetails.plan_expire_notification === "") {
            errorss.plan_expire_notification = getFormattedMessage(
                "Please.Enter.plan.expire.notification.day.validate.label"
            );
        } else if (parsedNumber !== true) {
            errorss.phone = getFormattedMessage(
                "globally.input.phonenumber.validate.label"
            );
        } else if (
            mainTabDetails.address === "" ||
            mainTabDetails.address.trim().length === 0
        ) {
            errorss.address = getFormattedMessage(
                "globally.input.address.validate.label"
            );
        }
        // else if (emailList.length === 0) {
        //     errorss["emailList"] = getFormattedMessage("globally.input.email.validate.label")
        // }
        else {
            isValid = true;
        }

        setErrors(errorss);
        return isValid;
    };

    const prepareFormData = (prepareData) => {
        const formData = new FormData();
        formData.append("title", prepareData?.website_title);
        formData.append(
            "enable_new_users_registration",
            prepareData?.enable_new_users_registration
        );
        formData.append("email_confirmation", prepareData?.email_confirmation);
        formData.append("currency", prepareData?.currency?.value);
        formData.append(
            "plan_expire_notification",
            prepareData?.plan_expire_notification
        );
        formData.append("email", prepareData?.email);
        formData.append("phone", "+" + prepareData?.phone.split("+").pop());
        formData.append("address", prepareData?.address);
        if (selectLogo) {
            formData.append("logo", selectLogo);
        }
        if (selectfavIcon) {
            formData.append("favicon", selectfavIcon);
        }
        return formData;
    };

    const prepareFormDataEm = (data) => {
        const formValue = {
            emails_to_be_notified: emailList.toString(),
            new_user: data ? (data.new_user === false ? 0 : 1) : 0,
            delete_user: data ? (data.delete_user === false ? 0 : 1) : 0,
            new_payment: data ? (data.new_payment === false ? 0 : 1) : 0,
            // new_custom_domain: data ? data.new_custom_domain : 0,
            contact_page_emails: data
                ? data.contact_page_emails === false
                    ? 0
                    : 1
                : 0,
        };
        return formValue;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        const valid = handleValidation();
        if (valid) {
            updateMainSetting(prepareFormData(mainTabDetails), navigate);
            // updateEmailNotificationSetting(prepareFormDataEm(emailNotiyValue));
            // dispatch(addToast(
            //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
        }
    };

    const onSubmitEm = (event) => {
        event.preventDefault();
        setEmailNotiyValue(emailNotiyValue);
        const valid = handleValidation();
        if (valid) {
            updateEmailNotificationSetting(prepareFormDataEm(emailNotiyValue));
            // dispatch(addToast(
            //     { text: "This action is not allowed in demo.", type: toastType.ERROR }));
            setEmailNotiyValue({
                // emails_to_be_notified: emailsetting ? emailsetting.emails_to_be_notified : '',
                new_user:
                    emailsetting && emailsetting.new_user
                        ? Number(emailsetting.new_user) === 0
                            ? false
                            : true
                        : false,
                delete_user:
                    emailsetting && emailsetting.delete_user
                        ? Number(emailsetting.delete_user) === 0
                            ? false
                            : true
                        : false,
                new_payment:
                    emailsetting && emailsetting.new_payment
                        ? Number(emailsetting.new_payment) === 0
                            ? false
                            : true
                        : false,
                // new_custom_domain: emailsetting ? Number(emailsetting.new_custom_domain) : 0,
                contact_page_emails:
                    emailsetting && emailsetting.contact_page_emails
                        ? Number(emailsetting.contact_page_emails) === 0
                            ? false
                            : true
                        : false,
            });
            setEmailList([]);
        }
    };

    const onClickCancelButton = () => {
        // const findCurrencies = currenciesTypeDefaultValue.filter((items) => items?.value === Number(settings.currency))
        // setMainTabDetails({
        //     website_title: settings?.title ? settings?.title : "QR Builder",
        //     logo: settings.logo ? settings.logo : frontSettings?.logo ? frontSettings?.logo : localStorage.getItem("website_favicon"),
        //     favicon: settings.favicon ? settings.favicon : frontSettings?.favicon ? frontSettings?.favicon : localStorage.getItem("website_logo"),
        //     enable_new_users_registration: settings?.enable_new_users_registration === "0" ? 0 : 1 || 0,
        //     email_confirmation: settings?.email_confirmation === "0" ? 0 : 1 || 0,
        //     currency: findCurrencies && findCurrencies ? findCurrencies[0] : { value: 0, label: placeholderText("settings.select.currency.placeholder") },
        //     plan_expire_notification: settings ? settings.plan_expire_notification : ''
        // })

        if (frontSettings?.favicon) {
            const logo = frontSettings?.favicon;
            let link = document.querySelector("link[rel~='icon']");
            if (!link) {
                link = document.createElement("link");
                link.rel = "icon";
                document.getElementsByTagName("head")[0].appendChild(link);
            }
            link.href = logo;
        }
    };

    const onChangeCountryCode = (code, name) => {
        setCountryCode(code);
        setMainTabDetails((inputs) => ({
            ...inputs,
            [name]: `${code} ${phoneNumber.replace(/ /g, "")}`,
        }));
        setErrors("");
    };

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle
                    title={placeholderText("admin.settings.main-setting.title")}
                />
                <WebsiteSettings page={"main"} />
                <div className="card setting-card">
                    <div className="card-body">
                        <Form>
                            <div className="row">
                                {/* <div className='mb-3'>
                            <ReactSelect title={getFormattedMessage('globally.input.default-language.label')}
                                name='default_language'
                                value={mainTabDetails.default_language}
                                // errors={errors['type']}
                                defaultValue={languageTypeDefaultValue[0]}
                                multiLanguageOption={languageTypeOptions}
                                onChange={onLanguageChange}
                            />
                        </div> */}
                                <div className="col-6 mb-3">
                                    <label
                                        htmlFor="exampleInputEmail1"
                                        className="form-label"
                                    >
                                        {getFormattedMessage(
                                            "globally.input.website-title.label"
                                        )}
                                        :<span className="required" />
                                    </label>
                                    <input
                                        type="text"
                                        name="website_title"
                                        value={mainTabDetails.website_title}
                                        placeholder={placeholderText(
                                            "globally.placeholder.website-title.label"
                                        )}
                                        className="form-control"
                                        autoFocus={true}
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["website_title"]
                                            ? errors["website_title"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-6 mb-3">
                                    <label
                                        htmlFor="exampleInputEmail1"
                                        className="form-label"
                                    >
                                        {getFormattedMessage(
                                            "globally.input.email.label"
                                        )}
                                        :<span className="required" />
                                    </label>
                                    <input
                                        type="text"
                                        name="email"
                                        value={mainTabDetails.email}
                                        placeholder={placeholderText(
                                            "globally.input.email.placeholder"
                                        )}
                                        className="form-control"
                                        autoFocus={true}
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["email"]
                                            ? errors["email"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-md-6">
                                    <label className="form-label">
                                        {getFormattedMessage(
                                            "setting.plan-expire-notification.title"
                                        )}
                                        :
                                    </label>
                                    <span className="required" />
                                    <input
                                        autoComplete="off"
                                        type="text"
                                        name="plan_expire_notification"
                                        className="form-control"
                                        onChange={(e) => onChangeInput(e)}
                                        pattern="[0-9]"
                                        min={0}
                                        placeholder={placeholderText(
                                            "setting.placeholder.plan-expire-notification.title"
                                        )}
                                        onKeyPress={(event) =>
                                            numValidate(event)
                                        }
                                        value={
                                            mainTabDetails.plan_expire_notification !==
                                            undefined
                                                ? mainTabDetails.plan_expire_notification
                                                : ""
                                        }
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["plan_expire_notification"]
                                            ? errors["plan_expire_notification"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-md-6 mb-3">
                                    <label
                                        htmlFor="exampleInputEmail1"
                                        className="form-label"
                                    >
                                        {getFormattedMessage(
                                            "globally.input.phone-number.lable"
                                        )}
                                        :<span className="required" />
                                    </label>
                                    <InputGroup className="country_code_dropdown">
                                        <DropdownButton
                                            variant="primary p-3"
                                            title={
                                                <span>
                                                    <img
                                                        className="thumbnail-image"
                                                        src={`https://flagcdn.com/16x12/${countryFlag[0]?.data?.code?.toLowerCase()}.png`}
                                                        srcSet={`https://flagcdn.com/32x24/${countryFlag[0]?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${countryFlag[0]?.data?.code?.toLowerCase()}.png 3x`}
                                                        width={16}
                                                        height={12}
                                                        alt={
                                                            countryFlag[0]?.data
                                                                ?.code
                                                        }
                                                    />{" "}
                                                    {countryCode}
                                                </span>
                                            }
                                            id="input-group-dropdown-1"
                                        >
                                            {flagData.map((d, i) => {
                                                return (
                                                    <Dropdown.Item
                                                        key={i + 1}
                                                        onClick={() =>
                                                            onChangeCountryCode(
                                                                d.data
                                                                    .dial_code,
                                                                "phone"
                                                            )
                                                        }
                                                        value={d.data.dial_code}
                                                    >
                                                        <img
                                                            src={`https://flagcdn.com/16x12/${d?.data?.code?.toLowerCase()}.png`}
                                                            srcSet={`https://flagcdn.com/32x24/${d?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${d?.data?.code?.toLowerCase()}.png 3x`}
                                                            width={16}
                                                            height={12}
                                                            alt="South Africa"
                                                        />{" "}
                                                        {d.data.dial_code}
                                                    </Dropdown.Item>
                                                );
                                            })}
                                        </DropdownButton>
                                        <Form.Control
                                            type="text"
                                            name="phone"
                                            value={phoneNumber}
                                            pattern="^\(?\d{3}\)?[- ]?\d{3}[- ]?\d{4}$"
                                            className="form-control"
                                            placeholder={placeholderText(
                                                "globally.input.phone-number.placeholder.label"
                                            )}
                                            onKeyPress={(event) =>
                                                numValidate(event)
                                            }
                                            onChange={(e) => {
                                                setPhoneNumber(
                                                    e.target.value.replace(
                                                        / /g,
                                                        ""
                                                    )
                                                );
                                                setMainTabDetails((inputs) => ({
                                                    ...inputs,
                                                    [e.target
                                                        .name]: `${countryCode} ${e.target.value.replace(
                                                        / /g,
                                                        ""
                                                    )}`,
                                                }));
                                            }}
                                        />
                                    </InputGroup>
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["phone"]
                                            ? errors["phone"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-md-6 mb-3">
                                    <ReactSelect
                                        title={getFormattedMessage(
                                            "globally.heading.currency.title"
                                        )}
                                        name="currency"
                                        value={
                                            mainTabDetails.currency !==
                                            undefined
                                                ? mainTabDetails.currency
                                                : {
                                                      value: 0,
                                                      label: placeholderText(
                                                          "settings.select.currency.placeholder"
                                                      ),
                                                  }
                                        }
                                        // errors={errors['currency']}
                                        defaultValue={
                                            currenciesTypeDefaultValue[0]
                                        }
                                        multiLanguageOption={currenciesOption}
                                        onChange={onCurrencyChange}
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["currency"]
                                            ? errors["currency"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-md-6 mb-3">
                                    <label
                                        htmlFor="exampleInputEmail1"
                                        className="form-label"
                                    >
                                        {getFormattedMessage(
                                            "globally.input.address.label"
                                        )}
                                        :<span className="required" />
                                    </label>
                                    <textarea
                                        name="address"
                                        value={mainTabDetails.address}
                                        placeholder={placeholderText(
                                            "globally.input.address.placeholder.label"
                                        )}
                                        className="form-control"
                                        autoFocus={true}
                                        onChange={(e) => onChangeInput(e)}
                                    />
                                    <span className="text-danger d-block fw-400 fs-small mt-2">
                                        {errors["address"]
                                            ? errors["address"]
                                            : null}
                                    </span>
                                </div>
                                <div className="col-6 mb-4 position-relative">
                                    <ImagePicker
                                        user={mainTabDetails.logo}
                                        isCreate={true}
                                        avtarName={"No Logo Selected"}
                                        imageTitle={"globally.input.logo.label"}
                                        imagePreviewUrl={mainTabDetails.logo}
                                        handleImageChange={handleLogoChanges}
                                        isRequired
                                        tooltipContent={placeholderText(
                                            "The.image.must.be.of.pixel.90.x.60"
                                        )}
                                        tooltipId={"my-element1"}
                                        tooltip={true}
                                    />
                                    <ReactTooltip anchorId="my-element1" />
                                </div>
                                <div className="col-6 mb-4 position-relative">
                                    <ImagePicker
                                        user={mainTabDetails.favicon}
                                        isCreate={true}
                                        avtarName={"No Favicon Selected"}
                                        imageTitle={
                                            "globally.input.favicon.label"
                                        }
                                        imagePreviewUrl={mainTabDetails.favicon}
                                        handleImageChange={handleFaviconChanges}
                                        isRequired
                                        tooltipContent={placeholderText(
                                            "The.image.must.be.of.pixel.16.x.16"
                                        )}
                                        tooltipId={"my-element2"}
                                        tooltip={true}
                                    />
                                    <ReactTooltip anchorId="my-element2" />
                                </div>
                                <div className="col-md-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off"
                                            name="enable_new_users_registration"
                                            data-id="704"
                                            className="form-check-input admin-status cursor-pointer"
                                            type="checkbox"
                                            checked={
                                                mainTabDetails.enable_new_users_registration ===
                                                0
                                                    ? false
                                                    : true
                                            }
                                            value={
                                                mainTabDetails.enable_new_users_registration
                                            }
                                            onChange={(event) =>
                                                onChecked(event)
                                            }
                                        />
                                        <>
                                            {getFormattedMessage(
                                                "globally.input.enable-new-user-registration.label"
                                            )}
                                        </>
                                        <span
                                            className="switch-slider"
                                            data-checked="✓"
                                            data-unchecked="✕"
                                        ></span>
                                    </label>
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={settings}
                                        onSubmit={onSubmit}
                                        editDisabled={disabled}
                                        // editDisabled={!qrDetails.name}
                                        onClickCancelButton={
                                            onClickCancelButton
                                        }
                                        link="/app/admin/settings-main"
                                        addDisabled={
                                            !mainTabDetails.website_title
                                        }
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
                {/* <div className="card mt-3">
                    <div className="card-body">
                        <h4 className="mb-5">{getFormattedMessage("globally.email.notifications.setting.title")}:</h4>
                        <div className='col-12 mb-3'>
                            <label
                                className='form-label'>{getFormattedMessage("emails.to.be.notified.label")}:<span className="required" />
                            </label>
                            <ReactMultiEmailInput
                                className="p-5"
                                placeholder={placeholderText("emails.to.be.notified.placeholder")}
                                emails={emailList}
                                setEmails={setEmailList}
                            />
                            <figure className='figure-caption mt-1'>
                                {getFormattedMessage("emails.that.will.receive.a.notification.when.one.of.the.actions.from.below.are.performed.add.valid.email.addresses.separated.by.a.comma")}
                            </figure>
                            <span className="text-danger">
                                {errors.emailList && errors.emailList}
                            </span>
                        </div>
                        <div className='col-6 mb-3'>
                            <label className="form-check form-switch form-switch-sm cursor-pointer">
                                <input
                                    autoComplete="off" name="new_user" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                    checked={emailNotiyValue.new_user}
                                    value={emailNotiyValue.new_user}
                                    onChange={(event) => onCheckedEm(event)} />
                                <>
                                    {getFormattedMessage("new.user.title")}
                                    <figure className='figure-caption'>
                                        {getFormattedMessage("receive.an.email.when.a.new.users.registers.to.the.website")}
                                    </figure>
                                </>
                                <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                            </label>
                        </div>
                        <div className='col-6 mb-3'>
                            <label className="form-check form-switch form-switch-sm cursor-pointer">
                                <input
                                    autoComplete="off" name="delete_user" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                    checked={emailNotiyValue.delete_user}
                                    value={emailNotiyValue.delete_user}
                                    onChange={(event) => onCheckedEm(event)} />
                                <>
                                    {getFormattedMessage("delete.user.title")}
                                    <figure className='figure-caption'>
                                        {getFormattedMessage("receive.an.email.when.any.user.deletes.their.account")}
                                    </figure>
                                </>
                                <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                            </label>
                        </div>
                        <div className='col-6 mb-3'>
                            <label className="form-check form-switch form-switch-sm cursor-pointer">
                                <input
                                    autoComplete="off" name="new_payment" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                    checked={emailNotiyValue.new_payment}
                                    value={emailNotiyValue.new_payment}
                                    onChange={(event) => onCheckedEm(event)} />
                                <>
                                    {getFormattedMessage("new.payment.title")}
                                    <figure className='figure-caption'>
                                        {getFormattedMessage("receive.an.email.when.a.new.payment.is.successfully.processed")}
                                    </figure>
                                </>
                                <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                            </label>
                        </div>
                        <div className='col-6 mb-3'>
                                    <label className="form-check form-switch form-switch-sm">
                                        <input
                                            autoComplete="off" name="new_custom_domain" data-id="704" className="form-check-input admin-status" type="checkbox"
                                            checked={emailNotiyValue.new_custom_domain === 0 ? false : true}
                                            value={emailNotiyValue.new_custom_domain}
                                            onChange={(event) => onCheckedEm(event)} />
                                        <>
                                            {'New Custom Domain'}
                                            <figure className='figure-caption'>
                                                Receive an email when a new custom domain is pending approval.
                                            </figure>
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                        <div className='col-6 mb-3'>
                            <label className="form-check form-switch form-switch-sm cursor-pointer">
                                <input
                                    autoComplete="off" name="contact_page_emails" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                    checked={emailNotiyValue.contact_page_emails}
                                    value={emailNotiyValue.contact_page_emails}
                                    onChange={(event) => onCheckedEm(event)} />
                                <>
                                    {getFormattedMessage("contact.page.emails.title")}
                                    <figure className='figure-caption'>
                                        {getFormattedMessage("enable.the.contact.system")}
                                    </figure>
                                </>
                                <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                            </label>
                        </div>
                        <div className="d-flex justify-content-start">
                            <ModelFooter
                                onEditRecord={emailsetting}
                                onSubmit={onSubmitEm}
                                editDisabled={disabledEm}
                                // editDisabled={!qrDetails.name}
                                onClickCancelButton={onClickCancelButton}
                                link='/app/admin/settings-main'
                                addDisabled={!emailList}
                            />
                        </div>
                    </div>

                </div> */}
                <div className="w-100 mx-auto pt-lg-10 pt-5">
                    <h4 className="mb-5">
                        {getFormattedMessage("settings.clear-cache.title")}
                    </h4>
                    <Form className="card card-body">
                        <div className="row">
                            <div>
                                <button
                                    className="btn btn-primary"
                                    onClick={(event) => onCacheClear(event)}
                                >
                                    {getFormattedMessage(
                                        "settings.clear-cache.title"
                                    )}
                                </button>
                            </div>
                        </div>
                    </Form>
                </div>
            </MasterLayout>
        </>
    );
};

const mapStateToProps = (state) => {
    const { currencies, settings, frontSettings, emailsetting, emailNotiy } =
        state;
    return { currencies, settings, frontSettings, emailsetting, emailNotiy };
};

export default connect(mapStateToProps, {
    fetchAllCurrency,
    fetchCacheClear,
    updateMainSetting,
    fetchMainSetting,
    updateEmailNotificationSetting,
    fetchEmailNotificationSetting,
})(MainTab);
