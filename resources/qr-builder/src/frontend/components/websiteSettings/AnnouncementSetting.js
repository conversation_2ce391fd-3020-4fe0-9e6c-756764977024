import React, { useEffect, useRef, useState } from "react";
import Form from 'react-bootstrap/Form';
import { connect } from 'react-redux';
import { getFormattedMessage, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import ReactSelect from '../../../shared/select/reactSelect';
import { commonYesOrNoOptions, enabledPaypalPaymentsOptions, paypalModeOptions } from "../../../constants";
import { fetchAnnouncementSetting, updateAnnouncementSetting } from "../../../store/action/adminActions/settingsActions"
import MasterLayout from "../MasterLayout";
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import TabTitle from "../../../shared/tab-title/TabTitle";
import WebsiteSettings from "./WebsiteSettings";
import ReactColorPicker from "../../../shared/colorpocker/ReactColorPicker";

const AnnouncementSetting = ( props ) => {

    const { fetchAnnouncementSetting, updateAnnouncementSetting, announcement } = props

    useEffect( () => {
        fetchAnnouncementSetting()
    }, [] )
    const [ backgroundColor, setBackgroundColor ] = useState( "#A561FF" )

    const [ announcementValue, setAnnouncementValue ] = useState( {
        user_announcement_content: "",
        user_text_color: "#ffffff",
        user_bg_color: "#A561FF",
        announcement_enabled: 0
    } )

    useEffect( () => {
        if ( announcement ) {
            setAnnouncementValue( {
                user_announcement_content: announcement && announcement.user_announcement_content ? announcement.user_announcement_content : '',
                user_text_color: announcement && announcement.user_text_color ? announcement.user_text_color : '#ffffff',
                user_bg_color: announcement && announcement.user_bg_color ? announcement.user_bg_color : '#A561FF',
                announcement_enabled: announcement && announcement?.announcement_enabled ? announcement?.announcement_enabled === "0" ? 0 : announcement?.announcement_enabled === "1" ? 1 : 0 : 0,
            } )
        }
    }, [ announcement ] )

    const disabled = announcement &&
        announcement.user_announcement_content === announcementValue.user_announcement_content &&
        announcement.user_text_color === announcementValue.user_text_color &&
        announcement.user_bg_color === announcementValue.user_bg_color &&
        Number( announcement.announcement_enabled ) === announcementValue.announcement_enabled

    const handleUserTextColorChange = ( color ) => {
        setAnnouncementValue( inputs => ( {
            ...inputs,
            user_text_color: color.hex
        } ) )
        setBackgroundColor( color.hex )
    }

    const handleUserBackgroundColorChange = ( color ) => {
        setAnnouncementValue( inputs => ( {
            ...inputs,
            user_bg_color: color.hex
        } ) )
        setBackgroundColor( color.hex )
    }

    const [ errors, setErrors ] = useState( {
        stripe_Key: "",
    } )


    const onChangeInput = ( e ) => {
        setAnnouncementValue( inputs => ( { ...inputs, [ e.target.name ]: e.target.value } ) )
        setErrors( '' );
    }

    const prepareFormData = ( data ) => {
        const formValue = {
            user_announcement_content: data.user_announcement_content,
            user_text_color: data.user_text_color,
            user_bg_color: data.user_bg_color,
            // announcement_enabled: data.announcement_enabled.toString(),
            announcement_enabled: data.announcement_enabled.toString(),
        }
        return formValue
    }

    const onSubmit = ( event ) => {
        event.preventDefault();
        setAnnouncementValue( announcementValue );
        updateAnnouncementSetting( prepareFormData( announcementValue ) );
    }

    const onChecked = ( e ) => {
        const check = e.target.checked
        if ( check ) {
            setAnnouncementValue( inputs => ( { ...inputs, [ e.target.name ]: 1 } ) )
        } else {
            setAnnouncementValue( inputs => ( { ...inputs, [ e.target.name ]: 0 } ) )
        }
    };

    return (
        <>
            <MasterLayout>
                <TopProgressBar />
                <TabTitle title={placeholderText( 'settings.announcement.label' )} />
                <WebsiteSettings page={'announcement'} />

                <div className='card'>
                    <div className='card-body'>
                        <Form>
                            <div className='row'>
                                <div className='col-md-6 col-12 mb-3 mt-2'>
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage( "admin.user-announcement-content.settings.key.title" )}:
                                    </label>
                                    <input
                                        type='text'
                                        name='user_announcement_content'
                                        placeholder={placeholderText( "admin.user-announcement-content.settings.key.placeholder" )}
                                        value={( announcementValue.user_announcement_content !== null && announcementValue.user_announcement_content !== undefined ) ? announcementValue.user_announcement_content : ""}
                                        className='form-control'
                                        autoFocus={true}
                                        onChange={( e ) => onChangeInput( e )}
                                    />
                                    <span
                                        className='text-danger d-block fw-400 fs-small mt-2'>
                                        {errors[ 'user_announcement_content' ] ? errors[ 'user_announcement_content' ] : null}
                                    </span>
                                </div>
                                <div className="col-md-3 col-12 mt-3">
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage( "text-color.title" )}:
                                    </label>
                                    <ReactColorPicker onChangeColor={handleUserTextColorChange} selectedColor={announcementValue.user_text_color} />
                                </div>
                                <div className="col-md-3 col-12 mt-3">
                                    <label htmlFor="exampleInputEmail1" className="form-label">
                                        {getFormattedMessage( "background-color.title" )}:
                                    </label>
                                    <ReactColorPicker onChangeColor={handleUserBackgroundColorChange} selectedColor={announcementValue.user_bg_color} />
                                </div>
                                <div className="col-6 d-flex align-items-center my-4">
                                    <label className="form-check form-switch form-switch-sm cursor-pointer">
                                        <input
                                            autoComplete="off" name="announcement_enabled" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                            checked={announcementValue.announcement_enabled === 0 ? false : true}
                                            value={announcementValue.announcement_enabled}
                                            onChange={( event ) => onChecked( event )} />
                                        <>
                                            {getFormattedMessage( "settings.announcement.toggle.message" )}
                                        </>
                                        <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                                    </label>
                                </div>
                                <div className="d-flex justify-content-start">
                                    <ModelFooter
                                        onEditRecord={announcement}
                                        editDisabled={disabled}
                                        onSubmit={onSubmit}
                                        link='/app/admin/settings-main'
                                    />
                                </div>
                            </div>
                        </Form>
                    </div>
                </div>
            </MasterLayout>
        </>
    )
};

const mapStateToProps = ( state ) => {
    const { announcement } = state;
    return { announcement }
};

export default connect( mapStateToProps, { fetchAnnouncementSetting, updateAnnouncementSetting } )( AnnouncementSetting );
