import React, { useEffect, useState } from 'react'
import { Tab, Tabs } from 'react-bootstrap'
import { connect } from 'react-redux';
import { placeholderText } from '../../../shared/sharedMethod';


const WebsiteSettings = (props) => {
    const { allConfigData, page } = props;
    const [key, setKey] = useState(page);

    useEffect(() => {
        setKey(page)
    }, [page])

    useEffect(() => {
        window.location.href = '#/app/admin/payment-' + key;
    }, [key])


    return (
        <>
            <Tabs defaultActiveKey={'Paypal'} activeKey={page} id='uncontrolled-tab-example' onSelect={(k) => setKey(k)}
                className='mt-7 mb-0 responsive-setting-tab payment-setting-tab setting-tab'>
                <Tab eventKey='Paypal' title={placeholderText("globally.paypal.payment.title")}
                    tabClassName='position-relative mb-0 me-3'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                <Tab eventKey='stripe' title={placeholderText("globally.stripe.payment.title")}
                    tabClassName='position-relative mb-0 me-3'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
                <Tab eventKey='razorpay' title={placeholderText("globally.razorpay.payment.title")}
                    tabClassName='position-relative mb-0 me-0'>
                    <div className='w-100 mx-auto'>
                    </div>
                </Tab>
            </Tabs>

        </>
    )
}
const mapStateToProps = (state) => {
    const { allConfigData } = state;
    return { allConfigData }
};

export default connect(mapStateToProps, {})(WebsiteSettings);
