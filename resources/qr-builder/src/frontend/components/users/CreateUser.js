import React from 'react';
import { connect, useSelector } from 'react-redux';
import UserForm from './UserForm';
import { addUser } from '../../../store/action/userAction';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { Filters } from '../../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import { Helmet } from 'react-helmet';

const CreateUser = (props) => {
    const { addUser } = props;
    const navigate = useNavigate();
    const {frontSettings} = useSelector(state => state)
    const addUserData = (formValue) => {
        addUser(formValue, navigate, Filters.OBJ);
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText('user.create.title') + ' | ' + frontSettings?.title}/>
            <HeaderTitle title={getFormattedMessage('user.create.title')} to='/app/admin/users' />
            <UserForm addUserData={addUserData} />
        </MasterLayout>
    );
}

export default connect(null, { addUser })(CreateUser);
