import React, { useEffect, useState } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import moment from 'moment';
import { getFormattedDate, getFormattedMessage } from '../../../../shared/sharedMethod';
import ReactDataTable from "../../../../shared/table/ReactDataTable"
import { Filters, toastType } from '../../../../constants';
import { fetchAdminLnkById, fetchQrcodeById } from '../../../../store/action/userAction';
import ActionButton from '../../../../shared/action-buttons/ActionButton';
import { environment } from '../../../../config/environment';
import { addToast } from '../../../../store/action/toastAction';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChartLine } from '@fortawesome/free-solid-svg-icons';
import DeleteAdminLink from '../../links/DeleteAdminLink';
import { saveAs } from 'file-saver';
import ImageModal from '../../../../shared/imageModal/ImageModal';

const LinkDetails = ( props ) => {
  const { id } = useParams();
  const dispatch = useDispatch()
  const { adminLinksById, totalRecord, isLoading, userQrCodeDteails, allConfigData } = useSelector( state => state )
  const [ deleteModel, setDeleteModel ] = useState( false );
  const [ isDelete, setIsDelete ] = useState( null );
  const [ showImageSlider, setShowImageSlider ] = useState( {
    display: false,
    src: '',
    name: ""
  } )

  const closeImageSlider = () => {
    setShowImageSlider( {
      display: false,
      src: '',
      name: ""
    } )
  }

  const onDownloadClick = ( e, url, name, ext ) => {
    e.preventDefault();
    saveAs( url, `${name}.${ext}` );
  }

  //   useEffect(() => {
  //     dispatch(fetchAdminLnkById(Filters.OBJ, true, id))
  //   }, [])

  const onChange = ( filter ) => {
    dispatch( fetchAdminLnkById( filter, true, id ) )
  };

  const onClickDeleteModel = ( isDelete = null ) => {
    setDeleteModel( !deleteModel );
    setIsDelete( isDelete );
  };

  const copyClickBoard = ( data ) => {
    const unsecuredCopyToClipboard = () => {
      const textArea = document.createElement( "textarea" );
      textArea.value = environment.URL + "/" + data.url_alias;
      document.body.appendChild( textArea );
      textArea.focus();
      textArea.select();
      try {
        document.execCommand( 'copy' );
        dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
      } catch ( err ) {
        dispatch( addToast( { text: `Failed to copy text to clipboard: ${err}`, type: toastType.ERROR } ) )
      }
      document.body.removeChild( textArea );
    }

    if ( window.isSecureContext && navigator.clipboard ) {
      navigator.clipboard.writeText( environment.URL + "/" + data.url_alias );
      dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
    } else {
      unsecuredCopyToClipboard();
    }
  }

  const goToAnalytics = ( item ) => {
    const id = item.id;
    window.location.href = '#/app/admin/minify-link/' + id + '/analytics';
  };

  const itemsValue = adminLinksById.length >= 0 && adminLinksById?.map( link => ( {
    date: getFormattedDate( link.created_at, "d-m-y" ),
    time: moment( link.created_at ).format( 'LT' ),
    url_alias: link.url_alias,
    destination_url: link.destination_url,
    image_url: link.image_url,
    id: link.id,
  } ) );

  const columns = [
    {
      name: getFormattedMessage( 'globally.links.title' ),
      selector: row => row.url_alias,
      sortField: 'destination_url',
      sortable: true,
      cell: row => {
        return <div className='d-flex align-items-center'>
          <div className='d-flex flex-column'>
            <div className='text-decoration-none'>{row.url_alias}</div>
            <a href={row.destination_url} target={"_blank"} className='text-decoration-none text-muted'>{row.destination_url}</a>
          </div>
        </div>
      }
    },
    {
      name: getFormattedMessage( 'globally.react-table.column.stats.label' ),
      selector: row => row.name,
      sortField: 'stats',
      sortable: false,
      cell: row => {
        return <div className='d-flex cursor-pointer'>
          <div className='text-info'><FontAwesomeIcon icon={faChartLine} onClick={() => goToAnalytics( row )} /></div>
        </div>
      }
    },
    {
      name: getFormattedMessage( "qrcode.lable" ),
      selector: row => row.image_url,
      sortField: 'image_url',
      sortable: false,
      cell: row => {
        return <div className='me-2' >
          <img src={row?.image_url} height='50' width='50' alt='QR Code Image'
            className='image image-mini image-effect cursor-pointer' onClick={() => setShowImageSlider( {
              display: true,
              src: row?.image_url,
              name: row?.url_alias
            } )} />
        </div>
      }
    },
    {
      name: getFormattedMessage( 'globally.react-table.column.created-date.label' ),
      selector: row => row.date,
      sortField: 'created_at',
      sortable: true,
      cell: row => {
        return (
          <span className='badge bg-light-info'>
            <div className='mb-1'>{row.time}</div>
            <div>{row.date}</div>
          </span>
        )
      }
    },
    {
      name: getFormattedMessage( 'react-data-table.action.column.label' ),
      right: true,
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
      cell: row =>
        <div>
          <ActionButton item={row} isCopyBtn onCopybtn={copyClickBoard} isEditMode={false} onClickDeleteModel={onClickDeleteModel} />
        </div>

    }
  ];

  return (
    <>
      <ReactDataTable columns={columns} isEmpty={isLoading} items={itemsValue} onChange={onChange} totalRows={totalRecord} isLoading={isLoading} isShowSearch subHeader={false} />
      <ImageModal display={showImageSlider} closeSlider={closeImageSlider} onDownloadClick={onDownloadClick} />
      <DeleteAdminLink onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
    </>

  )
};

export default LinkDetails;

