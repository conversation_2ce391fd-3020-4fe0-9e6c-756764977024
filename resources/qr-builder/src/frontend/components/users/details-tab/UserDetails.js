import React from 'react'
import { Card, Table } from 'react-bootstrap-v5'
import { getAvatarName, getFormattedDate, getFormattedMessage } from '../../../../shared/sharedMethod'
import moment from 'moment'
import RoundLoader from '../../../../shared/components/loaders/RoundLoader'
import { useSelector } from 'react-redux'

function UserDetails(props) {
    const { users, image_url } = props
    const {isLoading} = useSelector(state => state)

    return (
        <>
            <Card className='mt-4'>
                {/* <Card.Header as='h5'>{getFormattedMessage('user.details.title')}</Card.Header> */}
                <Card.Body className=''>
                    {isLoading ? <RoundLoader /> : <Table responsive>
                        <tbody>
                            {/* <tr>
                                <td className='py-4'>{getFormattedMessage('plan.table.plan.column.title')}</td>
                                <td className='py-4'>{users && users.plan}</td>
                            </tr> */}
                            <tr>
                                <td className='py-4'>{getFormattedMessage('globally.react-table.column.created-date.label')}</td>
                                <td className='py-4'>{users && getFormattedDate(users.created_at) + " " + moment(users.created_at).format("LT")}</td>
                            </tr>
                            <tr>
                                <td className='py-4'>{getFormattedMessage('globally.react-table.column.updated-date.label')}</td>
                                <td className='py-4'>{users && getFormattedDate(users.updated_at) + " " + moment(users.updated_at).format("LT")}</td>
                            </tr>
                        </tbody>
                    </Table>}
                </Card.Body>
            </Card>
        </>
    )
}

export default UserDetails
