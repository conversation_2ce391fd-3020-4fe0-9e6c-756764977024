import React, { useEffect, useState } from 'react'
import { Card, Table } from 'react-bootstrap-v5'
import { getFormattedDate, getFormattedMessage } from '../../../../shared/sharedMethod'
import moment from 'moment'
import { fetchAllCurrency } from '../../../../store/action/adminActions/currencyAction'
import { useDispatch, useSelector } from 'react-redux'
import RoundLoader from '../../../../shared/components/loaders/RoundLoader'

function SubscriptionDetails(props) {
  const { subscription } = props
  const dispatch = useDispatch();
  const { currencies, isLoading } = useSelector(state => state)
  const [subscriptionDteais, setSubscription] = useState({})


  useEffect(() => {
    dispatch(fetchAllCurrency());
    if (subscription) {
      setSubscription(subscription)
    }
  }, [subscription])

  const getcurrencies = currencies && currencies.length >= 1 && currencies.filter((item) => item.id === Number(subscriptionDteais?.plan?.currency_id))

  return (
    <Card className='mt-4'>
      {/* <Card.Header as='h5'>{getFormattedMessage('plan.table.plan.column.title')}</Card.Header> */}
      <Card.Body className=''>
        {isLoading ? <RoundLoader /> : <Table responsive>
          <tbody>
            <tr>
              <td className='py-4'>{getFormattedMessage('globally.plan.name.title')}</td>
              <td className='py-4'>{subscriptionDteais && subscriptionDteais?.plan?.name ? subscriptionDteais?.plan?.name : '-'}</td>
            </tr>
            <tr>
              <td className='py-4'>{getFormattedMessage('globally.plan.price.title')}</td>
              <td className='py-4'>{getcurrencies && getcurrencies.length === 1 && getcurrencies[0].attributes.symbol} {subscriptionDteais && subscriptionDteais?.price_of_plan ? subscriptionDteais?.price_of_plan : "-"}</td>
            </tr>
            <tr>
              <td className='py-4'>{getFormattedMessage('globally.plan.start.date.title')}</td>
              <td className='py-4'>{subscriptionDteais && getFormattedDate(subscriptionDteais?.start_date) + " " + moment(subscriptionDteais?.start_date).format("LT")}</td>
            </tr>
            <tr>
              <td className='py-4'>{getFormattedMessage('globally.plan.end.date.title')}</td>
              <td className='py-4'>{subscriptionDteais && getFormattedDate(subscriptionDteais?.end_date) + " " + moment(subscriptionDteais?.end_date).format("LT")}</td>
            </tr>
          </tbody>
        </Table>}
      </Card.Body>
    </Card>
  )
}

export default SubscriptionDetails
