import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link, useParams } from 'react-router-dom';
import moment from 'moment';
import { getAvatarName, getFormattedDate, getFormattedMessage, getFormattedOptions, placeholderText } from '../../../../shared/sharedMethod';
import ReactDataTable from "../../../../shared/table/ReactDataTable"
import { downloadExtentionOptions, Filters, typeOptions } from '../../../../constants';
import Dropdown from "react-bootstrap/Dropdown";
import { faDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { fetchQrcodeById } from '../../../../store/action/userAction';
import { environment } from '../../../../config/environment';
import RoundLoader from '../../../../shared/components/loaders/RoundLoader';
import { Card } from 'react-bootstrap';

const QrCode = (props) => {
    const { totalRecord, isLoading, userQrCodeDteails, fetchQrcodeById, id } = props;

    const itemsValue = userQrCodeDteails.length >= 0 && userQrCodeDteails.map(qrcode => ({
        date: getFormattedDate(qrcode.created_at, "d-m-y"),
        time: moment(qrcode.created_at).format('LT'),
        name: qrcode.name,
        project_name: qrcode?.project !== null ? qrcode?.project?.name : "N/A",
        type: qrcode.type,
        type_name: placeholderText(typeOptions.filter(d => d.id === qrcode?.type)[0].name),
        id: qrcode.id,
        image: qrcode.image_url
    }));


    const downloadExtentionTypeOptions = getFormattedOptions(downloadExtentionOptions)
    const onDownloadClick = (e, url, name, ext) => {
        e.preventDefault();
        saveAs(url, `${name}.${ext}`);
    }

    const columns = [
        {
            name: getFormattedMessage('react-data-table.name.column.title'),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                const imageUrl = row.image ? row.image : null;
                const lastName = row.name ? row.name : '';
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        {
                            imageUrl ?
                                <img src={imageUrl} height='50' width='50' alt='User Image'
                                    className='image image-mini image-effect' /> :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName(row.name)}
                                </span>
                        }
                    </div>
                    <div className='d-flex flex-column text-primary'>
                        {row.name}
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage("globally.input.type.lable"),
            selector: row => row.type_name,
            sortField: 'type_name',
            sortable: false,
        },
        {
            name: getFormattedMessage("globally.input.project.lable"),
            selector: row => row.project_name,
            sortField: 'project_name',
            sortable: false,
            cell: row => {
                return (
                    <span>
                        {row.project_name || "N/A"}
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('react-data-table.download.placeholder'),
            cell: row => {
                return (
                    <Dropdown className="table-dropdown">
                        <Dropdown.Toggle className='text-primary hide-arrow bg-transparent border-0 p-0' id="dropdown-basic">
                            <FontAwesomeIcon icon={faDownload} className={'fs-3'} />
                        </Dropdown.Toggle>
                        <Dropdown.Menu className="w-100">
                            {
                                downloadExtentionTypeOptions?.map((d) => {
                                    return <Dropdown.Item key={d.id} onClick={e => onDownloadClick(e, row.image, row.name, d.name)}>{d.name.toUpperCase()}</Dropdown.Item>
                                })
                            }
                        </Dropdown.Menu>
                    </Dropdown>
                )
            }
        }
    ];


    const onChange = (filter) => {
        fetchQrcodeById(id, filter, true)
    };

    return (<>
        <div className='subHeader-hide'>
            <ReactDataTable isShowSearch isEmpty={isLoading} subHeader={false} columns={columns} items={itemsValue} onChange={onChange} totalRows={totalRecord} isLoading={isLoading} />
        </div></>)

};

const mapStateToProps = (state) => {
    const { totalRecord, isLoading, userQrCodeDteails } = state;
    return { totalRecord, isLoading, userQrCodeDteails }
};
export default connect(mapStateToProps, { fetchQrcodeById })(QrCode);
