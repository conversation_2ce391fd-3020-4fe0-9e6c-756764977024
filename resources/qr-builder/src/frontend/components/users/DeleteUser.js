import React from 'react';
import { connect, useDispatch } from 'react-redux';
import { deleteUser } from '../../../store/action/userAction';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { addToast } from '../../../store/action/toastAction';
import { toastType } from '../../../constants';

const DeleteUser = (props) => {
    const { deleteUser, onDelete, deleteModel, onClickDeleteModel } = props;
    const dispatch = useDispatch()

    const deleteUserClick = () => {
        if (onDelete !== null) {
            if (onDelete?.email === "<EMAIL>") {
                dispatch(addToast(
                    { text: "This action is not allowed on default user.", type: toastType.ERROR }));
            } else {
                deleteUser(onDelete.id);
            }
        }
        onClickDeleteModel(false);
    };

    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage('users.table.user.column.title')} />}
        </div>
    )
};

export default connect(null, { deleteUser })(DeleteUser);
