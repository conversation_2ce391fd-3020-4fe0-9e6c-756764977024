import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import moment from 'moment';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import { fetchUsers, userEmailVerify, userStatus } from '../../../store/action/userAction';
import DeleteUser from './DeleteUser';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedDate } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";

const User = (props) => {
    const { users, fetchUsers, totalRecord, isLoading, allConfigData, userEmailVerify, userStatus } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);
    const [isStatus, setIsStatus] = useState(false);
    const [isEmailStatus, setIsEmailStatus] = useState(false);

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    const itemsValue = users.length > 0 && users.map(user => ({
        date: getFormattedDate(user.attributes?.created_at, "d-m-y"),
        time: moment(user.attributes?.created_at).format('LT'),
        name: user.attributes?.name,
        email: user.attributes?.email,
        image: user.attributes?.image,
        role_name: user.attributes?.role_name,
        status: user.attributes?.status,
        plan: user.attributes?.plan,
        plan_id: user.attributes?.plan_id,
        email_verified_at: user.attributes?.email_verified_at,
        id: user?.id
    }));

    const onChange = (filter) => {
        fetchUsers(filter, true);
    };

    const onCheckedStatus = (e, id) => {
        setIsStatus(true)
        userStatus(id, setIsStatus)
    };

    const onCheckedEmail = (e, id) => {
        setIsEmailStatus(true)
        userEmailVerify(id, setIsEmailStatus)
    };

    const goToEdit = (item) => {
        const id = item.id;
        window.location.href = '#/app/admin/users/edit/' + id;
    };

    const columns = [
        {
            name: getFormattedMessage('react-data-table.name.column.title'),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/users/detail/${row.id}`}>
                            {row.image ?
                                <img src={row.image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName(row.name)}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row.id}`} className='text-decoration-none'>{row.name}</Link>
                        <span>{row.email}</span>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.plan.name.title'),
            selector: row => row.plan,
            sortField: 'plan',
            sortable: false,
            cell: row => {
                return (
                    <>{row.plan === null ? <div className='d-flex px-8 flex-colum'>
                        -
                    </div> : <div className='d-flex flex-column badge bg-light-success'>
                        {row.plan}
                    </div>}</>

                )
            }
        },
        {
            name: getFormattedMessage('globally.react-table.column.email-verified.label'),
            selector: row => row.status,
            cell: row => {
                return (
                    <div className="col-md-4 d-flex align-items-center mt-4">
                        <label className="form-check form-switch form-switch-sm cursor-pointer">
                            <input autoComplete="off" name="paypal" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                checked={row.email_verified_at !== null ? true : false}
                                onChange={(e) => onCheckedEmail(e, row.id)}
                                disabled={row.email_verified_at !== null ? true : false}
                            />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                )
            }
        },
        {
            name: getFormattedMessage('globally.user.status.title'),
            selector: row => row.status,
            cell: row => {
                return (
                    <div className="col-md-4 d-flex align-items-center mt-4 ">
                        <label className="form-check form-switch form-switch-sm cursor-pointer">
                            <input autoComplete="off" name="paypal" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                checked={row.status}
                                onChange={(e) => onCheckedStatus(e, row.id)}
                            />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                )
            }
        },

        // {
        //     name: getFormattedMessage('globally.react-table.column.created-date.label'),
        //     selector: row => row.date,
        //     sortField: 'created_at',
        //     sortable: true,
        //     cell: row => {
        //         return (
        //             <span className='badge bg-light-info'>
        //                 <div className='mb-1'>{row.time}</div>
        //                 <div>{row.date}</div>
        //             </span>
        //         )
        //     }
        // },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton item={row} goToEditProduct={goToEdit} isEditMode={true}
                onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('users.title')} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                ButtonValue={getFormattedMessage('user.create.title')} isVStatus={isStatus} isEmailStatus={isEmailStatus}
                to='#/app/admin/users/create' totalRows={totalRecord} isLoading={isLoading} />
            <DeleteUser onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { users, totalRecord, isLoading, allConfigData } = state;
    return { users, totalRecord, isLoading, allConfigData }
};
export default connect(mapStateToProps, { fetchUsers, userEmailVerify, userStatus })(User);
