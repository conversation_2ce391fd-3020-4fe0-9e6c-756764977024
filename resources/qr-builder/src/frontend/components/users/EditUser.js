import React, {useEffect, useState} from 'react';
import {connect} from 'react-redux';
import UserForm from './UserForm';
import { fetchUser } from '../../../store/action/userAction';
import {useParams} from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import {getFormattedMessage, placeholderText} from '../../../shared/sharedMethod';
import TopProgressBar from '../../../shared/components/loaders/TopProgressBar';
import TabTitle from "../../../shared/tab-title/TabTitle";

const EditUser = (props) => {
    const {fetchUser, users} = props;
    const {id} = useParams();
    const [isEdit, setIsEdit] = useState(false);

    useEffect(() => {
        fetchUser(id);
        setIsEdit(true);
    }, []);

    const itemsValue = users && users?.length === 1 && users[0]?.user?.map(user => ({
        name: user?.name,
        email: user?.email,
        image: user?.image_url,
        id: user?.id
    }));

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('user.edit.title')}/>
            <HeaderTitle title={getFormattedMessage('user.edit.title')} to='/app/admin/users'/>
            {users.length === 1 && <UserForm singleUser={itemsValue} id={id} isEdit={isEdit}/>}
        </MasterLayout>
    );
}

const mapStateToProps = (state) => {
    const {users} = state;
    return {users}
};

export default connect(mapStateToProps, {fetchUser})(EditUser);
