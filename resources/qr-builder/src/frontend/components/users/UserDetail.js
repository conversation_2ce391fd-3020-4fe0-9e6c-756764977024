import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Card } from 'react-bootstrap';
import { Image, Tab, Tabs } from 'react-bootstrap-v5';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../../shared/tab-title/TabTitle';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { getAvatarName, placeholderText } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import { fetchUser } from '../../../store/action/userAction';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import UserDetails from './details-tab/UserDetails';
import QrCodeDetails from './details-tab/QrCodeDetails';
import LinkDetails from './details-tab/LinkDetails';
import SubscriptionDetails from './details-tab/subscriptionDetails';
import RoundLoader from '../../../shared/components/loaders/RoundLoader';

const UserDetail = (props) => {
    const { users, fetchUser, isLoading } = props;
    const { id } = useParams();
    const [key, setKey] = useState('overview');
    const [user, setUser] = useState([])
    const [subscription, setSubscription] = useState([])

    useEffect(() => {
        fetchUser(id);
    }, []);

    useEffect(() => {
        users[0]?.user && setUser(users[0]?.user)
        users[0]?.user && setSubscription(users[0]?.user[0]?.subscription)
    }, [users])

    return (
        <MasterLayout>
            <TopProgressBar />
            <HeaderTitle title={getFormattedMessage('user-details.title')} to='/app/admin/users' editLink={`/app/admin/users/edit/${id}`} />
            <TabTitle title={placeholderText('user-details.title')} />

            {<>
                <div>
                    <Card>
                        <Card.Body>
                            {<div className='row'>
                                <div className='col-xxl-5 col-12'>
                                    <div
                                        className='d-sm-flex align-items-center mb-5 mb-xxl-0 flex-row text-sm-start'>
                                        <div
                                            className='image image-circle pe-5 image-small'>
                                            {user && user[0]?.image_url ?
                                                <Image src={user[0]?.image_url} alt='User Profile'
                                                    className='object-fit-cover image-effect' /> :
                                                <span className='user_avatar'>
                                                    {getAvatarName(user && user[0]?.name)}
                                                </span>
                                            }
                                        </div>
                                        <div className='ms-0 ms-md-10 mt-5 mt-sm-0'>
                                            {user && user[0]?.status === true ?
                                                <span className='badge bg-light-success mb-2'>{getFormattedMessage('globally.active.label')}</span>
                                                : <span className='badge bg-light-danger mb-2'>{getFormattedMessage("globally.deactive.label")}</span>
                                            }
                                            <h2> {user && user[0]?.name}</h2>
                                            <a href={user && `mailto:${user[0]?.email}`}
                                                className='text-gray-600 text-decoration-none fs-4'>
                                                {user && user[0]?.email}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>}
                        </Card.Body>
                    </Card>
                </div>
                <Tabs defaultActiveKey='overview' id='uncontrolled-tab-example' onSelect={(k) => setKey(k)}
                    className='mt-2'>
                    <Tab eventKey='overview' title={getFormattedMessage('user-details.table.title')}
                        tabClassName='position-relative bg-transparent border-0'>
                        <div className='w-100 mx-auto'>
                            {key === 'overview' && <UserDetails users={user && user[0]} />}
                        </div>
                    </Tab>
                    <Tab eventKey='subscription' title={getFormattedMessage('subscription.titile')}
                        tabClassName='position-relative bg-transparent border-0'>
                        <div className='w-100 mx-auto'>
                            {key === 'subscription' && <SubscriptionDetails subscription={subscription} />}
                        </div>
                    </Tab>
                    <Tab eventKey='qr-code' title={getFormattedMessage('globally.qrcode.title')}
                        tabClassName='position-relative bg-transparent border-0'>
                        <div className='w-100 mx-auto'>
                            {key === 'qr-code' && <QrCodeDetails id={id} />}
                        </div>
                    </Tab>
                    <Tab eventKey='links' title={getFormattedMessage('links.title')}
                        tabClassName='position-relative bg-transparent border-0'>
                        <div className='w-100 mx-auto'>
                            {key === 'links' && <LinkDetails />}
                        </div>
                    </Tab>
                </Tabs>
            </>
            }
        </MasterLayout>
    )
};

const mapStateToProps = state => {
    const { users, isLoading } = state;
    return { users, isLoading }
};

export default connect(mapStateToProps, { fetchUser })(UserDetail);
