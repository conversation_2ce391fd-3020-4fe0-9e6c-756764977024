import React, { useEffect, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import * as EmailValidator from 'email-validator';
import { editUser } from '../../../store/action/userAction';
import { getAvatarName, getFormattedMessage, placeholderText, numValidate } from '../../../shared/sharedMethod';
import user from '../../../assets/images/avatar.png';
import ModelFooter from '../../../shared/components/modelFooter';
import ImagePicker from '../../../shared/image-picker/ImagePicker';
import PasswordHandling from '../../../shared/password-handler/PasswordHandling';
import ConfrimPasswordHandling from '../../../shared/password-handler/ConfrimPasswordHandling';
import { addToast } from '../../../store/action/toastAction';
import { toastType } from '../../../constants';


const UserForm = (props) => {
    const { addUserData, id, singleUser, isEdit, isCreate, roles } = props;
    const Dispatch = useDispatch()
    const navigate = useNavigate();
    const role_id = localStorage.getItem('user_role')
    const [userValue, setUserValue] = useState({
        name: singleUser ? singleUser[0].name : '',
        email: singleUser ? singleUser[0].email : '',
        password: '',
        confirm_password: '',
        role_id: singleUser ? singleUser[0].role_id : '',
        image: singleUser ? singleUser[0].image : '',
    });

    const [errors, setErrors] = useState({
        name: '',
        email: '',
        password: '',
        confirm_password: '',
        role_id: '',
    });

    const avatarName = getAvatarName(singleUser && singleUser[0].image === '' && singleUser[0].first_name && singleUser[0].last_name && singleUser[0].first_name + ' ' + singleUser[0].last_name)
    const newImg = singleUser && singleUser[0].image && singleUser[0].image === null && avatarName;
    const [imagePreviewUrl, setImagePreviewUrl] = useState(newImg && newImg);
    const [selectImg, setSelectImg] = useState(null);

    const disabled = singleUser && singleUser[0]?.name === userValue.name &&
        singleUser[0]?.email === userValue.email &&
        userValue.image === imagePreviewUrl &&
        !userValue.password && !userValue.confirm_password

    useEffect(() => {
        setImagePreviewUrl(singleUser ? singleUser[0].image && singleUser[0].image : user);
        setUserValue({
            name: singleUser ? singleUser[0].name : '',
            email: singleUser ? singleUser[0].email : '',
            password: '',
            confirm_password: '',
            role_id: singleUser ? singleUser[0].role_id : '',
            image: singleUser ? singleUser[0].image : '',
        })
    }, [singleUser]);

    const handleImageChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === 'image/jpeg' || file.type === 'image/png') {
                setSelectImg(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setImagePreviewUrl(fileReader.result);
                };
                fileReader.readAsDataURL(file);
                setErrors('');
            }
        }
    };

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        const nameReg = /^[a-zA-Z ]{2,30}$/
        if (!userValue.name || userValue['name'].trim().length === 0) {
            errorss['name'] = getFormattedMessage("user.input.first-name.validate.label");
        } else if (!EmailValidator.validate(userValue['email'])) {
            if (!userValue['email']) {
                errorss['email'] = getFormattedMessage("user.input.email.validate.label");
            } else {
                errorss['email'] = getFormattedMessage("user.input.email.valid.validate.label");
            }
        } else if (!isEdit && !userValue['password']) {
            errorss['password'] = getFormattedMessage("user.input.password.validate.label");
        } else if (!isEdit && userValue['password'].length <= 5) {
            errorss['password'] = getFormattedMessage("change-password.input.characters.valid.validate.label");
        } else if (!isEdit && (!userValue['password'] || userValue['password'].trim().length === 0)) {
            errorss['password'] = getFormattedMessage("change-password.input.valid.validate.label");
        } else if (!isEdit && (userValue['password'] !== userValue['confirm_password'])) {
            errorss['confirm_password'] = getFormattedMessage("change-password.input.confirm.valid.validate.label");
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };

    const onChangeInput = (e) => {
        e.preventDefault();
        setUserValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    };


    const prepareFormData = (data) => {
        const formData = new FormData();
        formData.append('name', data.name);
        formData.append('email', data.email);
        formData.append('password', data.password);
        formData.append('confirm_password', data.confirm_password);
        if (!isEdit) {
            formData.append('role_id', 2);
        }
        if (selectImg) {
            formData.append('image', data.image);
        }
        return formData;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        userValue.image = selectImg;
        const valid = handleValidation();
        if (singleUser && valid) {
            if (!disabled) {
                userValue.image = selectImg;
                if (singleUser[0]?.email === "<EMAIL>") {
                    Dispatch(addToast(
                        { text: "This action is not allowed on default user.", type: toastType.ERROR }));
                    setImagePreviewUrl(singleUser ? singleUser[0].image && singleUser[0].image : user);
                    setUserValue({
                        name: singleUser ? singleUser[0].name : '',
                        email: singleUser ? singleUser[0].email : '',
                        password: '',
                        confirm_password: '',
                        role_id: singleUser ? singleUser[0].role_id : '',
                        image: singleUser ? singleUser[0].image : '',
                    })
                } else {
                    Dispatch(editUser(id, prepareFormData(userValue), navigate));
                }
            }
        } else {
            if (valid) {
                setUserValue(userValue);
                addUserData(prepareFormData(userValue));
                setImagePreviewUrl(imagePreviewUrl ? imagePreviewUrl : user);
            }
        }
    };

    return (
        <div className='card'>
            <div className='card-body'>
                <Form>
                    <div className='row'>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("globally.input.name.label")}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={userValue.name}
                                placeholder={placeholderText("globally.input.name.label")}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['name'] ? errors['name'] : null}</span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label className='form-label'>
                                {getFormattedMessage("user.input.email.label")}:
                            </label>
                            <span className='required' />
                            <input type='text' name='email' className='form-control'
                                placeholder={placeholderText("user.input.email.label")}
                                onChange={(e) => onChangeInput(e)}
                                value={userValue.email}
                                autoComplete={"username"} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['email'] ? errors['email'] : null}</span>
                        </div>
                        {/* {isEdit ? '' : */}
                        <div className='col-md-6 mb-3'>
                            <PasswordHandling onChangeInput={onChangeInput} passwordValue={userValue.password} IsRequired={!isEdit} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['password'] ? errors['password'] : null}</span>
                        </div>
                        {/* } */}
                        {/* {isEdit ? '' : */}
                        <div className='col-md-6 mb-3'>
                            <ConfrimPasswordHandling onChangeInput={onChangeInput} passwordValue={userValue.confirm_password} IsRequired={!isEdit} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['confirm_password'] ? errors['confirm_password'] : null}</span>
                        </div>
                        {/* } */}
                        <div className='mb-4'>
                            <ImagePicker user={user}
                                isCreate={isCreate} avtarName={avatarName}
                                imageTitle={"header.profile-menu.profile.label"} imagePreviewUrl={imagePreviewUrl} handleImageChange={handleImageChanges}
                            />
                        </div>
                        <ModelFooter onEditRecord={singleUser} onSubmit={onSubmit} editDisabled={disabled}
                            link='/app/admin/users' addDisabled={!userValue.name} />
                    </div>
                </Form>
            </div>
        </div>
    )
};

const mapStateToProps = (state) => {
    const { roles } = state;
    return { roles }
};

export default connect(mapStateToProps, {})(UserForm);

