import React from 'react';
import { connect } from 'react-redux';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { deleteSubscriber } from '../../../store/action/subscribersAction';

const DeleteSubscribers = (props) => {
    const { deleteSubscriber, onDelete, deleteModel, onClickDeleteModel } = props;

    const deleteUserClick = () => {
        deleteSubscriber(onDelete.id);
        onClickDeleteModel(false);
    };


    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage("subscriber.title")} />}
        </div>
    )
};

export default connect(null, { deleteSubscriber })(DeleteSubscribers);
