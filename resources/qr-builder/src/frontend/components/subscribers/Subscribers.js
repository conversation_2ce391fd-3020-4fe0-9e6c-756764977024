import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import DeleteSubscribers from './DeleteSubscribers';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { fetchSubscribers } from '../../../store/action/subscribersAction';

const Subscribers = (props) => {
    const { subscriber, fetchSubscribers, totalRecord, isLoading, makeDefaultPlan } = props;
    const [deleteModel, setDeleteModel] = useState(false);
    const [isDelete, setIsDelete] = useState(null);
    const [itemsValue, setitemsValue] = useState([])

    const navigate = useNavigate()

    const onClickDeleteModel = (isDelete = null) => {
        setDeleteModel(!deleteModel);
        setIsDelete(isDelete);
    };

    useEffect(() => {
        const itemsValue = subscriber?.length >= 0 && subscriber?.map(subscriber => ({
            // date: getFormattedDate(Plan.attributes.created_at, "d-m-y"),
            // time: moment(Plan.attributes.created_at).format('LT'),
            email: subscriber?.email,
            id: subscriber?.id
        }));
        setitemsValue(itemsValue)
    }, [subscriber, subscriber?.length])


    const onChange = (filter) => {
        fetchSubscribers(filter, true);
    };

    const columns = [
        {
            name: getFormattedMessage('globally.input.email.placeholder.label'),
            selector: row => row?.email,
            sortField: 'email',
            sortable: true,
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton isDeleteMode={true} item={row} isEditMode={false}
                onClickDeleteModel={onClickDeleteModel} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('subscribers.title')} />
            <ReactDataTable columns={columns} items={itemsValue} onChange={onChange} totalRows={totalRecord} isLoading={isLoading} />
            <DeleteSubscribers onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )
};

const mapStateToProps = (state) => {
    const { subscriber, totalRecord, isLoading, allConfigData } = state;
    return { subscriber, totalRecord, isLoading, allConfigData }
};
export default connect(mapStateToProps, { fetchSubscribers })(Subscribers);
