import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Card, Table } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { formatAmount, getFormattedDate, getFormattedOptions, placeholderText } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import moment from 'moment';
import { fetchTransaction } from '../../../store/action/adminActions/transactionAction';
import { paymentMethodLocalizedOptions } from '../../../constants';
import { fetchAllCurrency } from '../../../store/action/adminActions/currencyAction';
import HeaderTitle from '../../../components/header/HeaderTitle';
import RoundLoader from '../../../shared/components/loaders/RoundLoader';


const TransactionsDetails = (props) => {
    const { transactions, currencies, fetchTransaction, fetchAllCurrency, isLoading } = props;
    const { id } = useParams();

    useEffect(() => {
        fetchTransaction(id)
        fetchAllCurrency()
    }, []);

    const getTypeName = (typeId) => {
        return getFormattedMessage(paymentMethodLocalizedOptions.filter((d) => d.id === typeId)[0]?.name)
    }

    const currenciesType = currencies.map((d) => {
        return {
            id: d.id,
            name: d.attributes.symbol
        }
    })
    const currenciesOption = getFormattedOptions(currenciesType)
    const currenciesTypeDefaultValue = currenciesOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const findCurrencies = currenciesTypeDefaultValue.filter((items) => { return (items.value === transactions?.subscription?.plan?.currency_id) })

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('transactions.details.title')} />
            <HeaderTitle title={getFormattedMessage('transactions.details.title')} to={"/app/admin/transactions"} />
            {<>
                <div className='pt-5'>
                    <Card>
                        <Card.Body>
                            {
                                isLoading ? <RoundLoader /> :
                                    <Table responsive>
                                        <tbody>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.plan.name.title')}</td>
                                                <td className='py-4'>{transactions?.subscription?.plan?.name}</td>
                                            </tr>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.heading.frequency.title')}</td>
                                                <td className='py-4'>{transactions?.subscription?.plan?.type === 1 ? "Month" : "Year"}</td>
                                            </tr>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('new.plan.payable.amount.title')}</td>
                                                <td className='py-4'>{findCurrencies[0]?.label + ' ' + formatAmount(transactions?.price_of_plan)}</td>
                                            </tr>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.react-table.column.payment-date.label')}</td>
                                                <td className='py-4'>{getFormattedDate(transactions?.subscription?.start_date) + " " + moment(transactions?.subscription?.start_date).format("LT")}</td>
                                            </tr>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.react-table.column.payment-status.label')}</td>
                                                <td className='py-4'>
                                                    {
                                                        transactions?.payment_mode === 4
                                                            ? transactions?.is_manual_payment === 0
                                                                ? <span className='badge bg-light-primary'>
                                                                    {getFormattedMessage("globally.pending.label")}
                                                                </span>
                                                                : transactions?.is_manual_payment === 1
                                                                    ? <span className={`badge bg-light-success`}>
                                                                        <span>{getFormattedMessage("globally.paid.label")}</span>
                                                                    </span>
                                                                    : <span className={`badge bg-light-danger`}>
                                                                        <span>{getFormattedMessage("globally.reject.label")}</span>
                                                                    </span>
                                                            : transactions?.status === false
                                                                ? <span className={`badge bg-light-danger`}>
                                                                    <span>{getFormattedMessage("globally.reject.label")}</span>
                                                                </span>
                                                                : <span className={`badge bg-light-success`}>
                                                                    <span>{getFormattedMessage("globally.paid.label")}</span>
                                                                </span>
                                                    }
                                                </td>
                                            </tr>
                                            <tr>
                                                <td className='py-4'>{getFormattedMessage('globally.react-table.column.payment-type.label')}</td>
                                                <td className='py-4'>
                                                    <span className={`badge bg-light-success`}>
                                                        <span>{transactions?.payment_mode === undefined ? getTypeName(4) : getTypeName(transactions?.payment_mode)}</span>
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </Table>
                            }
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </MasterLayout>
    )

};


const mapStateToProps = state => {
    const { isLoading, transactions, currencies, frontSettings } = state;
    return { isLoading, transactions, currencies, frontSettings }
};


export default connect(mapStateToProps, { fetchTransaction, fetchAllCurrency })(TransactionsDetails);
