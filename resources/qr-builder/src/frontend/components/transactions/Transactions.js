import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { formatAmount, getFormattedDate, getFormattedOptions } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { fetchAllCurrency } from '../../../store/action/adminActions/currencyAction';
import moment from 'moment';
import { fetchTransactions } from '../../../store/action/adminActions/transactionAction';
import { paymentMethodLocalizedOptions } from '../../../constants';

const Transactions = (props) => {
    const { totalRecord, isLoading, fetchAllCurrency, currencies, fetchTransactions, transactions } = props;
    const navigate = useNavigate()

    useEffect(() => {
        fetchAllCurrency()
    }, [])

    const getTypeName = (typeId) => {
        return getFormattedMessage(paymentMethodLocalizedOptions.filter((d) => d.id === typeId)[0]?.name)
    }

    const currenciesType = currencies.map((d) => {
        return {
            id: d.id,
            name: d.attributes.symbol
        }
    })
    const currenciesOption = getFormattedOptions(currenciesType)
    const currenciesTypeDefaultValue = currenciesOption.map((option) => {
        return {
            value: option.id,
            label: option.name
        }
    })

    const itemsValue = transactions.length >= 0 && transactions.map(item => {
        const findCurrencies = currenciesTypeDefaultValue.filter((items) => { return (items.value === item?.attributes?.currency_id) })
        return {
            user_name: item?.attributes?.user_name,
            name: item?.attributes?.name,
            email: item?.attributes?.email,
            payment_date: getFormattedDate(item?.attributes?.payment_date) + " " + moment(item?.attributes?.payment_date).format("LT"),
            plan_name: item?.attributes?.plan_name,
            payment_type: getTypeName(item?.attributes?.payment_type),
            amount: findCurrencies[0]?.label + ' ' + formatAmount(item?.attributes?.amount),
            plan_amount: findCurrencies[0]?.label + ' ' + formatAmount(item?.attributes?.plan_amount),
            currency_id: findCurrencies[0]?.label,
            id: item?.id,
            status: item?.attributes?.payment_type === 4
                ? item?.attributes?.is_manual_payment === 0
                    ? 0
                    : item?.attributes?.is_manual_payment === 1
                        ? 1
                        : 2
                : item?.attributes?.status === false
                    ? 2
                    : 1
        }
    });

    const onChange = (filter) => {
        fetchTransactions(filter, true);
    };

    const goToDetailScreen = (id) => {
        navigate("/app/admin/transactions/" + id)
    }

    const columns = [
        {
            name: getFormattedMessage('globally.react-table.column.payment-date.label'),
            selector: row => row?.payment_date,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return <span className='badge bg-light-info text-wrap'>
                    {row?.payment_date}
                </span>
            }
        },
        {
            name: getFormattedMessage('users.table.user-name.column.title'),
            selector: row => row?.user_name,
            sortField: 'plan_name',
            sortable: false,
        },
        {
            name: getFormattedMessage('globally.plan.name.title'),
            selector: row => row?.plan_name,
            sortField: 'subscription.plan.name',
            sortable: false,
        },
        {
            name: getFormattedMessage('globally.plan.price.title'),
            sortField: 'price_of_plan',
            sortable: true,
            cell: row => {
                return <div className=''>
                    {row?.plan_amount}
                </div>
            }
        },
        {
            name: getFormattedMessage('new.plan.payable.amount.title'),
            sortField: 'price_of_plan',
            sortable: true,
            cell: row => {
                return <div className='w-75'>
                    {row?.amount}
                </div>
            }
        },
        {
            name: getFormattedMessage('globally.react-table.column.payment-type.label'),
            selector: row => row?.payment_type,
            sortField: 'payment_type',
            sortable: false,
            cell: row => {
                return (
                    <span className='badge bg-light-success'>
                        <div>{row?.payment_type}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage('dashboard.recentSales.status.label'),
            selector: row => row?.status,
            sortField: 'status',
            sortable: false,
            cell: row => {
                return row?.status === 1 ?
                    <span className='badge bg-light-success'>
                        {getFormattedMessage("globally.paid.label")}
                    </span>
                    : row?.status === 2 ?
                        <span className='badge bg-light-danger'>
                            {getFormattedMessage("globally.reject.label")}
                        </span>
                        :
                        <span className='badge bg-light-primary'>
                            {getFormattedMessage("globally.pending.label")}
                        </span>
            }
        },
        {
            name: getFormattedMessage('react-data-table.action.column.label'),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <ActionButton item={row} isViewIcon={true} isEditMode={false} isDeleteMode={false} goToDetailScreen={goToDetailScreen} />
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('transactions.title')} />
            <div className='transactions-table'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                    totalRows={totalRecord} isLoading={isLoading} />
            </div>
        </MasterLayout>
    )
};


const mapStateToProps = (state) => {
    const { totalRecord, isLoading, currencies, transactions } = state;
    return { totalRecord, isLoading, currencies, transactions }
};
export default connect(mapStateToProps, { fetchAllCurrency, fetchTransactions })(Transactions);
