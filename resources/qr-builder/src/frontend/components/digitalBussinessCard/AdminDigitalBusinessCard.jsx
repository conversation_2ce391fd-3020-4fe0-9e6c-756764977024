import React, { useState } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import MasterLayout from '../MasterLayout';
import ReactDataTable from '../../../shared/table/ReactDataTable';
import { fetchLinks } from '../../../store/action/linkAction';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getAvatarName, getFormattedDate, getFormattedMessage } from '../../../shared/sharedMethod';
import { placeholderText } from '../../../shared/sharedMethod';
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import { environment } from "../../../config/environment";
import { addToast } from '../../../store/action/toastAction';
import { businessCardLinkActionType, toastType } from '../../../constants';
import { saveAs } from 'file-saver';
import ImageModal from '../../../shared/imageModal/ImageModal';
import { adminGetBusinessCards, updateStatusBusinessCards } from '../../../store/action/digitalBusinessCardsAction';
import ActionButton from '../../../shared/action-buttons/ActionButton';
import { Link, useNavigate } from 'react-router-dom';
import ActionDropDownButton from '../../../shared/action-buttons/ActionDropDownButton';
import DeleteBusinessCard from './DeleteBusinessCard';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPaste } from '@fortawesome/free-solid-svg-icons';

const AdminDigitalBusinessCard = ( props ) => {
    const { totalRecord, isLoading } = props;
    const [ isStatus, setIsStatus ] = useState( false )
    const [ deleteModel, setDeleteModel ] = useState( false );
    const [ isDelete, setIsDelete ] = useState( null );
    const [ showImageSlider, setShowImageSlider ] = useState( {
        display: false,
        src: '',
        name: ""
    } )

    const dispatch = useDispatch()
    const navigate = useNavigate()
    const { adminBusinessCard } = useSelector( state => state )

    const closeImageSlider = () => {
        setShowImageSlider( {
            display: false,
            src: '',
            name: ""
        } )
    }

    const onDownloadClick = ( e, url, name, ext ) => {
        e.preventDefault();
        saveAs( url, `${name}.${ext}` );
    }

    const itemsValue = adminBusinessCard.length > 0 && adminBusinessCard.map( card => ( {
        profile_image: card?.attributes?.profile_image,
        name: card?.attributes?.name,
        email: card?.attributes?.email,
        website: `${environment.URL}/${card?.attributes?.url_alias}`,
        date: getFormattedDate( card?.attributes?.created_at, "d-m-y" ),
        status: card?.attributes?.status === false ? false : card?.attributes?.status === 0 ? false : true,
        user_name: card?.attributes?.user_name,
        user_id: card?.attributes?.user_id,
        id: card?.id,
        url_alias: `${environment.URL}/${card?.attributes?.url_alias}`,
        job_title: card?.attributes?.job_title,
        company: card?.attributes?.company,
        phone: card?.attributes?.phone,
        template_id: card?.attributes?.template_id
    } ) );

    const onChange = ( filter ) => {
        dispatch( adminGetBusinessCards( filter ) )
        dispatch( { type: businessCardLinkActionType.FETCH_BUSINESS_CARD_LINK, payload: [] } )
    };
    const onClickDeleteModel = ( isDelete = null ) => {
        setDeleteModel( !deleteModel );
        setIsDelete( isDelete );
    };

    const goToEdit = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/admin/digital-business-cards/edit/' + id;
    };

    const copyClickBoard = ( data ) => {
        const unsecuredCopyToClipboard = () => {
            const textArea = document.createElement( "textarea" );
            textArea.value = data.url_alias;
            document.body.appendChild( textArea );
            textArea.focus();
            textArea.select();
            try {
                document.execCommand( 'copy' );
                dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
            } catch ( err ) {
                dispatch( addToast( { text: `Failed to copy text to clipboard: ${err}`, type: toastType.ERROR } ) )
            }
            document.body.removeChild( textArea );
        }

        if ( window.isSecureContext && navigator.clipboard ) {
            navigator.clipboard.writeText( data.url_alias );
            dispatch( addToast( { text: getFormattedMessage( 'globally.copy-link.message' ), type: toastType.ADD_TOAST } ) )
        } else {
            unsecuredCopyToClipboard();
        }
    }

    const goToAnalytics = ( item ) => {
        const id = item.id;
        window.location.href = '#/app/admin/digital-business-cards/' + id + '/analytics';
    };

    const onCheckedStatus = ( e, id ) => {
        setIsStatus( true )
        dispatch( updateStatusBusinessCards( id, setIsStatus ) )
    };

    const goToDetailScreen = ( id ) => {
        window.location.href = '#/app/admin/digital-business-cards/detail/' + id;
    }

    const columns = [
        {
            name: getFormattedMessage( 'react-data-table.name.column.title' ),
            selector: row => row.name,
            sortField: 'name',
            sortable: true,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='me-2'>
                        <Link to={`/app/admin/digital-business-cards`}>
                            {row.profile_image ?
                                <img src={row.profile_image} height='50' width='50' alt='User Image'
                                    className='image image-circle image-mini image-effect' />
                                :
                                <span className='custom-user-avatar fs-5'>
                                    {getAvatarName( row.name )}
                                </span>
                            }
                        </Link>
                    </div>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/digital-business-cards`} className='text-decoration-none'>{row.name}</Link>
                        <span>{row.email}</span>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'alias-url.input.placeholder.label' ),
            selector: row => row.url_alias,
            sortField: 'url_alias',
            sortable: false,
            cell: row => {
                return <div className='text-primary pe-3'>
                    <a href={row.website} target={"_blank"} className={"text-decoration-none"}>{row.url_alias}</a>
                    <span className='text-blue ms-2 fs-3 cursor-pointer' title={placeholderText( 'globally.copy-link.tool-tip.message' )} onClick={( e ) => { copyClickBoard( row ) }}>
                        <FontAwesomeIcon icon={faPaste} className='text-primary' />
                    </span>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'users.table.user-name.column.title' ),
            selector: row => row.user_name,
            sortField: 'user_name',
            sortable: false,
            cell: row => {
                return <div className='d-flex align-items-center'>
                    <div className='d-flex flex-column'>
                        <Link to={`/app/admin/users/detail/${row.user_id}`} className='text-decoration-none'>{row.user_name}</Link>
                    </div>
                </div>
            }
        },
        {
            name: getFormattedMessage( 'globally.react-table.column.status.label' ),
            selector: row => row.status,
            cell: row => {
                return (
                    <div className="col-md-4 d-flex align-items-center mt-4 ">
                        <label className="form-check form-switch form-switch-sm cursor-pointer">
                            <input autoComplete="off" name="paypal" data-id="704" className="form-check-input admin-status cursor-pointer" type="checkbox"
                                checked={row.status}
                                onChange={( e ) => onCheckedStatus( e, row.id )}
                            />
                            <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                        </label>
                    </div>
                )
            }
        },
        {
            name: getFormattedMessage( 'globally.react-table.column.created-date.label' ),
            selector: row => row.date,
            sortField: 'created_at',
            sortable: true,
            cell: row => {
                return (
                    <span className='badge bg-light-info'>
                        {/* <div className='mb-1'>{row.date}</div> */}
                        <div>{row.date}</div>
                    </span>
                )
            }
        },
        {
            name: getFormattedMessage( 'react-data-table.action.column.label' ),
            right: true,
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
            cell: row => <><ActionButton item={row} isEditMode={true}
                isAnalytics={true}
                isCopyBtn={false}
                isDeleteMode={true}
                isViewIcon={true}
                goToEditProduct={goToEdit}
                onClickDeleteModel={onClickDeleteModel}
                goToAnalytics={goToAnalytics}
                onCopybtn={copyClickBoard}
                goToDetailScreen={goToDetailScreen}
            />
            </>
        }
    ];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText( "digital.business.card.title" )} />
            <div className='business-card-table'>
                <ReactDataTable columns={columns} items={itemsValue} onChange={onChange}
                    ButtonValue={getFormattedMessage( "digital.business.card.add.title" )}
                    to='#/app/admin/digital-business-cards/create' totalRows={totalRecord} isLoading={isLoading} isStatus={isStatus} />
            </div>
            <ImageModal display={showImageSlider} closeSlider={closeImageSlider} onDownloadClick={onDownloadClick} />
            <DeleteBusinessCard onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel} onDelete={isDelete} />
        </MasterLayout>
    )
};

const mapStateToProps = ( state ) => {
    const { links, totalRecord, isLoading, allConfigData } = state;
    return { links, totalRecord, isLoading, allConfigData }
};
export default connect( mapStateToProps, { fetchLinks } )( AdminDigitalBusinessCard );
