import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { getFormattedMessage, placeholderText } from "./../../../../shared/sharedMethod";
import { Card, ProgressBar, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const OsAnalyticsTab = (props) => {
    const { weekSalePurchase, frontSetting, linkData, changeTab } = props
    const [operatingSystemDetails, setOperatingSystemDetails] = useState([])
    const variants = ["success", "primary", "dark", "secondary", "info", "warning", "danger"]

    useEffect(() => {
        if (linkData) {

            let osData = [];
            [linkData.operating_system]?.map((d) => {
                for (let key in d) {
                    osData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: key,
                        color: variants[Math.floor(Math.random() * variants.length)]
                    })
                }
            })
            setOperatingSystemDetails(osData)
        }
    }, [linkData])

    return <>
        {operatingSystemDetails.length >= 1 &&
            // <Card>
                <Card.Body>
                    <Card>
                        <Card.Body className='border rounded'>
                            <Card.Title className='mb-5 '>{getFormattedMessage("globally.input.os.label")}</Card.Title>
                            {operatingSystemDetails.map((d, i) => {
                                return <React.Fragment key={i + 1}>
                                    <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                        <span>
                                            {d.name === "OS X" ? "Mac OS" : d.name || "Windows"}
                                        </span>
                                        <span>
                                            {d.per}% {d.count}
                                        </span>
                                    </Card.Subtitle>
                                    <>
                                        <ProgressBar variant={d.color} now={d.per} />
                                    </>
                                </React.Fragment>
                            })}
                            {/* <Button variant='Link'
                                className="text-muted p-0 my-2 text-decoration-underline"
                                onClick={() => changeTab("os")}
                            >View more</Button> */}
                        </Card.Body>
                    </Card>
                </Card.Body>
            // </Card>
            }
        {operatingSystemDetails.length === 0 &&
            <Card>
                <Card.Body className='border text-center'>
                    <h1>{getFormattedMessage("no-data-available.title")}</h1>
                </Card.Body>
            </Card>}
    </>
}

const mapStateToProps = (state) => {
    const { weekSalePurchase, yearTopProduct } = state;
    return { weekSalePurchase, yearTopProduct }
};

export default connect(mapStateToProps, {})(OsAnalyticsTab);

