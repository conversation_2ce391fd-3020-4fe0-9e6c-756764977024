import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { getFormattedMessage, placeholderText } from "./../../../../shared/sharedMethod";
import { Card, ProgressBar, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const BrowserAnalyticsTab = (props) => {
    const { weekSalePurchase, frontSetting, linkData, changeTab } = props
    const [browserDetails, setBrowserDetails] = useState([])
    const variants = ["success", "primary", "dark", "secondary", "info", "warning", "danger"]

    useEffect(() => {
        if (linkData) {
            let browserData = [];
            [linkData.browser]?.map((d) => {
                for (let key in d) {
                    browserData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: key,
                        color: variants[Math.floor(Math.random() * variants.length)]
                    })
                }
            })
            setBrowserDetails(browserData)
        }
    }, [linkData])

    return <>
        {browserDetails.length >= 1 &&
        //<Card>
            <Card.Body>
                <Card>
                    <Card.Body className='border rounded'>
                        <Card.Title className='mb-5 '>{getFormattedMessage("globally.input.browser.label")}</Card.Title>
                        {
                            browserDetails.map((d, i) => {
                                return <React.Fragment key={i + 1}>
                                    <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                        <span>
                                            {d.name.charAt(0).toUpperCase() + d.name.slice(1)}
                                        </span>
                                        <span>
                                            {d.per}% {d.count}
                                        </span>
                                    </Card.Subtitle>
                                    <>
                                        <ProgressBar variant={d.color} now={d.per} />
                                    </>
                                </React.Fragment>
                            })
                        }
                        {/* <Button variant='Link'
                            className="text-muted p-0 my-2 text-decoration-underline"
                            onClick={() => changeTab("browser")}
                        >View more</Button> */}
                    </Card.Body>
                </Card>
            </Card.Body>
       // </Card >
        }
        {browserDetails.length === 0 &&
            <Card>
                <Card.Body className='border text-center'>
                    <h1>{getFormattedMessage("no-data-available.title")}</h1>
                </Card.Body>
            </Card>}
    </>
}

const mapStateToProps = (state) => {
    const { weekSalePurchase, yearTopProduct } = state;
    return { weekSalePurchase, yearTopProduct }
};

export default connect(mapStateToProps, {})(BrowserAnalyticsTab);

