import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { getFormattedMessage, placeholderText } from "./../../../../shared/sharedMethod";
import { <PERSON><PERSON>, Card, ProgressBar } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const Overview = (props) => {
    const { weekSalePurchase, frontSetting, linkData, changeTab } = props
    const [browserDetails, setBrowserDetails] = useState([])
    const [countryDetails, setCountryDetails] = useState([])
    const [deviceDetails, setDeviceDetails] = useState([])
    const [languageDetails, setLanguageDetails] = useState([])
    const [operatingSystemDetails, setOperatingSystemDetails] = useState([])
    const [translatedText, setTranslatedText] = useState('');
    const variants = ["success", "primary", "dark", "secondary", "info", "warning", "danger"]

    useEffect(() => {
        if (linkData) {
            let browserData = [];
            [linkData.browser]?.map((d) => {
                for (let key in d) {
                    browserData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: key,
                        color: variants[Math.round(Math.random() * variants.length)]
                    })
                }
            })
            setBrowserDetails(browserData)

            let countryData = [];
            [linkData.country]?.map((d) => {
                for (let key in d) {
                    countryData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: key,
                        color: variants[Math.round(Math.random() * variants.length)]
                    })
                }
            })
            setCountryDetails(countryData)

            let deviceData = [];
            [linkData.device]?.map((d) => {
                for (let key in d) {
                    deviceData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: key,
                        color: variants[Math.round(Math.random() * variants.length)]
                    })
                }
            })
            setDeviceDetails(deviceData)

            let languageData = [];
            let languageNames = new Intl.DisplayNames(['en'], { type: 'language' });
            [linkData.language]?.map((d) => {
                for (let key in d) {
                    languageData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: languageNames?.of(key.length > 1 ? key : "en"),
                        color: variants[Math.round(Math.random() * variants.length)]
                    })
                }
            })
            setLanguageDetails(languageData)

            let osData = [];
            [linkData.operating_system]?.map((d) => {
                for (let key in d) {
                    osData.push({
                        per: Math.round(d[key].per),
                        count: d[key].count,
                        name: key,
                        color: variants[Math.round(Math.random() * variants.length)]
                    })
                }
            })
            setOperatingSystemDetails(osData)
        }
    }, [linkData])

    return <>
        {
            !linkData
                ?
                <Card>
                    <Card.Body className='border rounded'>
                        <h1>{getFormattedMessage("no-data-available.title")}</h1>
                    </Card.Body>
                </Card>
                :
                linkData?.noRecord
                    ?
                    <Card>
                        <Card.Body className='border rounded text-center'>
                            <h1>{getFormattedMessage("no-data-available.title")}</h1>
                        </Card.Body>
                    </Card>
                    :
                    // <Card>
                    <Card.Body>
                        <div className='row'>
                            <div className='col-12 my-3'>
                                <Card>
                                    <Card.Body className='border rounded'>
                                        <Card.Title className='mb-5 '>{getFormattedMessage("globally.input.country.label")}</Card.Title>
                                        {
                                            countryDetails && countryDetails.map((d, i) => {
                                                return <React.Fragment key={i + 1}>
                                                    <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                                        <span>
                                                            {d.name.charAt(0).toUpperCase() + d.name.slice(1) || "India"}
                                                        </span>
                                                        <span>
                                                            {d.per}% {d.count}
                                                        </span>
                                                    </Card.Subtitle>
                                                    <>
                                                        <ProgressBar variant={d.color} now={d.per} />
                                                    </>
                                                </React.Fragment>
                                            })
                                        }
                                        <Button variant='Link'
                                            className="text-muted p-0 my-2 text-decoration-underline"
                                            onClick={() => changeTab("country")}
                                        >{getFormattedMessage("view-more.title")}</Button>
                                    </Card.Body>
                                </Card>
                            </div>
                            <div className='col-12 my-3'>
                                <Card>
                                    <Card.Body className='border rounded'>
                                        <Card.Title className='mb-5 '>{getFormattedMessage("globally.input.device.label")}</Card.Title>
                                        {
                                            deviceDetails && deviceDetails.map((d, i) => {
                                                return <React.Fragment key={i + 1}>
                                                    <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                                        <span>
                                                            {d?.name?.charAt(0).toUpperCase() + d?.name?.slice(1)}
                                                        </span>
                                                        <span>
                                                            {d.per}% {d.count}
                                                        </span>
                                                    </Card.Subtitle>
                                                    <>
                                                        <ProgressBar variant={d.color} now={d.per} />
                                                    </>
                                                </React.Fragment>
                                            })
                                        }
                                        <Button variant='Link'
                                            className="text-muted p-0 my-2 text-decoration-underline"
                                            onClick={() => changeTab("device")}
                                        >{getFormattedMessage("view-more.title")}</Button>
                                    </Card.Body>
                                </Card>
                            </div>
                            <div className='col-12 my-3'>
                                <Card>
                                    <Card.Body className='border rounded'>
                                        <Card.Title className='mb-5 '>{getFormattedMessage("globally.input.os.label")}</Card.Title>
                                        {
                                            operatingSystemDetails && operatingSystemDetails.map((d, i) => {
                                                return <React.Fragment key={i + 1}>
                                                    <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                                        <span>
                                                            {d.name === "OS X" ? "Mac OS" : d.name || "Windows"}
                                                        </span>
                                                        <span>
                                                            {d.per}% {d.count}
                                                        </span>
                                                    </Card.Subtitle>
                                                    <>
                                                        <ProgressBar variant={d.color} now={d.per} />
                                                    </>
                                                </React.Fragment>
                                            })
                                        }
                                        <Button variant='Link'
                                            className="text-muted p-0 my-2 text-decoration-underline"
                                            onClick={() => changeTab("os")}
                                        >{getFormattedMessage("view-more.title")}</Button>
                                    </Card.Body>
                                </Card>
                            </div>
                            <div className='col-12 my-3'>
                                <Card>
                                    <Card.Body className='border rounded'>
                                        <Card.Title className='mb-5 '>{getFormattedMessage("globally.input.browser.label")}</Card.Title>
                                        {
                                            browserDetails && browserDetails.map((d, i) => {
                                                return <React.Fragment key={i + 1}>
                                                    <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                                        <span>
                                                            {d.name}
                                                        </span>
                                                        <span>
                                                            {d.per}% {d.count}
                                                        </span>
                                                    </Card.Subtitle>
                                                    <>
                                                        <ProgressBar variant={d.color} now={d.per} />
                                                    </>
                                                </React.Fragment>
                                            })
                                        }
                                        <Button variant='Link'
                                            className="text-muted p-0 my-2 text-decoration-underline"
                                            onClick={() => changeTab("browser")}
                                        >{getFormattedMessage("view-more.title")}</Button>
                                    </Card.Body>
                                </Card>
                            </div>
                            <div className='col-12 my-3'>
                                <Card>
                                    <Card.Body className='border rounded'>
                                        <Card.Title className='mb-5 '>{getFormattedMessage("settings.select.language.label")}s</Card.Title>
                                        {
                                            languageDetails && languageDetails.map((d, i) => {
                                                return <React.Fragment key={i + 1}>
                                                    <Card.Subtitle className="mt-2 mb-1 text-muted d-flex justify-content-between">
                                                        <span>
                                                            {d.name || "India"}
                                                        </span>
                                                        <span>
                                                            {d.per}% {d.count}
                                                        </span>
                                                    </Card.Subtitle>
                                                    <>
                                                        <ProgressBar variant={d.color} now={d.per} />
                                                    </>
                                                </React.Fragment>
                                            })
                                        }
                                        <Button variant='Link'
                                            className="text-muted p-0 my-2 text-decoration-underline"
                                            onClick={() => changeTab("language")}
                                        >{getFormattedMessage("view-more.title")}</Button>
                                    </Card.Body>
                                </Card>
                            </div>
                        </div>
                    </Card.Body>
            // </Card>
        }
    </>
}

const mapStateToProps = (state) => {
    const { weekSalePurchase, yearTopProduct } = state;
    return { weekSalePurchase, yearTopProduct }
};

export default connect(mapStateToProps, {})(Overview);

