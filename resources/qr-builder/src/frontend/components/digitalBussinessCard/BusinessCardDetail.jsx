import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Card, Table } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from '../../../shared/tab-title/TabTitle';
import { getFormattedDate, placeholderText } from '../../../shared/sharedMethod';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { useParams } from 'react-router-dom';
import { fetchPlan } from "../../../store/action/plansAction"
import TopProgressBar from "../../../shared/components/loaders/TopProgressBar";
import HeaderTitle from "../../../components/header/HeaderTitle";
import RoundLoader from '../../../shared/components/loaders/RoundLoader';
import { useDispatch } from 'react-redux';
import { fetchBusinessAdminCardLink, fetchBusinessCard, fetchBusinessCardLink } from '../../../store/action/digitalBusinessCardsAction';
import { useSelector } from 'react-redux';
import moment from 'moment';
import DynamicLayout from '../../../shared/masterLayouts/DynamicMasterLayouts';

const BusinessCardDetail = (props) => {
    const { isLoading, plans } = props;
    const { id } = useParams();
    const dispatch = useDispatch()
    const { socialLink, businessCard } = useSelector(state => state)
    const isAdmin = JSON.parse(localStorage.getItem("isAdmin"))

    const cardDetails = isAdmin === false ? businessCard?.length === 1 ? businessCard[0] : [] : socialLink

    useEffect(() => {
        if (isAdmin) {
            dispatch(fetchBusinessAdminCardLink(id))
        } else {
            dispatch(fetchBusinessCard(id))
            dispatch(fetchBusinessCardLink(id))
        }
    }, []);

    return (
        <DynamicLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('digital.business.card.detail.title')} />
            {<>
                <HeaderTitle title={getFormattedMessage('digital.business.card.detail.title')} to={isAdmin ? '/app/admin/digital-business-cards/' : '/app/digital-business-cards/'} />
                <div className='pt-5'>
                    <Card>
                        {/* <Card.Header as='h5'>{getFormattedMessage('plan.details.title')}</Card.Header> */}
                        <Card.Body className=''>
                            {isLoading ? <RoundLoader /> : <Table responsive>
                                <tbody>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('users.table.user-name.column.title')}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.user_name}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('URL.alias.title')}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.url_alias}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('qrcode.website-url.title')}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.website}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('react-data-table.name.column.title')}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.name}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.phone-number.lable')}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.phone}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.input.email.label")}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.email}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.job-title.label')}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.job_title}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('globally.input.company.label')}</td>
                                        <td className='py-4'>{cardDetails?.attributes?.company}</td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage('input.template.status.label')}</td>
                                        <td className='py-4'>
                                            {cardDetails?.attributes?.status === true
                                                ?
                                                <span className='badge bg-light-success'>
                                                    <div>{getFormattedMessage("globally.active.label")}</div>
                                                </span>
                                                :
                                                <span className='badge bg-light-danger'>
                                                    <div>{getFormattedMessage("globally.deactive.label")}</div>
                                                </span>
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.created-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(cardDetails?.attributes?.created_at, "d-m-y")} {moment(cardDetails?.attributes?.created_at).format('LT')}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className='py-4'>{getFormattedMessage("globally.react-table.column.updated-date.label")}</td>
                                        <td className='py-4'>
                                            {getFormattedDate(cardDetails?.attributes?.updated_at, "d-m-y")} {moment(cardDetails?.attributes?.updated_at).format('LT')}
                                        </td>
                                    </tr>
                                </tbody>
                            </Table>}
                        </Card.Body>
                    </Card>
                </div>
            </>
            }
        </DynamicLayout>
    )
};

const mapStateToProps = state => {
    const { isLoading, plans } = state;
    return { isLoading, plans }
};

export default connect(mapStateToProps, { fetchPlan })(BusinessCardDetail);
