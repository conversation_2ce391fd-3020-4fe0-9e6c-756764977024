import React from 'react';
import { connect, useSelector } from 'react-redux';
import { addLink } from '../../../store/action/linkAction';
import MasterLayout from '../MasterLayout';
import HeaderTitle from '../../../components/header/HeaderTitle';
import { Filters } from '../../../constants';
import { useNavigate } from 'react-router-dom';
import { getFormattedMessage, placeholderText } from '../../../shared/sharedMethod';
import { Helmet } from 'react-helmet';
import AdminBusinessCardForm from './AdminBusinessCardForm';

const AdminCreateBusinessCard = (props) => {
    const { addLink } = props;
    const navigate = useNavigate();
    const { frontSettings } = useSelector(state => state)
    const addLinkData = (formValue) => {
        addLink(formValue, navigate, Filters.OBJ);
    };

    return (
        <MasterLayout>
            <Helmet title={placeholderText('digital.business.card.add.title') + ' | ' + frontSettings?.title} />
            <HeaderTitle title={getFormattedMessage('digital.business.card.add.title')} to='/app/admin/digital-business-cards' />
            <AdminBusinessCardForm addLinkData={addLinkData} />
        </MasterLayout>
    );
}

export default connect(null, { addLink })(AdminCreateBusinessCard);
