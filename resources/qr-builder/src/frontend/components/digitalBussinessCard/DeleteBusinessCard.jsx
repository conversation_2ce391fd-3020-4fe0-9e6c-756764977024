import React from 'react';
import { connect, useDispatch } from 'react-redux';
import DeleteModel from '../../../shared/action-buttons/DeleteModel';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { deletePlan } from '../../../store/action/plansAction';
import { deleteFrontEnquiries } from '../../../store/action/adminActions/frontcmsActions';
import { addToast } from '../../../store/action/toastAction';
import { toastType } from '../../../constants';
import { deleteAdminBusinessCard } from '../../../store/action/digitalBusinessCardsAction';

const DeleteBusinessCard = (props) => {
    const { deleteAdminBusinessCard, onDelete, deleteModel, onClickDeleteModel } = props;
    const dispatch = useDispatch();

    const deleteUserClick = () => {
        // dispatch(addToast(
        //     { text: 'This action is not allowed in demo.', type: toastType.ERROR }));
        deleteAdminBusinessCard(onDelete.id);
        onClickDeleteModel(false);
    };


    return (
        <div>
            {deleteModel && <DeleteModel onClickDeleteModel={onClickDeleteModel} deleteModel={deleteModel}
                deleteUserClick={deleteUserClick} name={getFormattedMessage("single.digital.business.card.title")} />}
        </div>
    )
};

export default connect(null, { deleteAdminBusinessCard })(DeleteBusinessCard);
