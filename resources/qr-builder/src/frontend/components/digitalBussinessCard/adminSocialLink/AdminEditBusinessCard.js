import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import MasterLayout from '../../MasterLayout';
import HeaderTitle from '../../../../components/header/HeaderTitle';
import { getFormattedMessage, placeholderText } from '../../../../shared/sharedMethod';
import TopProgressBar from '../../../../shared/components/loaders/TopProgressBar';
import TabTitle from "../../../../shared/tab-title/TabTitle";
import { fetchBusinessAdminCardLink } from '../../../../store/action/digitalBusinessCardsAction';
import web from "../../../../../../../public/default-images/socialimages/web.svg"
import twitter from "../../../../../../../public/default-images/socialimages/twitter.svg"
import facebook from "../../../../../../../public/default-images/socialimages/facebook.svg"
import insta from "../../../../../../../public/default-images/socialimages/insta.svg"
import reditt from "../../../../../../../public/default-images/socialimages/reditt.svg"
import tunbir from "../../../../../../../public/default-images/socialimages/tumbir.svg"
import youtube from "../../../../../../../public/default-images/socialimages/youtube.svg"
import linkedin from "../../../../../../../public/default-images/socialimages/linkedin.svg"
import whatsapp from "../../../../../../../public/default-images/socialimages/whatsapp.svg"
import pinterest from "../../../../../../../public/default-images/socialimages/pinteresht.svg"
import tiktok from "../../../../../../../public/default-images/socialimages/tiktok.svg"
import user from '../../../../assets/images/avatar.png';
import AdminBusinessCradLinks from './AdminBusinessCradLinks';
import { fetchAllUsers } from '../../../../store/action/userAction';
import { environment } from '../../../../config/environment';

const AdminEditBusinessCard = (props) => {
    const { socialLink, fetchAllUsers, users, fetchBusinessAdminCardLink } = props;
    const { id } = useParams();
    const [isEdit, setIsEdit] = useState(false);
    const [updateDatas, setUpdateDatas] = useState([])
    const [linkValues, setLinkValues] = useState([
        { icone: environment.URL + '/default-images/socialimages/web.svg', url: '', pivImage: user, name: 'globally.input.website.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/twitter.svg', url: '', pivImage: user, name: 'globally.input.twitter.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/facebook.svg', url: '', pivImage: user, name: 'globally.input.facebook.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/insta.svg', url: '', pivImage: user, name: 'globally.input.instagram.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/reditt.svg', url: '', pivImage: user, name: 'globally.input.raddit.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/tumbir.svg', url: '', pivImage: user, name: 'globally.input.tumblr.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/youtube.svg', url: '', pivImage: user, name: 'globally.input.youtube.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/linkedin.svg', url: '', pivImage: user, name: 'globally.input.linkedin.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/whatsapp.svg', url: '', pivImage: user, name: 'globally.input.whatsapp.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/pinteresht.svg', url: '', pivImage: user, name: 'globally.input.pinterest.url.placeholder' },
        { icone: environment.URL + '/default-images/socialimages/tiktok.svg', url: '', pivImage: user, name: 'globally.input.tiktok.url.placeholder' }
    ])
    useEffect(() => {
        fetchBusinessAdminCardLink(id)
        fetchAllUsers();
        setIsEdit(true);
    }, []);

    const itemsValue = [];

    return (
        <MasterLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('edit.digital.business.card.url.title')} />
            <HeaderTitle title={getFormattedMessage('edit.digital.business.card.url.title')} to='/app/admin/digital-business-cards' />
            <AdminBusinessCradLinks singleBusinessCard={itemsValue} users={users} ids={id} isEdit={isEdit} updateDatas={updateDatas} setUpdateDatas={setUpdateDatas} socialLink={socialLink} linkValues={linkValues} setLinkValues={setLinkValues} />
        </MasterLayout>
    );
}

const mapStateToProps = (state) => {
    const { businessCard, socialLink, users } = state;
    return { businessCard, socialLink, users }
};

export default connect(mapStateToProps, { fetchAllUsers, fetchBusinessAdminCardLink })(AdminEditBusinessCard);
