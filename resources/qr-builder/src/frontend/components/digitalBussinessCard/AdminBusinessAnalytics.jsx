import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Card, Col, NavDropdown, Row, Tab, Tabs } from 'react-bootstrap';
import MasterLayout from '../MasterLayout';
import TabTitle from './../../../shared/tab-title/TabTitle';
import { getFormattedDate, getFormattedMessage, placeholderText } from './../../../shared/sharedMethod';
import TopProgressBar from "./../../../shared/components/loaders/TopProgressBar";
import LineChart from './LineChart';
import Overview from './AllAnalyticTabs/Overview';
import { fetchAllBusinessCard, fetchBusinessChartData } from './../../../store/action/linkAnalyticsAction';
import { useParams } from 'react-router';
import CountryAnalyticsTab from './AllAnalyticTabs/CountryAnalyticsTab';
import DeviceAnalyticsTab from './AllAnalyticTabs/DeviceAnalyticsTab';
import OsAnalyticsTab from './AllAnalyticTabs/OsAnalyticsTab';
import LanguageAnalyticsTab from './AllAnalyticTabs/LanguageAnalyticsTab';
import BrowserAnalyticsTab from './AllAnalyticTabs/BrowserAnalyticsTab';
import DateRangePicker from "./../../../shared/datepicker/DateRangePicker"
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';
import { Button } from 'react-bootstrap-v5';
import { faLineChart, faBarChart } from '@fortawesome/free-solid-svg-icons';
import DynamicLayout from './../../../shared/masterLayouts/DynamicMasterLayouts';
import OverviewFooter from './../../../shared/overviewFooter/OverviewFooter';
import HeaderTitle from "./../../../components/header/HeaderTitle";
import PieChart from './PieChart';
import moment from "moment";

const AdminBusinessAnalytics = (props) => {
    const { fetchAllBusinessCard, linkAnalyticsData, fetchBusinessChartData } = props;
    // const [warehouseValue, setWarehouseValue] = useState({label: getFormattedMessage("unit.filter.all.label"), value: null});
    const [key, setKey] = useState('overview');
    const [selectDate, setSelectDate] = useState();
    const [isBarChart, isSetBarChart] = useState(false);
    const [isLineChart, isSetLineChart] = useState(false);
    const { id } = useParams()

    useEffect(() => {
        fetchAllBusinessCard(id);
        fetchBusinessChartData({
            business_card_id: id,
            start_date: moment(new Date).format("YYYY-MM-DD"),
            end_date: moment(new Date).format("YYYY-MM-DD"),
        })
    }, []);

    const changeTab = (name) => {
        setKey(name)
    }

    const onDateSelector = (date) => {
        if (date.type === "clean") {
            setSelectDate(date.params);
            fetchAllBusinessCard(id);
        } else {
            setSelectDate(date.params);
            fetchBusinessChartData({
                business_card_id: id,
                ...date.params
            })
        }
    };

    const isAdmin = localStorage.getItem("isAdmin")

    return (
        <DynamicLayout>
            <TopProgressBar />
            <TabTitle title={placeholderText('globally.input.business-card-analytics.lable')} />
            <div className='row justify-content-evenly align-items-start'>
                <div className='col-md-5 col-12 charts_style'>
                    <Card>
                        <div className='pt-5 px-5'>
                            <HeaderTitle title={getFormattedMessage("globally.input.business-card-analytics.lable")} to={(isAdmin === "false") ? "/app/digital-business-cards" : "/app/admin/digital-business-cards"} />
                        </div>

                        <Card.Body>
                            <PieChart data={linkAnalyticsData} keyName={key} />
                        </Card.Body>
                    </Card>
                    <Card className='mt-3'>
                        <div className='card-body'>
                            <div className=''>
                                <Card.Header className='d-flex p-0 align-items-center justify-content-end'>
                                    <div className='d-flex align-items-center'>
                                        {/* <div className='chart-dropdown'>
                                    {isLineChart === true ? <Button variant="primary" className='me-3' onClick={() => isSetLineChart(false)} >
                                        <FontAwesomeIcon icon={faBarChart} />
                                    </Button> : <Button variant="primary" className='me-3' onClick={() => isSetLineChart(true)}>
                                        <FontAwesomeIcon icon={faLineChart} />
                                    </Button>}
                                </div> */}
                                        <DateRangePicker chart_date_picker={true} onDateSelector={onDateSelector} selectDate={selectDate} />
                                    </div>
                                </Card.Header>
                                <Card.Body>
                                    <LineChart linkData={linkAnalyticsData} isLineChart={isLineChart} />
                                </Card.Body>
                            </div>
                        </div>

                    </Card>
                </div>
                <div className='col-md-7 mt-md-0 mt-3 col-12'>
                    <Card className='p-5'>
                        <Tabs id='uncontrolled-tab-example' activeKey={key} onSelect={(k) => setKey(k)}
                            className='mt-7 mb-5'>
                            <Tab eventKey='overview' title={getFormattedMessage('user-details.table.title')}
                                tabClassName={`position-relative mb-3 me-7`}>
                                <div className='w-100 mx-auto'>
                                    {key === 'overview' && <Overview changeTab={changeTab} linkData={linkAnalyticsData} />}
                                </div>
                            </Tab>
                            <Tab eventKey='country' title={getFormattedMessage('globally.input.country.label')}
                                tabClassName='position-relative mb-3 me-7'>
                                <div className='w-100 mx-auto'>
                                    {key === 'country' && <CountryAnalyticsTab changeTab={changeTab} linkData={linkAnalyticsData} />}
                                </div>
                            </Tab>
                            <Tab eventKey='device' title={getFormattedMessage('globally.input.device.label')}
                                tabClassName={`position-relative mb-3 me-7 `}>
                                <div className='w-100 mx-auto'>
                                    {key === 'device' && <DeviceAnalyticsTab changeTab={changeTab} linkData={linkAnalyticsData} />}
                                </div>
                            </Tab>
                            <Tab eventKey='os' title={getFormattedMessage('globally.input.os.label')}
                                tabClassName={`position-relative mb-3 me-7 `}>
                                <div className='w-100 mx-auto'>
                                    {key === 'os' && <OsAnalyticsTab changeTab={changeTab} linkData={linkAnalyticsData} />}
                                </div>
                            </Tab>
                            <Tab eventKey='language' title={getFormattedMessage('settings.select.language.label')}
                                tabClassName={`position-relative mb-3 me-7 ${key === "language" ? "active" : ""}`}>
                                <div className='w-100 mx-auto'>
                                    {key === 'language' && <LanguageAnalyticsTab changeTab={changeTab} linkData={linkAnalyticsData} />}
                                </div>
                            </Tab>
                            <Tab eventKey='browser' title={getFormattedMessage('globally.input.browser.label')}
                                tabClassName={`position-relative mb-3 me-7 ${key === "browser" ? "active" : ""}`}>
                                <div className='w-100 mx-auto'>
                                    {key === 'browser' && <BrowserAnalyticsTab changeTab={changeTab} linkData={linkAnalyticsData} />}
                                </div>
                            </Tab>
                        </Tabs>
                    </Card>
                </div>
            </div>
            {/*<Row className='mt-6'>*/}
            {/*    <Col md={6}>*/}
            {/*        <WarehouseQtyChart/>*/}
            {/*    </Col>*/}
            {/*    <Col md={6}>*/}
            {/*        <WarehouseQtyChart/>*/}
            {/*    </Col>*/}
            {/*</Row>*/}
        </DynamicLayout >
    )
};

const mapStateToProps = (state) => {
    const { linkAnalyticsData } = state;
    return { linkAnalyticsData }
    return state
};

export default connect(mapStateToProps, { fetchAllBusinessCard, fetchBusinessChartData })(AdminBusinessAnalytics);
