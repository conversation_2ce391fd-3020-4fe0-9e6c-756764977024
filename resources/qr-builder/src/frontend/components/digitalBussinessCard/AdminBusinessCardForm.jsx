import React, { useEffect, useState } from 'react';
import Form from 'react-bootstrap/Form';
import { connect, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Dropdown, DropdownButton, InputGroup } from 'react-bootstrap-v5';
import * as EmailValidator from 'email-validator';
import { getFormattedMessage, placeholderText, numValidate, isValidHttpUrl, getAvatarName } from '../../../shared/sharedMethod';
import ModelFooter from '../../../shared/components/modelFooter';
import { environment } from "../../../config/environment"
import parsePhoneNumberFromString from 'libphonenumber-js';
import countryData from 'country-telephone-data';
import UserDropdown from '../project/UserDropdown';
import ImagePicker from '../../../shared/image-picker/ImagePicker';
import user from '../../../assets/images/avatar.png';
import { adminAddBusinessCard } from '../../../store/action/digitalBusinessCardsAction';
import CountryList from 'country-list-with-dial-code-and-flag'

const AdminBusinessCardForm = (props) => {
    const { addLinkData, id, singleLink, isEdit, isCreate, roles } = props;
    const dispatch = useDispatch()
    const navigate = useNavigate();
    const flagData = CountryList.getAll()
    const coverAvtarName = getAvatarName(singleLink && singleLink[0]?.cover_image_url === '' && singleLink[0].name);
    const coverNewImg = singleLink && singleLink[0]?.cover_image_url === null && coverAvtarName;
    const profileAvtarName = getAvatarName(singleLink && singleLink[0]?.profile_image_url === '' && singleLink[0].name);
    const profileNewImg = singleLink && singleLink[0]?.profile_image_url === null && profileAvtarName;
    const [profileImagePreviewUrl, setProfileImagePreviewUrl] = useState(profileNewImg && profileNewImg);
    const [selectProfileImg, setProfileSelectImg] = useState(null);
    const [imagePreviewUrl, setImagePreviewUrl] = useState(coverNewImg && coverNewImg);
    const [selectImg, setSelectImg] = useState(null);

    const [linkValue, setLinkValue] = useState({
        name: '',
        url_alias: '',
        website: '',
        job: '',
        company: '',
        phone: '',
        email: '',
        template_id: 1,
        status: false,
        profile_image: '',
        cover_image: '',
    });
    const [countryCode, setCountryCode] = useState("+91")
    const [phoneNumber, setPhoneNumber] = useState("")

    const countryFlag = flagData?.filter(d => d?.dial_code === countryCode)

    const [errors, setErrors] = useState({
        name: '',
        url_alias: '',
        job: '',
        company: '',
        phone: '',
        email: '',
        website: '',
        template_id: '',
        status: ''
    });

    useEffect(() => {
        if (singleLink?.length) {
            setLinkValue({
                name: singleLink ? singleLink[0].name : '',
                url_alias: singleLink ? singleLink[0].url_alias : '',
                website: singleLink ? singleLink[0].website : '',
                job: singleLink ? singleLink[0].job : '',
                company: singleLink ? singleLink[0].company : '',
                phone: singleLink ? singleLink[0].phone : '',
                email: singleLink ? singleLink[0].email : '',
                template_id: singleLink ? singleLink[0].template_id : 1,
                status: singleLink ? singleLink[0].status : false
            })

            setCountryCode(singleLink[0]?.phone ? singleLink[0]?.phone?.split(" ")[0] : "+91")
            setPhoneNumber(singleLink[0]?.phone ? singleLink[0]?.phone?.split(" ")[1] : "")
        }
    }, [singleLink])

    const disabled = singleLink && singleLink[0].name === linkValue.name

    const handleValidation = () => {
        let errorss = {};
        let isValid = false;
        const countryName = countryData.allCountries.find(c => `+${c?.dialCode}` === countryCode);
        const parsedNumber = parsePhoneNumberFromString(linkValue?.phone?.split(' ')?.join(''), countryName?.iso2)?.isValid()
        if (linkValue?.tenant_id === undefined) {
            errorss['tenant_id'] = getFormattedMessage("user.input.validate.label");
        } else if (!linkValue['url_alias'] || linkValue['url_alias']?.trim()?.length === 0) {
            errorss['url_alias'] = getFormattedMessage('alias.input.validate.label');
        } else if (!linkValue['website'] || linkValue['website']?.trim()?.length === 0) {
            errorss['website'] = getFormattedMessage("website.input.validate.label");
        } else if (isValidHttpUrl(linkValue.website) === false) {
            errorss['website'] = getFormattedMessage("website.input.valid.validate.label");
        } else if (!linkValue['name'] || linkValue['name']?.trim()?.length === 0) {
            errorss['name'] = getFormattedMessage('user.input.first-name.validate.label');
        } else if (parsedNumber !== true) {
            errorss.phone = getFormattedMessage("globally.input.phonenumber.validate.label")
        } else if (linkValue.email?.trim()?.length === 0) {
            errorss.email = getFormattedMessage("globally.input.email.validate.label")
        } else if (!EmailValidator.validate(linkValue.email?.trim())) {
            errorss.email = getFormattedMessage("user.input.email.valid.validate.label")
        } else if (!linkValue['job'] || linkValue['job']?.trim()?.length === 0) {
            errorss['job'] = getFormattedMessage('job-title.input.validate.label');
        } else if (!linkValue['company'] || linkValue['company']?.trim()?.length === 0) {
            errorss['company'] = getFormattedMessage('company.input.validate.label');
        } else if (!linkValue['template_id']) {
            errorss['template_id'] = getFormattedMessage('user.input.first-name.validate.label');
        } else {
            isValid = true;
        }
        setErrors(errorss);
        return isValid;
    };
    const onChangeCountryCode = (code, name) => {
        setCountryCode(code)
        setErrors('')
        setLinkValue(inputs => ({
            ...inputs,
            [name]: `${code} ${phoneNumber.replace(/ /g, "")}`
        }))
    }
    const onChangeInput = (e) => {
        e.preventDefault();
        setLinkValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    };


    const onChangeAliasInput = (e) => {
        e.preventDefault();
        setLinkValue(inputs => ({ ...inputs, [e.target.name]: e.target.value }))
        setErrors('');
    }

    const onPasswordProtection = (e) => {
        const check = e.target.checked
        if (check) {
            setLinkValue(inputs => ({ ...inputs, status: true }))
        } else {
            setLinkValue(inputs => ({ ...inputs, status: false }))
        }
        setErrors('')
    };

    const handleImageChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === 'image/jpeg' || file.type === 'image/png') {
                setSelectImg(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setImagePreviewUrl(fileReader.result);
                };
                fileReader.readAsDataURL(file);
                setErrors('');
            }
        }
    };

    const handleProfileImageChanges = (e) => {
        e.preventDefault();
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.type === 'image/jpeg' || file.type === 'image/png') {
                setProfileSelectImg(file);
                const fileReader = new FileReader();
                fileReader.onloadend = () => {
                    setProfileImagePreviewUrl(fileReader.result);
                };
                fileReader.readAsDataURL(file);
                setErrors('');
            }
        }
    };

    const prepareFormData = (data) => {
        const formData = new FormData();
        formData.append('name', data.name);
        formData.append('url_alias', data.url_alias?.replace(/\s/g, ''));
        formData.append('website', data.website?.replace(/\s+/g, '-'));
        formData.append('job_title', data.job);
        formData.append('company', data.company);
        formData.append('phone', "+" + data.phone.split("+").pop());
        formData.append('email', data.email);
        formData.append('status', data.status === false ? 0 : 1);
        formData.append('template_id', data.template_id);
        if (selectProfileImg) {
            formData.append('profile_image', data.profile_image);
        }
        if (selectImg) {
            formData.append('cover_image', data.cover_image);
        }
        formData.append('tenant_id', data.tenant_id.value);
        return formData;
    };

    const onSubmit = (event) => {
        event.preventDefault();
        linkValue.profile_image = selectProfileImg;
        linkValue.cover_image = selectImg;
        setLinkValue(linkValue);
        const valid = handleValidation();
        if (valid) {
            setLinkValue(linkValue);
            dispatch(adminAddBusinessCard(prepareFormData(linkValue), navigate))
        }
    };

    const onUserChange = (obj) => {
        setLinkValue(inputs => ({ ...inputs, tenant_id: obj }))
        setErrors('')
    };

    return (
        <div className='card'>
            <div className='card-body'>
                <Form>
                    <div className='row'>
                        <div className='col-md-12 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("users.table.user.column.title")}:<span className="required" />
                            </label>
                            <UserDropdown onUserChange={onUserChange} disabledKey={"Type"} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['tenant_id'] ? errors['tenant_id'] : null}
                            </span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("URL.alias.title")}:<span className="required" />
                            </label>
                            <InputGroup>
                                <InputGroup.Text>{environment.URL}</InputGroup.Text>
                                <input type='text' name='url_alias' value={linkValue.url_alias.replace(/\s/g, '')}
                                    placeholder={placeholderText("alias-url.input.placeholder.label")}
                                    className='form-control' autoFocus={true}
                                    onChange={(e) => onChangeAliasInput(e)} /><br />
                            </InputGroup>
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['url_alias'] ? errors['url_alias'] : null}</span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("qrcode.website-url.title")}:<span className="required" />
                            </label>
                            <input type='url' name='website' value={linkValue.website?.replace(/\s+/g, '-')}
                                placeholder={placeholderText("qrcode.website-url.title")}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)} />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['website'] ? errors['website'] : null}</span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("globally.input.name.label")}:<span className="required" />
                            </label>
                            <input type='text' name='name' value={linkValue.name || ""}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText("user.input.name.placeholder.label")}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['link_name'] ? errors['link_name'] : null}
                            </span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label className="form-label">
                                {getFormattedMessage("globally.input.phone.lable")}:<span className="required" />
                            </label>
                            <InputGroup className="country_code_dropdown">
                                <DropdownButton
                                    variant="primary p-3"
                                    title={<span>
                                        <img className="thumbnail-image"
                                            src={`https://flagcdn.com/16x12/${countryFlag[0]?.data?.code?.toLowerCase()}.png`}
                                            srcSet={`https://flagcdn.com/32x24/${countryFlag[0]?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${countryFlag[0]?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12}
                                            alt={countryFlag[0]?.data?.code}
                                        /> {countryCode}
                                    </span>}
                                    id="input-group-dropdown-1"
                                >{
                                        flagData.map((d, i) => {
                                            return <Dropdown.Item key={i + 1} onClick={() => onChangeCountryCode(d.data.dial_code, "phone")} value={d.data.dial_code}>
                                                <img src={`https://flagcdn.com/16x12/${d?.data?.code?.toLowerCase()}.png`} srcSet={`https://flagcdn.com/32x24/${d?.data?.code?.toLowerCase()}.png 2x,
                                                                https://flagcdn.com/48x36/${d?.data?.code?.toLowerCase()}.png 3x`} width={16} height={12} alt="South Africa"
                                                /> {d.data.dial_code}</Dropdown.Item>
                                        })
                                    }
                                </DropdownButton>
                                <Form.Control
                                    type='text' name='phone' value={phoneNumber}
                                    pattern="^(?:\(\d{3}\)|\d{3})[- ]?\d{3}[- ]?\d{4}$"
                                    className='form-control'
                                    placeholder={placeholderText("globally.input.phone-number.placeholder.label")}
                                    onKeyPress={(event) => numValidate(event)}
                                    onChange={(e) => {
                                        setPhoneNumber(e.target.value.replace(/ /g, ""))
                                        setLinkValue(inputs => ({
                                            ...inputs,
                                            [e.target.name]: `${countryCode} ${e.target.value.replace(/ /g, "")}`
                                        }))
                                    }}
                                />
                            </InputGroup>
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>{errors['phone'] ? errors['phone'] : null}</span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label htmlFor="exampleInputEmail1" className="form-label">
                                {getFormattedMessage("globally.input.email.label")}:<span className="required" />
                            </label>
                            <input
                                type='text'
                                name='email'
                                value={linkValue.email}
                                placeholder={placeholderText("globally.input.email.placeholder")}
                                className='form-control'
                                autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                            />
                            <span
                                className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['email'] ? errors['email'] : null}
                            </span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label className="form-label">
                                {getFormattedMessage("globally.input.job-title.label")}:<span className="required" />
                            </label>
                            <input type='text' name='job' value={linkValue.job || ""}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText("globally.input.job-title.label")}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['job'] ? errors['job'] : null}
                            </span>
                        </div>
                        <div className='col-md-6 mb-3'>
                            <label className="form-label">
                                {getFormattedMessage("globally.input.company.label")}:<span className="required" />
                            </label>
                            <input type='text' name='company' value={linkValue.company || ""}
                                className='form-control' autoFocus={true}
                                onChange={(e) => onChangeInput(e)}
                                placeholder={placeholderText("globally.input.company.label")}
                            />
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['company'] ? errors['company'] : null}
                            </span>
                        </div>
                        <div className="col-md-6 d-flex align-items-center mb-3">
                            <label className="form-check form-switch form-switch-sm cursor-pointer pt-md-5 pt-0">
                                <input autoComplete="off" name="password_protection" data-id="704" className="form-check-input admin-status cursor-pointer" checked={linkValue.status} type="checkbox" value={linkValue.status}
                                    onChange={(e) => onPasswordProtection(e)} />
                                {getFormattedMessage("input.template.status.label")}
                                <span className="switch-slider" data-checked="✓" data-unchecked="✕"></span>
                            </label>
                        </div>
                        <div className='col-md-3 mb-3'>
                            <div className='mb-4'>
                                <ImagePicker imageTitle={'header.profile-menu.profile.label'} imagePreviewUrl={profileImagePreviewUrl}
                                    user={user}
                                    avtarName={profileAvtarName} handleImageChange={handleProfileImageChanges} />
                                <span className='text-danger d-block fw-400 fs-small mt-2'>
                                    {errors.profile_image ? errors.profile_image : null}
                                </span>
                            </div>
                        </div>
                        <div className='col-md-3 mb-3'>
                            <div className='mb-4'>
                                <ImagePicker imageTitle={"globally.input.cover-image.lable"} imagePreviewUrl={imagePreviewUrl}
                                    user={user}
                                    avtarName={coverAvtarName} handleImageChange={handleImageChanges} />
                                <span className='text-danger d-block fw-400 fs-small mt-2'>
                                    {errors.cover_image ? errors.cover_image : null}
                                </span>
                            </div>
                        </div>
                        <div className='col-md-12 mb-3 template-hover-sec'>
                            <label className="form-label">
                                {getFormattedMessage("input.template.label")}:<span className="required" />
                            </label>
                            <div className='row'>
                                <div className='col-md-3 col-6 mb-3'>
                                    <div className={`image-wrap ${linkValue.template_id === 1 ? 'active' : ''}`} onClick={(e) => { setLinkValue(inputs => ({ ...inputs, template_id: 1 })) }}>
                                        <img src={environment.URL + '/default-images/templates/1.png'} />
                                    </div>
                                </div>
                                <div className='col-md-3 col-6 mb-3'>
                                    <div className={`image-wrap ${linkValue.template_id === 2 ? 'active' : ''}`} onClick={(e) => { setLinkValue(inputs => ({ ...inputs, template_id: 2 })) }}>
                                        <img src={environment.URL + '/default-images/templates/2.png'} />
                                    </div>
                                </div>
                                <div className='col-md-3 col-6 mb-3'>
                                    <div className={`image-wrap ${linkValue.template_id === 3 ? 'active' : ''}`} onClick={(e) => { setLinkValue(inputs => ({ ...inputs, template_id: 3 })) }}>
                                        <img src={environment.URL + '/default-images/templates/3.png'} />
                                    </div>
                                </div>
                                <div className='col-md-3 col-6 mb-3'>
                                    <div className={`image-wrap ${linkValue.template_id === 4 ? 'active' : ''}`} onClick={(e) => { setLinkValue(inputs => ({ ...inputs, template_id: 4 })) }}>
                                        <img src={environment.URL + '/default-images/templates/4.png'} />
                                    </div>
                                </div>
                                <div className='col-md-3 col-6 mb-3'>
                                    <div className={`image-wrap ${linkValue.template_id === 5 ? 'active' : ''}`} onClick={(e) => { setLinkValue(inputs => ({ ...inputs, template_id: 5 })) }}>
                                        <img src={environment.URL + '/default-images/templates/5.png'} />
                                    </div>
                                </div>
                            </div>
                            <span className='text-danger d-block fw-400 fs-small mt-2'>
                                {errors['template_id'] ? errors['template_id'] : null}
                            </span>
                        </div>
                        <ModelFooter onEditRecord={singleLink} onSubmit={onSubmit} editDisabled={disabled}
                            link='/app/digital-business-cards' addDisabled={!linkValue.name} />
                    </div>
                </Form>
            </div>
        </div>
    )
};

const mapStateToProps = (state) => {
    const { roles } = state;
    return { roles }
};

export default connect(mapStateToProps, {})(AdminBusinessCardForm);

