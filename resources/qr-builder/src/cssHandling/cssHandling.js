import { environment } from "../config/environment";
import { formatAmount } from "../shared/sharedMethod";

export const cssHandling = (updatedLanguage, meta, updateMeta) => {
    window.addEventListener("scroll", () => {
        var scroll = window.scrollY;
        var header = document.getElementById("header");
        if (header !== null) {
            if (scroll > 10) {
                header.classList.add("active-header");
            } else {
                header.classList.remove("active-header");
            }
        }
    });

    const counters = document.querySelectorAll(".widgetValue");
    const speed = 50;
    counters.forEach((counter) => {
        const animate = () => {
            if (
                counter.innerHTML === "NaN" ||
                counter.innerHTML === "undefined"
            ) {
                counter.innerHTML = "0";
            }
            const isAmount = counter.getAttribute("animation_on_amount");
            const value = +counter.getAttribute("animation_amount");
            const data = +counter.innerText;
            const time = value / speed;
            if (data < value) {
                counter.innerText = Math.ceil(data + time);
                setTimeout(animate, 70);
            } else {
                counter.innerText =
                    isAmount === "true" ? formatAmount(value) : value;
            }
        };

        animate();
    });

    // META TAG HANDLING ======
    if (meta && meta.length >= 1) {
        let metaTitle = document.createElement("meta");
        let metaDescription = document.createElement("meta");
        const oneTitle = document.getElementById("oneTitle");
        const oneDescription = document.getElementById("oneDescription");

        if (updateMeta) {
            document.querySelectorAll("#oneTitle").forEach((d) => d.remove());
            document
                .querySelectorAll("#oneDescription")
                .forEach((d) => d.remove());

            metaTitle.name = "title";
            metaTitle.content = meta[0]?.title;
            metaTitle.id = "oneTitle";
            document.getElementsByTagName("head")[0].appendChild(metaTitle);

            metaDescription.name = "description";
            metaDescription.content = meta[0]?.description;
            metaDescription.id = "oneDescription";
            document
                .getElementsByTagName("head")[0]
                .appendChild(metaDescription);
        } else {
            if (!oneTitle) {
                metaTitle.name = "title";
                metaTitle.content = meta[0]?.title;
                metaTitle.id = "oneTitle";
                document.getElementsByTagName("head")[0].appendChild(metaTitle);
            }
            if (!oneDescription) {
                metaDescription.name = "description";
                metaDescription.content = meta[0]?.description;
                metaDescription.id = "oneDescription";
                document
                    .getElementsByTagName("head")[0]
                    .appendChild(metaDescription);
            }
        }
    }

    // qr-builder home page with masterLayout styling=======
    let linkOne = document.createElement("link");
    let linkTow = document.createElement("link");
    let linkIndex = document.createElement("link");
    const one = document.getElementById("One");
    const two = document.getElementById("Tow");
    let indexCss = document.getElementById("index");
    const cssOne = environment.URL + "/frontend-css/css/custom.css";
    const cssTow = environment.URL + "/frontend-css/css/style.css";

    let BootstrapCss = document.getElementById("Bootstrap");
    let linkBootstrap = document.createElement("link");
    if (!BootstrapCss) {
        linkBootstrap.rel = "stylesheet";
        linkBootstrap.href =
            environment.URL +
            "/frontend-css/bootstrap/dist/css/bootstrap.min.css";
        linkBootstrap.id = "Bootstrap";
        if (one) {
            one.insertAdjacentElement("beforebegin", linkBootstrap);
        } else {
            document.getElementsByTagName("head")[0].appendChild(linkBootstrap);
        }
    }

    if (!one) {
        linkOne.rel = "stylesheet";
        linkOne.href = cssOne;
        linkOne.id = "One";
        document.getElementsByTagName("head")[0].appendChild(linkOne);
    }
    if (!two) {
        linkTow.rel = "stylesheet";
        linkTow.href = cssTow;
        linkTow.id = "Tow";
        document.getElementsByTagName("head")[0].appendChild(linkTow);
    }
    if (!indexCss) {
        linkIndex.rel = "stylesheet";
        linkIndex.href = environment.URL + "/frontend-css/css/frontend.css";
        linkIndex.id = "index";
        document.getElementsByTagName("head")[0].appendChild(linkIndex);
    }

    // frontPage Css Element Create =======
    let linkSlick = document.createElement("link");

    let linkFont = document.createElement("link");
    let linkSlickTheme = document.createElement("link");
    // let linkIndex = document.createElement('link');
    let linkIHeader = document.createElement("link");

    // frontPage Script Element Create =======
    let jqueryScript = document.createElement("script");
    let bootstrapScript = document.createElement("script");
    let slickScript = document.createElement("script");
    // let statsScript = document.createElement('script');

    // frontPage css Get Element =======
    // let indexCss = document.getElementById('index');
    let slickCss = document.getElementById("slick");
    let slickThemeCss = document.getElementById("slick-theme");

    let font = document.getElementById("font");
    let HeaderCss = document.getElementById("Header");

    // frontPage script Get Element =======
    let jqueryJsScript = document.getElementById("jquery-js");
    let bootstrapJsScript = document.getElementById("bootstrap-js");
    let slickJsScript = document.getElementById("slick-js");

    // frontPage css require ======
    if (!HeaderCss) {
        linkIHeader.rel = "stylesheet";
        linkIHeader.href =
            environment.URL + "/frontend-css/css/frontheader.css";
        linkIHeader.id = "Header";
        document.getElementsByTagName("head")[0].appendChild(linkIHeader);
    }
    if (!slickCss) {
        linkSlick.rel = "stylesheet";
        linkSlick.href =
            environment.URL + "/frontend-css/frontendCss/css/slick.css";
        linkSlick.id = "slick";
        document.getElementsByTagName("head")[0].appendChild(linkSlick);
    }
    if (!slickThemeCss) {
        linkSlickTheme.rel = "stylesheet";
        linkSlickTheme.href =
            environment.URL + "/frontend-css/frontendCss/css/slick-theme.css";
        linkSlickTheme.id = "slick-theme";
        document.getElementsByTagName("head")[0].appendChild(linkSlickTheme);
    }
    // if (!font) {
    //     linkFont.rel = 'stylesheet';
    //     linkFont.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css';
    //     linkFont.integrity = 'sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==';
    //     linkBootstrap.id = 'font';
    //     linkFont.crossorigin = "anonymous";
    //     linkFont.referrerpolicy = "no-referrer";
    //     document.getElementsByTagName('head')[0].appendChild(linkFont);
    // }

    // frontPage script =======
    // statsScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/stats.js/7/Stats.min.js'
    // statsScript.id = 'Stats-js';
    // statsScript.crossorigin = 'anonymous';
    // statsScript.referrerpolicy = 'no-referrer';
    // statsScript.integrity = 'sha512-ey3wf3z1WUgQ6/XU/lV1UVQkbCpWsyQANkBst88XXWHok9fXKp55G365lLhScYihGpkzhiZz75r+8isUSCKRIg==';
    // document.getElementsByTagName('body')[0].appendChild(statsScript);

    // frontPage script require ======
    if (!jqueryJsScript) {
        jqueryScript.src = environment.URL + "/frontend-css/js/jquery.min.js";
        jqueryScript.id = "jquery-js";
        document.getElementsByTagName("body")[0].appendChild(jqueryScript);
    }

    if (!bootstrapJsScript) {
        bootstrapScript.src =
            environment.URL + "/frontend-css/js/bootstrap.bundle.min.js";
        bootstrapScript.id = "bootstrap-js";
        document.getElementsByTagName("body")[0].appendChild(bootstrapScript);
    }
    if (!slickJsScript) {
        slickScript.src = environment.URL + "/frontend-css/js/slick.min.js";
        slickScript.id = "slick-js";
        document.getElementsByTagName("body")[0].appendChild(slickScript);
    }

    // WEB STYLING HENDLING ======
    if (updatedLanguage === "ar") {
        // const cssOne = './assets/css/custom.rtl.css'
        // const cssTow = './assets/css/style.rtl.css'
        // const cssThree = './assets/css/frontend.rtl.css'
        // link.rel = 'stylesheet';
        // link.href = cssOne;
        // link.id = 'arOne';
        // document.getElementsByTagName('head')[0].appendChild(link);
        // link.rel = 'stylesheet';
        // link.href = cssTow;
        // link.id = 'arTow';
        // document.getElementsByTagName('head')[0].appendChild(link);
        // if (!window.location.href.includes('/app')) {
        //     link.rel = 'stylesheet';
        //     link.href = cssThree;
        //     link.id = 'arTree';
        //     document.getElementsByTagName('head')[0].appendChild(link);
        // } else {
        //     let mainCss = document.getElementById('arTree')
        //     if (mainCss) {
        //         console.log('main', mainCss)
        //     }
        // }
    } else {
        if (
            !window.location.href.includes("/app") &&
            !window.location.href.includes("/login") &&
            !window.location.href.includes("reset-password") &&
            !window.location.href.includes("forgot-password") &&
            !window.location.href.includes("register") &&
            !window.location.href.includes("app/admin") &&
            !window.location.href.includes("/pages") &&
            !window.location.href.includes("/generate-qr-code") &&
            !window.location.href.includes("home-plans") &&
            !window.location.href.includes("/contact")
        ) {
            // document.querySelectorAll("#One").forEach(d => d.remove())
            // document.querySelectorAll("#Tow").forEach(d => d.remove())
        } else {
            if (
                window.location.href.includes("/generate-qr-code") ||
                window.location.href.includes("/home-plans") ||
                window.location.href.includes("/contact") ||
                window.location.href.includes("/pages")
            ) {
                if (window.location.href.includes("/generate-qr-code")) {
                    if (
                        indexCss ||
                        slickCss ||
                        slickThemeCss ||
                        BootstrapCss ||
                        font ||
                        jqueryJsScript ||
                        bootstrapJsScript ||
                        slickJsScript
                    ) {
                        // document.querySelectorAll("#index").forEach((d) => d.remove())
                        // document.querySelectorAll("#slick").forEach((d) => d.remove())
                        // document.querySelectorAll("#slick-theme").forEach((d) => d.remove())
                        // document.querySelectorAll("#Bootstrap").forEach((d) => d.remove())
                        // document.querySelectorAll("#font").forEach((d) => d.remove())
                        // document.querySelectorAll("#jquary-js").forEach((d) => d.remove())
                        // document.querySelectorAll("#bootstrap-js").forEach((d) => d.remove())
                        // document.querySelectorAll("#slick-js").forEach((d) => d.remove())
                    }
                }
            } else {
                if (
                    indexCss ||
                    slickCss ||
                    slickThemeCss ||
                    BootstrapCss ||
                    font ||
                    jqueryJsScript ||
                    bootstrapJsScript ||
                    slickJsScript ||
                    HeaderCss
                ) {
                    if (window.location.href.includes("/app")) {
                        document
                            .querySelectorAll("#index")
                            .forEach((d) => d.remove());
                    }
                    document
                        .querySelectorAll("#slick")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#slick-theme")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#Bootstrap")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#font")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#jquary-js")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#bootstrap-js")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#slick-js")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#Header")
                        .forEach((d) => d.remove());
                } else {
                    if (window.location.href.includes("/app")) {
                        document
                            .querySelectorAll("#index")
                            .forEach((d) => d.remove());
                    }
                    document
                        .querySelectorAll("#slick")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#slick-theme")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#Bootstrap")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#font")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#jquary-js")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#bootstrap-js")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#slick-js")
                        .forEach((d) => d.remove());
                    document
                        .querySelectorAll("#Header")
                        .forEach((d) => d.remove());
                }
            }
        }
    }
};
