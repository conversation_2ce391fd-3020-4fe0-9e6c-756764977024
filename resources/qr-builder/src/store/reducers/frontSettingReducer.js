import { frontSettingActionType } from '../../constants';


export default (state = {}, action) => {
    switch (action.type) {
        case frontSettingActionType.FETCH_FRONT_SETTING:
            return {
                ...state,
                ...action.payload
            };
        case frontSettingActionType.FETCH_USER_SUBSCRIPTION_END:
            return {
                ...state,
                ...action.payload
            };
        case frontSettingActionType.ANNOUNCEMENT_NOTIFICATION:
                return {
                    ...state,
                    ...action.payload
        };
        default:
            return state;
    }
};
