import { projectActionType, projectAdminActionType } from '../../constants';

export default (state = [], action) => {
    switch (action.type) {
        case projectActionType.FETCH_PROJECTS:
            return action.payload;
        case projectAdminActionType.ADMIN_ADD_PROJECT:
            return action.payload;
        case projectActionType.FETCH_PROJECT:
            return [action.payload];
        case projectActionType.ADD_PROJECT:
            return [...state, action.payload];
        case projectActionType.EDIT_PROJECT:
            return state.map(item => item.id === +action.payload.id ? action.payload : item);
        case projectActionType.DELETE_PROJECT:
            return state.filter(item => item.id !== action.payload);
        default:
            return state;
    }
};
