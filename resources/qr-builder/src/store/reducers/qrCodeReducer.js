import { qrcodeActionType } from '../../constants';


export default (state = [], action) => {
    switch (action.type) {
        case qrcodeActionType.FETCH_QRCODES:
            return action.payload;
        case qrcodeActionType.FETCH_QRCODE:
            return [action.payload];
        case qrcodeActionType.ADD_QRCODE:
            return action.payload;
        case qrcodeActionType.EDIT_QRCODE:
            return state.map(item => item.id === +action.payload.id ? action.payload : item);
        case qrcodeActionType.DELETE_QRCODE:
            return state.filter(item => item.id !== action.payload);
        default:
            return state;
    }
};
