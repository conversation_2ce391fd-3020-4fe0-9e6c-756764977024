import {projectAdminActionType} from '../../../constants';

export default (state = [], action) => {
    switch (action.type) {
        case projectAdminActionType.FETCH_PROJECTS:
            return action.payload;
        case projectAdminActionType.ADMIN_PROJECT:
            return action.payload;
        case projectAdminActionType.DELETE_PROJECT:
            return state.filter(item => item.id !== action.payload);
        default:
            return state;
    }
};
