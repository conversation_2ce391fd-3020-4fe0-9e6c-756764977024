import { settingActionType } from "../../../../constants";


export default (state = {}, action) => {
    switch (action.type) {
        case settingActionType.FETCH_GOOGLE_LOGIN_SETTING:
            return {
                ...state,
                ...action.payload
            }
        case settingActionType.FETCH_FACRBOOK_LOGIN_SETTING:
            return {
                ...state,
                ...action.payload
            }
        default:
            return state;
    }
};
