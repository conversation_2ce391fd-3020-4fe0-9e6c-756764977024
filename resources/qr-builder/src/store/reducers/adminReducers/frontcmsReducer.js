import { frontCmsActionTyps } from '../../../constants';


export default (state = {}, action) => {
    switch (action.type) {
        case frontCmsActionTyps.FETCH_FRONT_CMS:
            return {
                ...state,
                cms: action.payload
            };
        case frontCmsActionTyps.FETCH_FRONT_FEATURES:
            return {
                ...state,
                features: action.payload
            };
        case frontCmsActionTyps.FETCH_FRONT_SUB_FEATURES:
            return {
                ...state,
                subFeatures: action.payload
            };
        case frontCmsActionTyps.FETCH_ALL_PLANS:
            return {
                ...state,
                allPlans: action.payload
            };
        case frontCmsActionTyps.FETCH_FRONT_TREM_CONDITION:
            return {
                ...state,
                allTremCondition: action.payload
            };
        case frontCmsActionTyps.FETCH_FRONT_ENQUIRIES:
            return {
                ...state,
                allEnquiries: action.payload
            };
        case frontCmsActionTyps.DELETE_FRONT_ENQUIRIES:
            return {
                ...state,
                // allEnquiries: state.filter(item => item.id !== action.payload)
            };
        case frontCmsActionTyps.FETCH_FRONT_QR_CODE_TYPES:
            return {
                ...state,
                qrCodeTypes: action.payload
            };
        default:
            return state;
    }
};
