import { couponCodesActionType } from '../../../constants';

export default (state = [], action) => {
    switch (action.type) {
        case couponCodesActionType.FETCH_CODES:
            return action.payload;
        case couponCodesActionType.FETCH_CODE:
            return [action.payload];
        case couponCodesActionType.ADD_CODE:
            return [...state, action.payload];
        default:
            return state;
    }
};
