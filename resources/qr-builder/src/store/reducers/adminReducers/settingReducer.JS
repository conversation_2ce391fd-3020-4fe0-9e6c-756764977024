import { settingActionType } from '../../../constants';


export default (state = {}, action) => {
    switch (action.type) {
        case settingActionType.EDIT_MAIN_SETTINGS:
            return action.payload
        case settingActionType.FETCH_MAIN_SETTINGS:
            return action.payload
        case settingActionType.FETCH_CAPTCHA_SETTING:
            return action.payload
        case settingActionType.EDIT_CAPTCHA_SETTINGS:
            return action.payload
        case settingActionType.FETCH_PAYPAL_SETTINGS:
            return action.payload
        case settingActionType.EDIT_PAYPAL_SETTINGS:
            return action.payload
        // case settingActionType.FETCH_EMAIL_NOTIFICATION_SETTING:
        //     return action.payload
        // case settingActionType.EDIT_EMAIL_NOTIFICATION_SETTINGS:
        //     return action.payload
        case settingActionType.FETCH_SOCIAL_SETTING:
            return action.payload
        case settingActionType.EDIT_SOCIAL_SETTING:
            return action.payload
        default:
            return state;
    }
};
