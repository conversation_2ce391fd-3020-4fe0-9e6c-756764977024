import {linkActionType} from '../../constants';

export default (state = [], action) => {
    switch (action.type) {
        case linkActionType.FETCH_LINKS:
            return action.payload;
        case linkActionType.FETCH_LINK:
            return [action.payload];
        case linkActionType.ADD_LINK:
            return action.payload;
        case linkActionType.ADMIN_ADD_LINK:
            return action.payload;
        case linkActionType.EDIT_LINK:
            return state.map(item => item.id === +action.payload.id ? action.payload : item);
        case linkActionType.DELETE_LINK:
            return state.filter(item => item.id !== action.payload);
        default:
            return state;
    }
};
