import { combineReducers } from 'redux';
import loginReducers from './authReducer';
import toastReducer from './toastReducer';
import userReducers from './userReducers';
import totalRecordReduce from './totalRecordReduce';
import plansReducers from './plansReducers';
import projectReducer from './projectReducer';
import loadingReducer from './loadingReducer';
import updateProfileReducer from './updateProfileReducer';
import qrCodeReducer from './qrCodeReducer';
import qrCodeReducers from './adminReducers/qrCodeReducers';
import linksReducers from './adminReducers/linksReducers';
import projectReducers from './adminReducers/projectReducers';
import linkReducer from './linkReducer';
import changeLanguageReducer from './changeLanguageReducer';
import currencyReducer from './adminReducers/currencyReducer';
import dashboardReducer from './adminReducers/dashboardReducer';
import userDashboardReducer from "./userDashboardReducer";
import stripeReducer from "./adminReducers/stripeReducer";
import customStyleReducer from "./adminReducers/customStyleReducer";
import razorpayReducer from "./adminReducers/razorpayReducer";
import callBackReducer from './callBackReducer';
import adsReducers from './adminReducers/settingsReducers/adsReducers';
import smtpMailReducer from './adminReducers/settingsReducers/smtpMailReducer';
import settingReducer from './adminReducers/settingReducer';
import socialSettingReducer from './adminReducers/socialSettingReducer';
import announcementReducer from "./adminReducers/announcementReducer";
import couponCodesReducer from './adminReducers/couponCodesReducer';
import qrCodeTypesReducer from './qrCodeTypesReducer';
import homeQrcodeReducer from './homeQrcodeReducer';
import linkAnalyticsReducer from './linkAnalyticsReducer';
import puchasePlanReducer from './puchasePlanReducer';
import subscribedPlansReducer from './subscribedPlansReducer';
import frontSettingReducer from './frontSettingReducer';
import cashPaymentReducer from './adminReducers/cashPaymentReducer';
import subscribedUserPlanreducer from './adminReducers/subscribedUserPlanreducer';
import selectedPlanReducers from './selectedPlanReducers';
import adminQrTypeReducer from "./adminReducers/adminQrTypeReducer";
import userQrCodeReducer from './adminReducers/userQrCodeReducer';
import adminLinksByIdReducer from './adminReducers/adminLinksByIdReducer';
import transactionsreducer from './adminReducers/transactionsreducer';
import socialLoginReducer from './adminReducers/settingsReducers/socialLoginReducer';
import emailNotifitionSettingReducer from './adminReducers/emailNotifitionSettingReducer';
import frontcmsReducer from './adminReducers/frontcmsReducer';
import configReducer from './configReducer';
import subscribersReducer from './subscribersReducer';
import themeReducer from './adminReducers/themeReducer';
import updateLanguageReducer from './updateLanguageReducer';
import languageReducer from './languageReducer';
import languageDataReducer from './languageDataReducer';
import pagesReducers from './adminReducers/pagesReducers';
import pagesFrontReducer from './adminReducers/pagesFrontReducer';
import EmailNotiyReducer from './adminReducers/EmailNotiyReducer';
import thisWeekEarningReducer from './adminReducers/thisWeekEarningReducer';
import topVisitedLinksAdminREducer from './adminReducers/topVisitedLinksAdminREducer';
import topVisitedLinkUserReducer from './topVisitedLinkUserReducer';
import filterDropDownToggleReducer from './filterDropDownToggleReducer';
import digitalBusinessCardReducer from './digitalBusinessCardReducer';
import adminDigitalBusinessCardReducer from './adminReducers/adminDigitalBusinessCardReducer';
import cashPaymentDetails from './adminReducers/cashPaymentDetails';
import businessCardSocialLinkReducer from './businessCardSocialLinkReducer';

export default combineReducers({
    loginUser: loginReducers,
    isLoading: loadingReducer,
    toasts: toastReducer,
    users: userReducers,
    totalRecord: totalRecordReduce,
    plans: plansReducers,
    project: projectReducer,
    userProfile: updateProfileReducer,
    qrcode: qrCodeReducer,
    qrcodes_admin: qrCodeReducers,
    links_admin: linksReducers,
    project_admin: projectReducers,
    links: linkReducer,
    selectedLanguage: changeLanguageReducer,
    currencies: currencyReducer,
    adminDashboard: dashboardReducer,
    userDashboard: userDashboardReducer,
    settings: settingReducer,
    stripe: stripeReducer,
    customStyle: customStyleReducer,
    razorpay: razorpayReducer,
    callSetting: callBackReducer,
    adsSetting: adsReducers,
    smtpMailSetting: smtpMailReducer,
    socialSetting: socialSettingReducer,
    announcement: announcementReducer,
    couponCodes: couponCodesReducer,
    qrCodeTypes: qrCodeTypesReducer,
    homeQrcodeDetails: homeQrcodeReducer,
    linkAnalyticsData: linkAnalyticsReducer,
    paymentSessionId: puchasePlanReducer,
    subsctibedAllPlans: subscribedPlansReducer,
    frontSettings: frontSettingReducer,
    cashPayments: cashPaymentReducer,
    subscribedUserPlan: subscribedUserPlanreducer,
    selectedPlan: selectedPlanReducers,
    adminQrTypes: adminQrTypeReducer,
    userQrCodeDteails: userQrCodeReducer,
    adminLinksById: adminLinksByIdReducer,
    transactions: transactionsreducer,
    socialLogins: socialLoginReducer,
    emailsetting: emailNotifitionSettingReducer,
    frontCms: frontcmsReducer,
    config: configReducer,
    subscriber: subscribersReducer,
    theme: themeReducer,
    updateLanguage: updateLanguageReducer,
    languages: languageReducer,
    language: languageDataReducer,
    pages: pagesReducers,
    frontPage: pagesFrontReducer,
    thisWeekEarning: thisWeekEarningReducer,
    topVisitedLinkAdmin: topVisitedLinksAdminREducer,
    topVisitedLinkUser: topVisitedLinkUserReducer,
    dropDownToggle: filterDropDownToggleReducer,
    businessCard: digitalBusinessCardReducer,
    adminBusinessCard: adminDigitalBusinessCardReducer,
    cashPaymentDetails: cashPaymentDetails,
    socialLink: businessCardSocialLinkReducer
});
