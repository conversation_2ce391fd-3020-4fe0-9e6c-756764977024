import {businessCardLinkActionType} from '../../constants';

export default (state = [], action) => {
    switch (action.type) {
        // case businessCardLinkActionType.FETCH_BUSINESS_CARDS:
        //     return action.payload;
        case businessCardLinkActionType.FETCH_BUSINESS_CARD_LINK:
            return action.payload;
        // case businessCardLinkActionType.ADD_BUSINESS_CARD:
        //     return action.payload;
        // case businessCardActionType.ADMIN_ADD_LINK:
        //     return action.payload;
        case businessCardLinkActionType.EDIT_BUSINESS_CARD_LINK:
            return state.map(item => item.id === +action.payload.id ? action.payload : item);
        // case businessCardLinkActionType.DELETE_BUSINESS_CARD:
        //     return state.filter(item => item.id !== action.payload);
        default:
            return state;
    }
};
