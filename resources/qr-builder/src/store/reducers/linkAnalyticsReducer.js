import { linksAdminActionType } from '../../constants';


export default (state = {}, action) => {
    switch (action.type) {
        case linksAdminActionType.FETCH_LINKS_ANALYTICS:
            return action.payload?.noRecord === undefined ? ({
                ...state,
                noRecord: undefined,
                ...action.payload
            }) : ({
                ...action.payload,
                totalVisitorCount: state?.totalVisitorCount,
                weeklyLabels: state?.weeklyLabels
            })
        case linksAdminActionType.FETCH_CHART_LINKS_ANALYTICS:
            return {
                ...state,
                ...action.payload
            };
        default:
            return state;
    }
};
