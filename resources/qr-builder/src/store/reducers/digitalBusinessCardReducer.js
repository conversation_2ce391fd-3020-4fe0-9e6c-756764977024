import {businessCardActionType} from '../../constants';

export default (state = [], action) => {
    switch (action.type) {
        case businessCardActionType.FETCH_BUSINESS_CARDS:
            return action.payload;
        case businessCardActionType.FETCH_BUSINESS_CARD:
            return [action.payload];
        case businessCardActionType.ADD_BUSINESS_CARD:
            return action.payload;
        // case businessCardActionType.ADMIN_ADD_LINK:
        //     return action.payload;
        case businessCardActionType.EDIT_BUSINESS_CARD:
            return state.map(item => item.id === +action.payload.id ? action.payload : item);
        case businessCardActionType.DELETE_BUSINESS_CARD:
            return state.filter(item => item.id !== action.payload);
        default:
            return state;
    }
};
