import { plansActionType } from '../../constants';

export default (state = [], action) => {
    switch (action.type) {
        case plansActionType.FETCH_PLANS:
            return action.payload;
        case plansActionType.FETCH_USER_PLAN:
            return action.payload;
        case plansActionType.FETCH_PLAN:
            return [action.payload];
        case plansActionType.ADD_PLAN:
            return action.payload;
        case plansActionType.PURCHASE_PLAN:
            return action.payload;
        case plansActionType.EDIT_PLAN:
            return state.map(item => item.id === +action.payload.id ? action.payload : item);
        case plansActionType.DELETE_PLAN:
            return state.filter(item => item.id !== action.payload);
        default:
            return state;
    }
};
