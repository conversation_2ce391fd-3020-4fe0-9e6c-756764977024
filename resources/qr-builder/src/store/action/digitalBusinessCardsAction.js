import apiConfig from "../../config/apiConfig";
import {
    apiBaseURL,
    toastType,
    businessCardActionType,
    adminBusinessCardActionType,
    businessCardLinkActionType,
} from "../../constants";
import requestParam from "../../shared/requestParam";
import { addToast } from "./toastAction";
import {
    setTotalRecord,
    addInToTotalRecord,
    removeFromTotalRecord,
} from "./totalRecordAction";
import { setLoading } from "./loadingAction";
import { getFormattedMessage } from "../../shared/sharedMethod";
import { setSavingButton } from "./saveButtonAction";

export const addBusinessCard = (data, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true));
    await apiConfig
        .post(apiBaseURL.ADD_BUSINESS_CARD, data)
        .then((response) => {
            dispatch({
                type: businessCardActionType.ADD_BUSINESS_CARD,
                payload: response.data.data,
            });
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "business.card.added.successfully.message"
                    ),
                })
            );
            navigate("/app/digital-business-cards");
            dispatch(addInToTotalRecord(1));
            dispatch(setSavingButton(false));
        })
        .catch((response) => {
            dispatch(setSavingButton(false));
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const getBusinessCards =
    (filter, navigate, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.ADD_BUSINESS_CARD;
        if (
            !_.isEmpty(filter) &&
            (filter.page ||
                filter.pageSize ||
                filter.search ||
                filter.order_By ||
                filter.created_at)
        ) {
            url += requestParam(filter);
        }
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: businessCardActionType.FETCH_BUSINESS_CARDS,
                    payload: response.data.data,
                });
                dispatch(setTotalRecord(response?.data?.meta?.total));
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch(({ response }) => {
                dispatch(setSavingButton(false));
                dispatch(
                    addToast({
                        text:
                            response?.response?.data?.message ||
                            response?.response?.data?.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const adminGetBusinessCards =
    (filter, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.ADMIN_ADD_BUSINESS_CARD;
        if (
            !_.isEmpty(filter) &&
            (filter.page ||
                filter.pageSize ||
                filter.search ||
                filter.order_By ||
                filter.created_at)
        ) {
            url += requestParam(filter);
        }
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: adminBusinessCardActionType.ADMIN_FETCH_BUSINESS_CARDS,
                    payload: response.data.data,
                });
                dispatch(setTotalRecord(response?.data?.meta?.total));
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch(({ response }) => {
                dispatch(setSavingButton(false));
                dispatch(
                    addToast({
                        text:
                            response?.response?.data?.message ||
                            response?.response?.data?.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const deleteBusinessCard = (id) => async (dispatch) => {
    apiConfig
        .delete(apiBaseURL.ADD_BUSINESS_CARD + "/" + id)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({
                type: businessCardActionType.DELETE_BUSINESS_CARD,
                payload: id,
            });
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "business.card.deleted.successfully.message"
                    ),
                })
            );
        })
        .catch((response) => {
            dispatch(setSavingButton(false));
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchBusinessCard =
    (id, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        apiConfig
            .get(apiBaseURL.ADD_BUSINESS_CARD + "/" + id)
            .then((response) => {
                dispatch({
                    type: businessCardActionType.FETCH_BUSINESS_CARD,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const editBusinessCard = (id, data, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true));
    dispatch(setLoading(true));
    apiConfig
        .post(apiBaseURL.ADD_BUSINESS_CARD + "/" + id, data)
        .then((response) => {
            dispatch({
                type: businessCardActionType.EDIT_BUSINESS_CARD,
                payload: response.data.data,
            });
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "business.card.updated.successfully.message"
                    ),
                })
            );
            dispatch(setLoading(false));
            navigate("/app/digital-business-cards");
        })
        .catch((response) => {
            dispatch(setSavingButton(false));
            dispatch(setLoading(false));
            dispatch(
                addToast({
                    text:
                        response?.response?.data?.message ||
                        response?.response?.data?.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const updateStatusBusinessCard =
    (id, data, setIsStatus) => async (dispatch) => {
        dispatch(setSavingButton(true));
        apiConfig
            .post(apiBaseURL.ADD_BUSINESS_CARD + "/" + id, data)
            .then((response) => {
                // dispatch({ type: businessCardActionType.EDIT_BUSINESS_CARD, payload: response.data.data });
                setIsStatus(false);
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "status.updated.successfully.message"
                        ),
                    })
                );
                // navigate('/app/digital-business-cards')
            })
            .catch((response) => {
                dispatch(setSavingButton(false));
                dispatch(
                    addToast({
                        text:
                            response?.response?.data?.message ||
                            response?.response?.data?.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const updateStatusBusinessCards =
    (id, setIsStatus) => async (dispatch) => {
        dispatch(setSavingButton(true));
        dispatch(setLoading(true));
        apiConfig
            .post(apiBaseURL.CHANGE_STATUS_BUSINES_CARD + "/" + id)
            .then((response) => {
                setIsStatus(false);
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "status.updated.successfully.message"
                        ),
                    })
                );
            })
            .catch((response) => {
                dispatch(setSavingButton(false));
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text:
                            response?.response?.data?.message ||
                            response?.response?.data?.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const adminAddBusinessCard = (data, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true));
    dispatch(setLoading(true));
    await apiConfig
        .post(apiBaseURL.ADMIN_ADD_BUSINESS_CARD, data)
        .then((response) => {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "business.card.added.successfully.message"
                    ),
                })
            );
            navigate("/app/admin/digital-business-cards");
            // dispatch(adminGetBusinessCards())
            dispatch(addInToTotalRecord(1));
            dispatch(setSavingButton(false));
            dispatch(setLoading(false));
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response?.response?.data?.message ||
                        response?.response?.data?.title,
                    type: toastType.ERROR,
                })
            );
            dispatch(setSavingButton(false));
            dispatch(setLoading(false));
        });
};

export const deleteAdminBusinessCard = (id) => async (dispatch) => {
    apiConfig
        .delete(apiBaseURL.ADMIN_ADD_BUSINESS_CARD + "/" + id)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({
                type: adminBusinessCardActionType.DELETE_ADMIN_BUSINESS_CARD,
                payload: id,
            });
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "business.card.deleted.successfully.message"
                    ),
                })
            );
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response?.response?.data?.message ||
                        response?.response?.data?.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchBusinessCardLink =
    (id, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        apiConfig
            .get(apiBaseURL.ADD_BUSINESS_CARD_LINK + "/" + id)
            .then((response) => {
                dispatch({
                    type: businessCardLinkActionType.FETCH_BUSINESS_CARD_LINK,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const editBusinessCardLink = (data, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true));
    apiConfig
        .post(apiBaseURL.UPDATE_BUSINESS_CARD_LINK, data)
        .then((response) => {
            // dispatch({ type: businessCardLinkActionType.EDIT_BUSINESS_CARD_LINK, payload: response.data.data });
            // fetchBusinessCardLink(data?.business_card_id)
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "business.card.updated.successfully.message"
                    ),
                })
            );
            navigate("/app/digital-business-cards");
        })
        .catch((response) => {
            dispatch(setSavingButton(false));
            dispatch(
                addToast({
                    text:
                        response?.response?.data?.message ||
                        response?.response?.data?.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchBusinessAdminCardLink =
    (id, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        apiConfig
            .get(apiBaseURL.ADMIN_ADD_BUSINESS_CARD_LINK + "/" + id)
            .then((response) => {
                dispatch({
                    type: businessCardLinkActionType.FETCH_BUSINESS_CARD_LINK,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const editBusinessAdminCardLink =
    (data, id, navigate) => async (dispatch) => {
        dispatch(setSavingButton(true));
        dispatch(setLoading(true));
        apiConfig
            .post(apiBaseURL.ADMIN_UPDATE_BUSINESS_CARD_LINK + "/" + id, data)
            .then((response) => {
                // dispatch({ type: businessCardLinkActionType.EDIT_BUSINESS_CARD_LINK, payload: response.data.data });
                fetchBusinessAdminCardLink(data?.business_card_id);
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "business.card.Social-link.updated.successfully.message"
                        ),
                    })
                );
                dispatch(setLoading(false));
                navigate("/app/admin/digital-business-cards");
            })
            .catch((response) => {
                dispatch(setSavingButton(false));
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text:
                            response?.response?.data?.message ||
                            response?.response?.data?.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };
