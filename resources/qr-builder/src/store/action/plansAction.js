import apiConfig from '../../config/apiConfig';
import { apiBaseURL, Filters, plansActionType, toastType } from '../../constants';
import requestParam from '../../shared/requestParam';
import { addToast } from './toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from './totalRecordAction';
import { setLoading } from './loadingAction';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { setSavingButton } from "./saveButtonAction";

export const fetchPlans = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.PLANS;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: plansActionType.FETCH_PLANS, payload: response.data.data });
            dispatch(setTotalRecord(response.data.meta.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchPlan = (planId, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.get(apiBaseURL.PLANS + '/' + planId)
        .then((response) => {
            dispatch({ type: plansActionType.FETCH_PLAN, payload: response.data.data })
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}

export const addPlan = (plans, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true))
    await apiConfig.post(apiBaseURL.PLANS, plans)
        .then((response) => {
            dispatch({ type: plansActionType.ADD_PLAN, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('plan.success.create.message') }));
            navigate('/app/admin/plans');
            dispatch(addInToTotalRecord(1))
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const editPlan = (planId, plans, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.UPDATE_PLANS + '/' + planId, plans)
        .then((response) => {
            dispatch({ type: plansActionType.EDIT_PLAN, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('plan.success.edit.message') }));
            navigate('/app/admin/plans')
            dispatch(setLoading(false))
            dispatch(setSavingButton(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const deletePlan = (planId) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.PLANS + '/' + planId)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({ type: plansActionType.DELETE_PLAN, payload: planId });
            dispatch(addToast({ text: getFormattedMessage('plan.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const makeDefaultPlan = (planId, navigate) => async (dispatch) => {
    apiConfig.post(apiBaseURL.DEFAULT_PLAN + '/' + planId)
        .then((response) => {
            dispatch(fetchPlans(Filters.OBJ, true))
            dispatch(addToast({ text: getFormattedMessage('default.plan.changed.successfully.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchUserPlan = (isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.SUBSCRIPTION_PLANS;
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: plansActionType.FETCH_USER_PLAN, payload: response.data.data });
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchSelectedPlan = (data, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.post(apiBaseURL.SELECTED_PLAN, data)
        .then((response) => {
            if (response.data.data.discount.coupon_code !== '') {
                dispatch(addToast(
                    { text: getFormattedMessage("coupon.code.applied.message") }));
            }
            dispatch({ type: plansActionType.FETCH_SELECTED_PLAN, payload: response.data.data })
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.title ? response.response.data.title : response.response.data.message, type: toastType.ERROR }));
        });
}

export const purchasePlan = (plans, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.post(apiBaseURL.PURCHASE_PLAN, plans)
        .then((response) => {
            dispatch({ type: plansActionType.FETCH_SELECTED_PLAN, payload: response.data.data })
            dispatch(fetchSubscribedAllPlan(Filters.OBJ, true))
            dispatch(addToast(
                { text: response.data.message }));
            if (isLoading) {
                dispatch(setLoading(false))
            }

        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}

export const fetchSubscribedAllPlan = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.SUBSCRIBED_ALL_PLANS;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: plansActionType.SUBSCRIBED_ALL_PLANS, payload: response.data.data.data })
            dispatch(setTotalRecord(response.data.data.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}
