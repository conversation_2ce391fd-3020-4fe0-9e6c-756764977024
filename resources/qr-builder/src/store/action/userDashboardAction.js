import apiConfig from '../../config/apiConfig';
import { apiBaseURL, dashboardActionType, dashboardAdminActionType, qrcodeActionType, toastType } from '../../constants';
import { addToast } from './toastAction';
import { setLoading } from "./loadingAction";
import { getFormattedMessage } from '../../shared/sharedMethod';

export const fetchDashboardData = () => async (dispatch) => {
    dispatch(setLoading(true));
    apiConfig.get(apiBaseURL.USER_DASHBOARD)
        .then((response) => {
            dispatch({ type: dashboardActionType.FETCH_ALL_DATA, payload: response.data.data })
            dispatch(setLoading(false));
        })
        .catch(({ response }) => {
            response?.data?.message?.includes('Your plan has been expired') ? '' : dispatch(addToast(
                { text: response.data.message || response.data.title, type: toastType.ERROR }));
            dispatch(setLoading(false));
        });
}

export const deleteUserLatestQrcode = (qrcodeId) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.QR_CODE + '/' + qrcodeId)
        .then((response) => {
            // dispatch({ type: qrcodeActionType.DELETE_QRCODE, payload: qrcodeId });
            dispatch(fetchDashboardData())
            dispatch(addToast({ text: getFormattedMessage('qr-code.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const getTopVisitedLinkUserAction = () => (dispatch) => {
    apiConfig.get(apiBaseURL.TOP_VISITED_LINK_USER)
        .then((response) => {
            response && dispatch({ type: dashboardAdminActionType.TOP_VISITED_LINK_USER, payload: response.data.data });
        })
        .catch((response) => {
            response?.response?.data?.message?.includes('Your plan has been expired') ? '' : dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
}

