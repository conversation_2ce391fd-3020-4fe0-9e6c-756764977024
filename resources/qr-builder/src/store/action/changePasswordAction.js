import { apiBaseURL, authActionType, toastType } from '../../constants';
import apiConfig from '../../config/apiConfig';
import { addToast } from './toastAction';

export const onChangePassword = (passwordInputs, onClickDeleteModel) => async (dispatch) => {
    apiConfig.post(apiBaseURL.CHANGE_PASSWORD, passwordInputs)
        .then((response) => {
            dispatch({ type: authActionType.CHANGE_PASSWORD, payload: response.data });
            dispatch(addToast({ text: response.data.message }));
            onClickDeleteModel(false)
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};
