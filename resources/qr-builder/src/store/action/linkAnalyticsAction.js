import { toastType, apiBaseURL, linksAdminActionType } from '../../constants';
import apiConfig from '../../config/apiConfig';
import { addToast } from './toastAction';


export const fetchAllLinks = (linkId) => async (dispatch) => {
    await apiConfig.get(`${apiBaseURL.LINK_ANALITYCS}${linkId}/analytics`)
        .then((response) => {
            dispatch({ type: linksAdminActionType.FETCH_LINKS_ANALYTICS, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.data.message || response.data.title, type: toastType.ERROR }));
        });
};

export const fetchAllBusinessCard = (linkId) => async (dispatch) => {
    await apiConfig.get(`${apiBaseURL.BUSINESS_CARD_ANALITYCS}${linkId}/analytics`)
        .then((response) => {
            dispatch({ type: linksAdminActionType.FETCH_LINKS_ANALYTICS, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.data.message || response.data.title, type: toastType.ERROR }));
        });
};


export const fetchChartData = (data) => async (dispatch) => {
    await apiConfig.post(apiBaseURL.CHART_LINK_ANALITYCS, data)
        .then((response) => {
            dispatch({ type: linksAdminActionType.FETCH_CHART_LINKS_ANALYTICS, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response.data.message || response?.response.data.title, type: toastType.ERROR }));
        });
};


export const fetchBusinessChartData = (data) => async (dispatch) => {
    await apiConfig.post(apiBaseURL.BUSINESS_CHART_LINK_ANALITYCS, data)
        .then((response) => {
            dispatch({ type: linksAdminActionType.FETCH_CHART_LINKS_ANALYTICS, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response.data.message || response?.response.data.title, type: toastType.ERROR }));
        });
};
