import apiConfig from '../../config/apiConfig';
import { Tokens, apiBaseURL, frontSettingActionType, toastType } from '../../constants';
import { addToast } from './toastAction';
import { setLoading } from "./loadingAction";
import { fetchFrontCms } from './adminActions/frontcmsActions';


export const fetchFrontSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.FRONT_SETTING)
        .then((response) => {
            dispatch({ type: frontSettingActionType.FETCH_FRONT_SETTING, payload: response.data.data });
            localStorage.setItem(Tokens.WEBSITE_LOGO, response.data.data.logo)
            dispatch(fetchFrontCms())
        })
        .catch(({ response }) => {
            dispatch(addToast(
                { text: response.data.message || response.data.title, type: toastType.ERROR }));
        });
}

export const remainingSubscriptionNotification = (navigate) => async (dispatch) => {
    apiConfig.get(apiBaseURL.REMAINING_SUBSCRIPTION_PLAN)
        .then((response) => {
            if (response.data.data?.plan_expired === true) {
                localStorage.setItem('plan_expired', true)
                if (!window.location.href.includes("upgrade-plan") && !window.location.href.includes("/selected-plan/") && !window.location.href.includes('/profile/edit')) {
                    // navigate("/app/manage-subscription")
                }
            } else {
                localStorage.setItem('plan_expired', false)
            }
            dispatch({ type: frontSettingActionType.FETCH_USER_SUBSCRIPTION_END, payload: response.data.data });
        })
        .catch(({ response }) => {
            dispatch(addToast(
                { text: response.data.message || response.data.title, type: toastType.ERROR }));
        });
}



export const announcementNotification = (navigate) => async (dispatch) => {
    apiConfig.get(apiBaseURL.ANNOUNCEMENT_NOTIFICATION)
        .then((response) => {
            dispatch({ type: frontSettingActionType.ANNOUNCEMENT_NOTIFICATION, payload: response.data.data });
        })
        .catch(({ response }) => {
            dispatch(addToast(
                { text: response.data.message || response.data.title, type: toastType.ERROR }));
        });
}
