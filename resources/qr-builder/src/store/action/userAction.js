import apiConfig from '../../config/apiConfig';
import { apiBaseURL, userActionType, toastType, Filters, userDetailsActionType } from '../../constants';
import requestParam from '../../shared/requestParam';
import { addToast } from './toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from './totalRecordAction';
import { setLoading } from './loadingAction';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { setSavingButton } from "./saveButtonAction";

export const fetchUsers = (filter = {}, isLoading = true) => async (dispatch) => {
    const user_role = localStorage.getItem("user_role")
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.USERS;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: userActionType.FETCH_USERS, payload: response.data.data });
            dispatch(setTotalRecord(response.data.meta.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            if (user_role === '2') {

            } else {
                dispatch(addToast(
                    { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
            }
        });
};

export const fetchQrcodeById = (id, filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    const admin = true;
    let url = '/admin/user-qrcodes/' + id;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter, admin);
    }
    await apiConfig.get(url)
        .then((response) => {
            dispatch({ type: userDetailsActionType.FETCH_QRCODE, payload: response.data.data.data });
            dispatch(setTotalRecord(response.data.data.total));
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch(({ response }) => {
            dispatch(addToast(
                { text: response?.data?.message || response?.data?.title, type: toastType.ERROR }));
        });
};

export const fetchUser = (userId, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.get(apiBaseURL.USERS + '/' + userId)
        .then((response) => {
            dispatch({ type: userActionType.FETCH_USER, payload: response.data.data })
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
}

export const addUser = (users, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true));
    await apiConfig.post(apiBaseURL.USERS, users)
        .then((response) => {
            dispatch({ type: userActionType.ADD_USER, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('user.success.create.message') }));
            navigate('/app/admin/users');
            dispatch(addInToTotalRecord(1))
            dispatch(setSavingButton(false))
            dispatch(setLoading(false));
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false));
            dispatch(addToast(
                { text: response?.response?.data?.title ? response?.response?.data?.title : response?.response?.data?.message, type: toastType.ERROR }));
        });
};

export const editUser = (userId, users, navigate, ) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true));
    apiConfig.post(apiBaseURL.UPDATED_USER + '/' + userId, users)
        .then((response) => {
            dispatch({ type: userActionType.EDIT_USER, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('user.success.edit.message') }));
            navigate('/app/admin/users')
            dispatch(setSavingButton(false))
            dispatch(setLoading(false));
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false));
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
};

export const deleteUser = (userId) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.USERS + '/' + userId)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({ type: userActionType.DELETE_USER, payload: userId });
            dispatch(addToast({ text: getFormattedMessage('user.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const userEmailVerify = (userId, setIsEmailStatus) => async (dispatch) => {
    apiConfig.get(apiBaseURL.EMAIL_VERIFY + '/' + userId)
        .then((response) => {
            // dispatch({ type: userActionType.EMAIL_VERIFY_USER, payload: response.data });
            // dispatch(fetchUsers(Filters.OBJ))
            setIsEmailStatus(false)
            dispatch(addToast({ text: response.data.message }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const userStatus = (userId, setIsStatus) => async (dispatch) => {
    apiConfig.get(apiBaseURL.USER_STATUS + '/' + userId)
        .then((response) => {
            // dispatch({ type: userActionType.USER_STATUS, payload: response.data });
            // dispatch(fetchUsers(filterValue))
            setIsStatus(false)
            dispatch(addToast({ text: response.data.message }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchAdminLnkById = (filter = {}, isLoading = true, id) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = 'admin/user-links/' + id;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: userDetailsActionType.FETCH_LINK_BY_ID, payload: response.data.data.data });
            dispatch(setTotalRecord(response.data.data.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch(({ response }) => {
            dispatch(addToast(
                { text: response?.data?.message || response?.data?.title, type: toastType.ERROR }));
        });
};



export const fetchAllUsers = () => async (dispatch) => {
    const user_role = localStorage.getItem("user_role")
    let url = apiBaseURL.ALL_USERS;
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: userActionType.FETCH_USERS, payload: response.data.data });
        })
        .catch((response) => {
            if (user_role === '2') {

            } else {
                dispatch(addToast(
                    { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
            }
        });
};
