import apiConfig from '../../config/apiConfig';
import { apiBaseURL, toastType, profileActionType, Tokens } from '../../constants';
import { addToast } from './toastAction'
import { getFormattedMessage } from '../../shared/sharedMethod';

export const fetchProfile = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.EDIT_PROFILE)
        .then((response) => {
            dispatch({ type: profileActionType.FETCH_PROFILE, payload: response.data.data })
        })
        .catch(({ response }) => {
            dispatch(addToast(
                { text: response.data.message || response.data.title, type: toastType.ERROR }));
        });
};

export const updateProfile = (profile, navigate) => async (dispatch) => {
    apiConfig.post(apiBaseURL.UPDATE_PROFILE, profile)
        .then((response) => {
            dispatch({ type: profileActionType.UPDATE_PROFILE, payload: response.data.data });
            localStorage.setItem(Tokens.USER_IMAGE_URL, response.data.data.image_url)
            localStorage.setItem(Tokens.UPDATED_EMAIL, response.data.data.email)
            localStorage.setItem(Tokens.UPDATED_FIRST_NAME, response.data.data.name)
            localStorage.setItem(Tokens.UPDATED_LAST_NAME, response.data.data.name)
            dispatch(addToast({ text: getFormattedMessage('update-profile.success.update.message') }));
            navigate('/app/profile/edit');
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};
