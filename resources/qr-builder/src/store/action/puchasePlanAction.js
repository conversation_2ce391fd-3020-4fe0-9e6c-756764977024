import apiConfig from '../../config/apiConfig';
import { apiBaseURL, toastType, membershipPlanActionType } from '../../constants';
import { addToast } from './toastAction'
import { setLoading } from './loadingAction';
import axios from 'axios';
import { environment } from '../../config/environment';
import { getFormattedMessage } from '../../shared/sharedMethod';


export const purchasePlanStripe = (data, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true));
    }
    await apiConfig.post(apiBaseURL.PURCHASE_PLAN_STRIPE, data)
        .then((response) => {
            dispatch({ type: membershipPlanActionType.FETCH_SESSION_ID, payload: response.data.data });
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .then(function (result) {
            if (result.error) {
                // console.log(result.error.message);
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const purchasePlanPaypal = (data, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.post(apiBaseURL.PURCHASE_PLAN_PAYPAL, data)
        .then((response) => {
            if (response.data.data.error) {
                dispatch(addToast(
                    { text: response.data.data.error.message || response.data.data.error.title, type: toastType.ERROR }));
                if (isLoading) {
                    dispatch(setLoading(false))
                }
            } else {
                window.location.href = response.data.data.links[1].href
                if (isLoading) {
                    dispatch(setLoading(false))
                }
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title || response?.response?.data?.error, type: toastType.ERROR }));
        });
}

export const purchasePlanRazorepay = (data, navigate, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    const image = localStorage.getItem("website_logo")
    apiConfig.post(apiBaseURL.PURCHASE_PLAN_RAZORPAY, data)
        .then(async (response) => {
            let options = {
                key: process.env.RAZOR_KEY,
                key_secret: process.env.RAZOR_SECRET,
                order_id: response.data.data,
                handler: function (response) {
                    const data = {
                        razorpay_payment_id: response.razorpay_payment_id
                    }
                    apiConfig.post('/razorpay-payment-success', data)
                        .then((response) => {
                            navigate('/app/upgrade-plan')
                            dispatch(addToast({ text: getFormattedMessage('user.plan.purchase.success.message') }));
                        })
                        .catch((response) => {
                            dispatch(addToast(
                                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
                        });
                },
                subscription_card_change: true,
                name: 'InfyOm',
                image: image,
                theme: {
                    color: "#A561FF"
                }
            };
            var pay = new window.Razorpay(options);
            pay.open();

            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}
