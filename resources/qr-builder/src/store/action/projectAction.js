import apiConfig from '../../config/apiConfig';
import { apiBaseURL, userActionType, toastType, projectActionType } from '../../constants';
import requestParam from '../../shared/requestParam';
import { addToast } from './toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from './totalRecordAction';
import { setLoading } from './loadingAction';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { setSavingButton } from "./saveButtonAction";

export const fetchProjects = ( filter = {}, isLoading = true ) => async ( dispatch ) => {
    if ( isLoading ) {
        dispatch( setLoading( true ) )
    }
    let url = apiBaseURL.PROJECT;
    if ( !_.isEmpty( filter ) && ( filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at ) ) {
        url += requestParam( filter );
    }
    apiConfig.get( url )
        .then( ( response ) => {
            dispatch( { type: projectActionType.FETCH_PROJECTS, payload: response.data.data } );
            dispatch( setTotalRecord( response.data.meta.total ) )
            if ( isLoading ) {
                dispatch( setLoading( false ) )
            }
        } )
        .catch( ( response ) => {
            response?.response?.data?.message?.includes( 'Your plan has been expired' ) ? '' : dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};

export const fetchProject = ( userId, isLoading = true ) => async ( dispatch ) => {
    if ( isLoading ) {
        dispatch( setLoading( true ) )
    }
    apiConfig.get( apiBaseURL.PROJECT + '/' + userId )
        .then( ( response ) => {
            dispatch( { type: projectActionType.FETCH_PROJECT, payload: response.data.data } )
            if ( isLoading ) {
                dispatch( setLoading( false ) )
            }
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
}

export const addProject = ( project, navigate ) => async ( dispatch ) => {
    dispatch( setSavingButton( true ) )
    dispatch( setLoading( true ) )
    await apiConfig.post( apiBaseURL.PROJECT, project )
        .then( ( response ) => {
            dispatch( { type: projectActionType.ADD_PROJECT, payload: response.data.data } );
            dispatch( addToast( { text: getFormattedMessage( 'project.success.create.message' ) } ) );
            navigate && navigate( -1 );
            dispatch( addInToTotalRecord( 1 ) )
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
        } )
        .catch( ( response ) => {
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};

export const editProject = ( id, project, navigate ) => async ( dispatch ) => {
    dispatch( setSavingButton( true ) )
    dispatch( setLoading( true ) )
    apiConfig.post( apiBaseURL.UPDATE_PROJECT + '/' + id, project )
        .then( ( response ) => {
            dispatch( { type: projectActionType.EDIT_PROJECT, payload: response.data.data } );
            dispatch( addToast( { text: getFormattedMessage( 'project.success.edit.message' ) } ) );
            navigate( '/app/collections' )
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
        } )
        .catch( ( response ) => {
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};

export const deleteProject = ( id ) => async ( dispatch ) => {
    apiConfig.delete( apiBaseURL.PROJECT + '/' + id )
        .then( ( response ) => {
            dispatch( removeFromTotalRecord( 1 ) );
            dispatch( addToast( { text: getFormattedMessage( 'project.success.delete.message' ) } ) );
            dispatch( { type: projectActionType.DELETE_PROJECT, payload: id } );
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};

export const deleteAdminProject = ( id ) => async ( dispatch ) => {
    apiConfig.delete( apiBaseURL.ADMIN_PROJECT + '/' + id )
        .then( ( response ) => {
            dispatch( removeFromTotalRecord( 1 ) );
            dispatch( addToast( { text: getFormattedMessage( 'project.success.delete.message' ) } ) );
            dispatch( { type: projectActionType.DELETE_PROJECT, payload: id } );
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};
