import apiConfig from '../../config/apiConfig';
import { apiBaseURL, toastType, linkActionType } from '../../constants';
import requestParam from '../../shared/requestParam';
import { addToast } from './toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from './totalRecordAction';
import { setLoading } from './loadingAction';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { setSavingButton } from "./saveButtonAction";

export const fetchLinks = ( filter = {}, isLoading = true ) => async ( dispatch ) => {
    if ( isLoading ) {
        dispatch( setLoading( true ) )
    }
    let url = apiBaseURL.LINK;
    if ( !_.isEmpty( filter ) && ( filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at ) ) {
        url += requestParam( filter );
    }
    apiConfig.get( url )
        .then( ( response ) => {
            dispatch( { type: linkActionType.FETCH_LINKS, payload: response.data.data } );
            dispatch( setTotalRecord( response.data.meta.total ) )
            if ( isLoading ) {
                dispatch( setLoading( false ) )
            }
        } )
        .catch( ( response ) => {
            response?.response?.data?.message?.includes( 'Your plan has been expired' ) ? '' : dispatch( addToast(
                { text: response.response.data.title || response.response.data.message, type: toastType.ERROR } ) );
        } );
};

export const fetchLink = ( userId, isLoading = true ) => async ( dispatch ) => {
    if ( isLoading ) {
        dispatch( setLoading( true ) )
    }
    apiConfig.get( apiBaseURL.LINK + '/' + userId )
        .then( ( response ) => {
            dispatch( { type: linkActionType.FETCH_LINK, payload: response.data.data } )
            if ( isLoading ) {
                dispatch( setLoading( false ) )
            }
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
}

export const addLink = ( project, navigate ) => async ( dispatch ) => {
    dispatch( setSavingButton( true ) )
    dispatch( setLoading( true ) )
    await apiConfig.post( apiBaseURL.LINK, project )
        .then( ( response ) => {
            dispatch( { type: linkActionType.ADD_LINK, payload: response.data.data } );
            dispatch( addToast( { text: getFormattedMessage( 'link.success.create.message' ) } ) );
            navigate( '/app/minify-link' );
            dispatch( addInToTotalRecord( 1 ) )
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
        } )
        .catch( ( response ) => {
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
            if ( response.response.data.message.includes( "The URL alias has been already taken." ) ) {
                dispatch( addToast(
                    { text: getFormattedMessage( "The.URL.alias.has.been.already.taken.error.message" ), type: toastType.ERROR } ) );
            } else if ( response.response.data.message.includes( "The destination URL must be a valid URL" ) ) {
                dispatch( addToast(
                    { text: getFormattedMessage( "The.destination.URL.must.be.a.valid.URL.error.message" ), type: toastType.ERROR } ) );
            } else if ( response.response.data.message.includes( "The name has already been taken" ) ) {
                dispatch( addToast(
                    { text: getFormattedMessage( "The.name.has.already.been.taken.error.message" ), type: toastType.ERROR } ) );
            } else {
                dispatch( addToast(
                    { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
            }
        } );
};

export const editLink = ( id, project, navigate ) => async ( dispatch ) => {
    dispatch( setSavingButton( true ) )
    dispatch( setLoading( true ) )
    apiConfig.post( apiBaseURL.UPDATED_LINK + '/' + id, project )
        .then( ( response ) => {
            dispatch( { type: linkActionType.EDIT_LINK, payload: response.data.data } );
            dispatch( addToast( { text: getFormattedMessage( 'link.success.edit.message' ) } ) );
            navigate( '/app/minify-link' )
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
        } )
        .catch( ( response ) => {
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
            if ( response.response.data.message.includes( "The URL alias has been already taken." ) ) {
                dispatch( addToast(
                    { text: getFormattedMessage( "The.URL.alias.has.been.already.taken.error.message" ), type: toastType.ERROR } ) );
            } else if ( response.response.data.message.includes( "The destination URL must be a valid URL" ) ) {
                dispatch( addToast(
                    { text: getFormattedMessage( "The.destination.URL.must.be.a.valid.URL.error.message" ), type: toastType.ERROR } ) );
            } else if ( response.response.data.message.includes( "The name has already been taken" ) ) {
                dispatch( addToast(
                    { text: getFormattedMessage( "The.name.has.already.been.taken.error.message" ), type: toastType.ERROR } ) );
            } else {
                dispatch( addToast(
                    { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
            }
        } );
};

export const deleteLink = ( id ) => async ( dispatch ) => {
    apiConfig.delete( apiBaseURL.LINK + '/' + id )
        .then( ( response ) => {
            dispatch( removeFromTotalRecord( 1 ) );
            dispatch( { type: linkActionType.DELETE_LINK, payload: id } );
            dispatch( addToast( { text: getFormattedMessage( 'link.success.delete.message' ) } ) );
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};

export const deleteAdminLink = ( id ) => async ( dispatch ) => {
    apiConfig.delete( apiBaseURL.ADMIN_LINK + '/' + id )
        .then( ( response ) => {
            dispatch( removeFromTotalRecord( 1 ) );
            dispatch( { type: linkActionType.DELETE_LINK, payload: id } );
            dispatch( addToast( { text: getFormattedMessage( 'link.success.delete.message' ) } ) );
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};
