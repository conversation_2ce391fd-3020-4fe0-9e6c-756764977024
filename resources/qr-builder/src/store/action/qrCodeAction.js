import apiConfig from '../../config/apiConfig';
import { apiBaseURL, toastType, qrcodeActionType, Filters } from '../../constants';
import requestParam from '../../shared/requestParam';
import { addToast } from './toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from './totalRecordAction';
import { setLoading } from './loadingAction';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { setSavingButton } from "./saveButtonAction";
import { fetchAdminQrcodes } from './adminActions/qrCodeAction';

export const fetchQrcodes = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.QR_CODE;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: qrcodeActionType.FETCH_QRCODES, payload: response.data.data });
            dispatch(setTotalRecord(response.data.meta.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            response?.response?.data?.message?.includes('Your plan has been expired') ? '' : dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchQrcode = (qrcodeId, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.get(apiBaseURL.QR_CODE + '/' + qrcodeId)
        .then((response) => {
            dispatch({ type: qrcodeActionType.FETCH_QRCODE, payload: response.data.data })
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}



export const fetchQrcodeType = () => async (dispatch) => {
    await apiConfig.get(apiBaseURL.QR_CODE_TYPES)
        .then((response) => {
            dispatch({ type: qrcodeActionType.FETCH_QRCODES_TYPES, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const addQrcode = (qrcodes, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true))
    await apiConfig.post(apiBaseURL.QR_CODE, qrcodes)
        .then((response) => {
            dispatch({ type: qrcodeActionType.ADD_QRCODE, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('qr-code.success.create.message') }));
            navigate("/app/qrcode");
            dispatch(addInToTotalRecord(1))
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const editQrcode = (qrcodeId, qrcodes, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.UPDATED_QR_CODE + '/' + qrcodeId, qrcodes)
        .then((response) => {
            dispatch({ type: qrcodeActionType.EDIT_QRCODE, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('qr-code.success.edit.message') }));
            navigate('/app/qrcode')
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const deleteQrcode = (qrcodeId) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.QR_CODE + '/' + qrcodeId)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({ type: qrcodeActionType.DELETE_QRCODE, payload: qrcodeId });
            dispatch(addToast({ text: getFormattedMessage('qr-code.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const deleteAdminQrcode = (qrcodeId) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.ADMIN_QR_CODE + '/' + qrcodeId)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch(fetchAdminQrcodes(Filters.OBJ, true))
            dispatch(addToast({ text: getFormattedMessage('qr-code.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const addAdminQrcode = (qrcodes, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true))
    await apiConfig.post(apiBaseURL.QRCODE_ADMIN, qrcodes)
        .then((response) => {
            dispatch({ type: qrcodeActionType.ADD_QRCODE, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('qr-code.success.create.message') }));
            navigate("/app/admin/qr-codes");
            dispatch(addInToTotalRecord(1))
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};
