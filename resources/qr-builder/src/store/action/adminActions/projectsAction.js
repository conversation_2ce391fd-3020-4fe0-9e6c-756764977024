import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, toastType, projectAdminActionType, linksAdminActionType } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord, removeFromTotalRecord, addInToTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { setSavingButton } from "../saveButtonAction";

export const fetchProjects = ( filter = {}, isLoading = true ) => async ( dispatch ) => {
    if ( isLoading ) {
        dispatch( setLoading( true ) )
    }
    let url = apiBaseURL.PROJECT_ADMIN;
    if ( !_.isEmpty( filter ) && ( filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at ) ) {
        url += requestParam( filter );
    }
    apiConfig.get( url )
        .then( ( response ) => {
            dispatch( { type: projectAdminActionType.FETCH_PROJECTS, payload: response.data.data } );
            dispatch( setTotalRecord( response.data.meta.total ) )
            if ( isLoading ) {
                dispatch( setLoading( false ) )
            }
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};

export const DeleteProject = ( id ) => async ( dispatch ) => {
    apiConfig.delete( apiBaseURL.PROJECT_ADMIN + '/' + id )
        .then( ( response ) => {
            dispatch( removeFromTotalRecord( 1 ) );
            dispatch( { type: projectAdminActionType.DELETE_PROJECT, payload: id } );
            dispatch( addToast( { text: getFormattedMessage( 'user.success.delete.message' ) } ) );
        } )
        .catch( ( response ) => {
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};

export const adminProject = ( project ) => async ( dispatch ) => {
    dispatch( setSavingButton( true ) )
    await apiConfig.post( apiBaseURL.ADMIN_PROJECT_COLLECT, {
        tenant_id: project?.value
    } )
        .then( ( response ) => {
            dispatch( { type: projectAdminActionType.ADMIN_PROJECT, payload: response.data.data } );
            dispatch( addInToTotalRecord( 1 ) )
            dispatch( setSavingButton( false ) )
        } )
        .catch( ( response ) => {
            dispatch( setSavingButton( false ) )
            dispatch( addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};


export const adminAddGroup = ( project, navigate ) => async ( dispatch ) => {
    dispatch( setSavingButton( true ) )
    dispatch( setLoading( true ) )
    await apiConfig.post( apiBaseURL.ADMIN_PROJECT_ADD, project )
        .then( ( response ) => {
            dispatch( { type: projectAdminActionType.ADMIN_ADD_PROJECT, payload: response.data.data } );
            dispatch( addToast( { text: getFormattedMessage( 'project.success.create.message' ) } ) );
            navigate( '/app/admin/collections' );
            dispatch( addInToTotalRecord( 1 ) )
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
        } )
        .catch( ( response ) => {
            dispatch( setSavingButton( false ) )
            dispatch( setLoading( false ) )
            dispatch( addToast( { text: response.response.data.message || response.response.data.title, type: toastType.ERROR } ) );
        } );
};
