import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, toastType, transactionsActionsTypes } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';


export const fetchTransactions = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.FETCH_TRANSACTIONS;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: transactionsActionsTypes.FETCH_TRANSACTIONS, payload: response.data.data });
            dispatch(setTotalRecord(response.data.meta.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};


export const fetchTransaction = (id, isLoading = true) => (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.get(apiBaseURL.FETCH_TRANSACTION_BY_ID + "/" + id)
        .then((response) => {
            dispatch({ type: transactionsActionsTypes.FETCH_TRANSACTION_BY_ID, payload: response.data.data });
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}
