import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, toastType, qrCodesAdminActionType, qrcodeActionType } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord, removeFromTotalRecord, addInToTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { setSavingButton } from "../saveButtonAction";

export const fetchAdminQrcodes = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.QRCODE_ADMIN;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    const user_role = localStorage.getItem("user_role")
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: qrCodesAdminActionType.FETCH_QR_CODES, payload: response.data.data });
            dispatch(setTotalRecord(response.data.meta.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            if (user_role === '2') {

            } else {
                dispatch(addToast(
                    { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
            }
        });
};

export const deleteQrCode = (id) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.QRCODE_ADMIN + '/' + id)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({ type: qrCodesAdminActionType.DELETE_QR_CODE, payload: id });
            dispatch(addToast({ text: getFormattedMessage('user.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const adminQrCodeTypes = (qrcodes) => async (dispatch) => {
    dispatch(setSavingButton(true))
    await apiConfig.post(apiBaseURL.ADMIB_QRCODE_TYPES, {
        tenant_id: qrcodes?.value
    })
        .then((response) => {
            dispatch({ type: qrCodesAdminActionType.ADMIN_QR_CODE, payload: response.data.data });
            dispatch(addInToTotalRecord(1))
            dispatch(setSavingButton(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};
