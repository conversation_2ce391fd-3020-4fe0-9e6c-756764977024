import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, toastType, projectAdminActionType, CashPaymentActionType, Filters } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord, removeFromTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { setSavingButton } from "../saveButtonAction";

export const fetchCashPayments = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.CASH_PAYMENTS;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: CashPaymentActionType.CASH_PAYMENTS, payload: response.data.data.data });
            dispatch(setTotalRecord(response.data.data.total));
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const approveCashPayment = (data) => async (dispatch) => {
    apiConfig.post(apiBaseURL.CHANGE_PAYMENT_STATUS, data)
        .then((response) => {
            dispatch(fetchCashPayments(Filters.OBJ))
            dispatch(addToast({ text: getFormattedMessage("cash.payment.status.updated.successfully.message") }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};


export const DeleteProject = (id) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.PROJECT_ADMIN + '/' + id)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({ type: projectAdminActionType.DELETE_PROJECT, payload: id });
            dispatch(addToast({ text: getFormattedMessage('user.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};


export const fetchCashPayment = (id, isLoading = true) => (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.get(apiBaseURL.CASH_PAYMENTS + "/" + id)
        .then((response) => {
            dispatch({ type: CashPaymentActionType.CASH_PAYMENTS_DETAILS, payload: response.data.data });
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}
