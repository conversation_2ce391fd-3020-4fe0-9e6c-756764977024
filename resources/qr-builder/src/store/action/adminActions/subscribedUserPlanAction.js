import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, toastType, projectAdminActionType, CashPaymentActionType, subscribedUserPlansActionTypes } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord, removeFromTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { setSavingButton } from "../saveButtonAction";

export const fetchSubsctibedUserPlan = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.SUBSCRIBED_USER_PLAN;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: subscribedUserPlansActionTypes.SUBSCRIBED_USER_PLAN, payload: response.data.data.data });
            dispatch(setTotalRecord(response.data.data.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const EditSubscribedUserPlanEndDate = (data) => async (dispatch) => {
    apiConfig.post(apiBaseURL.EDIT_SUBSCRIBED_USER_PLAN_END_DATE, data)
        .then((response) => {
            dispatch(fetchSubsctibedUserPlan())
            dispatch({ type: subscribedUserPlansActionTypes.EDIT_SUBSCRIBED_USER_PLAN_END_DATE, payload: response.data.message });
            dispatch(addToast({ text: response.data.message }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.data.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const DeleteProject = (id) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.PROJECT_ADMIN + '/' + id)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({ type: projectAdminActionType.DELETE_PROJECT, payload: id });
            dispatch(addToast({ text: getFormattedMessage('user.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};


export const updatePlanStatus = (id, status, userid) => async (dispatch) => {
    apiConfig.get(apiBaseURL.USER_STATUS_CHANGE + '/' + id + '/' + userid)
        .then((response) => {
            // dispatch({ type: userActionType.EMAIL_VERIFY_USER, payload: response.data });
            // dispatch(fetchUsers(Filters.OBJ))
            status(false)
            dispatch(addToast({ text: getFormattedMessage("status.updated.successfully.message") }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};
