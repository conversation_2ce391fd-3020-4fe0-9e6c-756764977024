import apiConfig from '../../../config/apiConfig';
import { toastType, apiBaseURL, settingActionType } from '../../../constants';
import { addToast } from '../toastAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { callBackAction } from '../callBackAction';
import { fetchFrontSetting } from '../frontSettingAction';
import { setLoading } from '../loadingAction';


export const fetchCacheClear = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.CACHE_CLEAR)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_CACHE_CLEAR, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('settings.clear-cache.success.message') }));
            window.location.reload();
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateMainSetting = (settings, navigate) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.MAIN_SETTINGS, settings)
        .then((response) => {
            dispatch(setLoading(false))
            if (response.data.data.logo) {
                localStorage.setItem("website_logo", response.data.data.logo)
            }
            if (response.data.data.favicon) {
                localStorage.setItem("website_favicon", response.data.data.favicon)
            }
            dispatch(fetchFrontSetting())
            dispatch(fetchMainSetting())
            dispatch({ type: settingActionType.EDIT_MAIN_SETTINGS, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage('settings.updated-setting.success.message') }));
        }).catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchCaptchaSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.CAPTCHA_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_CAPTCHA_SETTING, payload: response.data.data });
            // dispatch(addToast({ text: getFormattedMessage('settings.clear-cache.success.message') }));
            // window.location.reload();
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchMainSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.MAIN_SETTINGS)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_MAIN_SETTINGS, payload: response.data.data });
            // dispatch(addToast({ text: getFormattedMessage('settings.updated-setting.success.message') }));
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};


export const updateCaptchaSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.CAPTCHA_SETTING, data)
        .then((response) => {
            dispatch(setLoading(false))
            dispatch({ type: settingActionType.EDIT_CAPTCHA_SETTINGS, payload: response.data.message });
            dispatch(fetchCaptchaSetting())
            dispatch(addToast({ text: getFormattedMessage("captcha.update.successfully.message") }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updatePaypalSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.PAYPAL_SETTING, data)
        .then((response) => {
            // dispatch({ type: settingActionType.EDIT_PAYPAL_SETTINGS, payload: response.data.message });
            dispatch(setLoading(false))
            dispatch(fetchPaypalSetting())
            dispatch(addToast({ text: getFormattedMessage('settings.paypal.success.message') }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchPaypalSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.PAYPAL_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_PAYPAL_SETTINGS, payload: response.data.data });
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};


export const fetchEmailNotificationSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.EMAIL_NOTIFICATION)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_EMAIL_NOTIFICATION_SETTING, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

// export const updateEmailNotificationSetting = (data) => async (dispatch) => {
//     apiConfig.post(apiBaseURL.EMAIL_NOTIFICATION, data)
//         .then((response) => {
//             // dispatch({ type: settingActionType.EDIT_EMAIL_NOTIFICATION_SETTINGS, payload: response.data.message });
//             // dispatch({ type: settingActionType.FETCH_EMAIL_NOTIFICATION_SETTING, payload: response.data.data });
//             dispatch(fetchEmailNotificationSetting())
//             dispatch(addToast({ text: getFormattedMessage("email.notifications.updated.successfully.message") }));
//         })
//         .catch((response) => {
//             dispatch(addToast(
//                 { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
//         });
// };

export const updateEmailNotificationSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.EMAIL_NOTIFICATION, data)
        .then((response) => {
            dispatch(setLoading(false))
            // dispatch({ type: settingActionType.EDIT_EMAIL_NOTIFICATION_SETTINGS, payload: response.data.message });
            // dispatch({ type: settingActionType.FETCH_EMAIL_NOTIFICATION_SETTING, payload: response.data.data });
            dispatch(fetchEmailNotificationSetting())
            // dispatch(addToast({ text: getFormattedMessage("email.notifications.updated.successfully.message") }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateStripeSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.STRIPE_SETTING, data)
        .then((response) => {
            dispatch(setLoading(false))
            dispatch({ type: settingActionType.EDIT_STRIPE_SETTINGS, payload: response.data.message });
            dispatch(fetchStripeSetting())
            dispatch(addToast({ text: getFormattedMessage('settings.stripe.success.message') }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchStripeSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.STRIPE_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_STRIPE_SETTINGS, payload: response.data.data });
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateCustomStyleSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.CUSTOM_STYLE_SETTING, data)
        .then((response) => {
            dispatch({ type: settingActionType.EDIT_CUSTOM_STYLE_SETTING, payload: response.data.message });
            dispatch(fetchCustomStyleSetting())
            dispatch(setLoading(false))
            dispatch(addToast({ text: getFormattedMessage('settings.custom.style.success.message') }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.title, type: toastType.ERROR }));
        });
};
export const fetchSocialSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.SOCIAL_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_SOCIAL_SETTING, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchCustomStyleSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.CUSTOM_STYLE_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_CUSTOM_STYLE_SETTING, payload: response.data.data });
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateRazorpaySetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.RAZORPAY_SETTING, data)
        .then((response) => {
            dispatch(setLoading(false))
            dispatch({ type: settingActionType.EDIT_RAZORPAY_SETTINGS, payload: response.data.message });
            dispatch(fetchRazorpaySetting())
            dispatch(addToast({ text: getFormattedMessage('settings.razorpay.success.message') }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateSocialsSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.SOCIAL_SETTING, data)
        .then((response) => {
            dispatch({ type: settingActionType.EDIT_SOCIAL_SETTING, payload: response.data.message });
            dispatch(setLoading(false))
            dispatch(fetchSocialSetting())
            dispatch(addToast({ text: getFormattedMessage("update.socials.settings.successfully.message") }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchRazorpaySetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.RAZORPAY_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_RAZORPAY_SETTING, payload: response.data.data });
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};
export const fetchAdsSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.ADS_SETTINGS)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_ADS_SETTINGS, payload: response.data.data })
        })
        .catch((response) => {
            dispatch(addToast({ text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateAdsSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.ADS_SETTINGS, data)
        .then((response) => {
            dispatch({ type: settingActionType.EDIT_ADS_SETTINGS, payload: response.data.message });
            dispatch(setLoading(false))
            dispatch(fetchAdsSetting())
            dispatch(addToast({ text: getFormattedMessage("update.ads.Settings.successfully.message") }));
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast({ text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchSmtpSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.SMPT_MAIL_SETTINGS)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_SMTP_MAIL_SETTINGS, payload: response.data.data })
        })
        .catch((response) => {
            dispatch(addToast({ text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateSmtpSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.SMPT_MAIL_SETTINGS, data)
        .then((response) => {
            dispatch(setLoading(false))
            // dispatch({ type: settingActionType.EDIT_SMTP_MAIL_SETTINGS, payload: response.data.message });
            dispatch(addToast({ text: getFormattedMessage("Update.SMTP.Mail.Settings.successfully.message") }));
            dispatch(fetchSmtpSetting())
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast({ text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchAnnouncementSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.ANNOUNCEMENT_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_ANNOUNCEMENT_SETTING, payload: response.data.data });
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateAnnouncementSetting = (data) => async (dispatch) => {
    apiConfig.post(apiBaseURL.ANNOUNCEMENT_SETTING, data)
        .then((response) => {
            dispatch({ type: settingActionType.EDIT_ANNOUNCEMENT_SETTING, payload: response.data.message });
            dispatch(addToast({ text: getFormattedMessage('settings.announcement.success.message') }));
            dispatch(fetchAnnouncementSetting())
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchSocialGoogleLoginSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.SOCIAL_GOOGLE_LOGIN_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_GOOGLE_LOGIN_SETTING, payload: response.data.data });
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateSocialsGoogleLoginSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.SOCIAL_GOOGLE_LOGIN_SETTING, data)
        .then((response) => {
            dispatch({ type: settingActionType.EDIT_GOOGLE_LOGIN_SETTING, payload: response.data.message });
            dispatch(setLoading(false))
            dispatch(addToast({ text: getFormattedMessage("Social.Login.updated.successfully.message") }));
            dispatch(fetchSocialGoogleLoginSetting())
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const fetchSocialFacebookLoginSetting = () => async (dispatch) => {
    apiConfig.get(apiBaseURL.SOCIAL_FACEBOOK_LOGIN_SETTING)
        .then((response) => {
            dispatch({ type: settingActionType.FETCH_FACRBOOK_LOGIN_SETTING, payload: response.data.data });
        }).catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

export const updateSocialsFacebookLoginSetting = (data) => async (dispatch) => {
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.SOCIAL_FACEBOOK_LOGIN_SETTING, data)
        .then((response) => {
            dispatch(setLoading(false))
            dispatch({ type: settingActionType.EDIT_FACRBOOK_LOGIN_SETTING, payload: response.data.message });
            // dispatch(addToast({ text: 'Update Socials Login Settings Successfully' }));
            dispatch(fetchSocialFacebookLoginSetting())
        })
        .catch((response) => {
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};



