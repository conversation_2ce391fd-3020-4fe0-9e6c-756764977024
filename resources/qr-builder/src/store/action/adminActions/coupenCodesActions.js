import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, Filters, couponCodesActi, couponCodesActionType, toastType } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { setSavingButton } from "../saveButtonAction";

export const fetchCodes = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.COUPON_CODES;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: couponCodesActionType.FETCH_CODES, payload: response.data.data });
            dispatch(setTotalRecord(response.data.meta.total));
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.title || response.response.data.message, type: toastType.ERROR }));
        });
};

export const fetchCode = (planId, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    apiConfig.get(apiBaseURL.COUPON_CODES + '/' + planId)
        .then((response) => {
            dispatch({ type: couponCodesActionType.FETCH_CODE, payload: response.data.data })
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
}

export const addCode = (plans, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true))
    await apiConfig.post(apiBaseURL.COUPON_CODES, plans)
        .then((response) => {
            dispatch({ type: couponCodesActionType.ADD_CODE, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage("coupon.added.successfully.message") }));
            navigate('/app/admin/codes');
            dispatch(setLoading(false))
            // dispatch(addInToTotalRecord(1))
            // dispatch(setSavingButton(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.title || response.response.data.message, type: toastType.ERROR }));
        });
};

export const editCode = (planId, plans, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true))
    dispatch(setLoading(true))
    apiConfig.post(apiBaseURL.EDIT_COUPON_CODES + '/' + planId, plans)
        .then((response) => {
            dispatch({ type: couponCodesActionType.EDIT_CODE, payload: response.data.data });
            dispatch(addToast({ text: getFormattedMessage("coupon.code.updated.successfully.message") }));
            navigate('/app/admin/codes')
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
        })
        .catch((response) => {
            dispatch(setSavingButton(false))
            dispatch(setLoading(false))
            dispatch(addToast(
                { text: response.response.data.title || response.response.data.message, type: toastType.ERROR }));
        });
};

export const deleteCode = (planId) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.COUPON_CODES + '/' + planId)
        .then((response) => {
            dispatch({ type: couponCodesActionType.DELETE_CODE, payload: planId });
            dispatch(addToast({ text: getFormattedMessage('coupon.success.delete.message') }));
            dispatch(fetchCodes(Filters.OBJ, true))
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.title || response.response.data.message, type: toastType.ERROR }));
        });
};

