import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, Filters, pagesActionType, plansActionType, toastType } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { setSavingButton } from "../saveButtonAction";


export const fetchFrontPage = (slug) => async (dispatch) => {
    apiConfig.get(apiBaseURL.FRONT_PAGE + '/' + slug)
        .then((response) => {
            dispatch({ type: pagesActionType.FRONT_FETCH_PAGE, payload: response.data.data })
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
}
