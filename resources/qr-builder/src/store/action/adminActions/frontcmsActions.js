import apiConfig from "../../../config/apiConfig";
import {
    toastType,
    apiBaseURL,
    frontCmsActionTyps,
    Tokens,
} from "../../../constants";
import { cssHandling } from "../../../cssHandling/cssHandling";
import requestParam from "../../../shared/requestParam";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import { setLoading } from "../loadingAction";
import { addToast } from "../toastAction";
import { removeFromTotalRecord, setTotalRecord } from "../totalRecordAction";

export const fetchFrontCms = (update) => async (dispatch) => {
    apiConfig
        .get(apiBaseURL.FETCH_FRONT_CMS)
        .then((response) => {
            const updatedLanguage = localStorage.getItem(
                Tokens.UPDATED_LANGUAGE
            );
            dispatch({
                type: frontCmsActionTyps.FETCH_FRONT_CMS,
                payload: response.data.data,
            });
            response.data.data &&
                cssHandling(updatedLanguage, response.data.data, update);
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data?.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const updateFrontCms = (data, navigate) => async (dispatch) => {
    dispatch(setLoading(true));
    apiConfig
        .post(apiBaseURL.UPDATE_FRONT_CMS, data)
        .then((response) => {
            const update = true;
            dispatch(setLoading(false));
            dispatch(fetchFrontCms(update));
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "front.CMS.updated.successfully.message"
                    ),
                })
            );
        })
        .catch((response) => {
            dispatch(setLoading(false));
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchFrontFeatures =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.FETCH_FRONT_FEATURES;
        if (
            !_.isEmpty(filter) &&
            (filter.page ||
                filter.pageSize ||
                filter.search ||
                filter.order_By ||
                filter.created_at)
        ) {
            url += requestParam(filter);
        }
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_FEATURES,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const fetchHomeFrontFeatures =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.FETCH_FRONT_FEATURES;
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_FEATURES,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const updateFrontFeatures =
    (data, handleClose, subTitaledata) => async (dispatch) => {
        dispatch(setLoading(true));
        apiConfig
            .post(apiBaseURL.UPDATE_FRONT_FEATURES, data)
            .then((response) => {
                // handleClose()
                dispatch(
                    updateFrontFeaturesSubTitele(
                        response.data.data,
                        subTitaledata,
                        handleClose
                    )
                );
                dispatch(setLoading(false));
                dispatch(fetchFrontFeatures());
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "feature.updated.successfully.message"
                        ),
                    })
                );
            })
            .catch((response) => {
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const updateFrontFeaturesSubTitele =
    (responses, subTitaledata) => async (dispatch) => {
        let dataPreper = {
            id: responses.id,
            title: responses.title,
            sub_title: subTitaledata,
            description: responses.description,
            image_url: responses.image_url,
        };
        dispatch(setLoading(true));
        apiConfig
            .post(apiBaseURL.UPDATE_FRONT_FEATURES, dataPreper)
            .then((response) => {
                dispatch(setLoading(false));
                handleClose();
            })
            .catch((response) => {
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const fetchFrontSubFeatures =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.FETCH_FRONT_SUB_FEATURES;
        if (
            !_.isEmpty(filter) &&
            (filter.page ||
                filter.pageSize ||
                filter.search ||
                filter.order_By ||
                filter.created_at)
        ) {
            url += requestParam(filter);
        }
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_SUB_FEATURES,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const fetchHomeFrontSubFeatures =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.FETCH_FRONT_SUB_FEATURES;
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_SUB_FEATURES,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const updateFrontSubFeatures = (data) => async (dispatch) => {
    dispatch(setLoading(true));
    apiConfig
        .post(apiBaseURL.UPDATE_FRONT_SUB_FEATURES, data)
        .then((response) => {
            dispatch(setLoading(false));
            dispatch(fetchFrontSubFeatures());
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "sub.feature.updated.successfully.message"
                    ),
                })
            );
        })
        .catch((response) => {
            dispatch(setLoading(false));
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchAllPlans = () => async (dispatch) => {
    apiConfig
        .get(apiBaseURL.FETCH_ALL_PLANS)
        .then((response) => {
            dispatch({
                type: frontCmsActionTyps.FETCH_ALL_PLANS,
                payload: response.data.data,
            });
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const addSubscribeUser = (data, navigate, link) => async (dispatch) => {
    apiConfig
        .post(apiBaseURL.ADD_SUBSCRIBE, data)
        .then((response) => {
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "subscribed.successfully.message"
                    ),
                })
            );
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchTermsAndCondition = () => async (dispatch) => {
    apiConfig
        .get(apiBaseURL.FETCH_TERM_CONDITIOMS)
        .then((response) => {
            dispatch({
                type: frontCmsActionTyps.FETCH_FRONT_TREM_CONDITION,
                payload: response.data.data,
            });
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchTermsAndConditionData = () => async (dispatch) => {
    apiConfig
        .get(apiBaseURL.FETCH_TERM_CONDITIOMS)
        .then((response) => {
            dispatch({
                type: frontCmsActionTyps.FETCH_FRONT_TREM_CONDITION,
                payload: response.data.data,
            });
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const updateTermsAndCondition = (data, type) => async (dispatch) => {
    dispatch(setLoading(true));
    apiConfig
        .post(apiBaseURL.TERM_CONDITIOMS, data)
        .then((response) => {
            dispatch(setLoading(false));
            type === 1
                ? dispatch(
                      addToast({
                          text: getFormattedMessage(
                              "terms.conditions.are.updated.successfully.message"
                          ),
                      })
                  )
                : dispatch(
                      addToast({
                          text: getFormattedMessage(
                              "privacy-policy.are.updated.successfully.message"
                          ),
                      })
                  );

            dispatch(fetchTermsAndConditionData());
        })
        .catch((response) => {
            dispatch(setLoading(false));
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const addContactInfo =
    (data, captchaRef, setIsLoding) => async (dispatch) => {
        dispatch(setLoading(true));
        apiConfig
            .post(apiBaseURL.CONTACT_INFO, data)
            .then((response) => {
                dispatch(setLoading(false));
                // captchaRef && captchaRef.current.reset();
                setIsLoding(false);
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "message.sent.successfully.message"
                        ),
                    })
                );
            })
            .catch((response) => {
                dispatch(setLoading(false));
                // captchaRef && captchaRef.current.reset();
                setIsLoding(false);
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const fetchFrontEnquiries =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.ADMIN_CONTACT_INFO;
        if (
            !_.isEmpty(filter) &&
            (filter.page ||
                filter.pageSize ||
                filter.search ||
                filter.order_By ||
                filter.created_at)
        ) {
            url += requestParam(filter);
        }
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_ENQUIRIES,
                    payload: response.data.data.data,
                });
                dispatch(setTotalRecord(response.data.data.total));
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const deleteFrontEnquiries = (id) => async (dispatch) => {
    apiConfig
        .post(apiBaseURL.ADMIN_DELETE_CONTACT_INFO, { id })
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({
                type: frontCmsActionTyps.DELETE_FRONT_ENQUIRIES,
                payload: id,
            });
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "enquiries.success.delete.message"
                    ),
                })
            );
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response?.response?.data?.message ||
                        response?.response?.data?.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchFrontEnquirie =
    (id, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        apiConfig
            .get(apiBaseURL.ADMIN_CONTACT_INFO_DETAILS + "/" + id)
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_ENQUIRIES,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response?.response?.data?.message ||
                            response?.response?.data?.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const fetchFrontQrCodeTypes =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.FETCH_FRONT_QR_CODE_TYPES;
        if (
            !_.isEmpty(filter) &&
            (filter.page ||
                filter.pageSize ||
                filter.search ||
                filter.order_By ||
                filter.created_at)
        ) {
            url += requestParam(filter);
        }
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_QR_CODE_TYPES,
                    payload: response.data.data,
                });
                dispatch(setTotalRecord(response.data.meta.total));
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const updateFrontQrCodeType =
    (data, handleClose) => async (dispatch) => {
        dispatch(setLoading(true));
        apiConfig
            .post(apiBaseURL.UPDATE_FRONT_QR_CODE_TYPES, data)
            .then((response) => {
                handleClose();
                dispatch(fetchFrontQrCodeTypes());
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "qr-code-type.updated.successfully.message"
                        ),
                    })
                );
            })
            .catch((response) => {
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const fetchAllFrontQrCodeTypes =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        let url = apiBaseURL.FETCH_FRONT_QR_CODE_TYPES;
        apiConfig
            .get(url + "?page[size]=0")
            .then((response) => {
                dispatch({
                    type: frontCmsActionTyps.FETCH_FRONT_QR_CODE_TYPES,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };
