import apiConfig from '../../../config/apiConfig';
import { apiBaseURL, toastType, linksAdminActionType, dashboardAdminActionType } from '../../../constants';
import requestParam from '../../../shared/requestParam';
import { addToast } from '../toastAction'
import { setTotalRecord, removeFromTotalRecord } from '../totalRecordAction';
import { setLoading } from '../loadingAction';
import { getFormattedMessage } from '../../../shared/sharedMethod';
import { setSavingButton } from "../saveButtonAction";

export const fetchDashboardDetails = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.ADMIN_DASHBOARD;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            response && dispatch({ type: dashboardAdminActionType.FETCH_DASHBOARD_DETAILS, payload: response.data.data });
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
};

export const getWeeklyEarningAction = () => (dispatch) => {
    apiConfig.get(apiBaseURL.DASHBOARD_WEEK_EARNING)
        .then((response) => {
            response && dispatch({ type: dashboardAdminActionType.WEEKLY_EARNINGS, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
}

export const getTopVisitedLinkAdminAction = () => (dispatch) => {
    apiConfig.get(apiBaseURL.TOP_VISITED_LINK_ADMIN)
        .then((response) => {
            response && dispatch({ type: dashboardAdminActionType.TOP_VISITED_LINK_ADMIN, payload: response.data.data });
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
}

