import apiConfig from "../../../config/apiConfig";
import {
    apiBaseURL,
    Filters,
    pagesActionType,
    plansActionType,
    toastType,
} from "../../../constants";
import requestParam from "../../../shared/requestParam";
import { addToast } from "../toastAction";
import {
    setTotalRecord,
    addInToTotalRecord,
    removeFromTotalRecord,
} from "../totalRecordAction";
import { setLoading } from "../loadingAction";
import { getFormattedMessage } from "../../../shared/sharedMethod";
import { setSavingButton } from "../saveButtonAction";

export const fetchPages =
    (filter = {}, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        let url = apiBaseURL.PAGES;
        if (
            !_.isEmpty(filter) &&
            (filter.page ||
                filter.pageSize ||
                filter.search ||
                filter.order_By ||
                filter.created_at)
        ) {
            url += requestParam(filter);
        }
        apiConfig
            .get(url)
            .then((response) => {
                dispatch({
                    type: pagesActionType.FETCH_PAGES,
                    payload: response.data.data,
                });
                dispatch(setTotalRecord(response.data.meta.total));
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const fetchPage =
    (planId, isLoading = true) =>
    async (dispatch) => {
        if (isLoading) {
            dispatch(setLoading(true));
        }
        apiConfig
            .get(apiBaseURL.PAGES + "/" + planId)
            .then((response) => {
                dispatch({
                    type: pagesActionType.FETCH_PAGE,
                    payload: response.data.data,
                });
                if (isLoading) {
                    dispatch(setLoading(false));
                }
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const addPageAction = (page, navigate) => async (dispatch) => {
    dispatch(setSavingButton(true));
    dispatch(setLoading(true));
    await apiConfig
        .post(apiBaseURL.PAGES, page)
        .then((response) => {
            // dispatch({ type: pagesActionType.ADD_PAGE, payload: response.data.data });
            dispatch(
                addToast({
                    text: getFormattedMessage("page.success.create.message"),
                })
            );
            dispatch(fetchPages(Filters.OBJ, true));
            navigate("/app/admin/custom-pages");
            dispatch(addInToTotalRecord(1));
            dispatch(setSavingButton(false));
            dispatch(setLoading(false));
        })
        .catch((response) => {
            dispatch(setSavingButton(false));
            dispatch(setLoading(false));
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const editPage =
    (planId, plans, navigate, isVisibility) => async (dispatch) => {
        dispatch(setSavingButton(true));
        dispatch(setLoading(true));
        apiConfig
            .post(apiBaseURL.UPDATE_PAGE + "/" + planId, plans)
            .then((response) => {
                // dispatch({ type: plansActionType.EDIT_PLAN, payload: response.data.data });
                dispatch(fetchPages(Filters.OBJ, true));
                dispatch(setLoading(false));
                if (isVisibility === true) {
                    dispatch(
                        addToast({
                            text: getFormattedMessage(
                                "page.visibility.success.edit.message"
                            ),
                        })
                    );
                } else {
                    dispatch(
                        addToast({
                            text: getFormattedMessage(
                                "page.success.edit.message"
                            ),
                        })
                    );
                }
                navigate("/app/admin/custom-pages");
                dispatch(setSavingButton(false));
            })
            .catch((response) => {
                dispatch(setSavingButton(false));
                dispatch(setLoading(false));
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const deletePage = (planId) => async (dispatch) => {
    apiConfig
        .delete(apiBaseURL.PAGES + "/" + planId)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch(fetchPages(Filters.OBJ, true));
            // dispatch({ type: plansActionType.DELETE_PLAN, payload: planId });
            dispatch(
                addToast({
                    text: getFormattedMessage("page.success.delete.message"),
                })
            );
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const fetchFrontPages = () => async (dispatch) => {
    let url = apiBaseURL.FRONT_PAGES;
    apiConfig
        .get(url + "?page[size]=0")
        .then((response) => {
            dispatch({
                type: pagesActionType.FETCH_PAGES,
                payload: response.data.data,
            });
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};
