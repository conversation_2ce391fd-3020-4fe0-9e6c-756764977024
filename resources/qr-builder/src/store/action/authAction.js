import apiConfig from "../../config/apiConfig";
import { authActionType, Tokens, toastType, apiBaseURL } from "../../constants";
import { addToast } from "./toastAction";
import { setLanguage } from "./changeLanguageAction";
import { getFormattedMessage } from "../../shared/sharedMethod";
import { cssHandling } from "../../cssHandling/cssHandling";
import { fetchConfig } from "./configAction";
import { setLoading } from "./loadingAction";

export const loginAction =
    (user, navigate, setLoading, captchaRef) => async (dispatch) => {
        await apiConfig
            .post("login", user)
            .then((response) => {
                if (response.data.data.user.status === false) {
                    dispatch(
                        addToast({
                            text: getFormattedMessage(
                                "Your.account.is.currently.disabled.please.contact.administrator.message"
                            ),
                            type: toastType.ERROR,
                        })
                    );
                    setLoading(false);
                    captchaRef && captchaRef.current.reset();
                } else if (response.data.data.user.email_verified_at === null) {
                    localStorage.setItem(
                        Tokens.ADMIN,
                        response.data.data.token
                    );
                    localStorage.setItem(
                        Tokens.EMAIL_VERIFY,
                        response.data.data.user.email_verified_at
                    );
                    dispatch(
                        addToast({
                            text: "Please verify your email.",
                            type: toastType.ERROR,
                        })
                    );
                    // navigate("/app/reset-password");
                    setLoading(false);
                } else {
                    localStorage.setItem(
                        Tokens.ADMIN,
                        response.data.data.token
                    );
                    localStorage.setItem(
                        Tokens.USER,
                        response.data.data.user.email
                    );
                    localStorage.setItem(
                        Tokens.IMAGE,
                        response.data.data.user.image_url
                    );
                    localStorage.setItem(
                        Tokens.FIRST_NAME,
                        response.data.data.user.name
                    );
                    localStorage.setItem(
                        Tokens.USER_ROLE,
                        response.data.data.user.id
                    );
                    localStorage.setItem(
                        Tokens.LANGUAGE,
                        response.data.data.user.language
                    );
                    localStorage.setItem(
                        Tokens.LAST_NAME,
                        response.data.data.user.name
                    );
                    localStorage.setItem(
                        "loginUserArray",
                        JSON.stringify(response.data.data.user)
                    );
                    localStorage.setItem(
                        "isDarkMod",
                        localStorage.getItem("isDarkMod")
                    );
                    dispatch({
                        type: authActionType.LOGIN_USER,
                        payload: response.data.data,
                    });
                    const user_role = localStorage.getItem("user_role");
                    dispatch(setLanguage(response.data.data.user.language));
                    dispatch(fetchConfig());
                    localStorage.setItem(
                        Tokens.UPDATED_LANGUAGE,
                        response.data.data.user.language
                    );
                    if (user_role === "1") {
                        localStorage.setItem("isAdmin", "true");
                        navigate("/app/admin/dashboard");
                    } else {
                        if (localStorage.getItem(Tokens.QRCODE_DETAILS)) {
                            localStorage.setItem("isAdmin", "false");
                            navigate("/app/qrcode/create");
                        } else if (
                            JSON.parse(localStorage.getItem("choosePlan")) ===
                            true
                        ) {
                            localStorage.setItem("isAdmin", "false");
                            localStorage.removeItem("choosePlan");
                            navigate("/app/upgrade-plan");
                        } else {
                            localStorage.setItem("isAdmin", "false");
                            navigate("/app/dashboard");
                        }
                    }
                    dispatch(
                        addToast({
                            text: getFormattedMessage("login.success.message"),
                        })
                    );
                    setLoading(false);
                }
            })
            .catch(({ response }) => {
                dispatch(
                    addToast({
                        text: response.data.message || response.data.title,
                        type: toastType.ERROR,
                    })
                );
                setLoading(false);
                captchaRef && captchaRef.current.reset();
            });
    };

export const logoutAction = (token, navigate) => async (dispatch) => {
    await apiConfig
        .post("logout", token)
        .then(() => {
            localStorage.removeItem(Tokens.ADMIN);
            localStorage.removeItem(Tokens.USER);
            localStorage.removeItem(Tokens.IMAGE);
            localStorage.removeItem(Tokens.FIRST_NAME);
            localStorage.removeItem(Tokens.LAST_NAME);
            localStorage.removeItem(Tokens.USER_ROLE);
            localStorage.removeItem("loginUserArray");
            localStorage.removeItem("isAdmin");
            localStorage.removeItem(Tokens.UPDATED_EMAIL);
            localStorage.removeItem(Tokens.UPDATED_FIRST_NAME);
            localStorage.removeItem(Tokens.UPDATED_LAST_NAME);
            localStorage.removeItem(Tokens.USER_IMAGE_URL);
            navigate("/login");
            dispatch(
                addToast({
                    text: getFormattedMessage("logout.success.message"),
                })
            );
            cssHandling(localStorage.getItem("updated_language"));
        })
        .catch((response) => {
            dispatch(
                addToast({
                    text:
                        response.response.data.message ||
                        response.response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const forgotPassword = (user) => async (dispatch) => {
    await apiConfig
        .post(apiBaseURL.ADMIN_FORGOT_PASSWORD, user)
        .then((response) => {
            dispatch({
                type: authActionType.ADMIN_FORGOT_PASSWORD,
                payload: response.data.message,
            });
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "forgot-password-form.success.reset-link.label"
                    ),
                })
            );
        })
        .catch(({ response }) => {
            dispatch({ type: toastType.ERROR, payload: response.data.message });
            dispatch(
                addToast({
                    text: response?.data?.message || response?.data?.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const resetPassword =
    (user, navigate, setDisabled) => async (dispatch) => {
        await apiConfig
            .post(apiBaseURL.ADMIN_RESET_PASSWORD, user)
            .then((response) => {
                dispatch({
                    type: authActionType.ADMIN_RESET_PASSWORD,
                    payload: user,
                });
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "reset-password.success.update.message"
                        ),
                    })
                );
                navigate("/login");
            })
            .catch(({ response }) => {
                setDisabled(false);
                dispatch(
                    addToast({
                        text: response.data.message || response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const userRegister =
    (user, navigate, setLoading, captchaRef) => async (dispatch) => {
        await apiConfig
            .post(apiBaseURL.REGISTER_USER, user)
            .then((response) => {
                dispatch({
                    type: authActionType.REGISTER_USER,
                    payload: response.data.message,
                });
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "registration.success.message"
                        ),
                    })
                );
                navigate("/login");
                setLoading(false);
                captchaRef && captchaRef.current.reset();
            })
            .catch(({ response }) => {
                dispatch({
                    type: toastType.ERROR,
                    payload: response.data.message,
                });
                dispatch(
                    addToast({
                        text: response.data.message || response.data.title,
                        type: toastType.ERROR,
                    })
                );
                setLoading(false);
                captchaRef && captchaRef.current.reset();
            });
    };

export const resetLoginPassword = (user, navigate) => async (dispatch) => {
    await apiConfig
        .post(apiBaseURL.LOGIN_PASSWORD_CHANGE, user)
        .then((response) => {
            localStorage.removeItem(Tokens.ADMIN);
            dispatch({
                type: authActionType.CHANGE_LOGIN_PASSWORD,
                payload: user,
            });
            dispatch(
                addToast({
                    text: getFormattedMessage(
                        "reset-password.success.update.message"
                    ),
                })
            );
            navigate("/app/dashboard");
        })
        .catch(({ response }) => {
            dispatch(
                addToast({
                    text: response.data.message || response.data.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const deleteAccountAction =
    (data, onDeleteAccount, navigate) => async (dispatch) => {
        await apiConfig
            .post("delete-account", data)
            .then((response) => {
                localStorage.removeItem(Tokens.ADMIN);
                localStorage.removeItem(Tokens.USER);
                localStorage.removeItem(Tokens.IMAGE);
                localStorage.removeItem(Tokens.FIRST_NAME);
                localStorage.removeItem(Tokens.LAST_NAME);
                localStorage.removeItem(Tokens.USER_ROLE);
                localStorage.removeItem("loginUserArray");
                localStorage.removeItem("isAdmin");
                localStorage.removeItem(Tokens.UPDATED_EMAIL);
                localStorage.removeItem(Tokens.UPDATED_FIRST_NAME);
                localStorage.removeItem(Tokens.UPDATED_LAST_NAME);
                localStorage.removeItem(Tokens.USER_IMAGE_URL);
                navigate("/login");
                onDeleteAccount();
                dispatch(
                    addToast({
                        text: response.data.message,
                        type: toastType.ERROR,
                    })
                );
            })
            .catch((response) => {
                dispatch(
                    addToast({
                        text:
                            response.response.data.message ||
                            response.response.data.title,
                        type: toastType.ERROR,
                    })
                );
            });
    };

export const loginViaGoogleAction = (data, navigate) => async (dispatch) => {
    await apiConfig
        .post(apiBaseURL.LOGIN_VIA_GOOGLE, data)
        .then((response) => {
            if (response.data.data.user.status === false) {
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "Your.account.is.currently.disabled.please.contact.administrator.message"
                        ),
                        type: toastType.ERROR,
                    })
                );
            } else if (response.data.data.user.email_verified_at === null) {
                localStorage.setItem(Tokens.ADMIN, response.data.data.token);
                navigate("/app/reset-password");
            } else {
                localStorage.setItem(Tokens.ADMIN, response.data.data.token);
                localStorage.setItem(
                    Tokens.USER,
                    response.data.data.user.email
                );
                localStorage.setItem(
                    Tokens.IMAGE,
                    response.data.data.user.image_url
                );
                localStorage.setItem(
                    Tokens.FIRST_NAME,
                    response.data.data.user.name
                );
                localStorage.setItem(
                    Tokens.USER_ROLE,
                    response.data.data.user.id
                );
                localStorage.setItem(
                    Tokens.LANGUAGE,
                    response.data.data.user.language
                );
                localStorage.setItem(
                    Tokens.LAST_NAME,
                    response.data.data.user.name
                );
                localStorage.setItem(
                    "loginUserArray",
                    JSON.stringify(response.data.data.user)
                );
                dispatch({
                    type: authActionType.LOGIN_USER,
                    payload: response.data.data,
                });
                const user_role = localStorage.getItem("user_role");
                dispatch(setLanguage(response.data.data.user.language));
                localStorage.setItem(
                    Tokens.UPDATED_LANGUAGE,
                    response.data.data.user.language
                );
                if (user_role === "1") {
                    localStorage.setItem("isAdmin", "true");
                    navigate("/app/admin/dashboard");
                } else {
                    if (localStorage.getItem(Tokens.QRCODE_DETAILS)) {
                        localStorage.setItem("isAdmin", "false");
                        navigate("/app/qrcode/create");
                    } else {
                        localStorage.setItem("isAdmin", "false");
                        navigate("/app/dashboard");
                    }
                }
                dispatch(
                    addToast({
                        text: getFormattedMessage("login.success.message"),
                    })
                );
            }
        })
        .catch(({ response }) => {
            dispatch(
                addToast({
                    text: response?.data?.message || response?.data?.title,
                    type: toastType.ERROR,
                })
            );
        });
};

export const loginViaFacebookAction = (data, navigate) => async (dispatch) => {
    await apiConfig
        .post(apiBaseURL.LOGIN_VIA_FACEBOOK, data)
        .then((response) => {
            if (response.data.data.user.status === false) {
                dispatch(
                    addToast({
                        text: getFormattedMessage(
                            "Your.account.is.currently.disabled.please.contact.administrator.message"
                        ),
                        type: toastType.ERROR,
                    })
                );
                setLoading(false);
            } else if (response.data.data.user.email_verified_at === null) {
                localStorage.setItem(Tokens.ADMIN, response.data.data.token);
                navigate("/app/reset-password");
            } else {
                localStorage.setItem(Tokens.ADMIN, response.data.data.token);
                localStorage.setItem(
                    Tokens.USER,
                    response.data.data.user.email
                );
                localStorage.setItem(
                    Tokens.IMAGE,
                    response.data.data.user.image_url
                );
                localStorage.setItem(
                    Tokens.FIRST_NAME,
                    response.data.data.user.name
                );
                localStorage.setItem(
                    Tokens.USER_ROLE,
                    response.data.data.user.id
                );
                localStorage.setItem(
                    Tokens.LANGUAGE,
                    response.data.data.user.language
                );
                localStorage.setItem(
                    Tokens.LAST_NAME,
                    response.data.data.user.name
                );
                localStorage.setItem(
                    "loginUserArray",
                    JSON.stringify(response.data.data.user)
                );
                dispatch({
                    type: authActionType.LOGIN_USER,
                    payload: response.data.data,
                });
                const user_role = localStorage.getItem("user_role");
                dispatch(setLanguage(response.data.data.user.language));
                localStorage.setItem(
                    Tokens.UPDATED_LANGUAGE,
                    response.data.data.user.language
                );
                if (user_role === "1") {
                    localStorage.setItem("isAdmin", "true");
                    navigate("/app/admin/dashboard");
                } else {
                    if (localStorage.getItem(Tokens.QRCODE_DETAILS)) {
                        localStorage.setItem("isAdmin", "false");
                        navigate("/app/qrcode/create");
                    } else {
                        localStorage.setItem("isAdmin", "false");
                        navigate("/app/dashboard");
                    }
                }
                dispatch(
                    addToast({
                        text: getFormattedMessage("login.success.message"),
                    })
                );
            }
        })
        .catch(({ response }) => {
            dispatch(
                addToast({
                    text: response?.data?.message || response?.data?.title,
                    type: toastType.ERROR,
                })
            );
            // setLoading(false);
        });
};
