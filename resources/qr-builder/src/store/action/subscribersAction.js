import apiConfig from '../../config/apiConfig';
import { apiBaseURL, Filters, subscribersActionType, toastType } from '../../constants';
import requestParam from '../../shared/requestParam';
import { addToast } from './toastAction'
import { setTotalRecord, addInToTotalRecord, removeFromTotalRecord } from './totalRecordAction';
import { setLoading } from './loadingAction';
import { getFormattedMessage } from '../../shared/sharedMethod';
import { setSavingButton } from "./saveButtonAction";


export const fetchSubscribers = (filter = {}, isLoading = true) => async (dispatch) => {
    if (isLoading) {
        dispatch(setLoading(true))
    }
    let url = apiBaseURL.SUBSCRIBERS;
    if (!_.isEmpty(filter) && (filter.page || filter.pageSize || filter.search || filter.order_By || filter.created_at)) {
        url += requestParam(filter);
    }
    apiConfig.get(url)
        .then((response) => {
            dispatch({ type: subscribersActionType.FETCH_SUBSCRIBERS, payload: response.data.data.data });
            dispatch(setTotalRecord(response.data.data.total))
            if (isLoading) {
                dispatch(setLoading(false))
            }
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response?.response?.data?.message || response?.response?.data?.title, type: toastType.ERROR }));
        });
};


export const deleteSubscriber= (id) => async (dispatch) => {
    apiConfig.delete(apiBaseURL.SUBSCRIBERS + '/' + id)
        .then((response) => {
            dispatch(removeFromTotalRecord(1));
            dispatch({ type: subscribersActionType.DELETE_SUBSCRIBER, payload: id });
            dispatch(addToast({ text: getFormattedMessage('subscriber.success.delete.message') }));
        })
        .catch((response) => {
            dispatch(addToast(
                { text: response.response.data.message || response.response.data.title, type: toastType.ERROR }));
        });
};

