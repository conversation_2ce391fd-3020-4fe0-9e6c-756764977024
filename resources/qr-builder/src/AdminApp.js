import React, { useEffect, useState } from 'react';
import { Route, Navigate, Routes } from 'react-router-dom';
import '../../qr-builder/src/assets/sass/style.react.scss';
import { Tokens } from "./constants";
import { ProtectedRoute } from "./shared/sharedMethod";
import { route } from "./routes";
import WindowSpinner from './shared/components/loaders/WindowSpinner';

function AdminApp() {

    const [loading, setLoading] = useState(false)

    const token = localStorage.getItem(Tokens.ADMIN);

    const prepareRoutes = () => {
        let filterRoutes = [];
        route.forEach((route) => {
            if (route.permission === '') {
                filterRoutes.push(route)
            }
        });
        return filterRoutes;
    };

    useEffect(() => {
        const auth_token = localStorage.getItem("auth_token")
        if (!loading) {
            setLoading(true)
        }
    }, [])

    useEffect(() => {
        if (loading) {
            setTimeout(() => {
                setLoading(false)
            }, 1500)
        }
    }, [loading])

    const routes = prepareRoutes();

    return (
        loading
            ?
            <WindowSpinner />
            :
            <Routes>
                {routes.map((route, index) => {
                    return route.ele ? (
                        <Route key={index} exact={true} path={route.path} element={token ? <ProtectedRoute>
                            {route.ele}
                        </ProtectedRoute> :
                            <Navigate replace to={"login"} />
                        } />
                    ) : (null)
                })}
                <Route path='*' element={<Navigate replace to={"/"} />} />
            </Routes>
    )
}

export default AdminApp;
