document.addEventListener('DOMContentLoaded', langDropdown);

function langDropdown() {
    if (!$('.dropdown1').length) {
        return
    }
    $('.dropdown1').hover(function () {
        $(this).find('.dropdown-menu').stop(true, true).delay(100).fadeIn(100);
    }, function () {
        $(this).find('.dropdown-menu').stop(true, true).delay(100).fadeOut(100);
    });
}

$(window).resize(function () {
    if ($(window).width() < 992) {
        $('.vcard11-referral-text').addClass('d-none')
        $('.vcard11-referral-icon').removeClass('me-2')
    } else {
        $('.vcard11-referral-text').removeClass('d-none')
        $('.vcard11-referral-icon').addClass('me-2')
    }
})
$(window).trigger('resize')

// change language for template
$(document).on('click', '.change-language', function () {
    let languageName = $(this).attr('data-name');
    $.ajax({
        url: languageChange + '/' + languageName + '/' + templateAlias,
        type: 'GET',
        success: function (result) {
            // displaySuccessMessage(result.message)
            setTimeout(function () {
                location.reload();
            }, 2000);
        },
        error: function error(result) {
            // displayErrorMessage(result.responseJSON.message);
        },
    });
});

$(document).on('click', '.template1-share', function () {
    $('#template1ShareModel').modal('show');
});

$(document).on('click', '.template2-share', function () {
    $('#template2ShareModel').modal('show');
});

$(document).on('click', '.template3-share', function () {
    $('#template3ShareModel').modal('show');
});

$(document).on('click', '.template4-share', function () {
    $('#template4ShareModel').modal('show');
});

$(document).on('click', '.template5-share', function () {
    $('#template5ShareModel').modal('show');
});

$(document).on('click', '.copy-template-clipboard', function () {
    let vcardId = $(this).data('id')
    let $temp = $('<input>')
    $('.modal-body').append($temp)
    $temp.val($('#templateUrlCopy' + vcardId).text()).select()
    document.execCommand('copy')
    $temp.remove()
    // toast.success('Link copied successfully.')
});

window.downloadVcard = function (fileName, id) {
    $.ajax({
        url: '/download-template/' + id,
        type: 'GET',
        success: function (result) {
            if (result.success) {
                let digitalBusinessCard = result.data
                let url = window.location.origin + '/' + digitalBusinessCard.url_alias
                let vcardString = 'BEGIN:VCARD\n' +
                    'VERSION:3.0\n'

                vcardString += 'N;CHARSET=UTF-8:' + digitalBusinessCard.name + ';;;\n'

                // if (!isEmpty(vcard.dob)) {
                //     vcardString += 'BDAY;CHARSET=UTF-8:' + new Date(vcard.dob) +
                //         '\n'
                // }
                vcardString += 'EMAIL;CHARSET=UTF-8:' + digitalBusinessCard.email + '\n'

                // if (!isEmpty(vcard.alternative_email)) {
                //     vcardString += 'EMAIL;CHARSET=UTF-8:' +
                //         vcard.alternative_email + '\n'
                // }

                vcardString += 'TITLE;CHARSET=UTF-8:' + digitalBusinessCard.job_title +
                    '\n'

                vcardString += 'ORG;CHARSET=UTF-8:' + digitalBusinessCard.company + '\n'


                vcardString += 'TEL;TYPE=WORK,VOICE:' + digitalBusinessCard.phone + '\n'

                // if (!isEmpty(vcard.region_code) &&
                //     !isEmpty(vcard.alternative_phone)) {
                //     vcardString += 'TEL;TYPE=WORK,VOICE:' + '+' +
                //         vcard.region_code + ' ' + vcard.alternative_phone + '\n'
                // }

                vcardString += 'URL;CHARSET=UTF-8:' + url + '\n'

                // if (!isEmpty(vcard.description)) {
                //     vcardString += 'NOTE;CHARSET=UTF-8:' + vcard.description +
                //         '\n'
                // }
                // if (!isEmpty(vcard.location)) {
                //     vcardString += 'ADR;CHARSET=UTF-8:' + vcard.location + '\n'
                // }

                var extension = digitalBusinessCard.profile_image.split('.').pop()
                vcardString += 'PHOTO;ENCODING=BASE64;TYPE=' +
                    extension.toUpperCase() + ':' + digitalBusinessCard.profile_url_base64 +
                    '\n'
                vcardString += 'REV:' + moment().toISOString() + '\n'
                vcardString += 'END:VCARD'

                var a = $('<a />')
                a.attr('download', fileName)
                a.attr('href',
                    'data:text/vcard;charset=UTF-8,' + encodeURI(vcardString))
                $('body').append(a)
                a[0].click()
                $('body').remove(a)
            }
        },
        error: function (result) {
            toast.error(result.responseJSON.message)
        },
    })
}
