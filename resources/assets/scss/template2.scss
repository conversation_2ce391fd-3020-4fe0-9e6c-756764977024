@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.btn-check {
    &:focus+.btn {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

.btn {
    &:focus {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

//color variable
$primary: #4CA1F2;
$white: #fff;
$yellow: #FFCA2C;
$bg-color: #141E30;
$icon: #898e97;
$dark: #010101;
$grey: #B8B8B8;

.vcard3-sticky-btn {
    background: $primary;
    color: white;
}

body {
    background-color: #E5E5E5 !important;
    font-family: 'Manrope', sans-serif;
}

.vcard-three {
    &.main-content {
        max-width: 576px;
        min-height: 100vh;
        background-color: $bg-color;
    }

    .vcard-three-heading {
        font-weight: 500;
        font-size: 28px;
        color: $white;
    }

    .heading-line {
        padding-top: 30px;
        z-index: 1;

        &::before {
            content: "";
            position: absolute;
            width: 90%;
            height: 1px;
            border-radius: 50%;
            background: rgb(175, 175, 175);
            background: linear-gradient(90deg, rgba(20, 30, 48, 1) 0%, rgba(255, 255, 255, 1) 50%, rgba(20, 30, 48, 1) 100%);
            left: 50%;
            top: 0;
            right: 0;
            transform: translateX(-50%);
        }
    }

    //banner
    &__banner {
        height: 280px;

        .banner-image {
            height: 280px;
            object-fit: cover;
            width: 100%;
            z-index: 10;
        }
    }

    //profile
    &__profile {
        z-index: 10;

        .avatar {
            z-index: 11;

            img {
                min-width: 120px;
                max-width: 120px;
                width: 120px;
                height: 120px;
                object-fit: cover;
                border: 5px solid $white;
            }
        }
    }

    //profile details
    &__profile-details {
        margin-top: 60px;
        z-index: 111;

        .profile-designation {
            font-size: 18px;
        }

        .social-icons {
            z-index: 10;
            position: relative;

            a {
                width: 20%;
                display: flex;
                justify-content: center;
                margin: 0 0 20px;
                text-decoration: none;
            }

            .icon {
                color: $icon;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    color: $white;
                }
            }
        }
    }

    .activeSlot {
        background-color: white !important;
        color: #4CA1F2 !important;
    }

    //event
    &__event {
        .shape-one {
            top: -130px;

            @media (max-width: 575px) {
                top: 100px;
            }
        }

        .event-card {
            background-color: transparent;

            .event-icon {
                background-color: $primary;
                padding: 10px;
                border-radius: 50%;
                min-width: 48px;
                width: 48px;
                height: 48px;
                box-shadow: 0 0 0 5px $bg-color,
                    0 0 0 7px $primary;
            }

            .event-detail {
                h6 {
                    font-weight: 500;
                    font-size: 12px;
                    margin-bottom: 5px;
                }

                .event-name {
                    font-size: 16px;
                    word-break: break-all;
                }
            }
        }
    }

    //qr-code
    &__qr-code {
        .qr-code-card {
            border-radius: 15px;

            .qr-code-scanner {
                border-radius: 15px;
                box-shadow: inset 3px 3px 8px #e8e8e8, inset -3px -3px 4px #f5f5f5;

                img {
                    max-width: 146px;
                    min-width: 146px;
                    height: 146px;
                    object-fit: cover;
                }
            }

            .qr-code-btn {
                padding: 10px 16px;
                background: $primary;
                box-shadow: inset 6px 6px 12px #3172b1, inset -6px -6px 12px #51acff;
                border: none;
                outline: none;
                border-radius: 25px;
                transition: all 0.3s ease;

                &:hover {
                    background: $primary;
                    box-shadow: inset -6px -6px 12px #3172b1, inset 6px 6px 12px #51acff;
                }
            }
        }
    }

    //share btn
    .share-btn {
        a {
            padding: 10px 16px;
            background: $primary;
            box-shadow: inset 6px 6px 12px #3172b1, inset -6px -6px 12px #51acff;
            border: none;
            outline: none;
            border-radius: 25px;
            transition: all 0.3s ease;

            &:hover {
                background: $primary;
                box-shadow: inset -6px -6px 12px #3172b1, inset 6px 6px 12px #51acff;
            }
        }

        @media (max-width: 575px) {
            margin: 0 auto;
        }
    }

    //language btn
    .language {

        ul {
            list-style: none;

            .lang-list {
                padding: 10px 20px;
                background: #4CA1F2;
                box-shadow: inset 6px 6px 12px #3172b1, inset -6px -6px 12px #51acff;
                border: none;
                outline: none;
                border-radius: 25px;
                transition: all 0.3s ease;
                width: -moz-fit-content;
                width: fit-content;

                .lang-head {
                    color: white;

                    i {
                        color: white;
                    }
                }

                .lang-hover-list {
                    margin: 15px 0 0;
                    font-size: 15px;
                    width: 115%;
                    left: unset !important;
                    right: 0;
                    min-width: 150px;

                    li {
                        padding: 5px 15px;

                        &:hover {
                            background-color: #f1faff !important;


                            a {
                                color: #009ef7 !important;
                            }
                        }

                        &.active {
                            background-color: #f1faff !important;

                            a {
                                color: #009ef7 !important;
                            }
                        }

                        a {
                            color: #5e6278 !important;
                            text-decoration: none;
                        }

                        img {
                            height: 20px !important;
                            width: 25px !important;
                        }
                    }
                }
            }
        }
    }
}

//Download btn
.vcard-three-btn {
    padding: 10px 20px;
    background: $primary;
    box-shadow: inset 6px 6px 12px #3172b1, inset -6px -6px 12px #51acff;
    border: none;
    outline: none;
    border-radius: 50px;
    transition: all 0.3s ease;

    i {
        color: $white;
    }

    &:hover {
        background: $primary;
        box-shadow: inset -6px -6px 12px #3172b1, inset 6px 6px 12px #51acff;
    }

    @media (max-width: 575px) {
        margin: 0 auto;
    }
}

.vcard-three {
    .language ul {
        .lang-list {
            .lang-hover-list {
                margin: 0 0 0;

                &.top-dropdown {
                    margin: -80px 0 0;
                }
            }
        }
    }
}

.profile-description {
    font-size: 17px;
    text-align: center;
    color: white;
}

#wrap {
    background-color: #ddd;
    padding: 50px 0;
}

#slider {
    width: 500px;
    margin: 0 auto;

    img {
        width: 100%;
    }
}

button {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.slide-arrow {
    position: absolute;
    top: 99%;
    width: 15px !important;
}

.prev-arrow {
    z-index: 99;
    left: 137px;
    height: 0;
    border-left: 0 solid transparent;
    border-right: 11px solid #ffffff;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media (max-width:575px) {
        left: 40px;
    }
}

.next-arrow {
    z-index: 99;
    right: 137px;
    height: 0;
    border-right: 0 solid transparent;
    border-left: 11px solid #ffffff;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media (max-width:575px) {
        right: 40px;
    }
}


.slide-arrow-blog {
    position: absolute;
    bottom: 0;
    width: 15px !important;
}

.slick-dots {
    bottom: -32px !important;
}

.vcard-three__blog .blog-slider .slick-list {
    margin-bottom: 15px !important;
}

.vcard-three__blog .blog-slider .slick-dots {
    margin-bottom: 204px !important;
}

.morelink {
    color: rgba($white, 0.8);
}

.lb-data .lb-number {
    display: none !important;
}

.social-icons {
    img {
        border-radius: 50% !important;
    }
}