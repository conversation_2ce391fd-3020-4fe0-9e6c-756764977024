@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;500&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.btn-check {
    &:focus+.btn {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

.btn {
    &:focus {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

//color variable
$primary: #203556;
$white: #fff;
$yellow: #FFCA2C;
$bg-color: #00081c;
$icon: #898e97;
$dark: #010101;
$grey: #B8B8B8;

.vcard8-sticky-btn {
    background: #416290 !important;
    color: white;
}

body {
    background-color: #E5E5E5;
    font-family: 'Nunito', sans-serif;
}

.vcard-eight {
    &.main-content {
        max-width: 576px;
        min-height: 100vh;
        background-color: $bg-color;
        position: relative;


    }

    &::after {
        content: '';
        position: absolute;
        top: 250px;
        left: 0;
        right: 0;
        height: 100%;
        width: 100%;
        background-image: url("../../images/background-image.png");
    }

    .vcard-eight-heading {
        font-weight: 500;
        font-size: 28px;
        color: $white;
    }

    .heading-line {
        width: fit-content;

        &::after {
            content: '';
            position: absolute;
            bottom: 10px;
            width: 70%;
            height: 1px;
            background: rgb(175, 175, 175);
            background: linear-gradient(90deg, rgba(20, 30, 48, 1) 0%, rgba(255, 255, 255, 1) 50%, rgba(20, 30, 48, 1) 100%);
            left: 50%;
            transform: translateX(-50%);
        }
    }

    //banner
    &__banner {
        .banner-image {
            height: 250px;
            object-fit: cover;
            width: 100%;
            z-index: 10;
        }
    }

    //profile
    &__profile {
        margin-top: -45px;
        z-index: 10;
    }

    &__avatar {
        img {
            min-width: 120px;
            width: 120px;
            height: 120px;
            object-fit: cover;
            border: 5px solid $white;
        }
    }

    &__position {
        margin-top: 60px;

        @media (max-width: 575px) {
            margin-top: 20px;
        }

        .avatar-designation {
            font-size: 18px;
        }
    }

    //qr code
    &__qr-code {
        z-index: 200;

        .qr-code-card {
            border-radius: 0 15px 0 15px;
            margin-top: 30px;
            max-width: 350px;

            &::before {
                content: '';
                position: absolute;
                top: -10px;
                left: 0;
                width: 30%;
                height: 1px;
                background: linear-gradient(90deg, white 0%, white 50%, #141e30 100%);
            }

            &::after {
                content: '';
                position: absolute;
                bottom: -10px;
                right: 0;
                width: 30%;
                height: 1px;
                background: linear-gradient(90deg, #141e30 0%, white 50%, white 100%);
            }

            .qr-profile {
                transform: translateY(-50%);

                img {
                    min-width: 80px;
                    width: 80px;
                    height: 80px;
                    object-fit: cover;
                    border: 2px solid $white;
                }
            }

            .qr-code-scanner {
                border-radius: 5px;

                img {
                    max-width: 146px;
                    min-width: 146px;
                    height: 146px;
                    object-fit: cover;
                }
            }

            .qr-code-btn {
                padding: 10px 13px;
                background: $primary;
                border: none;
                outline: none;
                border-radius: 0 10px 0 10px;
                transition: all 0.3s ease;

                &:hover {
                    background: rgba(65, 98, 144, 1);
                    border-radius: 10px 0 10px 0;
                }
            }
        }
    }

    //social media
    &__social {
        z-index: 1;

        .social-icons {
            z-index: 200;
            position: relative;
            max-width: 380px;
            width: 100%;
            margin: 0 auto;

            @media (max-width: 575px) {
                max-width: 270px;
            }

            .social-back {
                min-width: 60px;
                width: 60px;
                height: 60px;
                background-color: rgba(#416290, 0.5);
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                z-index: 100;

                @media (max-width: 575px) {
                    min-width: 45px;
                    width: 45px;
                    height: 45px;
                }

                .icon {
                    color: #8f9aaa;
                    transition: all 0.3s ease;

                    @media (max-width: 575px) {
                        font-size: 1.5em;
                    }
                }

                &:hover {
                    box-shadow: 0 0 10px 10px rgba(#416290, 0.8);
                    background-color: rgba(#416290, 1);

                    .icon {
                        color: $white;
                    }
                }
            }
        }
    }

    //event
    &__event {
        z-index: 200;

        .event-card {
            background: rgba(65, 98, 144, 0.6);
            border-radius: 20px;

            .event-icon {
                min-width: 48px;
                width: 48px;
                height: 48px;
                background: $primary;
            }

            .event-details {
                span {
                    font-size: 12px;
                }

                h5 {
                    font-size: 16px;
                    word-break: break-all;
                }
            }
        }
    }

    //share
    .share-btn {
        a {
            padding: 10px 13px;
            background: rgba(65, 98, 144, 1);
            border: none;
            outline: none;
            border-radius: 0 10px 0 10px;
            transition: all 0.3s ease;
            color: $white;

            &:hover {
                background: #374e72;
                border-radius: 10px 0 10px 0;
            }
        }

        @media (max-width: 575px) {
            margin: 0 auto;
        }
    }

    //business hour
    &__timing {
        z-index: 200;

        .business-card {
            padding: 20px !important;
            border-radius: 15px;
            border: none;
            background-color: #284E75;

            @media (max-width: 575px) {
                padding: 15px !important;
            }

            .calendar-icon {
                background-color: $primary;
                border-radius: 12px !important;
            }

            .business-hour-day-text {
                color: $white !important;
            }

            .business-hour-time-text {
                color: $white !important;
            }

        }

        .business-card-today {
            border: 1px solid $white;
        }

        .time-section {
            background-color: rgba(65, 98, 144, 0.6);
            border-radius: 20px;

            .time-zone {
                padding: 10px 13px;
                margin-bottom: 15px;
                transition: all 0.3s ease;
                cursor: default;
                z-index: 10;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -7px;
                    width: 100%;
                    height: 1px;
                    background: linear-gradient(90deg, #2e4a70 0%, white 50%, #2e4a70 100%);
                }

                &:last-child {
                    &::after {
                        content: unset;
                    }
                }

                span {
                    font-weight: 500;
                    font-size: 18px;
                    color: $white;

                    @media (max-width: 575px) {
                        font-size: 16px;
                    }

                    @media (max-width: 340px) {
                        width: unset;
                    }
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    //contact
    &__contact {
        z-index: 200;

        .contact-form {
            label {
                color: $white;
            }

            input,
            textarea {
                border-color: $white;
                background-color: $white;
                border-radius: 0 10px 10px 10px;
                padding: 10px;

                &:focus {
                    box-shadow: none;
                    border-color: $white;
                }

                &::placeholder {
                    color: $primary;
                }
            }

            .contact-btn {
                padding: 10px 13px;
                background: rgba(65, 98, 144, 1);
                border: none;
                outline: none;
                border-radius: 0 10px 0 10px;
                transition: all 0.3s ease;

                &:hover {
                    background: #374e72;
                    border-radius: 10px 0 10px 0;
                }
            }
        }

        //Download btn
        .vcard-eight-btn {
            padding: 10px 20px;
            background: rgba(65, 98, 144, 1);
            border: none;
            outline: none;
            border-radius: 0 10px 0 10px;
            transition: all 0.3s ease;
            color: $white;

            i {
                color: $white;
            }

            &:hover {
                background: #374e72;
                border-radius: 10px 0 10px 0;
            }

            @media (max-width: 575px) {
                margin: 0 auto;
            }
        }
    }

    //language btn
    .custom-language {
        z-index: 9999;
    }

    .language {

        ul {
            list-style: none;

            .lang-list {
                padding: 10px 20px;
                background: #203556;
                border: none;
                outline: none;
                border-radius: 0 10px 0 10px;
                transition: all 0.3s ease;
                width: -moz-fit-content;
                width: fit-content;

                .lang-head {
                    color: white;

                    i {
                        color: white;
                    }
                }

                .lang-hover-list {
                    margin: 15px 0 0;
                    font-size: 15px;
                    width: 115%;
                    left: unset !important;
                    right: 0;
                    min-width: 150px;

                    li {
                        padding: 5px 15px;

                        &:hover {
                            background-color: #f1faff !important;


                            a {
                                color: #009ef7 !important;
                            }
                        }

                        &.active {
                            background-color: #f1faff !important;

                            a {
                                color: #009ef7 !important;
                            }
                        }

                        a {
                            color: #5e6278 !important;
                            text-decoration: none;
                        }

                        img {
                            height: 20px !important;
                            width: 25px !important;
                        }
                    }
                }
            }
        }
    }
}

.slick-slide div {
    width: 100% !important;
}

.vcard-eight {
    .language ul {
        .lang-list {
            .lang-hover-list {
                margin: 0 0 0;

                &.top-dropdown {
                    margin: -80px 0 0;
                }
            }
        }
    }
}


.vcard-eight__testimonial .testimonial-next-prev .slick-prev::before {
    float: left;
    margin-left: -8px !important;
}

.vcard-eight__testimonial .testimonial-next-prev .slick-next::before {
    float: right;
    margin-right: -28px !important;
}

.vcard-eight__testimonial .testimonial-slider-demo .slick-prev::before {
    margin-left: -8px !important;
}

.vcard-eight__testimonial .testimonial-slider-demo .slick-next::before {
    margin-right: -28px !important
}

.card-height {
    height: 440px !important;
}

.vcard-eight__blog {
    .blog-slider {
        .blog-card {
            @media (max-width: 575px) {
                height: auto !important;
            }
        }
    }
}

.vcard-eight__testimonial {
    .testimonial-card {
        height: auto !important;
    }
}

.profile-description {
    font-size: 17px;
    text-align: center;
    color: white;
}

#wrap {
    background-color: #ddd;
    padding: 50px 0;
}

#slider {
    width: 500px;
    margin: 0 auto;

    img {
        width: 100%;
    }
}

button {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.slide-arrow {
    position: absolute;
    top: 99%;
    width: 15px !important;
}

.prev-arrow {
    z-index: 99;
    left: 137px;
    height: 0;
    border-left: 0 solid transparent;
    border-right: 11px solid #ffffff;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media(max-width:575px) {
        left: 40px;

    }
}

.next-arrow {
    z-index: 99;
    right: 137px;
    height: 0;
    border-right: 0 solid transparent;
    border-left: 11px solid #ffffff;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media(max-width:575px) {
        right: 40px;

    }
}

.slide-arrow-blog {
    position: absolute;
    top: 85%;
    width: 15px !important;
}

.slick-dots {
    bottom: -32px !important;
}

.vcard-eight__blog .blog-slider .slick-list {
    margin-bottom: 20px !important;
}

.morelink {
    color: $white;
}

.lb-data .lb-number {
    display: none !important;
}

.social-icons {
    img {
        width: 50px !important;
        height: 50px !important;
        border-radius: 50% !important;
        padding: 6px;

    }
}