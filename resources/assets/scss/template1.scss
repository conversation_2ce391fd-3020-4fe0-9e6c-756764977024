@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@400;500&display=swap");

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.btn-check {
    &:focus + .btn {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

.btn {
    &:focus {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

//color variable
$primary: #3a4151;
$white: #fff;
$red: #fb5253;
$bg-color: #edf2f8;
$yellow: #ffca2c;
$grey: #969696;
$dark: #010101;

.vcard2-sticky-btn {
    background: $red;
    color: white;
}

body {
    background-color: #e5e5e5 !important;
    font-family: "Outfit", sans-serif;
}

.vcard-two {
    &.main-content {
        max-width: 576px;
        min-height: 100vh;
        background-color: $white;
    }

    .vcard-two-heading {
        font-size: 28px;
        font-weight: 500;
        color: $primary;
    }

    //banner
    &__banner {
        height: 250px;

        .banner-image {
            height: 250px;
            object-fit: cover;
            width: 100%;
            z-index: 10;
        }

        .banner-shape {
            @media (max-width: 575px) {
                display: none;
            }
        }
    }

    //profile
    &__profile {
        z-index: 111;

        .avatar {
            left: 85px;
            z-index: 11;

            img {
                min-width: 120px;
                max-width: 120px;
                width: 120px;
                height: 120px;
                object-fit: cover;
                border: 5px solid $white;
            }
        }
    }

    //profile details
    &__profile-details {
        margin-top: 50px;
        z-index: 111;

        .profile-designation {
            color: $grey;
            font-size: 20px;
        }

        .social-icons {
            @media (min-width: 576px) {
                max-width: calc(100% - 150px);
            }

            a {
                width: 20%;
                display: flex;
                margin: 0 0 20px;
                text-decoration: none;

                @media (max-width: 575px) {
                    justify-content: center;
                }
            }

            .icon {
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .facebook-icon {
                color: #1b95e0;
            }

            .youtube-icon {
                color: #ff0000;
            }

            .pinterest-icon {
                color: #bd081c;
            }

            .reddit-icon {
                color: #ff4500;
            }

            .instagram-icon {
                color: #f58ea9;
            }

            .linkedin-icon {
                color: #1b95e0;
            }

            .whatsapp-icon {
                color: #1ad086;
            }

            .twitter-icon {
                color: #1da1f3;
            }

            .tumblr-icon {
                color: #35465d;
            }

            .globe-africa-icon {
                color: #557b97;
            }
        }
    }

    .activeSlot {
        background-color: #fb5253 !important;
        color: white !important;
    }

    //event
    &__event {
        z-index: 111;

        .event-details {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .event-image {
                width: 45px;
            }

            span {
                color: $primary;
                font-weight: 500;
                font-size: 16px;
                word-break: break-all;
            }

            span a {
                color: $primary;
                font-weight: 500;
                font-size: 16px;
                word-break: break-all;
            }
        }
    }

    //qr code
    &__qr-code {
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAkAAAADmCAYAAAA9QDrsAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAEMXSURBVHgB7Z3rduO4kqUjZDtvVXV69cz8mNXv/3pn9emqU5nptK1okcQlIgBQvEq2tL+qtEUSBEBJFrd2BAD+558/hQAAAAAA7ogDAQAAAADcGRBAAAAAALg7IIAAAAAAcHdAAAEAAADg7oAAAgAAAMDdAQEEAAAAgLsDAggAAAAAdwcEEAAAAADuDgggAAAAANwdEEAAAAAAuDsggAAAAABwd0AAAQAAAODugAACAAAAwN0BAQQAAACAuwMCCAAAAAB3xyMBAAAAF0RE6O3tePr3dno8bHf/HQ4Heng40IGH3wDsCQQQAACA3enEzq+XV3p5faXXl7dGqZMaIh7E0EkEPT090qfu36cnAmBr+J9//hQCAAAAduD19Y1+/vxFr2+vvbyh6h0n7mTqLaHwK9I5Q1+/fqbPEEJgQyCAAAAAbM7xeKTvP55TmIt5CHX16iahbz+ctqP46c9ROzoh9OULhBDYBoTAAAAAbMqvXy/0/PzSh7L4pGI4aJ7usYgXPYFeG3GvdU46J2ie0/ldGR7EUXfujx8/e1H19SSEmJkAWAoEEAAAgM14fv7V5/p0uuXQ5/ME+kTn0z7mehQsiST9e7CAhLRzxPRyqv/19Ui///b1JJYggsAyIIAAAABsws+T+Hl7fRtcH6IkWzrnhg+DmOm2SycoEERSpC8XXSDDUPv37z/oWyeC4ASBBUAAAQAAWE3n+nShqU7osOTxXBTlS45k9XDNCeqFU9gb6/DiJiYUhaq6kNi3r18QDgOzgQACAACwim6kVyd+uiRlCaO4+vBXtICiOFGRrD4cRvGxGgWWfjfG56S6BiF0PEo/yqwbJQbAHCCAAAC70Qx1LCwH3h/d6/baOT9BtAxOTJzLR4kgq3jij/48DuWHY5xFk2QHKb87sljqqzw5Tm/HI72cRNjT4wMBMBUIIADAJqwRMVPPMzdK8C7o3J+cr6xzfwanhoOekWjuBCGUw2Quz0clD8V9w/FILschQboTWq+nENzjwwGhMDAZCCAAwCbURMzWrg7Ez/uim+tH5+l4oyfSl9GT+1DO4xnOY3IDvQb35zCI6jIJmpSQyse60WGYNRpMBQIIAPAhgRt0fY4ieVi71jUpjzl4PEHMxLl+UiJ0qEficUcUV9FXinMDJVvJVjIIMhG4QGASEEAAgItQC5G1wmYjKbCJKeIHuUX7EZ0Z4SA4dIIzi83tYZfnc2A3CbRKbCZK4TTrJHFOCyJ3bkiI7g51+UCPD8gFAueBAAIA7IIXH/pxPNYSJ1tJFtMmHKPN4ZCnk0d7UZrrJ27Y8BWbHJ9MFD4HiuPf40NfimPDYbRZcpXCsW5UGEH/gAlAAAFwx2zlkNTExWi9sm37U4D42Z44lw/rYezsk3m0cCE3GiyPBMs1qPmBzEoZkoWVHlqvSsSeIAwGpgABBMAd0xIfWphMeTxXXEha9BKi5KOiE5/JCFkrPERKvaJGuA9laNBN7MwitY5GDojx2LttSvAUgAEIIABAQSt01Xq8lMVhqSKEss5NQnhsPS3HJaf3xIRpUULHjh5jHxbjXEbVWMocM84egGlAAAEAJmEnq5NKCGI+kxKZqfKdvtH0ORHUOg7xsz9RIMXZorkQsMPvfMzbQY74ksVcIR52dk7RRm9PcONAAAEAJmFEws53Fy5CKudHj01xfxBy24+pDlwKoVI9WDUld2dwk6iSKwTVA6ZzIAAA2IGlSajxRsqcZwGO+yP1ofPLb37t8A2PHgcZnRs2Rn5dx8v4evR2/ziU6f4d1OO4FAcA54AAAuAO2fKG3qprqUhJidUzHZ41YazmcHxBsvZczr5OlbmgamVmvf7sBttD/4AJQAAB8MFZImaKm5BKRtW/l9Q1WnZlrs0lhFvhNIDZXPp56wUTATAPCCAAPjjNtZLm1BGHpaftkiSOKje3S93wqq7SwrbPuT61MtGZ6HbXXAowMEsU4zkEVwICCIAbYMn337miKYmjhYue7iWS9mq77oalhafSYwih/Zj6qsGnA0uAAALgA7KFmLh00ODc7LxTQlJzz9VtV88buXV6NywOrc5tWacIImgHJr7PBzkKGQTmAQEEwAdkjetxzRvFWL+nhKS2Zp4IzGHCIQQW9qpE6W41crCd23fJ/DJwf0AAAXCjtBcaPT8K59rYKfDY/DblOIehztWzRa/6diQO07Z9iHQi6N7dILhh4CMAAQTAB2ZRbgv7tZqmzd9yScQ8bq83dn5o/Hbkpyc6PpQW3sz5QMO+bkVyiID5YNQduCQQQAC8c8ZGXS26yTZO+Sg37GvcJOOoL7UnHyB3LDzsnCCExOYx5z0IsQTWAgEEwDtn6airZn30/hyfc5ybBXr/9u22uJU4U//CelRxAyKoZMo8S1OXw0jlkQANFgABBMANM3Yj+Ughmj37Ov1m65dmSEfDfxS0T1zlajinC4e9vb3RLTNHgIzNs3Ruf7NOJECDBUAAAfCOWT3B4YRlB7ZmbRuX/jY/dhPWo73626yOdOm1YYfDpPeYgNlp4/Ukgm41L2ipAFn9XkEYDKwAAgiAd8zWN5ZJw+dpXYii2UZjd5GUvWRSR2ba2gTII71UqIV9+GtA3KP+Gkxa0JAU/XbDImgJa58LPJdgDRBAALwzNpnksDGya1K4h7YPUQyN03Z11eoYubQlEzCqEo1ZsEX9zHtqAo7VKLFbdoKWABcHXAsIIADeAXsl+c5dUX0LLnlDO9dWa7Rc3M90bnLG4fchlq/MAp0GhFFYky1qMdZFxEwt/foKERRpzuEEYQR2BgIIgHeAGdFyhQ/+qW1OKXXJG9q5hWDPOVh5vFadGAJLi6Cqs+KM0HEj9SOGyZQwSvsp73t9fb1pEZRF5vrFaiGGwB5AAAHwzqiueL5zYvCUZOk4tmmrNrZiixFAMrpfuT9hNJg0+uH7ol2h6AyJmkhxcILoJskiUz+L6+oCYEsggAD4AGw1zHfqN+nq3EM0nSlzvSxlSE5m4zBsLRCj45OyesJoMB06S6GusCP1Q1weVZEizWoexS4x+pXugV46bug0ArAWCCAA7ohLzaczZa6XNWSBkp2XuUJrvHw4FqNXHEeA5djWMPBM0ggvUTYP2wQgUtExc3PvDr3dwTxBkT5kOSkRH4D9gQAC4Eq0XJL18+i0t7lI5p1XV7XMjGU5mvXNuONtNnles57QIamVD9dK2c1JokZs7k9CxQ7jKDFxh7tQ2D2JIADeAxBAAFyYPnxDbZfk3A3inHiRkW2prV01oa6tZpRulpygtCaLtjUCUtKPbAKJXtjUKBnzsFwuQypVDyLK5AINnQ4iCMtmAHApIIAAuAYrbtKX+gatwzi1ETmXHpkzWbStWVCT4z62O81vvSnJAZKwr0iGFvVgUL6mPlZlbn1kGADvCQggAC6MdRSITDLtO6KVeD3XRboUSwRZe6HZPFIrvzhCJsIVFU8cFVYLf5kzuXo85g/F03/9eoEIAuACQAABcG2YrpL0WR3qfiV3Zws2FQ16dXdVrfWGxIilsCc+sL/D5EC69OD8SH6sTkEoDID9gQAC4J2xVHzMTaQ2YS2yicxjYuKa4mivtovrFjHpQEb2qH1lBM0OyTfBMOZC6NQed3ShsHtJigbgWkAAAfDOWDKTcncs37znuyFT5hmaM9qrde5ahpmft2d4/tJWaExvitll+uQO6OcySx4xbo+fidpWOOzFchkA7AsEEABX5OxaVurx+JpVKpSy4YiqWhtjdc1dhX7JBIay4tyiLqn5MGoUmEpatpeQZh+y4kcJNKOfXF/zrNK+Q7ns8XT818t9TJIIwDWAAALgipz7ht862hQatE27c1gz6WHLeZq7an1/Ds2n5moJKSFTt2jUQynqi4nTMX1ID3/X9afypmYlrIj6MNgrQmEA7AIEEAAfkGuGoaa0M7cl37cl17dE0hUj8oad1I/sqjk/auQXZU2TR3KpIe6iHSXOgo1HOm9DZsP26wuGxgOwBxBAAFyRS4qSS9UpMp5RxI1zLsX5kJ1NVWYzDdDg6QhJtnjSMc6n6okTgw2ki5N3nlLakXaehpO6UNjzrxcCAGwLBBAAF6B6m9WJy5Vjs9s4M1vzWZGxYCmJRblEdF3KkJ2/Bn1c/fYrwadTpTiPiNLCqK7xoc1KupE7mbQLdTwe+6RoAMB2QAABcAGqo4dGZlee64iwulkudntG5gVq7buoc1Pb17jWKc9AFj/iKyWtTNLaYCGsVXlGVKX5lxeIEuuO9ZvnkXKyNdU6i1mi75XO/fv+/QcE8A48EgDgIow5PlNnV27VISRFXefOmUJ7puR19Y7BJn9GtVsp23w+qU058msQJLaqIFKk3BatmXSaD7u8H9eWH/7OUjZHRc84hdu6VeO7m+DTEz6274UfP5/pv//1V58M3713/uv//z96fMTrvxVwgAC4EFuIhUXJwSvbPTe0ffNw3YqRYdPK8ZCozX7Auu3FUFc6xW6bklbsROFSr9GdF4bZi554Mf4jKZ6LF8wNdFf8+PFMcgp/Sid+3470779/EtgOCCAA3jFzZ3dek+w8dw6fsePLezGvnVpbNQfMblPVbdHqIz1Kho5oXaLOk2wi2b1l/SNuVRRMeh1Wn0MUHSTMDXQ/PDw89Enw3b9OBD0ccMveEnhpALwzmmGuCd/8fV5Ra7vWxuZraU2YuHELTKjoTFsxL0dMeItigg7pJB5W+qOVLlTrSz6BzCAxoapWqp4vrQMkfS7Qp1MY7FIjCMH1+OP3r/08UM/Pz/Tbt6/0xx/fCGwH//PPn/BTAQCrmKAN2ucGMTYnp2hJezopuRB/6YfeJ+pA6JvRSFI/J5wSXZ802aGbbLHWD1O32ud7ezg5AV+/fCYAwHLgpwHwwdhiCYitWfMtamoC+JL2JucNkZmnmbzq8OLHnJj6FPwdUYeqoi6LMLN4qlgBpYsPo+3zsePbEblAAKwEAgiAHdhTpExZuPQcW4ZPmkPRZ7Sx9yrveluMsIn/RCUzq5BX6iCNqi6dx0Oj1+LDb1KOHCM1+WLSYtG9yrlIv14wOSIAa4AAAmAH6iuCT2OPZOctlppo1ennu2m1MSaULuFm5JFfLvmn9VSqZS2KQ0rEZNmS1FSZ7yNK3IQqTU/6wm7OINIp2ZTmLYqlXn5hXiAA1gABBMDOzHVs5iQ7t+7dWwmeMfHmk4vPcW4OJNPuiMBrjYw7JwrzbNj2HGO2xPBVVCmklyaNpcLzwjGMZQ5RPJNEzb7NbBOqySZItxwmVj9jOE1y5+kFI8IAWAwEEAAfDK44BTXBs0UIaotw2xJk4giyeSIsZvrYc8QrEgpGjg5OZdWhynNTIPYl4qzPlRFxtt9k8n+GWb11GC77QTk0NhR4wUKpACwGAgiAhYwtE7F1Rsu55Sda+6aKoGveRC+X1B0DU3nL9MOk8KgEZTcyK8/4LCrn2YpSHfoyrg3FunXDlOqN7TDneX9892NydWz7BUskALAICCAAFjImRFrOzJZtneOSuTVr2NtlEpPLE0VG/BHzmKjdLzUxoaRcn1xHDHcVOTyVMCWb43mjrzWMCrPts3WmdL9C2TcIIAAWAQEEwAa082iv56xcqu33H4JhJXCinWNDUHbSw7wsRW2uH3a/qwJOCSKTcm1yeHSdbMJgWpwR53AYu7q6A91Eed1aUQCAeUAAAbABayXAR5jV9yPNPMwm0TmHtfK+PIItHVHaIyUmKwbBUXmtVaKQTlAeRJCpoDhPD7/P6UnGJ8pJ02LDbTqbCWEwAOYDAQTAlRkLVc0e7r5TPs2lwmlzOLegqp5g0SdLi1I8fmS8H56ewl5al7AKR2WFpTqQ1ZQ9YmyfMnm9P/Wok4ioUsKqohOvEEAAzAYCCIALMmW+nKnHquVXe1G0ST8uMQniuT5NWe8shb5UF0y0jHN4ysayVEknsEi5Sum05A4pm0nIiLQoljiF7GqiislGyYKTdTwiDAbATCCAALggfnHSJbznUFQKLR2nCya9QOuWlOJHt5HlSI6OuSHxHB9KIZD8khVsasz7SG0NSdQ1OCofdTy0ouo3w+ldaK3b+/ICAQTAHCCAALgSi278so2I2ot8TfP7tnWIzbfPTMVEiEO7qUTYQSbcxc0gVm1qgjyW3iZVS8Odc/E30nk+nI7q3KIhTOZrYXp5xaSIAMwBAgiAKzPlxp9u5jaF5GJ5OWNzHtUYbtLb923qDNFqb3o0aB8nOEwXJafz+JBXMc+P83xi22kEmXWC2DZDfv2vLGxtrpIpE/tI/hKZ4qzUrwiDATAZCCAA3gmjgqIyqeFlpE9uf8q+pZxd0kLnyjQQVyYKijBavFZdWQdVcoJUBZICYFK+AG7eHyYmnQ+kiUtppLhXzDUiNnWJr18q+yhf8yvCYABM5pEAAO+Cc4JibKTY1mLk0iO+/CitgjjD9pm+2cFRg0tjQl8q7DiISnVO+kEmGhbNnuj8lO1L3VTqR3NVErFd/lAx7KzY3b5m7zYhEdpixTCncOSBD4vCtOC2gAACYGPe45DxObznvp8f+dWV4RgfJCMjWGsFUXooShutQ9Q+n+jM1qUhKSofyoZ9cQbpqLD8HES5B8WFpHZc9U3e3o5Vt/DeiFMfiOjHeVkSOb4mAXw4HOjh4UCPj7gd3ht4xQHYiDhSZ+wmPUccpRvnGZYMB9+KvcXesvpDqKjTOCyFK9TXFzWSybkxLYeatEgR8uZN6CRVemDsoyHkJYM4E1VXUFVc1B+DcVxpMDfizaPuvG5OoKcnfLR3pPkoVVI6hde/oxus2C0l0i0qy4eXkxB6oE9PTydRBGfoHkAOEAAbwGl0jhT7NXNu5nPm9DmXP7TkvCnslQe0TX1+TxQVlARSkaKczB1Jc/PUKjZnhRwhVbtSUzlr6FgO3XKCx/Yhi7CW/pFKnjYjDNbRvUZq4EC/wCyR/deXSU8bHU9q6NevF/r7+w96fn4hcPtAAAGwAc0cjZkCoTaT8xRhsFSIbBkumT1r9QRxOHsiyEp5jnc4szOU10nN+nydBF0kBoWy8THnIeu+AXFHaqKlyAkyBYSqT2vlaYlJ31gWw77avdBJSedkxRHF9yGn87rn8efzM/311/deFIHbBQIIgImsXWZiyvlCOUk37ZsQ4vLnzGHqMPxz9c91mrZyj+Lzmh0d307/Mz9OiT2STRive3R6D/mbpak4CQ/XKcqrxksIV8XnsBZ243CO73xZdUzk9eVi37qb9kfOQdsSDlN9R5HDBy1M82ub3aL8+r4d3+ivf3+Ho3bDQAABMJG1y0zMOn9OUXHuxQ6kkNCK8/fCPq/i2rVl2SRH107h2q8gYtzsPcyuTG606SSlfKQodGMhIW8ExfPzzbpOHuCWe9clQ987+YtB9zPMnyQ5/JWOsS2fH/NJTB7pz5MThPmVbhMIIADeCcb1WSm2ZrdN9X58BPSNf0CPssoWSn7EprQRKS73J57DKvyVahsZmp7Lk1E0yQ0KzkSaYyiIGPPUh1FMrKpphvjc7rfjbQugOe/QFOIKzy+HHK4sYFlFxzhNdcDq9ejCYbf+nN4jEEAArGQrwaBvvHtIkNZszmaEDCl34sy5sziTHD6XWt90LkfYUZ4jVgYV0StfRf+z4n7VNlVdOZRJORwWa4xuTXB/OGbihvNjP6PzI+5a/TPnDa2OWw/bWKnbRjs9xq8LQohYb4eHpzBZNzRevxm61+vPv/5GaPHGgAACYCVLPxSrIkAZB8PmMqFQywtqJRnXXYW6KFpMI+G5XK+rItKoxPcmzvlCKamZVLhJ/443tVDGuT/V51vK/ufYCRsnaRhtxObU5PjEPWFbdDhMCSfdr+p1un1M9vXpHt5LCKz2royivvwb4CR8ousTk6PZiR3vAPWv18kB6nKCwO0AAQTAVszUCM2ZnU2V45W2nJR4M18zOmxJu2vbqYq0Se05r0Q9DNGmSuOuBjfnkEk19sIjOThSSZKuC03dbN6sh+ecX5Hrrwi0KLr0U3e8wXDNFLHcoSc9jNtDeff85ciYDY9xbZTYUEE3XxAWnb0dIIAA2IptNMEsHaVvrP6De00ekb8BrJnPaAvOtyejj1I8qXqaG1WlLZ3BrinDUXGGPX1CvPEW9Ycbr1SEj/hwjrGrKt0VU6+GnZt1a0O4p4jlGsP7N4a04k4iLTa17GTK4bE0V1AQT932jx/PBG4DCCAAFrB1ovAW9a0dqaX7YsIyE+ud4tFMaXsOvm8+pycNxlLqRSpqqOaWRQ9mcNOUhlKVDjnM2cUxDoPSKuL8nXhE33ilbNw6UD5EZ9rOgipyiy7QUpKISSk/rMRO3hcekH5tPJ0L9AoX6CaAAAJgAeXNsv5xOfV2/h6SK4s1qmZqsukezUiZTZ4HFVBiHXIK+8W348QPU5EXHX+ncqEOndrDpYxR/WEjhHzlxhjStlBRk3pt3OsT52SMuyGAatgZooffyeKx+UKU3SDjBJ1+fIcLdBNAAAGwAa1w0zVkTU237Dmb9BKmOj3Tcn804gIaVFgrrFUChRwa9s6NGGcnSapwF1SD4ZO9VJmeUIWsRN1SVTtNoaMeT0xej1G52CYEkMWMCIuJ0OzCXkRZ7MTCtpb+Z5cH9B6+tIB1QAABsCPVZMoVdUyh6kGMfFivCb+1zj1X49Sbx9gSI9UZmNUeabgxObwn7XZivCsKlxgGS6GossHavlqVeaMcaVdcUWyrOjqOdTHyxbCKQ0mpZ9jHwezoMGbtBZl8oG7dMPCxgQACYEf0Wlt7jchaQ1PATBRFreHsMqO9bfKfqEgwLjsRBIcyhmJejU0ep+L8mBcV3QIjZljVHc6TlGnEqfAQjssdPbdwbuqjL6tzmcQ/B7Fnw7DtW2TdmnNuhBfpxyrwxSovTIW/ckmiV6y59uGBAAJgZ/YSMEvnCNLoUIp1FOb1eY2jM7+t1k3QyJKU/5OPifolzetVaT3mbP9YVIeaIdA4XD7pHiaTa6tqnfI8pPri7bpqOw0XebzREI19reaFdjk8d72gOQwvTFfHQWdJq9wgboTAOl6x3MiHBwIIgA3YQozMZevlMlr1bT3ibW2d506NEa5032MlPqgi9rxMSoIwODnJXqo0LBXHiFULYaSYi30RMblQFzXfQe0wakuiaTfotln2NyBKHeeZ0Jly+MvkB6m5gfJcQcixugUeCQAwmVY4a2sxsgVpOPtK9riRatdpyXOXQz0mHblWkKK0ECdihERrohJmlQuU2/CnpG1WTZKfjdrGqozkSiEtf332NRSvmCi6Ybknpubb1z+L4JgtHp64oxzVa6he3U7fuBc7T3TJeH5vADhAAMygNvrmvfIR+jkmfrh5jn3EprRMr0waxeNXf7EOi/hTQhK2d4BYPSb2WSZZU8VwjEswCadZoa0XdmUvtFQHmNQzgTv0GQYBfFBDwUy2EMfEZ05OkOfWJpu8N+AAATCDHNa47AffmFExuY4L9Hupo1PD11LOBEzjJ488aVLIKFthsS+4Bl4XSaVdnfcTW/LPi+gfUq8nv16cjIkUkqMsoFrPyy0IoL3es6xUZMoGY0pJ8RQS1ofXW3ISfBKm/U86HJjAxwUOEAAz0DkimrWjqVqkD1y33T+mefWvvZFMmkuItr1Z+RE89lgUAdmXyZGNLCzSsHedhyOVWv1zq/eJEipMRbmcnKz6zJUAnQ9jlSovDIfXTmPep4qF3+md4UbCFY19SFqjDLcjv1bp9WZzhIbQogqdUdW0Ax8QCCAARpg7HHzq/qmMrX+kxdicG8TSm4n+BnwpyhE8/aNwzG7HMjkwJvY8XV5dRpIoksVKFk5KwGhHKVoG5uboBLIQ5dmhxbTLqj/mFY4T9JHFT+7IKvFIm1S3elPeywVKSc3DnpwIHV7fAx+s0FZP8OHwQOBjAwEEwAjnPnjXzUkyUo7mMecGseZmMmlNsA3uwuN1ZGHjw1E5tKQiTKaMjFU51Fs5zP5BdGlMWCv0IIWrtBhLp5HSLtXejM4onZyuMvk76zUhugH3Zy6T33dGVMdXI4QZ9QvNOoCpRoKFPQ8PN6o27wgIIABWsNt8OXQZpk5MuEWobW0dMR9mcFnIhXu4DC+F3baUW+g1d65qn5QCJjfMleZs4EQdVMpseCjkL9HUza5Nhx0aH2o1Hbq/UUpT/7b4YG97UdgYoRP3V9y4GCZ7eIAD9NGBAALgg7LVCvJL923R1tQ1yoqRTvGmr/8la0WV9yLDODapAaOotChiajkyOdxl83NyPGqQa0RlyG4IpfhrZ9+nEXz4Lw4Si47FLSRAz6H6Pqo8BSlf61xhlccVnaE4N1A88PQIAfTRgQACYCXXmASx4xo3ua1zgKYsBxFKUr7ZSxn6iookVVQ7X9xrVa6zJaSjXJK3QyNcNFPxCHotdUz1kQtXJVml87nEiixR+03V4oSUE16HENJh5s1fq/dM9W9BXb4fTFAU5Sxu0qKoqgo/+KALf8EB+vhAAAGwEn3rGrvprLkd7bFg6eg5jd5uEd4ao14/n9mmIvwzVJZqpRw6s1IkuTjqTL/gaczXqYXIinaNMyPOyck3YSkMh3mCJYYDh8e57jgvzTUS1t8zY18W8vMUlsQgssJYu0HBEnp8wAwytwAEEAAzGbuxjH3QLvFrWjNPTz7/zLn6W65m7nD2fWaLDg9SB+3QcPKHVV+qAq7RRf0cDFrCnd26tpRHJEW5dButPrlS9DeJLpP7Y2szfQ62lB/2rl2MAwTQZFiJnZgOxuTdIE4hsU+fIIBuAQggAGZQm5iNeVkQbGr+y5p6z50tE8rEOufOdXRu/znXYxjlVbooPCoqJJ9HY2GRRt5P3McmfpL2FzWWBo8NX0nZTym6pEWPqPOWikqGA7SAPApMSWLOa7pFB+jTpyc8tzcCBBAAM2glCFcDN6M39+WJquc+fNe4Ma260425cWzJ/rE6a+cPN3Ub8un3k93OmyrkFPJiYqIwt9wZ3R5ZZ0j8QWo5ZUJxFuEaXCmv9zHrvoVQGlHhfomUvRUlnBif7vMJzylrW1ANfec+/IXcn1sBfyIAXIG95+IZY8x5meMc7J38zeYuNLSYnRFxuTRhnzNPcniK0mgtP6lgqkE7P9oZEtOD2qnlkHzy6cm5QPnqccNZYn0hZERfTY31o8vyMcbH+yxYhb/6bYrD4eMPoaenB7g/NwT+QgDYgTUOT6u+rWg5L9V8o5FLmJr83WpnDJF6GMu0afarPUawsCs8rMnlQ2usG/YdUfVovaVTpznts73xLbT8Irdj8HN0BTp0R1S5pvIw1qmaT5oTmrnY+/j0iJFfNwYEEAA7YJdwWH8jOiumNhBb54YS11iSpD1aVur1+05ZAyQOT6+EJ6OaiKIinE9qWHRtCHrpsFhRWDpLtn/6VyhEreRn76TpRTi1o9NWo2JDN4HD4T4/3lf9vanQ6eAGDa/O4eGA0NcNAgEEwBJk+gft3kPHQ2Ujh/ZzAtYmaZcHyeSxRHGR97HKIUoVmvOHklmg6EN5eLs+wNV+6AdNDcI5TCKpU2JyqdOIda43URdupokiBDbsi8IsilCrsR4e7vPjfX2IePjBacZniJ9bBQIIgCXw8pv/2qHtc7mIAGtQa2MsOTomOtsDti4TGhpO1KlBQxkKwoJt/Tk0mbygEL5q9CkKDPH7cp35d3ZsOHctJ19X+uyXvKgN87cOk1r1/UzC+j04QHu8h/WozofTc3ivTto9gFcWgB0p7uUi04TPTG00fGmddzO4hACb00YUh4XD0x/MLpAzQ6rapdqqFq2SlzLNfeQspCi7RV5I1ZO/s8WjJE2qR3xneUixrYdKc12ikoGKnCgh45al3KOQzHsPOUB7vYf7eZQOByQ83zgQQADsSO3j+dzw+OEBzW5nLLF5DnqenktSzq9UlMi/VEzKO0JSLRceq9yftIhFmvlO2UcUEtmrzUu5c7gA21QhiHRRKUJfeni/TUSPwiZJqhSY88m6sfV7TNaFWAFzgQAC4MJMnftmr+TpcwLHhnWoWXaSkJuJv+9XIkK6NFWTa6oldUJOrD/PwJzHz1cab4S/iAaRY0NiuVdRrJAKe1KaWbjyfKbGpDiSuhNEkugzCpXGdxm2meoGQSiBCAQQABOY9KE5047fYkLD0TpGcm3079H6a46Eq2dO2+fJfbOXJupn7l0zpBhVCOk8HlcHW4dGh6laIbThlw5yiT5Q1lXro5RCiqJwUnlOOh8omlTmhBjEY9vJbt/TI5ZqaHGp3Dvw/oEAAmACkz40WY/SYXdoejLwHEbrmCDazoowWtbHKWf5BGB31OxrSLniEsUqn7GT3XmpI6F1dzxYO0xaQLqSomXaiPh0idnFcVO3dZcynB0h71Cd/n98xKglAM4BAQTADrRubC32MuW3cJn2ar8VYsupOlkAcLzhF3UQFWIjOT1SDX2lUFNwepRkMqJRVB9jqCvqklydUOEsaVGikq6pct36lL7HzWuMXRtzhfLhex0CD8Ac8FcCwDtgjQyZKjL2ZonLNeS22Bv/6Mg5pTJKSSNezVBNWmqPpxX+klQDq23t8Kk2tDBhW8CE25SYyru5HN3lsPotjgij9M/TzVmDPBcAzgMBBMDOTLoVrRAqe4icJTfQZf2IisWGu6IokGDgENcjerqfaYFTo4Nk+nMbrKckm0Q/aNRhQlZhIBmXmUD5Qqx089Xa3J/hqnQVXizaENnw+AHhLwAmAQEEwALmCASZUs87+cZeTNIo1rFolZ9c/4TzszfDxqLJ8wDlvh3lmM4zDtA50VMLWwXxkVNrxL14kvJuht+DqNFpPTmUJVHJxQstr1ecGCKd+0OmxoieSDPsobS2WeDTExKgAZgCBBAAC9jKdZk77H3v0EZxXVzepEfLN2AfEirOz0O6c16PclfYnh33cS38FVyYVA3XB5hL8aDW8dBGECYc26I4B484UTf0o5hDiMpQnl5MtXwaxSVE23pMPlDSWENPkAANwDQggAC4AP6bf40pYmKu8LpWLohvtzmMnqNikaRVWOXR2Fxom7eT7/8q/0Y9DEqkpiByI9HS4bKPcabmNNRc/JIUkoSMFjNkDRpqLTLLyjeqh/dct70ApOwqxVyiLvkZSzcAMA38pQBwAWQsy3cHilDWhZk8h1ExOkvtqiQ+U0hKLvKbnb0jROPOjgtNddujL4upi/OvJGRyPSk8FgrlRGdf5bj1JMWeKBpJNSdGgD09PREAYBoQQOBqHI9y+neke8SaBNw8tpRLCJ81S2bEhF6zj3IOjS6nPZYY6ko5NqZDNNLe+PNh9JPa0MnQdnxYDk+JOskepYoIy6E+vW335Rwi8aKOdEiu7O+nT8j/AWAq+GsBV+HPv/6mf/3PX/T2dqR//ON3+r//+Q+6B3LAJ1MkwvpzgoPQchImtz3xfL+8Q4s5M0pXziYds5LC5RGV/MPJD0m5P+Jyf0I/VMmQoBycHeX05KZCGdMTUiPJrMhKq7rrrlKKjpVXGF4z40zJMeUO6UrGn8L8PAzi6Eg1tdetXI4ZoAGYDhwgcHE65+fff/9IuQv//a8/+333wNSr1O7KqNDYwelZOvvzPLT40ZIwJxbXFvrUYTMhyUPfyTlpIQxlhI2picguhcFZmQrZvKKQiCRiXSAjYSpJz/G1M5lDToRKYT3p8+O+qMhETRCpBFbg8QnJzwDMAQIIXIXD6YP7EATQ4UqJuhdjgZ6Y7K7MGY5fJPleHr2+VeoH64QeJw7CvhQCS65QPmLX5kqVUqVx1wIlRycmOtfEX87pUW6NlNc1JRRYXfE+KzIqr9+GzGJIcDjXiqnPnz8RAGA6EEDg4hwOTL///pX4ZNl3H+L/5z//o993i2iH4ppwZZ2yvXwePnPUD33PqGyaiivCtVNIicV8thU7eWy9bVVs/YMG8i5SCjylZGejc4wwMiqlIV650n/VGLWvTwslM6SeqB/5hfAXAPPAXwy4Cr//9o1++/a1f3zL0/YvDSe18nXm5AG1wmdzcnaW5B21Suc8JiVWRId6Sk2i94kySHxOVDlpoXODRCrOjxv5FYasc46phbYoDYknduIshdFU/1ujvqr7VONUXwvMjNi3xRNPCH8BMBs4QOBqlLPa3iZbLivREkVz6jgHq3rnTtQ4Wm+qj8N/fS/J39cp7G8pmhSuksqZXKkrlONaZd7xodyjNEVQzGMumsi5ObrGWnhsqMf1YNT5UcKOrPiJIo3V0hpfvnwmAMA8IIAA2Jk5omVumVb9a+qURr1rRqCVLSgB4pOC+90qOcY1m+STFzvaISJqe28y1N8e7cakqzNpS8FoYtNZ35bdGmaFlkqHau+L1MFqMVHhtvh6PD089gugAgDmAQEEwAWYMjOyL7uV4DBJwpuJmAnt+nWvRtoe7VUr70fc+UHYtPwUsTZKIUryGmPK9TLlXXuVx3HjrNDUlpIKf9nE5+wsxdZr67J9+YLkZwCWAAEEwAWIN7MlI4VWt92QF2OLnDYqqtczJQSXLJpGHWTDPEmgVNqtTaBYbNVGvKmQkei+e7uoZjxxkXZT2UFqMFsl30c7O+zDfSV6dFxehcyW7ZKfMfoLgGUgCXoDutmMX15f03DZML9a/+GU/8282YAPx5QJBM+Jm6WTHdbuxecY62u1HzyhjG9D6hM4+lFeUvStfk21Pqdy7ENoMV/G5ufobVE7/OvnzzO9UPXYcvXr0efGftkn1F+tl3VODHbXehR6wsrvACwGfz0LeXl5pV8vL/3vAQ5JvWGrf/wW9nfz3pxE0MPhFKs/0APi9TfJFhMILnV/tvWMzoToglCYMldRdoeSNRL2W5PE3/45JRjXKlVtChVrioVOEZWridZbS1WJfRyPqWkDhjO7v+l8vC1b4nErk3KYS1R3xdXgEtDVKDTtWH37iuRnAJYCATSD7gPox49nev71kr7ZxplZ4+M8gy/lkRrhc7h3ik7f2rrlHzoR1LlC9zAK6l4Yc0QWOzsLz0vn0w7iaEaNadQX0+i8OGmG5dqQdSodkLTkBYsTBlrYKPcn/mC2+UNs6096R5ItRNHuYWk5Qvpay8vrlu2ws0GXIsg/8tMWxM8X3UQ38gsrvwOwHAigiXROz7///p4+gJLgCcH8KH5irgGHMlTZ3/F2fDsJIj4JoQM+xG6Epvih5SJmbT7Q1uJnVtsq/CXik8D7n+pxB7c7HE0UBetztTipOUJR1IzZTuTycmKb0YxKrlJj7JgWb6q/YpwebUyp6x9RqvUQosD9AWAlEEAT+Pv7D/r585eZt6YMd6WNLIzisSiSyJ1DFNbAEuQI3TBbhMY8a5yhuedOXRy1OMu8pX24R4fGlINjAk3kU2+o2FSRskHjMBXhr5SXIzm0phypNCNRMqDSt5xUucTt6Aap7hR5RqFf9ekP7PUXkS/TD1sfGffnE744AbASCKARug+qbtXyLmQVRY7SOUYMxd+FMOr+OwyfZsOgjjShfng8CCARQjjsxlkbztKsqaeVmOypLeY5o5XUjm27r5nIJANTI6SkO2PLJMEj7rBTD2J/5N/KySVdh1AOr+nTYos1c2k0yTuH/4J2GkEq55O+2vTo29cvBABYBwRQAy1+rHuTc32S8Ol/cA51kc0H6vcc8ty3w2NS9Xbf5La5MYL3ix4KvyqvZwMhNaUvq0UW2Tl5pFQVedtYHkI6Gbk+XF9IbMCqaKjWfansqI/8Et0Nqiqf1FxpTcXPhCh81CHyao65/VznxOx8ZjfsHe4PAOvBX1GD7PzoPB8lfkK55OqovJ+DKhMO2zrCiXk6e7FhNHDTlKuy11937zA2z1/xvhlL2q7VO6clUeVr4iebJGyMGV2WYx9bWozr7SajiYc93lCJXsr4nEIuJyh0Mz0vLYHIQ/2ihJi9/ry/D4PnsfuV9u17ALk/AGwHHKAK33/8TOKHiJv5PmYU2PArfvVTrhApoaOPly5R/tYsBO6EziHg1iEXJqLxcltSE1lDOGx+PTa3JkmiUDFRxSKpbuawV3aKsq6ouThl/2OBNDKLcz+NEC0ulCnl+pgcISo67J+lml7Skb/8FIhSXNbFiktfdAsIw/0BYBsggBzPz79SwnOHTk6OHzzRxRk+vPJQ95hPkF2hYf6f4RwKvw9GTHkgfu6Mla7flnlFYyxrQ5r7xP7IGPXC2SbR2kBpqPTQhNBSp1PhsVyjWKn52wttaoHln2u9KKmoRGtfL5n8o/jlSZS4URFAqZwXHnZTZ2DZCwC2A18lHD9+PudQ10E7Pe5GFd2f8C+FxA6cyvfiJwqjSoI0ADVaoS8fKvM35Gu+p7yTaY8RTdFPKhJk3Bqb/CwNXZWFVdZVWdSwPr9yKqdSORznnu6qCNShLtJ1qBLmV1A5JsyX7GMPm3O70Bc+NwDYDgggxa9+gkMyeT6scnX0PyKy2y4PyHxOuTAYPsTAGK3Ql7951+aGiVTzd7iVbbSediJ1elTk4YRe5c0odiSIO5Mv5KSM0hVFy9losWVE5dK4xGfdk1i2HO2l6gyOkH8+w0eBKkfKSQojwqikfP7EPPr8+QlrfgGwMRBAis79GVACJ2ymD8kU2uIsjsgKJi142OX7QPzcB9d+natuhbhFQDfC+B0V0ZDNnFqbkoaejzk9Tenm8nGSmRJycfx5Zli/mCiZS8COe4ad/lgtJ2pIdhaqJ1Yr50fcEX/dqq6O7rMGw94B2B4IoMCvl2Ex02F5CuXiRKFzyCGsOLdPFjw18UOmHgif+6I+Cd4274G19SzNG2q1K2NbzulJJVRCT06Qzucnx7RUGCajJikso2QGlWFcJKnPZ+RDY8y2F3myxHEGR8hd55ywX9pBJjm74yuWvABgF/BXFXh9fSU9Ysv+G/YfuDYijKz4YR8WoyJsFoEoui/2ngRx6vtp7izQ584rk3erpaiIW/GwobULqaTlnFgc2lYujzRaS6KIqJKb4+AQWouKTKR0sHS9uh1r2yhRKZXzhwsUia+RegKKGJp1w7rV3r9i2DsAuwABFHh9HVZuTyKm3xt/5oTnnOPTuUAHI3i6pOdUVoXGeip5HVNm4gXA03RixLknmzQ2oUj427DvbcpDu3UQSOuGY4qNWaFFFVukUDq5zrzraI7p0+35rIpZiWPDeUKFpFGhqZxPRErc6C5E14mp3hk7laMfSdZ9vvz+21cCAOwDhsGf6Gd7JuonMCTv8pAa5t5tD4UofOYPZdRG4RARlR+iE7nE8GZwfeaGpM7OC+TyZ9YwtV862bkn/L2IZL+mf8SUwlU6wmUSuDu3JCy9LqpK0nLCf7EI++JcPYbQBVZpznHkVzpbTO/bF6k67cN25VOlAmw5EkdZBInZZicif/v2BaEvAHYEf13UzcQavjkq8TJsRtWjwlzkcn/6fwfrEqmQ19T7ELye+2XLmZ334nyfsiuSxIXkUE9yhIzrkqJgoQblgIg6zra8FK26fjIXrk0Kd6lcHxn7apJyekzltp3Up/OvFxcOlFTzo+Lmly+f6fOnJwIA7AcEEA1W/IF1jo/7p/OCuhNSyCs7PrrsHPK3Upp9Lvi4jAmKSzp/U3PSxkSaqAl8ynlukv0SS5N+ZEZWVcJg5xwZL4gGpaW3y9+2b+FvVlu60nJ0qJiiQEQK/WKfK1HhwHw8pTR57dN9rpxcHyx3AcD+IARG5BIsXR6QcnPMaK9YOiQ15t80Cz/XCbgP3kt4c05OWkSH7PLK8v1WyqtJg7PIh6SUZHHOT+6IbaP6ZyFldg2ZMJrWPEZlESfjR4ksrZvin7veJ0VrqXBe4kOUwyWmJ4VQ49xdP1z/P/747V26gADcGnCAaPh4KhwfsqPADsrp0WEudi7QEuD8gL0oozjerqDZlC5IWVExfFzsAzF9DC6MkHVszvSNy2rDQ9H6xv59CZGZVDEKFv9ElbG16hcUdhdYlhCT65TOqThf3XPWJT0j7weAy4C/NKIQylLi5hDzeTiZ5EM5NST+oEZ7rfy2JslByuAb4O2ic8TGymyBFRoVl2dFM5xiOMp5ibti+xWF4vVSzM0xYbUYNkv5O15ccP6Zk4uqdQ/lONWlYl25lOgOc9ZGaXd95uexGZxN7pO+VqnX8e3bF/qEvB8ALgZCYEQ5d0eHvohssvNQ0HybnHuT6ke3NL7Wji1rAG6LKa/tLiu80/Yw+/wWUiJD1Mgm68KYOijmA+kQVsyNEyto/N9JrCGFrWQYaVaUERN2IlWvOHumNYllGuoe8oxM+DpE0e3fuP7N5lpt5URfPn/qJzwEAFwOOEBUC2uxWdQ0uUPRUmdnq/u6GnjxA5cHeJa8J8ZCqLX6NnOXUjKv/lJAVGYPJ4lD1XCTcWdiyXJJCZ2HE2szx0Q5QKJzayRXrNqsSSkauVYiP1ePvW4t/sS1RcX15DDip6en3v0BAFwWCCAKy1/o20gveA7kl7bQy2S07jlzVueGy3Pf1N4fS94TY8nztffjFu877aTU65OoSRTBXbHKpXjIbrdp0GoldQIX1YVelHXTEJeqPPtUQ08wGbsSvxjF7TRFUFBgNjdo6HFOFs89eXx8wGSHAFwJCCCiPunQOD4UR3u55Gea9+0ZAgeMMfb+mPo+u9770YZ/TApNvRglYWDEATfCWkTEZbhZ/ND3ZAWF1Gae1uv+N+vuKTXjyBlDkrYH0SOqm0J53iNdlTrbzS3UfaH64/dvcIIBuBIQQIFDzPVhsmEvYvd7PviAA3OZPgPzPFEz573I1HKpfJ16p1TLmKPKBTHD8H2lorJpgsVS671oD6whwMQdKiN10bE64wuJyinSfdNFxDpkOUEp7+++dP3jj98w4guAK4K/vsDDw0PK7cmjdFjty2XnChq/dtCUeiCaQMfWUyTMmuuHqJqcr9+aMQ9I570Y4yPscaZPeGgdm+TsWAUR6suCyIodG0Izs0mbSnWfqRlhs/VJ1Z3SoS8S/bcaXWJScTwVNoujSXvx8w3iB4Arg7/AwOFg5wJK4qeS77M0lLDlek/gPlgzOWY1AXqioJo287NvI4eIfKryIKZMscKWGb1WyXXnxsMPydtcdXAkhaliGE5dTeU69JHYvA5/5dBXLVk7yx52lyD0APEDwLsBw+AV3YdTyrXk/V0YiBywJ7X311JBlZ0evz/XbPflGaLtsHBn+YTdSV445ycJDh0S86IpW0fBoWHyyT56tmYRM36LdKgtXiuFes7PlG0FoHHBQo9iiYeHQ5/zA/EDwPsAf4mKnPtDi0forBFN+8otAMY5Pzmjc4LiTx0S6wsOP6L4YXtAuSZZqCTHiI00URVnZWFDTLpwTpROp3B0pILQEdf36nUGH0nV48Nc5enllAC69NPjI3J+AHhnwAFypA8w92XVHBthjasDPwi0GJtE8+y5zJPel+fK5Dlwsv7IQ7vLZJsofsTt830SbbtKW5rEZqIL5PQHxXl/zIrvySCSShd5vC2y3bJ9LsNo5lqSICL6/OkT5vkB4B2CryMtKloHicngWqzJBYrLOLgKZ9aRHycvhKkUMaGw1BKfVY5OFCRJO3ljJVk1XnRJEjv6hDzpYdnn0dnXJfeznKOovGbS1+6SmKJ7HHvbPf765QvEDwDvFDhAI0zLAZhQz4pv7x+V7rl6ezvS69sbHY9HEhPC6GbaPtDj4+lfP/oOwnIv4nv43Bpg596jypxJ5Lya3M4gDLj0VlRZm7Q8PBiWvBDboGtY0jaV/Rvtv8390QLLOj02TGYcH6qJI5XEpC8ziJ9v3772szwDAN4nEEAjbJWkfC/ip3u+Xl5e6dfp39tJ+PgE2fyl3roGT08P9OkUJvj86QliaGOi+9Ne30rOhshiUnO5L22F+ux+rXmoJlBE/ZLRxo1wMW+r4B7l/luxMxQTm16kkqoLtOASqbpn/n1t98UJDn/rfwMA3i/8zz9/3sfdeUfO3WBunSh8nk//qHN71P5cKAtBfcPTYqhLEO1Ww/7y5VM/Ig+cR4uYjq3fb3r0VLmfSAuOcETtJ3csnOeqS+8KI4iEfI1UqyedKuSqybk/lJOBir40qIb20jEd/mN9Bb2I7xY1hZAH4P0DAQRW8fLydnJ8fpEc7Q2rNoFePCjVb855iHP3zbnLnfj8GeGDGpcW1t4BygJF3fydTvILfxo3yBtBtfeKv0aJeT5UJD+HFoqyqUNKA+mLqj2DKdSVTyOpul/qqAyvSSd88J4F4OOAEBhYRHdziqGuPuMjGjaD+ulzfMyEefHQ6b8Du2OVb+Z/f/9Br6+vfQIpvk1bLu8qSmPTq55GeVILhya3JIa2uNVKOO4dHVs4eS+6kC6dBBmZC3A9bzi48aDKbxL9fh3ETxfC/fb1K0JeAHww4ACB2fTi59dLGJAjefRMvMnlu0q+3TmBE8t1N5NjcI9iWEG7QnwSS3/88Q0hsTPs4Qo165MsX/KjHBKqJwxTsb8QL6TdJV+OKu6hHfaenR59vneSwpsutCvqespBD6Z1sl0bwo5fOtfnE1wfAD4iEEBgFjHfR8TOypukThhaLDZBQ93E9AKXpoLi5hbpbjRIKt2P80nQPvyVt2r5MDJF/FgrhWy1YrfDhg6BGfFTE1XBWxL3vqrWTdTI9YnXFM5Tzs/j4+PJ9fmC9yQAHxgIIDCZ7obw+vqWxE/81m9vVFKEFcK9w9ysRN9QRsRPuvHxsIYSwmH7UZv2IU90KEXuS9wygig/HH6EYetFno8TLqaaoXG7TwmmWLWQGsGmuuUTn21TMoS0lGDnar5R0aO+usPDgb5+/nQKeyF7AICPDv6KwWT6UFWYzC7eAsMo5BxKON1MDqS+PaeJ4cJ38nSiycAI3+ZLtND6/uOZfsOkcptQc328+FFH9IlEItVDlVasuDVipJJe3HKDGiPc+vfMcThuBVVoqJExrZ2k/Dyowmz7End+/fLUj1KECAfgNoAAApMY8nTCcOschzh9JeZaPCGXIy2QwuHukBFI5bn2Vjgk0XZJ0c/PLxhpswHn84XYzPQcdjXDTZUDRt50r+lRjpRCU0oG+bJe5tiOU9BVY2IlFBX1IDpRuo1G7o9/Pz89PdGXk+uDcBcAtwUEEJjIcBNJ7k5v/XC+/+kbYWO2XqOE4vw1RJSHCA115J/xpByq+PXycvoW/ohv4QuZmixdiJ+OQujGX5K3lSWoE5TNGlmqkJJIScxYOWUnNSSXbxTbZCEX5krFe1dSd09Uf5i885Xbenx86KdjgPAB4DaBAAKT4CBq2N6JTjeXmO4cf+oZf5M6UuT8jOT0SM7naLefv9F3w+8x8mY/dHJ6DkEOW/oVHV6THLzULl8unbEjtqQMc0nNRcpnGwtHKxzTM+sMiRLQur7WxJHd7i6/53Pn+EBkA3DTQACBSWhxkpdp4v4GmJ2cIGRE5/rEDB9KN7r0rVzyzSp9gw+PKdws8zd0Tk5TFwr79AQXaAnn3B+vRfIzHEKRydGh4ghR6Riltcjijr6wTaLX9agKg4sUd+n3Ctt9onumhVI+u9I7Q9fPLr8H7ysA7gcIIDAZ7f6kEAKTGSlk/ADlGolOZk03pCyMJAojFYPobnS9luIYmgjBMaF+NBpG4myPDn353GDjAHmzxciLKFjZhpoKF4dc+FNXIc59iu8bt6aYeGHjPKM42pB89UNdXZirW5D3CcIHgLsDdxCwCH2zGLRNujXScLs5qBAZ57QgofwNn4xRlPJ8cjFRgTV1/mnj7XgkBMG2x8/Q3e8btigJ3MoIMeuwMPmRX6HytI+JC9GTxI7PITLbsSwXokskvi9VuIvr3k/n9jw9PtLDAybYBOBegQACkxhb8NUoGntU7U02TnKMmPO3+liE3I0x3xStwGqt1A3W4XNjtPsSH1k1ql4uzs5KLCraFUwZyF25Y37tdH5OK1al6hbVH/0ziejUvdzrLp+nd3sgegAAAQggMAlpDjUu9x/6dcC6R8Ow5/TFX7lA7PKF+jXCaFgjrA+HqSRptjJJta3DcmBLskgVJWp8qnGpVpJDpPLEBsUivgHSK8qLGeqVX3XSu+zh1APSoijpZ+knLeyWUIHoAQDUgAACZ2l+KW+4Qjk/KNyefFKtdgq04aNDGDF/iMjcdAehFG54/bwyDwT2gcOLlV2hYb8co++iXnv9JlGhM5byeMoHS2+M4AyGY1H+hNbD68xUrPtFZPrRuTyd0Dn0oucB7iAAYBQIIHAWae1vhMTSfmXRxJup251cBjuYmtUv7f1IdhSoNBXAvnBIVKcHTiGmODt4N8mhFyjpVUsveBbG3tBJ5UhNwNj9z+LcotPmYehHJ3S6f912J34geAAAc4AAAptiZw6u35C0GMrndTc2Mq5PSro1ibSU3IK2NAN7M7yEneMyvBIPIXwZOYYcLfMShffDsd8vZX2HEKbq3MFDFjS996NEDoQOAGALIIDAVclhFu0UpKMUMmbD8hlc5ImA94MWJg8QKQCAdw4EEDjL1OUT1tRjvtWr5JF+pmH2eUgS9uMmCwAAYBkQQOAsc8VPOUdLu540U7DYhNqY9+NXSPDnAgAAAEuAAAKbIzNyc8YcIT+azGYGEYbAAwAAWAwEEBhlq/DXXNqTLlKakQYOEAAAgKVgdjAwyjnxcy0RcjjgrQsAAGA5uIuA2dh1wKR5bG5dc7iGKwUAAOB2gAACsxkTH1OESU5w5up6XtycPwghLwAAANsAAQQmMVV8TCmVFsgcWV9M5/vo/QAAAMAWQACBSUwVH1OnJ9QjxcbWE4PkAQAAsAcQQGAS5xwgs0TBylBVrS2EvwAAAGwJhsGDSZxzgPTxtaGqMUcIAAAA2AI4QOBqwNUBAABwLSCAwGZUQ1cjGUHFEHosbgoAAOBCQACBzaiGrsiLnJHzkfIMAADgQkAAgYsyVeIgPAYAAGBPIIDAuwRJzwAAAPYEAgisZupMzufOAwAAAC4FBBBYjXdrJk+aCJcHAADAlYAAAgAAAMDdAQEEAAAAgLsDAggAAAAAdwcEEAAAAADuDgggAAAAANwdEEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAPxf8CCZKpk7liiDAAAAAASUVORK5CYII=);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;

        .qr-code {
            max-width: 286px;

            .qr-code-profile {
                img {
                    max-width: 80px;
                    min-width: 80px;
                    height: 80px;
                    object-fit: cover;
                }
            }

            .qr-code-image {
                img {
                    max-width: 146px;
                    min-width: 146px;
                    height: 146px;
                    object-fit: cover;
                }
            }
        }

        .qr-code-btn {
            padding: 10px 20px;
            background: $primary;
            border: none;
            outline: none;
            border-radius: 5px;
            transition: all 0.3s ease;
            width: fit-content;

            &:hover {
                background-color: transparentize($primary, 0.1);
            }
        }
    }

    //Share btn

    .share-btn {
        padding: 10px 20px;
        background: $red;
        border: none;
        outline: none;
        border-radius: 25px;
        transition: all 0.3s ease;
        transform: translateY(0);
        width: fit-content;

        a {
            color: $white;

            i {
                color: $white;
            }
        }

        &:hover {
            background-color: darken($red, 5%);
            transform: translateY(-3px);
        }

        @media (max-width: 575px) {
            margin: 0 auto;
        }
    }

    //contact
    &__contact {
        .contact-form {
            input,
            textarea {
                padding: 0.688rem 0.75rem;
                background-color: #f4f4f4;
                border: 1px solid #f4f4f4;
                outline: none;

                &:focus {
                    box-shadow: unset;
                    border: 1px solid $primary;
                }

                &::placeholder {
                    color: #969696;
                }
            }

            .contact-btn {
                padding: 10px 20px;
                background: $red;
                border: none;
                outline: none;
                border-radius: 50px;
                transition: all 0.3s ease;
                transform: translateY(0);

                &:hover {
                    background-color: darken($red, 5%);
                    transform: translateY(-3px);
                }
            }
        }

        //Download btn
        .vcard-two-btn {
            padding: 10px 20px;
            background: $red;
            border: none;
            outline: none;
            border-radius: 50px;
            transition: all 0.3s ease;
            transform: translateY(0);

            i {
                color: $white;
            }

            &:hover {
                background-color: darken($red, 5%);
                transform: translateY(-3px);
            }

            @media (max-width: 575px) {
                margin: 0 auto;
            }
        }
    }

    //language btn
    .custom-language {
        z-index: 10;
    }

    .language {
        ul {
            list-style: none;

            .lang-list {
                padding: 10px 20px;
                background: #fb5253;
                border: none;
                outline: none;
                border-radius: 25px;
                transition: all 0.3s ease;
                width: fit-content;

                .lang-head {
                    color: white;

                    i {
                        color: white;
                    }
                }

                .lang-hover-list {
                    margin: 15px 0 0;
                    font-size: 15px;
                    width: 115%;
                    left: unset !important;
                    right: 0;
                    min-width: 150px;

                    li {
                        padding: 5px 15px;

                        &:hover {
                            background-color: #f1faff !important;

                            a {
                                color: #009ef7 !important;
                            }
                        }

                        &.active {
                            background-color: #f1faff !important;

                            a {
                                color: #009ef7 !important;
                            }
                        }

                        a {
                            color: #5e6278 !important;
                            text-decoration: none;
                        }

                        img {
                            height: 20px !important;
                            width: 25px !important;
                        }
                    }
                }
            }
        }
    }
}

.vcard-two {
    .language ul {
        .lang-list {
            .lang-hover-list {
                margin: 0 0 0;

                &.top-dropdown {
                    margin: -80px 0 0;
                }
            }
        }
    }
}

.profile-description {
    @media (min-width: 576px) {
        max-width: calc(100% - 180px);
    }

    font-size: 17px;
    text-align: center;
    color: #6c757d !important;
}

#wrap {
    background-color: #ddd;
    padding: 50px 0;
}

#slider {
    width: 500px;
    margin: 0 auto;

    img {
        width: 100%;
    }
}

button {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.slide-arrow {
    position: absolute;
    top: 98%;
    width: 15px !important;
}

.prev-arrow {
    z-index: 99;
    left: 137px;
    height: 0;
    border-left: 0 solid transparent;
    border-right: 11px solid #3a4050;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media (max-width: 575px) {
        left: 40px;
    }
}

.next-arrow {
    z-index: 99;
    right: 137px;
    height: 0;
    border-right: 0 solid transparent;
    border-left: 11px solid #3a4050;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media (max-width: 575px) {
        right: 40px;
    }
}

.slide-arrow-blog {
    position: absolute;
    top: 85%;
    width: 15px !important;
}

.vcard-two__blog .blog-slider .slick-list {
    margin-bottom: 30px !important;
}

.vcard-two__blog .blog-slider .slick-dots {
    bottom: 20px !important;
}

.lb-data .lb-number {
    display: none !important;
}

.social-icons {
    img {
        border-radius: 50% !important;
    }
}
