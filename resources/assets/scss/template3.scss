* {
    margin: 0;
    padding-right: 0;
    box-sizing: border-box;
}

.btn-check {
    &:focus+.btn {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

.btn {
    &:focus {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

//color variable
$primary: #313BAC;
$white: #fff;
$dark: #010101;
$bg-color: #EDF2F8;
$yellow: #FFCA2C;
$grey: #B8B8B8;

.vcard6-sticky-btn {
    background: $white;
    color: #FE80AB !important;
}

.main-section {
    .main-bg {
        overflow: hidden;
        max-width: 576px;
        box-shadow: 1px 1px 25px 0 rgb(88 88 88 / 35%);
        background: linear-gradient(270deg, #8C9EFF 100%, #3D5AFE 100%);

        .head-img {
            height: 240px;

            img {
                height: 240px;
                width: 100%;
                object-fit: cover;
            }

            .image-icon {
                img {
                    height: 20px;
                    width: 25px;
                }
            }
        }

        .circle-img {
            left: 400px;
            top: 180px;
        }

        .triangle-down-img {
            left: 495px;
            top: 30px;
        }

        .oval-img {
            left: 520px;
            top: 256px;
        }


        .main-profile {
            background: linear-gradient(136.86deg, rgba(255, 255, 255, 0.59) -5.12%, rgba(255, 255, 255, 0.0001) 125.41%);
            box-shadow: 6px 10px 46px rgba(0, 0, 0, 0.202492);
            backdrop-filter: blur(32.6194px);
            border-radius: 0 0 20px 20px;

            .profile-img {
                padding: 20px;

                @media (max-width: 576px) {
                    flex-direction: column;
                    align-items: center;
                }

                .user-details-section {
                    @media (max-width: 767px) {
                        margin-top: -50px;
                    }
                }

                .pro-img {
                    min-width: 120px;
                    width: 120px;
                    height: 120px;
                    border-radius: 20px;
                    object-fit: cover;
                    top: -131px;

                    .big-title {
                        font-size: 28px;
                        color: #2D1863;
                    }

                    .small-title {
                        font-size: 20px;
                        color: #806EAF;
                    }
                }


                .social-section {
                    .social-icon {
                        max-width: 260px;
                        width: 100%;

                        .pro-icon {
                            min-width: 45px;
                            width: 45px;
                            height: 45px;
                            border: 1px solid white;
                            border-radius: 50%;
                            padding: 10px;
                            font-size: 20px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            margin: 3px;

                            @media (max-width: 575px) {
                                min-width: 40px;
                                width: 40px;
                                height: 40px;
                            }

                            .icon {
                                color: #fff;
                                font-size: 1.1em;
                                margin-top: 4px;
                            }
                        }
                    }
                }

                .event-icon {
                    .email-text {
                        word-break: break-all;
                    }
                }
            }
        }


        .about-section {
            .about-details {
                text-align: center;
                background: white;
                padding: 15px;
                border-radius: 10px;

                .about-icon {
                    font-size: 40px;
                    color: #806EAF;
                }

                .about-title {
                    font-size: 18px;
                    color: #2D1863;
                }
            }
        }

        //qr-code css
        .main-qrcode {
            .orengcircle-img {
                left: 30px;
                top: 25px;
            }

            .uptriangle-img {
                left: 490px;
                top: 20px;
            }

            .halfcircle-img {
                bottom: -10px;
                left: 47px;
            }

            .orengtriangle-img {
                left: 535px;
                bottom: -30px;
            }

            .circle2-img {
                bottom: -40px;
                left: 20px;
            }

            .main-Qr-section {
                background: #FE80AB;
                padding: 20px;
                border-radius: 20px;

                .qr-img {
                    min-width: 146px;
                    width: 146px;
                    height: 146px;
                    object-fit: cover;
                }

                .qr-logo {
                    min-width: 70px;
                    width: 70px;
                    height: 70px;
                    object-fit: cover;
                }

                .Qr-btn {
                    background: #FFFFFF;
                    border-radius: 5px;
                    color: #FE80AB;
                    font-size: 14px;
                    font-weight: 600;
                    transition: all 0.3s ease;

                    &:hover {
                        box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important;
                    }
                }
            }
        }

        //share
        .share-btn {
            background: #FFFFFF;
            border-radius: 5px;
            color: #FE80AB;
            padding: 10px 20px;
            transition: all 0.3s ease;

            a {
                color: #FE80AB;
                font-size: 14px;

                i {
                    color: #FE80AB;
                }
            }

            &:hover {
                box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important;
            }

            @media (max-width: 575px) {
                margin: 0 auto;
            }
        }

        //contact us

        .main-contactus {
            .lightyellow-img {
                top: 245px;
            }

            .smallpink-img {
                left: 170px;
                top: 265px;
            }

            .light-img {
                left: 230px;
                top: 150px;

                @media (max-width: 570px) {
                    left: 150px;
                    top: 50px;
                }
            }

            .smallblue-img {
                left: 305px;
                top: 175px;

                @media (max-width: 570px) {
                    display: none;
                }
            }

            .halfbox-img {
                right: 0;
                top: 280px;

                @media (max-width: 570px) {
                    top: 480px;
                }
            }

            .contactus-section {
                .main-contact {
                    padding: 20px;
                    background: linear-gradient(136.86deg, rgba(255, 255, 255, 0.59) -5.12%, rgba(255, 255, 255, 0.0001) 125.41%);
                    box-shadow: 6px 10px 46px rgba(0, 0, 0, 0.202492);
                    backdrop-filter: blur(32.6194px);
                    border-radius: 20px;

                    .form-label {
                        font-size: 14px;
                        font-weight: 500;
                        color: white;
                    }

                    .contact-icon {
                        border-color: white;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;

                        &::placeholder {
                            font-size: 12px;
                            color: white;
                            opacity: 1;
                        }

                    }

                    .contact-input {
                        border-color: white;
                        border-radius: 5px;

                        &::placeholder {
                            font-size: 12px;
                            color: white;
                            opacity: 1;
                        }

                        &:focus {
                            box-shadow: unset;
                        }
                    }

                    .contact-btn {
                        background: white;
                        color: #FE80AB;
                        font-size: 14px;
                        border-radius: 5px;
                        transition: all 0.3s ease;

                        &:hover {
                            box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important;
                        }
                    }
                }
            }

            //Download btn
            .vcard-six-btn {
                background: white;
                color: #FE80AB;
                font-size: 14px;
                border-radius: 5px;
                transition: all 0.3s ease;
                padding: 10px 20px;

                i {
                    color: #FE80AB;
                }

                &:hover {
                    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important;
                }

                @media (max-width: 575px) {
                    margin: 0 auto;
                }
            }
        }

        //language btn
        .language {

            ul {
                list-style: none;

                .lang-list {
                    padding: 10px 20px;
                    background: white;
                    border: none;
                    outline: none;
                    border-radius: 5px;
                    transition: all 0.3s ease;
                    width: -moz-fit-content;
                    width: fit-content;

                    .lang-head {
                        color: #FE80AB;

                        i {
                            color: #FE80AB;
                        }
                    }

                    .lang-hover-list {
                        margin: 15px 0 0;
                        font-size: 15px;
                        width: 115%;
                        left: unset !important;
                        right: 0;
                        min-width: 150px;

                        li {
                            padding: 5px 15px;

                            &:hover {
                                background-color: #f1faff !important;


                                a {
                                    color: #009ef7 !important;
                                }
                            }

                            &.active {
                                background-color: #f1faff !important;

                                a {
                                    color: #009ef7 !important;
                                }
                            }

                            a {
                                color: #5e6278 !important;
                                text-decoration: none;
                            }

                            img {
                                height: 20px !important;
                                width: 25px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

.heading-title {
    font-size: 28px;
    color: #ffffff;
}

.slick-slide div {
    width: 100% !important;
    height: 100%;
}

.product-block {
    height: 208px !important;
    width: 252px !important;
    max-height: 208px !important;
    max-width: 252px !important;
}

.main-section {
    .main-bg {
        .language ul {
            .lang-list {
                .lang-hover-list {
                    margin: 0 0 0;

                    &.top-dropdown {
                        margin: -80px 0 0;
                    }
                }
            }
        }
    }
}

.main-section .main-bg {
    width: 100% !important;
    height: 100% !important;
}

.main-section-vcard6 .main-bg {
    min-height: 100vh !important;
}


.profile-description {
    font-size: 17px;
    text-align: center;
    color: white;
}

#wrap {
    background-color: #ddd;
    padding: 50px 0;
}

#slider {
    width: 500px;
    margin: 0 auto;

    img {
        width: 100%;
    }
}

button {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.slide-arrow {
    position: absolute;
    top: 99%;
    width: 15px !important;
}

.prev-arrow {
    z-index: 99;
    left: 137px;
    height: 0;
    border-left: 0 solid transparent;
    border-right: 11px solid #eff3f6;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media(max-width:575px) {
        left: 40px;

    }
}

.next-arrow {
    z-index: 99;
    right: 137px;
    height: 0;
    border-right: 0 solid transparent;
    border-left: 11px solid #eff3f6;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media(max-width:575px) {
        right: 40px;

    }
}

.slide-arrow-blog {
    position: absolute;
    top: 85%;
    width: 15px !important;
}

.main-section .main-bg .vcard-six-blog .blog-slider .slick-list {
    margin-bottom: 17px;
}

.slick-dots {
    bottom: -30px !important;
}

.morelink {
    color: $white;
}

.lb-data .lb-number {
    display: none !important;
}