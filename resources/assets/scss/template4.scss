* {
    margin: 0;
    padding-right: 0;
    box-sizing: border-box;
}

.btn-check {
    &:focus+.btn {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

.btn {
    &:focus {
        outline: unset !important;
        box-shadow: unset !important;
    }
}

//color variable
$primary: #313BAC;
$white: #fff;
$dark: #010101;
$bg-color: #EDF2F8;
$yellow: #FFCA2C;
$grey: #B8B8B8;

.vcard7-sticky-btn {
    background: $bg-color;
    color: $dark;
}

.main-section {
    .main-bg {
        overflow: hidden;
        max-width: 576px;
        background: #ECF0F3;

        .main-banner {
            height: 280px;
        }

        .banner-img {
            object-fit: cover;
            height: 280px;
            width: 100%;
        }

        .main-profile {
            .profile-img {
                .user-profile {
                    img {
                        width: 120px;
                        min-width: 120px;
                        height: 120px;
                        object-fit: cover;
                    }
                }

                .big-title {
                    font-size: 28px;
                    color: #2E4D5C;

                    @media (max-width: 576px) {
                        font-size: 20px;
                    }
                }

                .small-title {
                    font-size: 20px;

                    @media (max-width: 576px) {
                        font-size: 14px;
                    }
                }


                .social-section {
                    .social-icon {
                        background: #ECF0F3;
                        box-shadow: inset -10px -10px 20px #FFFFFF, inset 10px 10px 20px #D1D9E6;
                        border-radius: 5px;
                        padding: 10px;

                        a {
                            width: 20%;
                            display: flex;
                            justify-content: center;
                            margin: 10px 0;
                            text-decoration: none;
                        }

                        .pro-icon {
                            min-width: 45px;
                            width: 100%;
                            padding: 10px;
                            font-size: 24px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            flex-wrap: wrap;
                        }

                        .icon {
                            font-size: 1.5em;

                            @media (max-width: 575px) {
                                font-size: 1.1em;
                            }
                        }

                        .facebook-icon {
                            color: #1B95E0;
                        }

                        .youtube-icon {
                            color: #ff0000;
                        }

                        .pinterest-icon {
                            color: #bd081c;
                        }

                        .reddit-icon {
                            color: #ff4500;
                        }

                        .instagram-icon,
                        {
                        color: #F58EA9;
                    }

                    .linkedin-icon {
                        color: #1B95E0;
                    }

                    .whatsapp-icon {
                        color: #1AD086;
                    }

                    .twitter-icon {
                        color: #1DA1F3;
                    }

                    .tumblr-icon {
                        color: #35465d;
                    }

                    .globe-africa-icon {
                        color: #557b97;
                    }
                }
            }
        }
    }

    .event-icon {
        background: #ECF0F3;
        box-shadow: inset -10px -10px 20px #FFFFFF, inset 10px 10px 20px #D1D9E6;
        border-radius: 10px;
        padding: 15px;

        .event-title {
            font-size: 12px;
            color: #2E4D5C;
        }

        .event-text {
            font-size: 16px;
            color: #2E4D5C;
        }
    }

    //qrcode
    .main-Qr-section {
        background: #ECF0F3;
        box-shadow: inset -10px -10px 20px #FFFFFF, inset 10px 10px 20px #D1D9E6;
        padding: 20px;
        border-radius: 10px;

        .qr-header-title {
            h4 {
                font-size: 28px;
                color: #2E4D5C;
            }
        }

        .qr-img {
            min-width: 146px;
            width: 146px;
            height: 146px;
            object-fit: cover;
        }

        .qr-logo {
            min-width: 90px;
            width: 90px;
            height: 90px;
            object-fit: cover;
        }


        .Qr-btn {
            background: #ECF0F3;
            border-radius: 5px;
            color: black;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 10px 10px 30px #D1D9E6, -10px -10px 30px #FFFFFF !important;
            transition: all 0.3s ease;

            &:hover {
                box-shadow: 5px 5px 15px #D1D9E6, -5px -5px 15px #FFFFFF !important;
            }
        }
    }

    //share
    .share-btn {
        background: #ECF0F3;
        border-radius: 5px;
        color: black;
        font-size: 14px;
        box-shadow: 10px 10px 30px #D1D9E6, -10px -10px 30px #FFFFFF !important;
        transition: all 0.3s ease;
        padding: 10px 20px;

        a {
            color: black;
            font-size: 14px;

            i {
                color: black;
            }
        }

        &:hover {
            box-shadow: 5px 5px 15px #D1D9E6, -5px -5px 15px #FFFFFF !important;
        }

        @media (max-width: 575px) {
            margin: 0 auto;
        }
    }


    //    contact-us

    .contactus-section {
        .header-title {
            h4 {
                font-size: 28px;
                color: #2E4D5C;
            }
        }

        .main-contact {
            background: #ECF0F3;
            box-shadow: inset -10px -10px 20px #FFFFFF, inset 10px 10px 20px #D1D9E6;
            padding: 20px;
            border-radius: 10px;

            .form-label {
                font-size: 14px;
                font-weight: 500;
            }

            .contact-icon {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;

                &::placeholder {
                    font-size: 12px;
                    opacity: 1;
                }

            }

            .contact-input {
                border-radius: 5px;

                &::placeholder {
                    font-size: 12px;
                    opacity: 1;
                }

                &:focus {
                    border-color: #ced4da;
                    box-shadow: none;
                }
            }

            .contact-btn {
                background: #ECF0F3;
                color: black;
                font-size: 14px;
                border-radius: 5px;
                box-shadow: 10px 10px 30px #d1d9e6, -10px -10px 30px #ffffff !important;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 5px 5px 15px #D1D9E6, -5px -5px 15px #FFFFFF;
                }
            }
        }
    }

    //Download btn
    .vcard-seven-btn {
        background: #ECF0F3;
        color: black;
        font-size: 14px;
        border-radius: 5px;
        box-shadow: 10px 10px 30px #d1d9e6, -10px -10px 30px #ffffff !important;
        transition: all 0.3s ease;
        padding: 10px 20px;

        i {
            color: black;
        }

        &:hover {
            box-shadow: 5px 5px 15px #D1D9E6, -5px -5px 15px #FFFFFF;
        }

        @media (max-width: 575px) {
            margin: 0 auto;
        }
    }

    //language btn
    .language {

        ul {
            list-style: none;

            .lang-list {
                padding: 10px 20px;
                background: #ECF0F3;
                border: none;
                outline: none;
                border-radius: 5px;
                transition: all 0.3s ease;
                width: -moz-fit-content;
                width: fit-content;

                .lang-head {
                    color: black;

                    i {
                        color: black;
                    }
                }

                .lang-hover-list {
                    margin: 15px 0 0;
                    font-size: 15px;
                    width: 115%;
                    left: unset !important;
                    right: 0;
                    min-width: 150px;

                    li {
                        padding: 5px 15px;

                        &:hover {
                            background-color: #f1faff !important;


                            a {
                                color: #009ef7 !important;
                            }
                        }

                        &.active {
                            background-color: #f1faff !important;

                            a {
                                color: #009ef7 !important;
                            }
                        }

                        a {
                            color: #5e6278 !important;
                            text-decoration: none;
                        }

                        img {
                            height: 20px !important;
                            width: 25px !important;
                        }
                    }
                }
            }
        }
    }

}
}

.slick-slide div {
    width: 100% !important;
    height: 100% !important;
}

.product-block {
    height: 208px !important;
    width: 252px !important;
    max-height: 208px !important;
    max-width: 252px !important;
}

.gallery-vcard-block {
    height: 254px !important;
    width: 254px !important;
    max-height: 254px !important;
    max-width: 254px !important;
    object-fit: cover !important;
}

.product-template-block {
    height: 350px !important;
    width: 252px !important;
    max-height: 350px !important;
    max-width: 252px !important;
}

.gallery-profile {
    img {
        height: 227px !important;
    }

    a {
        .gallery-item {
            height: 227px !important;
        }
    }
}

.main-section {
    .main-bg {
        .language ul {
            .lang-list {
                .lang-hover-list {
                    margin: 0 0 0;

                    &.top-dropdown {
                        margin: -80px 0 0;
                    }
                }
            }
        }
    }
}

.main-section .main-bg .activeSlot {
    background-color: #000000 !important;
    color: #fff !important;
}

@media (min-width: 350px) and (max-width: 573px) {
    .gallery-vcard-position {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
}

.main-section .main-bg {
    width: 100% !important;
    height: 100% !important;
}

.main-section-vcard7 .main-bg {
    min-height: 100vh !important;
}

.profile-description {
    font-size: 17px;
    text-align: center;
    color: #6c757d !important;
}

#wrap {
    background-color: #ddd;
    padding: 50px 0;
}

#slider {
    width: 500px;
    margin: 0 auto;

    img {
        width: 100%;
    }
}

button {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.slide-arrow {
    position: absolute;
    top: 99% !important;
    width: 15px !important;

    @media (max-width: 575px) {
        top: 100% !important;
    }
}

.prev-arrow {
    z-index: 99;
    left: 137px;
    height: 0;
    border-left: 0 solid transparent;
    border-right: 11px solid #2E4D5C;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media(max-width:575px) {
        left: 40px;

    }
}

.next-arrow {
    z-index: 99;
    right: 137px;
    height: 0;
    border-right: 0 solid transparent;
    border-left: 11px solid #2E4D5C;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;

    @media(max-width:575px) {
        right: 40px;

    }
}

.slide-arrow-blog {
    position: absolute;
    top: 99%;
    width: 15px !important;
}

.slick-dots {
    bottom: -32px !important;
}

.product-slide-arrow {
    position: absolute;
    top: 89%;
    width: 15px !important;
}

.main-section .main-bg .vcard-seven-blog .blog-slider .slick-list {
    margin-bottom: 22px !important;
}

.lb-data .lb-number {
    display: none !important;
}

.social-icons {
    img {
        width: 36px;
        height: 36px;
    }
}