@component('mail::layout')
    {{-- Header --}}
    @slot('header')
        @component('mail::header', ['url' => config('app.url')])
            <img src="{{ asset(getSettingValue('logo')) }}" class="logo" alt="{{ getSettingValue('title') }}"
                 style="position:relative !important;width: 100px !important;right: -7px; !important;">
        @endcomponent
    @endslot

    {{-- Body --}}
    <div>
        <h1>Hello <span>{{ $data->name }}</span>,</h1>
        <br>
        <h2>Welcome to {{ getSettingValue('title') }} <b></b></h2>
        <p>Your account has been created successfully on {{ getSettingValue('title') }}.</p>
        <div style="display: flex; justify-content: center">
            <a href="{{ url('/#/login') }}" class="btn btn-primary text-center"
               style="padding: 7px 15px; text-decoration: none; font-size: 14px; background-color: #598bff; font-weight: 500; border: none;
               border-radius: 8px; color: white">
                Login Now
            </a>
        </div>
        <br><br>
        <p>Regards</p>
        <p>{{ getSettingValue('title') }}</p>
    </div>

    {{-- Footer --}}
    @slot('footer')
        @component('mail::footer')
            <h6 @class(['p-0'])>© {{ date('Y') }} {{ getSettingValue('title') }}.</h6>
        @endcomponent
    @endslot
@endcomponent
