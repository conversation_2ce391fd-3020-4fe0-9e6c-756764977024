<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="keywords" content="template3">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ ucwords($digitalBusinessCard->name) }} | {{ getAppName() }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" href="{{ getFavicon() }}" type="image/png">

    {{--css link--}}
    <link rel="stylesheet" href="{{ asset('assets/css/template3.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/third-party.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/style.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('css/plugins.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom-template.css') }}">
</head>
<body>
<div class="container main-section">
    <div class="row d-flex justify-content-center">
        <div class="main-bg p-0 allSection collapse show">
            <div class="head-img position-relative">
                <img src="{{ $digitalBusinessCard->cover_image }}" height="400px" class="img-fluid"/>
                <div class="d-flex justify-content-end position-absolute top-0 end-0 me-3">
                    <div class="language pt-4 me-2">
                        @php $selectedLang = !empty(session()->get('languageChange_'.$digitalBusinessCard->url_alias)) ? session()->get('languageChange_'.$digitalBusinessCard->url_alias) : 'en' @endphp
                        <ul class="">
                            <li class="dropdown1 dropdown lang-list">
                                <a class="dropdown-toggle lang-head text-decoration-none" data-toggle="dropdown"
                                   role="button"
                                   aria-haspopup="true" aria-expanded="false">
                                    <i class="fa-solid fa-language me-2"></i>{{ getDefaultLang()[$selectedLang] }}
                                </a>
                                <ul class="dropdown-menu start-0 top-dropdown lang-hover-list top-100">
                                    @foreach (getDefaultLang() as $key => $language)
                                        <li class="{{ $selectedLang == $key ? 'active' : '' }} default-pointer">
                                            <a href="javascript:void(0)" class="change-language"
                                            data-name="{{ $key }}">{{ $language }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="position-relative">
                <img src="{{asset('default-images/template3/triangle.png')}}"
                     class="img-fluid position-absolute triangle-img"/>
                <img src="{{asset('default-images/template3/circle.png')}}" class="img-fluid position-absolute circle-img"/>
                <img src="{{asset('default-images/template3/triangledown.png')}}"
                     class="img-fluid position-absolute triangle-down-img"/>
                <img src="{{asset('default-images/template3/oval.png')}}" class="img-fluid position-absolute oval-img"/>

                <div class="container">
                    <div class="main-profile position-relative">
                        <div class="profile-img">
                            <div class="row d-flex align-items-center mb-4 justify-content-center">
                                <div class="col-md-4">
                                    <img src="{{ $digitalBusinessCard->profile_image }}"
                                         class="pro-img img-fluid position-relative"/>
                                </div>
                                <div class="col-md-8 user-details-section">
                                    <div>
                                        <h4 class="heading-title">{{ ucwords($digitalBusinessCard->name) }}</h4>
                                        <p class="small-title text-light">{{ ucwords($digitalBusinessCard->job_title) }}</p>
                                    </div>
                                    <div class="social-section">
                                        @if(count($socialLinks) > 0)
                                            <div class="social-icon d-flex flex-wrap">
                                                @foreach(getSocialLink($socialLinks) as $value)
                                                <div class="pro-icon">
                                                    {!! $value !!}
                                                </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="pt-2 px-2 profile-description">{{ ucwords($digitalBusinessCard->company) }}</span>
                                </div>
                            </div>
                            <div class="row">
                                    <div class="col-sm-6 mb-4">
                                        <div class="card border-0 bg-transparent">
                                            <div class="event-icon text-white">
                                                <img src="{{asset('default-images/template3/email.png')}}"
                                                     class="img-fluid me-3"/>
                                                <a href="mailto:{{ $digitalBusinessCard->email }}"
                                                   class="email-text text-white text-decoration-none">{{ $digitalBusinessCard->email }}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 mb-4">
                                        <div class="card border-0 bg-transparent">
                                            <div class="event-icon text-white">
                                                <img src="{{asset('default-images/template3/call.png')}}"
                                                     class="img-fluid me-3"/>
                                                <a href="tel:{{ $digitalBusinessCard->phone }}"
                                                   class="email-text text-white text-decoration-none">{{ $digitalBusinessCard->phone }}</a>
                                            </div>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{--Qr code--}}
	        <div class="main-qrcode position-relative pt-8">
		        <img src="{{asset('default-images/template3/orengcircle.png')}}"
		             class="img-fluid position-absolute orengcircle-img"/>
		        <img src="{{asset('default-images/template3/uptriangle.png')}}"
		             class="img-fluid position-absolute uptriangle-img"/>
		        <img src="{{asset('default-images/template3/halfcircle.png')}}"
		             class="img-fluid position-absolute halfcircle-img"/>
		        <img src="{{asset('default-images/template3/orengtriangle.png')}}"
		             class="img-fluid position-absolute orengtriangle-img"/>
		        <img src="{{asset('default-images/template3/halfblue.png')}}" class="img-fluid position-absolute circle2-img"/>

		        <div class="container mt-3 mb-5">
			        <div class="main-Qr-section mb-5">
				        <div>
					        <h4 class="mb-4 text-center heading-title">{{ __('messages.template.qr_code') }}</h4>
				        </div>
				        <div class="row d-flex align-items-center">
					        <div class="col-lg-12 pb-2">
						        <div class="text-center mb-4">
							        {!! QrCode::size(150)->format('svg')->generate(Request::url()) !!}
						        </div>
					        </div>
				        </div>
			        </div>
		        </div>
	        </div>
            <div class="w-100 d-flex justify-content-center sticky-vcard-div">
                <div class="btn-group" role="group" aria-label="Basic example" >
                    <button type="button" class="vcard-btn-group vcard6-sticky-btn rounded-0 px-2 ps-5 py-1" title="{{ __('messages.template.download_template')}}" onclick="downloadVcard('{{ $digitalBusinessCard->name }}.vcf',{{ $digitalBusinessCard->id }})"><i class="fas fa-download fs-4"></i></button>
                    <button type="button" class="template3-share vcard-btn-group vcard6-sticky-btn rounded-0 px-2 py-1"><i class="fas fa-share-alt fs-4"></i></button>
                    <a type="button" class="vcard-btn-group vcard6-sticky-btn rounded-0 px-2 pe-5 py-2" id="qr-code-btn" title="{{ __('messages.template.download_qr_code') }}" download="qr_code.png"><i class="fa-solid fa-qrcode fs-4"></i></a>
                </div>
            </div>
        </div>
    </div>
    @php
        $shareUrl = URL::to('/').'/'.$digitalBusinessCard->url_alias;
    @endphp
    {{-- share modal code--}}
    <div id="template3ShareModel" class="modal fade" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('messages.template.share_my_template') }}</h5>
                    <button type="button" aria-label="Close" class="btn btn-sm btn-icon btn-active-color-danger"
                            data-bs-dismiss="modal">
                    <span class="svg-icon svg-icon-1">
						<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24"
                             version="1.1">
							<g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)"
                               fill="#000000">
								<rect fill="#000000" x="0" y="7" width="16" height="2" rx="1"/>
								<rect fill="#000000" opacity="0.5"
                                      transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                      x="0" y="7" width="16" height="2" rx="1"/>
							</g>
						</svg>
					</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12 justify-content-between social-link-modal">
                            <a href="http://www.facebook.com/sharer.php?u={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Facebook">
                                <i class="fab fa-facebook fa-2x" style="color: #1B95E0"></i>
                            </a>
                            <a href="http://twitter.com/share?url={{$shareUrl}}&text={{$digitalBusinessCard->name}}&hashtags=sharebuttons"
                               target="_blank" class="mx-2 share2" title="Twitter">
                                <i class="fab fa-twitter fa-2x" style="color: #1DA1F3"></i>
                            </a>
                            <a href="http://www.linkedin.com/shareArticle?mini=true&url={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Linkedin">
                                <i class="fab fa-linkedin fa-2x" style="color: #1B95E0"></i>
                            </a>
                            <a href="mailto:?Subject=Share Template&Body={{$shareUrl}}" target="_blank"
                               class="mx-2 share2" title="Email">
                                <i class="fas fa-envelope fa-2x" style="color: #191a19  "></i>
                            </a>
                            <a href="http://pinterest.com/pin/create/link/?url={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Pinterest">
                                <i class="fab fa-pinterest fa-2x" style="color: #bd081c"></i>
                            </a>
                            <a href="http://reddit.com/submit?url={{$shareUrl}}&title={{$digitalBusinessCard->name}}"
                               target="_blank" class="mx-2 share2" title="Reddit">
                                <i class="fab fa-reddit fa-2x" style="color: #ff4500"></i>
                            </a>
                            <a href="https://wa.me/?text={{$shareUrl}}" target="_blank" class="mx-2 share2"
                               title="Whatsapp">
                                <i class="fab fa-whatsapp fa-2x" style="color: limegreen"></i>
                            </a>
                            <span id="vcardUrlCopy" class="d-none" target="_blank"> {{ $shareUrl }} </span>
                            <button class="mx-2 copy-vcard-clipboard"
                                    title="Copy Link" data-id="{{ $digitalBusinessCard->id }}">
                                <i class="fa-regular fa-copy fa-2x" style="color: #0099fb"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const svg = document.getElementsByTagName('svg')[0];
    const blob = new Blob([svg.outerHTML], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const image = document.createElement('img');
    image.src = url;
    image.addEventListener('load', () => {
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 150;
        const context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, canvas.width, canvas.height);
        const link = document.getElementById('qr-code-btn');
        link.href = canvas.toDataURL();
        URL.revokeObjectURL(url);
    });
    let languageChange = "{{ url('language') }}";
    let templateAlias = "{{ $digitalBusinessCard->url_alias }}";
</script>
<script type="text/javascript" src="{{ asset('assets/js/front-third-party.js') }}"></script>
<script type="text/javascript" src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('assets/js/templates/template-view.js') }}"></script>
</body>
</html>
