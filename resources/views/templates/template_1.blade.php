<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="keywords" content="QR builder generate">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{{ ucwords($digitalBusinessCard->name) }} | {{ getAppName() }}</title>
    <!-- Favicon -->
    <link rel="icon" href="{{ getFavicon() }}" type="image/png">
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">

    {{--css link--}}
    <link rel="stylesheet" href="{{ asset('assets/css/template1.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/toastr.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/third-party.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/style.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('css/plugins.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom-template.css') }}">

    {{--google Font--}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@500&family=Roboto&display=swap" rel="stylesheet">
</head>
<body>
<div class="container p-0">
    <div class="vcard-two main-content w-100 mx-auto overflow-hidden collapse show allSection">
        {{--banner--}}
        <div class="vcard-two__banner w-100 position-relative">
            <img src="{{ $digitalBusinessCard->cover_image }}" class="img-fluid banner-image position-relative"
                 alt="background"/>
            {{--shape img--}}
            <img src="{{asset('default-images/template1/shape1.png')}}" class="banner-shape position-absolute end-0"
                 alt="shape"/>

            <div class="d-flex justify-content-end position-absolute top-0 end-0 me-3 custom-language">
                <div class="language pt-4 me-2">
                    @php $selectedLang = !empty(session()->get('languageChange_'.$digitalBusinessCard->url_alias)) ? session()->get('languageChange_'.$digitalBusinessCard->url_alias) : 'en' @endphp
                    <ul class="text-decoration-none">
                        <li class="dropdown1 dropdown lang-list">
                            <a class="dropdown-toggle lang-head text-decoration-none" data-toggle="dropdown"
                               role="button"
                               aria-haspopup="true" aria-expanded="false">
                                <i class="fa-solid fa-language me-2"></i>{{ getDefaultLang()[$selectedLang] }}
                            </a>
                            <ul class="dropdown-menu start-0 top-dropdown lang-hover-list top-100 default-pointer">
                                @foreach (getDefaultLang() as $key => $language)
                                    <li class="{{ $selectedLang == $key ? 'active' : '' }} default-pointer">
                                        <a href="javascript:void(0)" class="change-language"
                                        data-name="{{ $key }}">{{ $language }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        {{--profile--}}
        <div class="vcard-two__profile position-relative">
            <div class="avatar position-absolute top-0 translate-middle">
                <img src="{{ $digitalBusinessCard->profile_image }}" alt="profile-img" class="rounded-circle"/>
            </div>
        </div>
        {{--profile details--}}
        <div class="vcard-two__profile-details py-3 px-3">
            <h4 class="vcard-two-heading">
                 {{ ucwords($digitalBusinessCard->name) }}
            </h4>
            <span class="profile-designation">{{ ucwords($digitalBusinessCard->job_title) }}</span>
            <span class="profile-description d-flex pt-4">{{ ucwords($digitalBusinessCard->company) }}</span>
            <div class="social-icons d-flex flex-wrap justify-content-start pt-4 ">
                @if(count($socialLinks) > 0)
                    @foreach(getSocialLink($socialLinks) as $value)
                        {!! $value !!}
                    @endforeach
                @endif
            </div>
        </div>
        {{--event--}}
        <div class="vcard-two__event py-3 px-3">
            <div class="event-details d-flex">
                <div class="event-image">
                    <img src="{{asset('default-images/email.png')}}" alt="email" class=""/>
                </div>
                <span><a href="mailto:{{ $digitalBusinessCard->email }}"
                         class="event-name text-center pt-3 mb-0 email-btn text-decoration-none">{{ $digitalBusinessCard->email }}</a></span>
            </div>
            <div class="event-details d-flex">
                <div class="event-image">
                    <img src="{{asset('default-images/phone.png')}}" alt="phone" class=""/>
                </div>
                <span> <a href="tel:{{ $digitalBusinessCard->phone }}"
                          class="event-name text-center pt-3 mb-0 email-btn text-decoration-none">{{ $digitalBusinessCard->phone }}</a></span>
            </div>
        </div>
        {{--qr code--}}
	    <div class="vcard-two__qr-code py-3 position-relative px-3">
		    {{--shape img--}}
		    <img src="{{asset('default-images/template1/shape2.png')}}"
		         class="banner-shape-four position-absolute end-0 d-sm-block d-none" alt="shape"/>
		    <div class="container">
			    <div class="row">
				    <div class="col-12">
					    <div class="card qr-code-card p-3 border-0">
						    <h4 class="vcard-two-heading text-center pb-1">{{ __('messages.template.qr_code') }}</h4>
						    <div class="row">
							    <div class="col-12">
								    <div class="qr-code-image d-flex justify-content-center" id="downloadQRCode">
									    {!! QrCode::size(150)->format('svg')->generate(Request::url()) !!}
								    </div>
							    </div>
						    </div>
					    </div>
				    </div>
			    </div>
		    </div>
	    </div>
        <div class="w-100 d-flex justify-content-center sticky-vcard-div">
            <div class="btn-group" role="group" aria-label="Basic example">
                <button type="button" class="text-white vcard-btn-group vcard2-sticky-btn rounded-0 px-2 ps-5 py-1" title="{{ __('messages.template.download_template')}}"
                        onclick="downloadVcard('{{ $digitalBusinessCard->name }}.vcf',{{ $digitalBusinessCard->id }})"><i class="fas fa-download fs-4"></i></button>
                <button type="button" class="template1-share vcard-btn-group vcard2-sticky-btn rounded-0 px-2 py-1"><i
                            class="fas fa-share-alt fs-4"></i></button>
                <a type="button" class="vcard-btn-group vcard2-sticky-btn rounded-0 px-2 pe-5 py-2 text-white" title="{{ __('messages.template.download_qr_code') }}"
                   id="qr-code-btn" download="qr_code.png"><i class="fa-solid fa-qrcode fs-4"></i></a>
            </div>
        </div>
    </div>
    @php
        $shareUrl = URL::to('/').'/'.$digitalBusinessCard->url_alias;
    @endphp
    {{-- share modal code--}}
    <div id="template1ShareModel" class="modal fade" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('messages.template.share_my_template') }}</h5>
                    <button type="button" aria-label="Close" class="btn btn-sm btn-icon btn-active-color-danger"
                            data-bs-dismiss="modal">
                    <span class="svg-icon svg-icon-1">
						<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24"
                             version="1.1">
							<g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)"
                               fill="#000000">
								<rect fill="#000000" x="0" y="7" width="16" height="2" rx="1"/>
								<rect fill="#000000" opacity="0.5"
                                      transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                      x="0" y="7" width="16" height="2" rx="1"/>
							</g>
						</svg>
					</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12 justify-content-between social-link-modal">
                            <a href="http://www.facebook.com/sharer.php?u={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Facebook">
                                <i class="fab fa-facebook fa-2x" style="color: #1B95E0"></i>
                            </a>
                            <a href="http://twitter.com/share?url={{$shareUrl}}&text={{$digitalBusinessCard->name}}&hashtags=sharebuttons"
                               target="_blank" class="mx-2 share2" title="Twitter">
                                <i class="fab fa-twitter fa-2x" style="color: #1DA1F3"></i>
                            </a>
                            <a href="http://www.linkedin.com/shareArticle?mini=true&url={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Linkedin">
                                <i class="fab fa-linkedin fa-2x" style="color: #1B95E0"></i>
                            </a>
                            <a href="mailto:?Subject=Share Template&Body={{$shareUrl}}" target="_blank"
                               class="mx-2 share2" title="Email">
                                <i class="fas fa-envelope fa-2x" style="color: #191a19  "></i>
                            </a>
                            <a href="http://pinterest.com/pin/create/link/?url={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Pinterest">
                                <i class="fab fa-pinterest fa-2x" style="color: #bd081c"></i>
                            </a>
                            <a href="http://reddit.com/submit?url={{$shareUrl}}&title={{$digitalBusinessCard->name}}"
                               target="_blank" class="mx-2 share2" title="Reddit">
                                <i class="fab fa-reddit fa-2x" style="color: #ff4500"></i>
                            </a>
                            <a href="https://wa.me/?text={{$shareUrl}}" target="_blank" class="mx-2 share2"
                               title="Whatsapp">
                                <i class="fab fa-whatsapp fa-2x" style="color: limegreen"></i>
                            </a>
                            <span id="templateUrlCopy{{ $digitalBusinessCard->id }}" class="d-none" target="_blank"> {{ $shareUrl }} </span>
                            <button class="mx-2 copy-template-clipboard"
                                    title="Copy Link" data-id="{{ $digitalBusinessCard->id }}">
                                <i class="fa-regular fa-copy fa-2x" style="color: #0099fb"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const svg = document.getElementsByTagName('svg')[0];
    const blob = new Blob([svg.outerHTML], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const image = document.createElement('img');
    image.src = url;
    image.addEventListener('load', () => {
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 150;
        const context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, canvas.width, canvas.height);
        const link = document.getElementById('qr-code-btn');
        link.href = canvas.toDataURL();
        URL.revokeObjectURL(url);
    });
    let languageChange = "{{ url('language') }}";
    let templateAlias = "{{ $digitalBusinessCard->url_alias }}";
</script>
<script type="text/javascript" src="{{ asset('assets/js/toastr.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('assets/js/front-third-party.js') }}"></script>
<script type="text/javascript" src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('assets/js/templates/template-view.js') }}"></script>
</body>
</html>
