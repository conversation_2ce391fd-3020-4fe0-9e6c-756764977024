<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="keywords" content="template4">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ ucwords($digitalBusinessCard->name) }} | {{ getAppName() }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" href="{{ getFavicon() }}" type="image/png">

    {{--css link--}}
    <link rel="stylesheet" href="{{ asset('assets/css/template4.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/third-party.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/style.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('css/plugins.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom-template.css') }}">
</head>
<body>
<div class="container main-section">
    <div class="row d-flex justify-content-center">
        <div class="main-bg p-0 collapse show allSection">
            <div class="main-banner position-relative">
                <img src="{{ $digitalBusinessCard->cover_image }}" class="banner-img"/>
                <div class="d-flex justify-content-end position-absolute top-0 end-0 me-3">
                    <div class="language pt-4 me-2">
                        @php $selectedLang = !empty(session()->get('languageChange_'.$digitalBusinessCard->url_alias)) ? session()->get('languageChange_'.$digitalBusinessCard->url_alias) : 'en' @endphp
                        <ul class="text-decoration-none">
                            <li class="dropdown1 dropdown lang-list">
                                <a class="dropdown-toggle lang-head text-decoration-none" data-toggle="dropdown"
                                   role="button"
                                   aria-haspopup="true" aria-expanded="false">
                                    <i class="fa-solid fa-language me-2"></i>{{ getDefaultLang()[$selectedLang] }}
                                </a>
                                <ul class="dropdown-menu start-0 top-dropdown lang-hover-list top-100">
                                    @foreach (getDefaultLang() as $key => $language)
                                        <li class="{{ $selectedLang == $key ? 'active' : '' }} default-pointer">
                                            <a href="javascript:void(0)" class="change-language"
                                            data-name="{{ $key }}">{{ $language }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="container">
                <div class="main-profile">
                    <div class="profile-img py-8">
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-4">
                                <div class="user-profile">
                                    <img src="{{ $digitalBusinessCard->profile_image }}"
                                         class="img-fluid rounded-circle"/>
                                </div>
                                <div class="ms-3">
                                    <h4 class="big-title">{{ ucwords($digitalBusinessCard->name) }}</h4>
                                    <p class="small-title mb-0">{{ ucwords($digitalBusinessCard->job_title) }}</p>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-5">
                                <span class="pt-2 profile-description">{{ ucwords($digitalBusinessCard->company) }}</span>
                            </div>
                            <div class="social-section mb-4">
                                <div class="container px-md-5 px-0">
                                    @if(count($socialLinks) > 0)
                                        <div class="social-icon d-flex justify-content-center">
                                            <div class="pro-icon">
                                                @foreach(getSocialLink($socialLinks) as $value)
                                                    {!! $value !!}
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row">
                                <div class="col-sm-6 mb-4">
                                    <div class="card border-0 bg-transparent h-100">
                                        <div class="event-icon text-center h-100">
                                            <div>
                                                <img src="{{asset('default-images/template4/email.png')}}"
                                                     class="img-fluid mb-2"/>
                                            </div>
                                            <span class="event-title">{{ __('messages.template.email') }}</span><br>
                                            <a href="mailto:{{ $digitalBusinessCard->email }}"
                                               class="mb-0 event-text text-decoration-none">{{ $digitalBusinessCard->email }}</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 mb-4">
                                    <div class="card border-0 bg-transparent h-100">
                                        <div class="event-icon text-center h-100">
                                            <div>
                                                <img src="{{asset('default-images/template4/phone.png')}}"
                                                     class="img-fluid mb-2"/>
                                            </div>
                                            <span class="event-title">{{ __('messages.template.phone_number') }}</span>
                                            <br>
                                            <a href="tel:{{ $digitalBusinessCard->phone }}"
                                               class="mb-0 event-text text-decoration-none">{{ $digitalBusinessCard->phone }}</a>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
             {{--Qrcode--}}
	        <div class="container mb-13 ">
                <div class="main-Qr-section mb-5">
                    <div class="qr-header-title">
                        <h4 class="mb-4 text-center">{{ __('messages.template.qr_code') }}</h4>
			        </div>
			        <div class="row d-flex align-items-center">
				        <div class="col-lg-12">
					        <div class="text-center mb-4">
						        {!! QrCode::size(150)->format('svg')->generate(Request::url()) !!}
					        </div>
				        </div>
			        </div>
		        </div>
	        </div>
            <div class="w-100 d-flex justify-content-center sticky-vcard-div">
                <div class="btn-group" role="group" aria-label="Basic example" >
                    <button type="button" class="vcard-btn-group vcard7-sticky-btn rounded-0 px-2 ps-5 py-1" title="{{ __('messages.template.download_template')}}" onclick="downloadVcard('{{ $digitalBusinessCard->name }}.vcf',{{ $digitalBusinessCard->id }})"><i class="fas fa-download fs-4"></i></button>
                    <button type="button" class="template4-share vcard-btn-group vcard7-sticky-btn rounded-0 px-2 py-1"><i class="fas fa-share-alt fs-4"></i></button>
                    <a type="button" class="vcard-btn-group vcard7-sticky-btn rounded-0 px-2 pe-5 py-2 text-dark" id="qr-code-btn" title="{{ __('messages.template.download_qr_code') }}" download="qr_code.png"><i class="fa-solid fa-qrcode fs-4"></i></a>
                </div>
            </div>
        </div>
    </div>

    @php
        $shareUrl = URL::to('/').'/'.$digitalBusinessCard->url_alias;
    @endphp
    {{-- share modal code--}}
    <div id="template4ShareModel" class="modal fade" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('messages.template.share_my_template') }}</h5>
                    <button type="button" aria-label="Close" class="btn btn-sm btn-icon btn-active-color-danger"
                            data-bs-dismiss="modal">
                    <span class="svg-icon svg-icon-1">
						<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24"
                             version="1.1">
							<g transform="translate(12.000000, 12.000000) rotate(-45.000000) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)"
                               fill="#000000">
								<rect fill="#000000" x="0" y="7" width="16" height="2" rx="1"/>
								<rect fill="#000000" opacity="0.5"
                                      transform="translate(8.000000, 8.000000) rotate(-270.000000) translate(-8.000000, -8.000000)"
                                      x="0" y="7" width="16" height="2" rx="1"/>
							</g>
						</svg>
					</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12 justify-content-between social-link-modal">
                            <a href="http://www.facebook.com/sharer.php?u={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Facebook">
                                <i class="fab fa-facebook fa-2x" style="color: #1B95E0"></i>
                            </a>
                            <a href="http://twitter.com/share?url={{$shareUrl}}&text={{$digitalBusinessCard->name}}&hashtags=sharebuttons"
                               target="_blank" class="mx-2 share2" title="Twitter">
                                <i class="fab fa-twitter fa-2x" style="color: #1DA1F3"></i>
                            </a>
                            <a href="http://www.linkedin.com/shareArticle?mini=true&url={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Linkedin">
                                <i class="fab fa-linkedin fa-2x" style="color: #1B95E0"></i>
                            </a>
                            <a href="mailto:?Subject=Share Template&Body={{$shareUrl}}" target="_blank"
                               class="mx-2 share2" title="Email">
                                <i class="fas fa-envelope fa-2x" style="color: #191a19  "></i>
                            </a>
                            <a href="http://pinterest.com/pin/create/link/?url={{$shareUrl}}"
                               target="_blank" class="mx-2 share2" title="Pinterest">
                                <i class="fab fa-pinterest fa-2x" style="color: #bd081c"></i>
                            </a>
                            <a href="http://reddit.com/submit?url={{$shareUrl}}&title={{$digitalBusinessCard->name}}"
                               target="_blank" class="mx-2 share2" title="Reddit">
                                <i class="fab fa-reddit fa-2x" style="color: #ff4500"></i>
                            </a>
                            <a href="https://wa.me/?text={{$shareUrl}}" target="_blank" class="mx-2 share2"
                               title="Whatsapp">
                                <i class="fab fa-whatsapp fa-2x" style="color: limegreen"></i>
                            </a>
                            <span id="vcardUrlCopy" class="d-none" target="_blank"> {{ $shareUrl }} </span>
                            <button class="mx-2 copy-vcard-clipboard"
                                    title="Copy Link" data-id="{{ $digitalBusinessCard->id }}">
                                <i class="fa-regular fa-copy fa-2x" style="color: #0099fb"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const svg = document.getElementsByTagName('svg')[0];
    const blob = new Blob([svg.outerHTML], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const image = document.createElement('img');
    image.src = url;
    image.addEventListener('load', () => {
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 150;
        const context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, canvas.width, canvas.height);
        const link = document.getElementById('qr-code-btn');
        link.href = canvas.toDataURL();
        URL.revokeObjectURL(url);
    });
    let languageChange = "{{ url('language') }}";
    let templateAlias = "{{ $digitalBusinessCard->url_alias }}";
</script>
</script>
<script type="text/javascript" src="{{ asset('assets/js/front-third-party.js') }}"></script>
<script type="text/javascript" src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('assets/js/templates/template-view.js') }}"></script>
</body>
</html>
