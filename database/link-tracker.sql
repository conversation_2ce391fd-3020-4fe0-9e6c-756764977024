-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Aug 24, 2023 at 07:24 AM
-- Server version: 8.0.33
-- PHP Version: 8.1.20

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `qr_builder`
--

-- --------------------------------------------------------

--
-- Table structure for table `analytics`
--

CREATE TABLE `analytics` (
  `id` bigint UNSIGNED NOT NULL,
  `session` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model_id` int DEFAULT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `link_id` bigint UNSIGNED DEFAULT NULL,
  `uri` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `browser` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `operating_system` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `language` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `business_cards`
--

CREATE TABLE `business_cards` (
  `id` bigint UNSIGNED NOT NULL,
  `url_alias` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `job_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `website` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `template_id` bigint UNSIGNED NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `business_card_social_links`
--

CREATE TABLE `business_card_social_links` (
  `id` bigint UNSIGNED NOT NULL,
  `business_card_id` bigint UNSIGNED NOT NULL,
  `social_link_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contact_us`
--

CREATE TABLE `contact_us` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `view` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `coupon_codes`
--

CREATE TABLE `coupon_codes` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `discount` double DEFAULT NULL,
  `how_many_can_use` int NOT NULL DEFAULT '0',
  `used` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `currencies`
--

CREATE TABLE `currencies` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `symbol` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `currencies`
--

INSERT INTO `currencies` (`id`, `name`, `code`, `symbol`, `created_at`, `updated_at`) VALUES
(1, 'Euro', 'EUR', '€', NULL, NULL),
(2, 'Australian Dollar', 'AUD', '$', NULL, NULL),
(3, 'Indian Rupee', 'INR', '₹', NULL, NULL),
(4, 'US Dollar', 'USD', '$', NULL, NULL),
(5, 'Japanese Yen', 'JPY', '¥', NULL, NULL),
(6, 'British Pound Sterling', 'GBP', '£', NULL, NULL),
(7, 'Canadian Dollar', 'CAD', '$', NULL, NULL),
(8, 'Kenyan Shilling', 'KES', 'Ksh', NULL, NULL),
(9, 'Swiss Franc', 'CHF', 'CHF', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `domains`
--

CREATE TABLE `domains` (
  `id` int UNSIGNED NOT NULL,
  `domain` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `features`
--

CREATE TABLE `features` (
  `id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sub_title` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `features`
--

INSERT INTO `features` (`id`, `title`, `sub_title`, `description`, `created_at`, `updated_at`) VALUES
(1, 'Specialized Link Crafting', '[\"Create compact, branded links for sharing ease.\",\"Real-time Insights -> as it happens.\",\"Track click-through rates and user interactions.\",\"Sub alias support\",\"Quick Link generation  by one tap\",\"HTTPS Encryption support\"]', 'Create tailored links and unveil their impact through advanced analytics tracking', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'QRPersona: Codes with Character', '[\"Tailor QR codes with your logo and colors.\",\"Create codes that match your visual identity.\",\"Maintain code quality for all applications.\",\"Generate, share QR Code in minutes.\",\"Organize and track multi-ple codes.\",\"Visualize QR code before generating.\"]', 'Transform QR codes into personalized gateways, enriching connections', '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `front_cms`
--

CREATE TABLE `front_cms` (
  `id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `front_cms`
--

INSERT INTO `front_cms` (`id`, `title`, `description`, `created_at`, `updated_at`) VALUES
(1, 'Create, customize, and share with ease.', 'Share smarter, not harder, with our URL shortener', '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `languages`
--

CREATE TABLE `languages` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `iso_code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `languages`
--

INSERT INTO `languages` (`id`, `name`, `iso_code`, `is_default`, `created_at`, `updated_at`) VALUES
(1, 'English', 'en', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'Chinese', 'zh', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(3, 'French', 'fr', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(4, 'Spanish', 'es', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `links`
--

CREATE TABLE `links` (
  `id` bigint UNSIGNED NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `destination_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url_alias` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `login_histories`
--

CREATE TABLE `login_histories` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `login_at` datetime NOT NULL,
  `country_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `os` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `media`
--

CREATE TABLE `media` (
  `id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `collection_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mime_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `disk` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `conversions_disk` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` bigint UNSIGNED NOT NULL,
  `manipulations` json NOT NULL,
  `custom_properties` json NOT NULL,
  `generated_conversions` json NOT NULL,
  `responsive_images` json NOT NULL,
  `order_column` int UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `media`
--

INSERT INTO `media` (`id`, `model_type`, `model_id`, `uuid`, `collection_name`, `name`, `file_name`, `mime_type`, `disk`, `conversions_disk`, `size`, `manipulations`, `custom_properties`, `generated_conversions`, `responsive_images`, `order_column`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\QrCodeType', 1, '38a1bb89-64b9-436e-bd6d-314f2a880d5d', 'image', '1', '1.svg', 'image/svg+xml', 'public', 'public', 4375, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'App\\Models\\QrCodeType', 2, '8bcd7c5c-1036-4dea-8ced-569301c3a09a', 'image', '7', '7.svg', 'image/svg+xml', 'public', 'public', 2791, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(3, 'App\\Models\\QrCodeType', 3, '8cedaab7-f4ff-4655-8bc4-46bb9b6e74f8', 'image', '2', '2.svg', 'image/svg+xml', 'public', 'public', 2269, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(4, 'App\\Models\\QrCodeType', 4, '3b5f9c51-47dc-474d-998b-d44da4369fee', 'image', '3', '3.svg', 'image/svg+xml', 'public', 'public', 2561, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(5, 'App\\Models\\QrCodeType', 5, 'e8df133e-1733-4b54-b0bd-8a60a53052f2', 'image', '10', '10.svg', 'image/svg+xml', 'public', 'public', 3672, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(6, 'App\\Models\\QrCodeType', 6, '16f850c1-f841-4970-b3d0-df1233f528a0', 'image', '5', '5.svg', 'image/svg+xml', 'public', 'public', 2559, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(7, 'App\\Models\\QrCodeType', 7, '5aaf70fc-26da-4114-9b6f-ff32409cc8d6', 'image', '4', '4.svg', 'image/svg+xml', 'public', 'public', 1275, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(8, 'App\\Models\\QrCodeType', 8, 'de9b53c0-b574-4d1d-9801-66068ade64b8', 'image', '11', '11.svg', 'image/svg+xml', 'public', 'public', 2929, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(9, 'App\\Models\\QrCodeType', 9, 'ec4a4482-87dc-4520-95bb-1f5e81847eb1', 'image', '9', '9.svg', 'image/svg+xml', 'public', 'public', 1586, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(10, 'App\\Models\\QrCodeType', 10, 'ef3382be-dbb3-492c-a5bb-b508179c50ee', 'image', '13', '13.svg', 'image/svg+xml', 'public', 'public', 2998, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(11, 'App\\Models\\QrCodeType', 11, '70c4aefe-5531-4872-8052-6b13e89faabd', 'image', '12', '12.svg', 'image/svg+xml', 'public', 'public', 3169, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(12, 'App\\Models\\QrCodeType', 12, '64eae5ee-a149-408c-a744-fab3b70a7b4f', 'image', '6', '6.svg', 'image/svg+xml', 'public', 'public', 2463, '[]', '[]', '[]', '[]', 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_resets_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_09_15_000010_create_tenants_table', 1),
(5, '2019_09_15_000020_create_domains_table', 1),
(6, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(7, '2022_12_12_095923_create_permission_tables', 1),
(8, '2022_12_13_122011_create_plans_table', 1),
(9, '2022_12_13_122050_create_plan_features_table', 1),
(10, '2022_12_16_064426_create_projects_table', 1),
(11, '2022_12_16_064427_create_qr_codes_table', 1),
(12, '2022_12_16_101321_add_tenant_username_field_on_tenants_table', 1),
(13, '2022_12_17_123454_add_tenant_id_field_on_users_table', 1),
(14, '2022_12_19_101031_create_links_table', 1),
(15, '2022_12_19_114841_create_media_table', 1),
(16, '2022_12_21_071600_add_foreground_gradient_style_column_on_qr_codes', 1),
(17, '2022_12_21_122103_create_coupon_codes_table', 1),
(18, '2022_12_22_112721_create_transactions_table', 1),
(19, '2022_12_23_050429_add_language_field_on_users', 1),
(20, '2022_12_23_060211_add_foreground_color_field_on_qr_codes', 1),
(21, '2022_12_23_064513_create_currencies_table', 1),
(22, '2022_12_23_064514_add_price_field_on_plans', 1),
(23, '2022_12_24_055134_create_subscriptions_table', 1),
(24, '2022_12_24_061445_add_is_default_column_on_plans', 1),
(25, '2022_12_26_052658_add_tenant_id_column_on_links_table', 1),
(26, '2022_12_26_052716_add_tenant_id_column_on_qr_codes_table', 1),
(27, '2022_12_27_123908_create_settings_table', 1),
(28, '2022_12_29_114012_add_status_column_in_users_table', 1),
(29, '2022_12_30_100944_create_analytics_table', 1),
(30, '2023_01_09_113421_add_is_manual_payment_column_on_transactions', 1),
(31, '2023_01_27_044415_add_coupon_code_related_columns', 1),
(32, '2023_01_30_063143_create_login_histories_table', 1),
(33, '2023_01_31_050236_create_social_accounts_table', 1),
(34, '2023_02_06_063803_add_name_field_into_links', 1),
(35, '2023_02_08_053531_add_name_field_to_links_table', 1),
(36, '2023_02_10_043534_create_front_cms_table', 1),
(37, '2023_02_10_044103_create_sub_features_table', 1),
(38, '2023_02_10_044115_create_features_table', 1),
(39, '2023_02_15_042819_create_subscribers_table', 1),
(40, '2023_02_15_104054_create_contact_us_table', 1),
(41, '2023_02_24_124624_add_view_column_to_contact_us_table', 1),
(42, '2023_03_01_042903_add_register_login_captcha_data_to_settings', 1),
(43, '2023_03_07_111718_create_languages_table', 1),
(44, '2023_03_11_053950_language_table_seeder', 1),
(45, '2023_03_20_064813_create_pages_table', 1),
(46, '2023_03_21_040849_add_some_columns_to_pages', 1),
(47, '2023_03_28_122705_add_date_column_to_transactions', 1),
(48, '2023_04_10_130607_set_default_nullable_color_field_in_projects_table', 1),
(49, '2023_04_11_040833_create_qr_code_types_table', 1),
(50, '2023_04_11_090609_run_default_qr_code_types_seeder', 1),
(51, '2023_04_12_061616_create_templates_table', 1),
(52, '2023_04_12_062205_run_default_template_seeder', 1),
(53, '2023_04_12_103504_create_business_cards_table', 1),
(54, '2023_04_13_115156_add_two_fields_in_plan_features_table', 1),
(55, '2023_04_14_105556_add_two_fields_in_analytics_table', 1),
(56, '2023_04_19_104517_create_social_links_table', 1),
(57, '2023_04_19_105035_create_business_card_social_links_table', 1),
(58, '2023_04_20_061033_add_default_link_field_in_social_links_table', 1);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(2, 'App\\Models\\User', 2);

-- --------------------------------------------------------

--
-- Table structure for table `pages`
--

CREATE TABLE `pages` (
  `id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `meta_description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `visibility` tinyint(1) NOT NULL DEFAULT '0',
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plans`
--

CREATE TABLE `plans` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` double NOT NULL,
  `currency_id` bigint UNSIGNED NOT NULL,
  `order` int DEFAULT NULL,
  `trial_days` int DEFAULT '0',
  `type` int NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plans`
--

INSERT INTO `plans` (`id`, `name`, `price`, `currency_id`, `order`, `trial_days`, `type`, `is_default`, `created_at`, `updated_at`) VALUES
(1, 'Standard', 10, 4, 1, 7, 1, 1, '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `plan_features`
--

CREATE TABLE `plan_features` (
  `id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED NOT NULL,
  `qr_code_limit` int NOT NULL,
  `links_limit` int NOT NULL,
  `projects_limit` int NOT NULL,
  `qr_code_types` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `allow_create_business_card` tinyint(1) NOT NULL DEFAULT '0',
  `business_card_limit` double NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plan_features`
--

INSERT INTO `plan_features` (`id`, `plan_id`, `qr_code_limit`, `links_limit`, `projects_limit`, `qr_code_types`, `created_at`, `updated_at`, `allow_create_business_card`, `business_card_limit`) VALUES
(1, 1, 10, 10, 10, '1,2,3,4,5,6,7,8,9,10,11,12,13', '2023-08-24 01:52:33', '2023-08-24 01:52:33', 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `projects`
--

CREATE TABLE `projects` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `qr_codes`
--

CREATE TABLE `qr_codes` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `project_id` bigint UNSIGNED DEFAULT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` int NOT NULL,
  `style` int NOT NULL,
  `foreground_type` int NOT NULL,
  `foreground_gradient_style` int DEFAULT NULL,
  `foreground_color` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `foreground_color1` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `foreground_color2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `background_color` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `background_transparency` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `custom_eye_color` tinyint(1) NOT NULL,
  `eye_inner_color` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `eye_outer_color` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` int NOT NULL,
  `margin_size` int NOT NULL,
  `error_correction` int NOT NULL,
  `extra_data` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `qr_code_types`
--

CREATE TABLE `qr_code_types` (
  `id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `qr_code_types`
--

INSERT INTO `qr_code_types` (`id`, `title`, `description`, `created_at`, `updated_at`) VALUES
(1, 'TEXT', 'Allows the encoding of plain text information that can be quickly accessed when scanned.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'URL', 'It links to a website or webpage, providing quick access when scanned.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(3, 'PHONE', 'Enables quick dialing of a phone number when scanned, eliminating the need for manual entry.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(4, 'EMAIL', 'Easy way to add an email address to the recipient field of an email application when scanned.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(5, 'LOCATION', 'It provides geographic location data when scanned, facilitating navigation and location-based services.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(6, 'WI-FI', 'Allows the automatic connection to a wireless network when scanned.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(7, 'FACETIME', 'Allows for quick and easy video or audio calls when scanned.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(8, 'EVENT', 'Allows easy access to event information such as date, time, and location when scanned.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(9, 'WHATSAPP', 'It Provides a quick and easy way to start a conversation with someone on WhatsApp when scanned.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(10, 'CRYPTO', 'Make payments of crypto currencies easily by scanning the QR Code.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(11, 'VCARD', 'Create Vcard QR Codes and mainly used to make it easier to share contact details on mobile devices.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(12, 'PAYPAL', 'Make payments on Paypal by scanning the QR Code.', '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `display_name`, `is_default`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'Admin', 1, 'web', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'user', 'User', 1, 'web', '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `role_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` bigint UNSIGNED NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `created_at`, `updated_at`) VALUES
(1, 'captcha_on_register_login', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'title', 'QR Code Builder', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(3, 'logo', 'default-images/infyom.png', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(4, 'favicon', 'default-images/favicon.png', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(5, 'enable_new_users_registration', '1', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(6, 'plan_expire_notification', '7', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(7, 'currency', '4', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(8, 'enable_paypal_payments', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(9, 'paypal_client_id', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(10, 'paypal_secret', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(11, 'paypal_payment_mode', 'sandbox', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(12, 'enable_stripe_payments', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(13, 'stripe_Key', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(14, 'stripe_secret', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(15, 'stripe_payment_mode', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(16, 'enable_razorpay_payments', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(17, 'razorpay_key', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(18, 'razorpay_secret', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(19, 'razorpay_payment_mode', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(20, 'captcha_key', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(21, 'captcha_on_the_lost_password', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(22, 'captcha_on_resend_activation', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(23, 'captcha_on_contact_page', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(24, 'captcha_site_key', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(25, 'captcha_secret_key', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(26, 'ads_header', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(27, 'ads_footer', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(28, 'youtube_link', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(29, 'facebook_link', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(30, 'twitter_link', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(31, 'instagram_link', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(32, 'tiktok_link', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(33, 'linked_in_link', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(34, 'from_name', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(35, 'from_email', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(36, 'host', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(37, 'encryption', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(38, 'port', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(39, 'email_username', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(40, 'email_password', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(41, 'custom_js', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(42, 'custom_css', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(43, 'user_announcement_content', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(44, 'user_text_color', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(45, 'user_bg_color', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(46, 'announcement_enabled', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(47, 'emails_to_be_notified', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(48, 'new_user', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(49, 'delete_user', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(50, 'new_payment', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(51, 'new_custom_domain', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(52, 'contact_page_emails', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(53, 'google_client_id', NULL, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(54, 'enable_google_login', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(55, 'facebook_app_id', NULL, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(56, 'enable_facebook_login', '0', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(57, 'term_conditions', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(58, 'privacy_policy', '', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(59, 'email', '<EMAIL>', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(60, 'address', 'C-303, Atlanta Shopping Mall, Nr. Sudama Chowk, Mota Varachha, Surat - 394101, Gujarat, India.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(61, 'phone', '+91 **********', '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `social_accounts`
--

CREATE TABLE `social_accounts` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `provider` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `social_links`
--

CREATE TABLE `social_links` (
  `id` bigint UNSIGNED NOT NULL,
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `default_link` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `subscribers`
--

CREATE TABLE `subscribers` (
  `id` bigint UNSIGNED NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `subscriptions`
--

CREATE TABLE `subscriptions` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED DEFAULT NULL,
  `price_of_plan` double(8,2) DEFAULT '0.00',
  `plan_type` int NOT NULL DEFAULT '1',
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `trial_ends_at` datetime DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `transaction_id` bigint UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscriptions`
--

INSERT INTO `subscriptions` (`id`, `user_id`, `plan_id`, `price_of_plan`, `plan_type`, `start_date`, `end_date`, `trial_ends_at`, `status`, `transaction_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, 10.00, 1, '2023-08-24 07:22:33', '2023-08-31 07:22:33', '2023-08-31 07:22:33', 1, NULL, '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `sub_features`
--

CREATE TABLE `sub_features` (
  `id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sub_features`
--

INSERT INTO `sub_features` (`id`, `title`, `description`, `created_at`, `updated_at`) VALUES
(1, 'QR Templates', 'Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'QR Templates', 'Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(3, 'QR Templates', 'Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(4, 'QR Templates', 'Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(5, 'QR Templates', 'Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s.', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(6, 'QR Templates', 'Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s.', '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `templates`
--

CREATE TABLE `templates` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `templates`
--

INSERT INTO `templates` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'Template 1', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'Template 2', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(3, 'Template 3', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(4, 'Template 4', '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(5, 'Template 5', '2023-08-24 01:52:33', '2023-08-24 01:52:33');

-- --------------------------------------------------------

--
-- Table structure for table `tenants`
--

CREATE TABLE `tenants` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant_username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `data` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tenants`
--

INSERT INTO `tenants` (`id`, `tenant_username`, `created_at`, `updated_at`, `data`) VALUES
('bad7cc68-4f68-4ffe-875b-a7b7068bf6c1', 'user', '2023-08-24 01:52:33', '2023-08-24 01:52:33', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` bigint UNSIGNED NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` date DEFAULT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `payment_mode` int NOT NULL,
  `price_of_plan` double NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `coupon_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_manual_payment` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `language` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `tenant_id`, `language`, `status`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Admin', '<EMAIL>', '2023-08-24 01:52:33', '$2y$10$dUhCuIibes3WmssKy4SdZOggp5pTHQ1usY0yQzA6J9rWiDq/RqzqO', NULL, 'en', 1, NULL, '2023-08-24 01:52:33', '2023-08-24 01:52:33'),
(2, 'User', '<EMAIL>', '2023-08-24 01:52:33', '$2y$10$zOGCBak3dDDfOTFHrquutuX9BTHabIMgYFNxTVEwrDf3O2PLUy936', 'bad7cc68-4f68-4ffe-875b-a7b7068bf6c1', 'en', 1, NULL, '2023-08-24 01:52:33', '2023-08-24 01:52:33');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `analytics`
--
ALTER TABLE `analytics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `analytics_link_id_foreign` (`link_id`);

--
-- Indexes for table `business_cards`
--
ALTER TABLE `business_cards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_cards_url_alias_unique` (`url_alias`),
  ADD KEY `business_cards_template_id_foreign` (`template_id`),
  ADD KEY `business_cards_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `business_card_social_links`
--
ALTER TABLE `business_card_social_links`
  ADD PRIMARY KEY (`id`),
  ADD KEY `business_card_social_links_business_card_id_foreign` (`business_card_id`),
  ADD KEY `business_card_social_links_social_link_id_foreign` (`social_link_id`);

--
-- Indexes for table `contact_us`
--
ALTER TABLE `contact_us`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `coupon_codes`
--
ALTER TABLE `coupon_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `coupon_codes_name_unique` (`name`),
  ADD UNIQUE KEY `coupon_codes_code_unique` (`code`);

--
-- Indexes for table `currencies`
--
ALTER TABLE `currencies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `currencies_name_unique` (`name`);

--
-- Indexes for table `domains`
--
ALTER TABLE `domains`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `domains_domain_unique` (`domain`),
  ADD KEY `domains_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `features`
--
ALTER TABLE `features`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `front_cms`
--
ALTER TABLE `front_cms`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `languages`
--
ALTER TABLE `languages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `languages_iso_code_unique` (`iso_code`);

--
-- Indexes for table `links`
--
ALTER TABLE `links`
  ADD PRIMARY KEY (`id`),
  ADD KEY `links_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `login_histories`
--
ALTER TABLE `login_histories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `login_histories_user_id_foreign` (`user_id`);

--
-- Indexes for table `media`
--
ALTER TABLE `media`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `media_uuid_unique` (`uuid`),
  ADD KEY `media_model_type_model_id_index` (`model_type`,`model_id`),
  ADD KEY `media_order_column_index` (`order_column`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `pages`
--
ALTER TABLE `pages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `pages_title_unique` (`title`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD KEY `password_resets_email_index` (`email`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `plans`
--
ALTER TABLE `plans`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `plans_name_unique` (`name`),
  ADD KEY `plans_currency_id_foreign` (`currency_id`);

--
-- Indexes for table `plan_features`
--
ALTER TABLE `plan_features`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plan_features_plan_id_foreign` (`plan_id`);

--
-- Indexes for table `projects`
--
ALTER TABLE `projects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `projects_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `qr_codes`
--
ALTER TABLE `qr_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `qr_codes_name_unique` (`name`),
  ADD KEY `qr_codes_project_id_foreign` (`project_id`),
  ADD KEY `qr_codes_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `qr_code_types`
--
ALTER TABLE `qr_code_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `social_accounts`
--
ALTER TABLE `social_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `social_accounts_provider_id_unique` (`provider_id`),
  ADD KEY `social_accounts_user_id_foreign` (`user_id`);

--
-- Indexes for table `social_links`
--
ALTER TABLE `social_links`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `subscribers`
--
ALTER TABLE `subscribers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscribers_email_unique` (`email`);

--
-- Indexes for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subscriptions_user_id_foreign` (`user_id`),
  ADD KEY `subscriptions_plan_id_foreign` (`plan_id`),
  ADD KEY `subscriptions_transaction_id_foreign` (`transaction_id`);

--
-- Indexes for table `sub_features`
--
ALTER TABLE `sub_features`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `templates`
--
ALTER TABLE `templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tenants`
--
ALTER TABLE `tenants`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `transactions_user_id_foreign` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD KEY `users_tenant_id_foreign` (`tenant_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `analytics`
--
ALTER TABLE `analytics`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `business_cards`
--
ALTER TABLE `business_cards`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `business_card_social_links`
--
ALTER TABLE `business_card_social_links`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `contact_us`
--
ALTER TABLE `contact_us`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `coupon_codes`
--
ALTER TABLE `coupon_codes`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `currencies`
--
ALTER TABLE `currencies`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `domains`
--
ALTER TABLE `domains`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `features`
--
ALTER TABLE `features`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `front_cms`
--
ALTER TABLE `front_cms`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `languages`
--
ALTER TABLE `languages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `links`
--
ALTER TABLE `links`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `login_histories`
--
ALTER TABLE `login_histories`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `media`
--
ALTER TABLE `media`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `pages`
--
ALTER TABLE `pages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `plans`
--
ALTER TABLE `plans`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `plan_features`
--
ALTER TABLE `plan_features`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `projects`
--
ALTER TABLE `projects`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `qr_codes`
--
ALTER TABLE `qr_codes`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `qr_code_types`
--
ALTER TABLE `qr_code_types`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62;

--
-- AUTO_INCREMENT for table `social_accounts`
--
ALTER TABLE `social_accounts`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `social_links`
--
ALTER TABLE `social_links`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `subscribers`
--
ALTER TABLE `subscribers`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `sub_features`
--
ALTER TABLE `sub_features`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `templates`
--
ALTER TABLE `templates`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `analytics`
--
ALTER TABLE `analytics`
  ADD CONSTRAINT `analytics_link_id_foreign` FOREIGN KEY (`link_id`) REFERENCES `links` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `business_cards`
--
ALTER TABLE `business_cards`
  ADD CONSTRAINT `business_cards_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `business_cards_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `business_card_social_links`
--
ALTER TABLE `business_card_social_links`
  ADD CONSTRAINT `business_card_social_links_business_card_id_foreign` FOREIGN KEY (`business_card_id`) REFERENCES `business_cards` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `business_card_social_links_social_link_id_foreign` FOREIGN KEY (`social_link_id`) REFERENCES `social_links` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `domains`
--
ALTER TABLE `domains`
  ADD CONSTRAINT `domains_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `links`
--
ALTER TABLE `links`
  ADD CONSTRAINT `links_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `login_histories`
--
ALTER TABLE `login_histories`
  ADD CONSTRAINT `login_histories_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plans`
--
ALTER TABLE `plans`
  ADD CONSTRAINT `plans_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`);

--
-- Constraints for table `plan_features`
--
ALTER TABLE `plan_features`
  ADD CONSTRAINT `plan_features_plan_id_foreign` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `projects`
--
ALTER TABLE `projects`
  ADD CONSTRAINT `projects_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `qr_codes`
--
ALTER TABLE `qr_codes`
  ADD CONSTRAINT `qr_codes_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `qr_codes_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `social_accounts`
--
ALTER TABLE `social_accounts`
  ADD CONSTRAINT `social_accounts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD CONSTRAINT `subscriptions_plan_id_foreign` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `subscriptions_transaction_id_foreign` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `subscriptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `transactions`
--
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
