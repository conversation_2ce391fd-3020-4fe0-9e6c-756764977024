<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class TermConditionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $termConditions = [
            [
                'key'   => 'term_conditions',
                'value' => '',
            ],
            [
                'key'   => 'privacy_policy',
                'value' => '',
            ],
            [
                'key'   => 'email',
                'value' => '<EMAIL>',
            ],
            [
                'key'   => 'address',
                'value' => 'C-303, Atlanta Shopping Mall, Nr. Sudama Chowk, Mota Varachha, Surat - 394101, Gujarat, India.',
            ],
            [
                'key'   => 'phone',
                'value' => '+91 9876543210',
            ],
        ];

        foreach ($termConditions as $termCondition) {
            Setting::create($termCondition);
        }
    }
}
