<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;
use JsonException;

class DefaultCurrencySeeder extends Seeder
{
    /**
     *
     * @throws JsonException
     *
     */
    public function run(): void
    {
        $currencies = file_get_contents(storage_path('currencies/defaultCurrency.json'));
        $currencies = json_decode($currencies, true, 512, JSON_THROW_ON_ERROR)['currencies'];

        Currency::insert($currencies);
    }
}
