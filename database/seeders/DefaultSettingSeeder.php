<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class DefaultSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings = [
            // Main Setting
            [
                'key'   => 'title',
                'value' => 'QR Code Builder',
            ],
            [
                'key'   => 'logo',
                'value' => ('assets/images/infyom.png'),
            ],
            [
                'key'   => 'favicon',
                'value' => ('assets/images/favicon.png'),
            ],
            [
                'key'   => 'enable_new_users_registration',
                'value' => true,
            ],
            [
                'key'   => 'plan_expire_notification',
                'value' => 7,
            ],
            [
                'key'   => 'currency',
                'value' => 4,
            ],

            // Payment Setting
            // 1. paypal
            [
                'key'   => 'enable_paypal_payments',
                'value' => false,
            ],
            [
                'key'   => 'paypal_client_id',
                'value' => '',
            ],
            [
                'key'   => 'paypal_secret',
                'value' => '',
            ],
            [
                'key'   => 'paypal_payment_mode',
                'value' => 'sandbox',
            ],
            // 2. stripe
            [
                'key'   => 'enable_stripe_payments',
                'value' => false,
            ],
            [
                'key'   => 'stripe_Key',
                'value' => '',
            ],
            [
                'key'   => 'stripe_secret',
                'value' => '',
            ],
            [
                'key'   => 'stripe_payment_mode',
                'value' => '',
            ],
            // 3. razor pay
            [
                'key'   => 'enable_razorpay_payments',
                'value' => false,
            ],
            [
                'key'   => 'razorpay_key',
                'value' => '',
            ],
            [
                'key'   => 'razorpay_secret',
                'value' => '',
            ],
            [
                'key'   => 'razorpay_payment_mode',
                'value' => '',
            ],

            // Captcha Setting
            [
                'key'   => 'captcha_key',
                'value' => '',
            ],
            [
                'key'   => 'captcha_on_the_lost_password',
                'value' => false,
            ],
            [
                'key'   => 'captcha_on_resend_activation',
                'value' => false,
            ],
            [
                'key'   => 'captcha_on_contact_page',
                'value' => false,
            ],
            [
                'key'   => 'captcha_site_key',
                'value' => false,
            ],
            [
                'key'   => 'captcha_secret_key',
                'value' => false,
            ],

            // Ads Setting
            [
                'key'   => 'ads_header',
                'value' => '',
            ],
            [
                'key'   => 'ads_footer',
                'value' => '',
            ],

            // Socials Setting
            [
                'key'   => 'youtube_link',
                'value' => '',
            ],
            [
                'key'   => 'facebook_link',
                'value' => '',
            ],
            [
                'key'   => 'twitter_link',
                'value' => '',
            ],
            [
                'key'   => 'instagram_link',
                'value' => '',
            ],
            [
                'key'   => 'tiktok_link',
                'value' => '',
            ],
            [
                'key'   => 'linked_in_link',
                'value' => '',
            ],

            // SMTP Setting
            [
                'key'   => 'from_name',
                'value' => '',
            ],
            [
                'key'   => 'from_email',
                'value' => '',
            ],
            [
                'key'   => 'host',
                'value' => '',
            ],
            [
                'key'   => 'encryption',
                'value' => '',
            ],
            [
                'key'   => 'port',
                'value' => '',
            ],
            [
                'key'   => 'email_username',
                'value' => '',
            ],
            [
                'key'   => 'email_password',
                'value' => '',
            ],

            // Custom CSS/JS Setting
            [
                'key'   => 'custom_js',
                'value' => '',
            ],
            [
                'key'   => 'custom_css',
                'value' => '',
            ],

            // Announcement Setting
            // 1. User Announcements
            [
                'key'   => 'user_announcement_content',
                'value' => '',
            ],
            [
                'key'   => 'user_text_color',
                'value' => '',
            ],
            [
                'key'   => 'user_bg_color',
                'value' => '',
            ],
            [
                'key'   => 'announcement_enabled',
                'value' => '',
            ],

            // Email Notifications Setting
            [
                'key'   => 'emails_to_be_notified',
                'value' => '',
            ],
            [
                'key'   => 'new_user',
                'value' => false,
            ],
            [
                'key'   => 'delete_user',
                'value' => false,
            ],
            [
                'key'   => 'new_payment',
                'value' => false,
            ],
            [
                'key'   => 'new_custom_domain',
                'value' => false,
            ],
            [
                'key'   => 'contact_page_emails',
                'value' => false,
            ],

            // Google Login
            [
                'key'   => 'google_client_id',
                'value' => null,
            ],
            [
                'key'   => 'enable_google_login',
                'value' => 0,
            ],

            // Facebook Login
            [
                'key'   => 'facebook_app_id',
                'value' => null,
            ],
            [
                'key'   => 'enable_facebook_login',
                'value' => 0,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
