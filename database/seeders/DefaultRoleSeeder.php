<?php

namespace Database\Seeders;

use App\Models\Role as CustomRole;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class DefaultRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $roles = [
            [
                'name'         => CustomRole::ADMIN,
                'display_name' => 'Admin',
                'is_default'   => true,
            ],
            [
                'name'         => CustomRole::USER,
                'display_name' => 'User',
                'is_default'   => true,
            ],
        ];

        foreach ($roles as $role) {
            Role::create($role);
        }
    }
}
