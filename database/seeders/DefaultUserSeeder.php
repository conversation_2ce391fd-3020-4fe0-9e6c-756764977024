<?php

namespace Database\Seeders;

use App\Models\MultiTenant;
use App\Models\Plan;
use App\Models\Role;
use App\Models\Subscription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DefaultUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $admin = [
            'name'              => 'Admin',
            'email'             => '<EMAIL>',
            'email_verified_at' => Carbon::now(),
            'password'          => Hash::make('123456'),
        ];

        $admin = User::create($admin);
        $admin->assignRole(Role::ADMIN);

        $tenant = MultiTenant::create(['tenant_username' => 'user']);
        $user = [
            'name'              => 'User',
            'email'             => '<EMAIL>',
            'email_verified_at' => Carbon::now(),
            'password'          => Hash::make('123456'),
            'tenant_id'         => $tenant->id,
        ];

        $user = User::create($user);
        $user->assignRole(Role::USER);

        $plan = Plan::where('is_default', 1)->first();
        $trialDays = $plan->trial_days;
        $subscription = [
            'user_id'       => $user->id,
            'plan_id'       => $plan->id,
            'price_of_plan' => $plan->price,
            'plan_type'     => $plan->type,
            'start_date'    => Carbon::now(),
            'end_date'      => Carbon::now()->addDays($trialDays),
            'trial_ends_at' => Carbon::now()->addDays($trialDays),
            'status'        => Subscription::ACTIVE,
        ];
        Subscription::create($subscription);
    }
}
