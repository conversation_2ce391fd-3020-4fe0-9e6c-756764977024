<?php

namespace Database\Seeders;

use App\Models\QrCodeType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class DefaultQRCodeTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $inputs = [
            [
                'title' => 'TEXT',
                'description' => 'Allows the encoding of plain text information that can be quickly accessed when scanned.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/1.svg'),
            ],
            [
                'title' => 'URL',
                'description' => 'It links to a website or webpage, providing quick access when scanned.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/7.svg'),
            ],
            [
                'title' => 'PHONE',
                'description' => 'Enables quick dialing of a phone number when scanned, eliminating the need for manual entry.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/2.svg'),
            ],
            [
                'title' => 'EMAIL',
                'description' => 'Easy way to add an email address to the recipient field of an email application when scanned.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/3.svg'),
            ],
            [
                'title' => 'LOCATION',
                'description' => 'It provides geographic location data when scanned, facilitating navigation and location-based services.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/10.svg'),
            ],
            [
                'title' => 'WI-FI',
                'description' => 'Allows the automatic connection to a wireless network when scanned.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/5.svg'),
            ],
            [
                'title' => 'FACETIME',
                'description' => 'Allows for quick and easy video or audio calls when scanned.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/4.svg'),
            ],
            [
                'title' => 'EVENT',
                'description' => 'Allows easy access to event information such as date, time, and location when scanned.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/11.svg'),
            ],
            [
                'title' => 'WHATSAPP',
                'description' => 'It Provides a quick and easy way to start a conversation with someone on WhatsApp when scanned.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/9.svg'),
            ],
            [
                'title' => 'CRYPTO',
                'description' => 'Make payments of crypto currencies easily by scanning the QR Code.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/13.svg'),
            ],
            [
                'title' => 'VCARD',
                'description' => 'Create Vcard QR Codes and mainly used to make it easier to share contact details on mobile devices.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/12.svg'),
            ],
            [
                'title' => 'PAYPAL',
                'description' => 'Make payments on Paypal by scanning the QR Code.',
                'image' => asset('default-images/frontEnd-images/QrCodeTyleImages/6.svg'),
            ],
        ];

        foreach ($inputs as $input) {
            $exist = QrCodeType::whereTitle($input['title'])->exists();

            $qrCodeTypeInput = Arr::except($input, ['image']);
            if (!$exist) {
                $qrCodeType = QrCodeType::create($qrCodeTypeInput);
                $qrCodeType->addMediaFromUrl($input['image'])->toMediaCollection(QrCodeType::PATH,
                    config('app.media_disc'));
            }
        }
    }
}
