<?php

namespace Database\Seeders;

use App\Models\Feature;
use App\Models\FrontCMS;
use App\Models\SubFeature;
use Illuminate\Database\Seeder;

class FrontCmsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        FrontCMS::create([
            'title'       => 'Create, customize, and share with ease.',
            'description' => 'Share smarter, not harder, with our URL shortener',
        ]);

        foreach (range(1, 6) as $i) {
            SubFeature::create([
                'title'       => 'QR Templates',
                'description' => 'Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s.',
            ]);
        }


        $feature1 = Feature::create([
            'title'       => 'Specialized Link Crafting',
            'sub_title'   => json_encode([
                '0' => 'Create compact, branded links for sharing ease.',
                '1' => 'Real-time Insights -> as it happens.',
                '2' => 'Track click-through rates and user interactions.',
                '3' => 'Sub alias support',
                '4' => 'Quick Link generation  by one tap',
                '5' => 'HTTPS Encryption support',
            ]),
            'description' => 'Create tailored links and unveil their impact through advanced analytics tracking',
        ]);

        $feature1Image = 'default-images/link.png';
        // $feature1->addMediaFromUrl($feature1Image)->toMediaCollection(FrontCMS::IMAGE,
                    // config('app.media_disc'));


        $feature2 = Feature::create([
            'title'       => 'QRPersona: Codes with Character',
            'sub_title'   => json_encode([
                '0' => 'Tailor QR codes with your logo and colors.',
                '1' => 'Create codes that match your visual identity.',
                '2' => 'Maintain code quality for all applications.',
                '3' => 'Generate, share QR Code in minutes.',
                '4' => 'Organize and track multi-ple codes.',
                '5' => 'Visualize QR code before generating.',
            ]),
            'description' => 'Transform QR codes into personalized gateways, enriching connections',
        ]);

        $feature12Image = ('default-images/qr.png');
        // $feature2->addMediaFromUrl($feature12Image)->toMediaCollection(FrontCMS::IMAGE,
                    // config('app.media_disc'));
    }
}
