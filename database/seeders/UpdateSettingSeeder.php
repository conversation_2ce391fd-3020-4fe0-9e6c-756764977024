<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class UpdateSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Setting::where('key', 'logo')->first()->update(['value' => ('default-images/infyom.png')]);
        Setting::where('key', 'favicon')->first()->update(['value' => ('default-images/favicon.png')]);
    }
}
