<?php

namespace Database\Seeders;

use App\Models\Language;
use Illuminate\Database\Seeder;

class DefaultLanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
       $enLanguage =  Language::firstOrCreate(['name' => 'English', 'iso_code' => 'en']);
       $enLanguage->is_default = true;
       $enLanguage->save();

       $zhLanguage =  Language::firstOrCreate(['name' => 'Chinese', 'iso_code' => 'zh']);
       $zhLanguage->is_default = true;
       $zhLanguage->save();

       $frLanguage =  Language::firstOrCreate(['name' => 'French', 'iso_code' => 'fr']);
       $frLanguage->is_default = true;
       $frLanguage->save();

       $esLanguage =  Language::firstOrCreate(['name' => 'Spanish', 'iso_code' => 'es']);
       $esLanguage->is_default = true;
       $esLanguage->save();
    }
}
