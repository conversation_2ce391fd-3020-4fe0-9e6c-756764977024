<?php

namespace Database\Seeders;

use App\Models\Template;
use Illuminate\Database\Seeder;

class DefaultTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $templates = [
            [
                'name' => 'Template 1',
            ],
            [
                'name' => 'Template 2',
            ],
            [
                'name' => 'Template 3',
            ],
            [
                'name' => 'Template 4',
            ],
            [
                'name' => 'Template 5',
            ],
        ];

        foreach ($templates as $template) {
            $exist = Template::whereName($template['name'])->exists();
            
            if (!$exist) {
                Template::create($template);
            }
        }
    }
}
