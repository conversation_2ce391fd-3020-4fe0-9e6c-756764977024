<?php

namespace Database\Seeders;

use App\Models\Plan;
use App\Models\PlanFeatures;
use Illuminate\Database\Seeder;

class DefaultPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $planInput = [
            'name'        => 'Standard',
            'price'       => 10,
            'currency_id' => 4,
            'order'       => 1,
            'trial_days'  => Plan::TRAIL_DAYS,
            'type'        => Plan::MONTHLY,
            'is_default'  => 1,
        ];
        Plan::create($planInput);

        $planFeaturesInput = [
            'plan_id'                     => 1,
            'qr_code_limit'               => 10,
            'links_limit'                 => 10,
            'projects_limit'              => 10,
            'qr_code_types'               => "1,2,3,4,5,6,7,8,9,10,11,12,13",
        ];

        PlanFeatures::create($planFeaturesInput);
    }
}
