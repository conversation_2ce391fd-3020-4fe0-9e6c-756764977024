<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('business_cards', function (Blueprint $table) {
            $table->id();
            $table->string('url_alias')->unique();
            $table->string('name');
            $table->string('job_title');
            $table->string('company');
            $table->string('phone');
            $table->string('email');
            $table->string('website');
            $table->boolean('status')->default(false);
            $table->unsignedBigInteger('template_id');
            $table->string('tenant_id');
            $table->timestamps();
            
            $table->foreign('template_id')->references('id')->on('templates')
                        ->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('tenant_id')->references('id')->on('tenants')
                ->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('business_cards');
    }
};
