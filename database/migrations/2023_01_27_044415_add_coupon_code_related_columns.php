<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coupon_codes', function (Blueprint $table) {
            $table->dropColumn('quantity');
            $table->string('code')->unique()->change();
            $table->integer('how_many_can_use')->default(0)->after('discount');
            $table->integer('used')->default(0)->after('how_many_can_use');
        });
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('coupon_code')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coupon_codes', function (Blueprint $table) {
            $table->integer('quantity')->nullable();
            $table->string('code')->change();
            $table->dropColumn('how_many_can_use');
            $table->dropColumn('used');
        });
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn('coupon_code');
        });
    }
};
