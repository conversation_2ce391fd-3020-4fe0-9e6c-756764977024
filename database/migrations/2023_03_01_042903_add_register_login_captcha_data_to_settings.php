<?php

use App\Models\Setting;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \Illuminate\Support\Facades\Artisan::call('db:seed', ['--class' => 'UpdateCaptchaSeeder', '--force' => true]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Setting::where('key', 'captcha_on_register_login')->delete();
        Setting::create([
            'key'   => 'captcha_on_resend_activation',
            'value' => 0,
        ]);
    }
};
