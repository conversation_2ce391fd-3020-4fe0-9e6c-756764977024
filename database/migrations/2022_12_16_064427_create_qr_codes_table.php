<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('qr_codes', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->foreignId('project_id')->nullable()->references('id')->on('projects')
                ->onUpdate('cascade')->onDelete('cascade');
            $table->integer('type');
            $table->integer('style');
            $table->integer('foreground_type');
            $table->string('foreground_color1')->nullable();
            $table->string('foreground_color2')->nullable();
            $table->string('background_color');
            $table->string('background_transparency');
            $table->boolean('custom_eye_color');
            $table->string('eye_inner_color')->nullable();
            $table->string('eye_outer_color')->nullable();
            $table->integer('size');
            $table->integer('margin_size');
            $table->integer('error_correction');
            $table->text('extra_data');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('qr_codes');
    }
};
