<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('plan_features', function (Blueprint $table) {
            $table->dropColumn('password_protection_enabled');
            $table->boolean('allow_create_business_card')->default(false);
            $table->double('business_card_limit')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('plan_features', function (Blueprint $table) {
            $table->boolean('password_protection_enabled');
            $table->dropColumn('allow_create_business_card');
            $table->dropColumn('business_card_limit');
        });
    }
};
