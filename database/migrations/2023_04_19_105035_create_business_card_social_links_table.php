<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('business_card_social_links', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_card_id');
            $table->unsignedBigInteger('social_link_id');
            $table->timestamps();

            $table->foreign('business_card_id')->references('id')->on('business_cards')
                    ->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('social_link_id')->references('id')->on('social_links')
                    ->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('business_card_social_links');
    }
};
