<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('qr_codes', static function (Blueprint $table) {
            $table->integer('foreground_gradient_style')->nullable()->after('foreground_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('qr_codes', static function (Blueprint $table) {
            $table->dropColumn('foreground_gradient_style');
        });
    }
};
