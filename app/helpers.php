<?php

use App\Jobs\MailSenderJob;
use App\Models\Currency;
use App\Models\DigitalBusinessCard;
use App\Models\Plan;
use App\Models\QrCode;
use App\Models\Setting;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Stancl\Tenancy\Database\TenantScope;
use Stripe\Stripe;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

function getPageSize($request): mixed
{
    return $request->input('page.size', 10);
}

function currentActiveSubscription(): ?Subscription
{
    if (!Auth::user()) {
        return null;
    }

    /** @var Subscription $currentActivePlan */
    static $currentActivePlan;

    if ($currentActivePlan === null) {
        $currentActivePlan = Subscription::with('plan')
            ->whereStatus(Subscription::ACTIVE)
            ->whereUserId(getLoginUserId())
            ->first();
    }

    return $currentActivePlan;
}

/**
 * @return mixed
 */
function getAppName(): mixed
{
    $record = getSuperAdminSettingValue('title');

    return (!empty($record)) ? $record : config('app.name');
}

/**
 * @param $key
 * @return mixed
 */
function getSuperAdminSettingValue($key): mixed
{
    static $settings;

    if (empty($settings)) {
        $settings = Setting::all()->keyBy('key');
    }

    return $settings[$key]->value;
}

/**
 * @param $localeLanguage
 * @return bool
 */
function setLocalLang($localeLanguage): bool
{
    if (!isset($localeLanguage)) {
        App::setLocale('en');
    } else {
        App::setLocale($localeLanguage);
    }

    return true;
}

/**
 * @return mixed
 */
function getLocalLanguage()
{
    $templateLang = Session::get('languageChange_' . request()->alias);

    return $templateLang;
}

/**
 * @return mixed
 */
function getFaviconUrl(): mixed
{
    static $settings;

    if (empty($settings)) {
        $settings = Setting::all()->keyBy('key');
    }

    $favicon = $settings['favicon'];

    return $favicon->favicon_url;
}

/**
 * @return mixed
 */
function getFavicon(): mixed
{
    static $favicon;

    if (empty($favicon)) {
        $setting = Setting::where('key', 'favicon')->first();
        $favicon = $setting->value;
    }

    return $favicon;
}

function getUserCurrentActiveSubscription($userId): ?Subscription
{
    /** @var Subscription $currentActivePlan */
    static $currentActivePlan;

    if ($currentActivePlan === null) {
        $currentActivePlan = Subscription::with('plan')
            ->whereStatus(Subscription::ACTIVE)
            ->whereUserId($userId)
            ->first();
    }

    return $currentActivePlan;
}

function getLoginUserTenantId()
{
    return Auth::user()->tenant_id;
}

function getLoginUserId()
{
    return Auth::id();
}

/**
 * @return mixed
 */
function getLoginAdminName(): mixed
{
    static $adminName;

    if (empty($adminName)) {
        $admin = \App\Models\User::withoutGlobalScope(new TenantScope())->first();
        $adminName = !empty($admin) ? $admin->name : '';
    }

    return $adminName;
}

function daysOfPlan($plan): int
{
    return match($plan->type) {
        Plan::MONTHLY => 30,
        Plan::ANNUALLY => 365,
    };
}

function checkIfPlanIsInTrial($currentSubscription): bool
{
    $now = Carbon::now();
    if (!empty($currentSubscription->trial_ends_at) && $currentSubscription->trial_ends_at > $now) {
        return true;
    }

    return false;
}

function qrCodeTypeRelatedInputs(&$input)
{
    switch ($input['type']) {
        case QrCode::TEXT:
            $input['extra_data'] = ["text_content" => $input["text_content"]];
            break;
        case QrCode::URL:
            $input['extra_data'] = ["url" => $input["url"]];
            break;
        case QrCode::PHONE:
            $input['extra_data'] = ["phone_number" => $input["phone_number"]];
            break;
        case QrCode::SMS:
            $input['extra_data'] = [
                "phone_number" => $input["phone_number"],
                "prefilled_message" => $input["prefilled_message"],
            ];
            break;
        case QrCode::EMAIL:
            $input['extra_data'] = [
                "email" => $input["email"],
                "prefilled_subject" => $input["prefilled_subject"],
                "prefilled_message" => $input["prefilled_message"],
            ];
            break;
        case QrCode::WHATSAPP:
            $input['extra_data'] = [
                "wp_phone_number" => $input["wp_phone_number"],
                "prefilled_message" => $input["prefilled_message"],
            ];
            break;
        case QrCode::FACE_TIME:
            $input['extra_data'] = ["phone_or_email" => $input["phone_or_email"]];
            break;
        case QrCode::LOCATION:
            $input['extra_data'] = ["latitude" => $input["latitude"], "longitude" => $input["longitude"]];
            break;
        case QrCode::WIFI:
            $input['extra_data'] = [
                "wifi_name" => $input["wifi_name"], "encryption" => $input["encryption"],
                "password" => $input["password"], "wifi_is_hidden" => $input["wifi_is_hidden"],
            ];
            break;
        case QrCode::EVENT:
            $input['extra_data'] = [
                "event_name" => $input["event_name"], "geo_location" => $input["geo_location"],
                "event_url" => $input["event_url"], "notes" => $input["notes"], "starts_on" => $input["starts_on"],
                "ends_on" => $input["ends_on"], "timezone" => $input["timezone"],
            ];
            break;
        case QrCode::CRYPTO:
            $input['extra_data'] = [
                "coin" => $input["coin"], "address" => $input["address"], "amount" => $input["amount"],
            ];
            break;
        case QrCode::VCARD:
            $input['extra_data'] = [
                "first_name" => $input["first_name"], "last_name" => $input["last_name"],
                "email" => $input["email"], "website_url" => $input["website_url"],
                "company" => $input["company"], "job_title" => $input["job_title"],
                "birthday" => $input["birthday"], "street_address" => $input["street_address"],
                "city" => $input["city"], "zip" => $input["zip"], "region" => $input["region"],
                "country" => $input["country"], "phone" => $input["phone"],
            ];
            break;
        case QrCode::PAYPAL:
            $input['extra_data'] = [
                "paypal_type" => $input["paypal_type"], "paypal_email" => $input["paypal_email"],
                "product_title" => $input["product_title"],
                "currency_code" => $input["currency_code"], "price" => $input["price"],
                "thanks_url" => $input["thanks_url"], "cancel_url" => $input["cancel_url"],
            ];
            break;
    }
}

/**
 * @return bool
 */
function setAdminStripeApiKey(): bool
{
    $stripeSecret = Setting::where('key', 'stripe_secret')->first();

    if ($stripeSecret == null || $stripeSecret->value == null) {
        Stripe::setApiKey(config('services.stripe.secret_key'));
    } else {
        Stripe::setApiKey($stripeSecret->value);
    }

    return true;
}

/**
 * @param $key
 * @return string
 */
function getSubscriptionPlanCurrencyCode($key): string
{
    $currencyData = Currency::where('id', $key)->first();

    if ($currencyData != null) {
        return $currencyData->code;
    }
}

/**
 * @param $live
 * @return mixed
 */
function getPaypalClientId($live): mixed
{
    $paypalClientId = Setting::where('key', 'paypal_client_id')->first();

    if ($paypalClientId == null || $paypalClientId->value == null) {
        return isset($live) ? config('paypal.live.client_id') : config('paypal.sandbox.client_id');
    }

    return $paypalClientId->value;
}

/**
 * @param $live
 * @return mixed
 */
function getPaypalSecret($live): mixed
{
    $paypalSecret = Setting::where('key', 'paypal_secret')->first();

    if ($paypalSecret == null || $paypalSecret->value == null) {
        return isset($live) ? config('paypal.live.client_secret') : config('paypal.sandbox.client_secret');
    }

    return $paypalSecret->value;
}

/**
 * @return mixed
 */
function getPaypalMode(): mixed
{
    $paypalPaymentMode = Setting::where('key', 'paypal_payment_mode')->first();
    if ($paypalPaymentMode == null || $paypalPaymentMode->value == null) {
        return config('paypal.mode');
    }

    return $paypalPaymentMode->value;
}

/**
 * @return mixed
 */
function getAdminRazorpayKey()
{
    $razorpayKey = Setting::where('key', 'razorpay_key')->first();

    if ($razorpayKey == null || $razorpayKey->value == null) {
        return config('services.razorpay.key');
    }

    return $razorpayKey->value;
}

/**
 * @return mixed
 */
function getAdminRazorpaySecret()
{
    $razorpaySecret = Setting::where('key', 'razorpay_secret')->first();

    if ($razorpaySecret == null || $razorpaySecret->value == null) {
        return config('services.razorpay.secret');
    }

    return $razorpaySecret->value;
}

/**
 * @return array
 */
function zeroDecimalCurrencies(): array
{
    return [
        'BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF',
    ];
}

function getStripeKey()
{
    $stripeKey = Setting::where('key', 'stripe_key')->first();

    if ($stripeKey == null || $stripeKey->value == null) {
        return config('services.stripe.key');
    }

    return $stripeKey->value;
}

/**
 * @param $model
 * @param string $columnName
 * @param string $id
 * @return bool
 */
function canDelete($model, string $columnName, string $id): bool
{
    $result = $model::where($columnName, $id)->exists();
    if ($result) {
        return true;
    }

    return false;
}

function getSettingValue($key)
{
    return Setting::where('key', '=', $key)->first()->value;
}

function sendMailOnPayment($userName, $userEmail, $planName, $paymentAmount)
{
    if (getSettingValue('new_payment')) {
        $subject = "Payment Received";
        $data['data'] = "$paymentAmount received from " . '<strong>' . $userName . '</strong>' . " on subscribe " . '<strong>' .
            $planName . '</strong>' . " plan.";
        sendMailToAdmin($subject, $data);

        $subject = "Subscription Activated";
        $data['data'] = "Hello " . '<strong>' . $userName . '</strong>' . ", you has been subscribed " . '<strong>' .
            $planName . '</strong>' . " plan successfully.";
        sendMailToUser($userEmail, $subject, $data, 'emails.mail-sender');
    }
}

function sendMailToAdmin($subject, $data)
{
    $emails = explode(',', getSettingValue('emails_to_be_notified'));
    if (getSettingValue('emails_to_be_notified') != null) {
        foreach ($emails as $email) {
            MailSenderJob::dispatch($email, $subject, $data, 'emails.mail-sender');
        }
    }
}

function sendMailToUser($email, $subject, $data, $emailTemplate)
{
    MailSenderJob::dispatch($email, $subject, $data, $emailTemplate);
}

/**
 * @return array
 */
function getPayPalSupportedCurrencies(): array
{
    return [
        'AUD', 'BRL', 'CAD', 'CNY', 'CZK', 'DKK', 'EUR', 'HKD', 'HUF', 'ILS', 'JPY', 'MYR', 'MXN', 'TWD', 'NZD', 'NOK',
        'PHP', 'PLN', 'GBP', 'RUB', 'SGD', 'SEK', 'CHF', 'THB', 'USD',
    ];
}

function defaultFrontCmsImage(): string
{
    return asset('default-images/home.png');
}

function verifyCaptcha()
{
    if (isset($input['token'])) {
        $url = 'https://www.google.com/recaptcha/api/siteverify';
        $data = [
            'secret' => getSettingValue('captcha_secret_key'),
            'response' => $input['token'],
            'remoteip' => $_SERVER['REMOTE_ADDR'],
        ];
        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data),
            ],
        ];

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        $checkIsVerifyCaptcha = json_decode($result)->success;
        if (!$checkIsVerifyCaptcha) {
            throw new UnprocessableEntityHttpException('Please enter Valid captcha');
        }
    }
}

/**
 * @return mixed
 */
function getDigitalBusinessCardRecordsCount(): mixed
{
    return DigitalBusinessCard::tenant()->count();
}

/**
 * @param $tenantId
 * @return int
 */
function getUserDigitalBusinessCardRecordsCount($tenantId): int
{
    return DigitalBusinessCard::whereTenantId($tenantId)->count();
}

function getDefaultLang()
{
    $langArr = [
        'en' => 'English',
        'es' => 'Spanish',
        'fr' => 'French',
        'zh' => 'Chinese',
    ];

    return $langArr;
}

/**
 * @param $socialLinks
 * @return array
 */
function getSocialLink($socialLinks): array
{
    foreach ($socialLinks as $social) {
        if ($url = parse_url($social->link, PHP_URL_SCHEME) === null ?
            'https://' . $social->link : $social->link) {

            switch ($social->default_link) {
                case 0:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                <img src="' . asset('default-images/socialimages/web.svg') . '" alt="" class="" style="width: 30px">
            </a>';
                    break;
                case 1:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                <img src="' . asset('default-images/socialimages/twitter.svg') . '" alt="" class="" style="width: 30px">
            </a>';
                    break;
                case 2:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                <img src="' . asset('default-images/socialimages/facebook.svg') . '" alt="" class="" style="width: 30px">
            </a>';
                    break;
                case 3:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                    <img src="' . asset('default-images/socialimages/insta.svg') . '" alt="" class="" style="width: 30px">
                </a>';
                    break;
                case 4:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                        <img src="' . asset('default-images/socialimages/reditt.svg') . '" alt="" class="" style="width: 30px">
                    </a>';
                    break;
                case 5:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                            <img src="' . asset('default-images/socialimages/tumbir.svg') . '" alt="" class="" style="width: 30px">
                        </a>';
                    break;
                case 6:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                                <img src="' . asset('default-images/socialimages/youtube.svg') . '" alt="" class="" style="width: 30px">
                            </a>';
                    break;
                case 7:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                                <img src="' . asset('default-images/socialimages/linkedin.svg') . '" alt="" class="" style="width: 30px">
                            </a>';
                    break;
                case 8:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                                <img src="' . asset('default-images/socialimages/whatsapp.svg') . '" alt="" class="" style="width: 30px">
                            </a>';
                    break;
                case 9:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                                <img src="' . asset('default-images/socialimages/pinteresht.svg') . '" alt="" class="" style="width: 30px">
                            </a>';
                    break;
                case 10:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                                <img src="' . asset('default-images/socialimages/tiktok.svg') . '" alt="" class="" style="width: 30px">
                            </a>';
                    break;
                default:
                    $businessCardSocialLinks[$social->link] = '<a href="' . $url . '" target="_blank">
                    <img src="' . $social->icon . '" alt="" class="" style="width: 30px">
                </a>';
            }
        }
    }

    return $businessCardSocialLinks;
}
