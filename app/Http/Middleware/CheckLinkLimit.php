<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AppBaseController;
use App\Models\Link;
use Closure;
use Illuminate\Http\Request;

class CheckLinkLimit extends AppBaseController
{
    public function handle(Request $request, Closure $next)
    {
        $linkCount = Link::whereTenantId(getLoginUserTenantId())->count();
        $linkLimit = currentActiveSubscription()->plan->planFeatures->links_limit;
        if ($linkLimit <= $linkCount) {
            return $this->sendError('Link create limit exceeded for your account, Update your subscription plan.');
        }

        return $next($request);
    }
}
