<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AppBaseController;
use Closure;
use Illuminate\Http\Request;

class CheckQrCodeTypeLimit extends AppBaseController
{
    public function handle(Request $request, Closure $next)
    {
        $input = $request->all();
        $qrCodeTypeLimit = currentActiveSubscription()->plan->planFeatures->qr_code_types;
        if (!str_contains($qrCodeTypeLimit, $input['type'])) {
            return $this->sendError('This QR Code type is not contains in your subscription, Update your subscription plan.');
        }

        return $next($request);
    }
}
