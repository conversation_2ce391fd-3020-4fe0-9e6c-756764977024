<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AppBaseController;
use App\Models\QrCode;
use Closure;
use Illuminate\Http\Request;

class CheckQrCodeLimit extends AppBaseController
{
    public function handle(Request $request, Closure $next)
    {
        $qrCodeCount = QrCode::whereTenantId(getLoginUserTenantId())->count();
        $qrCodeLimit = currentActiveSubscription()->plan->planFeatures->qr_code_limit;
        if ($qrCodeLimit <= $qrCodeCount) {
            return $this->sendError('QR Code create limit exceeded for your account, Update your subscription plan.');
        }

        return $next($request);
    }
}
