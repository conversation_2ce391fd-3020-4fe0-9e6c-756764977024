<?php

namespace App\Http\Middleware;

use App\Models\Analytic;
use App\Models\DigitalBusinessCard;
use App\Models\Link;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Jenssegers\Agent\Agent;
use <PERSON><PERSON><PERSON>\Location\Facades\Location;

class Analytics
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $uri = str_replace($request->root(), '', $request->url()) ?: '/';
        $urlAlias = Route::current()->parameters['alias'];
        $link = Link::whereUrlAlias($urlAlias)->first();
        $digitalBusinessCard = DigitalBusinessCard::whereUrlAlias($urlAlias)->first();

        if(!empty($link)) {
            $modelId = $link->id;
            $modelType = Link::class;
        }elseif(!empty($digitalBusinessCard)) {
            $modelId = $digitalBusinessCard->id;
            $modelType = DigitalBusinessCard::class;
        }else {
            return abort('404');
        }
        
        $agent = new Agent();
        if (!$agent->isRobot()) {
            $agent->setUserAgent($request->headers->get('user-agent'));
            $agent->setHttpHeaders($request->headers);
            $sessionExists = Analytic::where('session', $request->session()->getId())->where('model_id',
                $modelId)->where('model_type',$modelType)->exists();
            if ($sessionExists) {
                return $next($request);
            }

            $items = implode($agent->languages());
            $lang = substr($items, 0, 2);
            $ip = Location::get($request->ip());
            $country = $ip ? $ip->countryName : '';

            Analytic::create([
                'session'          => $request->session()->getId(),
                'model_id'         => $modelId,
                'model_type'       => $modelType,
                'uri'              => $uri,
                'source'           => $request->headers->get('referer'),
                'country'          => $country,
                'browser'          => $agent->browser() ?? null,
                'device'           => $agent->deviceType(),
                'operating_system' => $agent->platform(),
                'ip'               => $request->ip(),
                'language'         => $lang,
                'meta'             => json_encode(Location::get($request->ip())),
            ]);

            return $next($request);
        }

        return $next($request);
    }
}
