<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AppBaseController;
use App\Models\Project;
use Closure;
use Illuminate\Http\Request;

class CheckProjectLimit extends AppBaseController
{
    public function handle(Request $request, Closure $next)
    {
        $projectCount = Project::whereTenantId(getLoginUserTenantId())->count();
        $projectLimit = currentActiveSubscription()->plan->planFeatures->projects_limit;
        if ($projectLimit <= $projectCount) {
            return $this->sendError('Project create limit exceeded for your account, Update your subscription plan.');
        }

        return $next($request);
    }
}
