<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AppBaseController;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckSubscription extends AppBaseController
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {

        if (!Auth::check() || (Auth::check() && !Auth::user()->hasRole('user'))) {
            return $next($request);
        }

        $subscription = currentActiveSubscription();

        if (!$subscription) {
            return $this->sendError('Please choose a plan to continue the service.');
        }

        if ($subscription->isExpired()) {
            return $this->sendError('Your plan has been expired, please choose new plan to continue service.');
        }

        return $next($request);
    }
}
