<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AppBaseController;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckUserStatus extends AppBaseController
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        if (Auth::check() && !$request->user()->status) {
            $request->user()->tokens()->delete();
            $request->user()->currentAccessToken()->delete();

            return $this->sendError('Your account is currently disabled, please contact administrator.');
        }

        return $next($request);
    }
}
