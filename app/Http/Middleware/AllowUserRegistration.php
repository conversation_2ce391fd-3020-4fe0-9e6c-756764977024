<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AppBaseController;
use App\Models\Setting;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AllowUserRegistration extends AppBaseController
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return JsonResponse
     */
    public function handle(Request $request, Closure $next): JsonResponse
    {
        $checkAllowRegistration = Setting::where('key', 'enable_new_users_registration')->first()->value;
        if (!$checkAllowRegistration) {
            return $this->sendError('Registration is not possible until the admin approves the registration for the new user');
        }

        return $next($request);
    }
}
