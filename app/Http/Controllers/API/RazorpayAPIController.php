<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\CouponCode;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Repositories\SubscriptionRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Razorpay\Api\Api;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class RazorpayAPIController extends AppBaseController
{
    /** @var SubscriptionRepository */
    private SubscriptionRepository $subscriptionRepository;

    /**
     * @param SubscriptionRepository $subscriptionRepository
     */
    public function __construct(SubscriptionRepository $subscriptionRepository)
    {
        $this->subscriptionRepository = $subscriptionRepository;
    }

    public function onBoard(Request $request)
    {
        $input = $request->all();
        try {
            $plan = Plan::whereId($input['plan_id'])->first();
            $subscription = $this->subscriptionRepository->createSubscription($input['plan_id']);
            $data = $this->subscriptionRepository->getPlan($input);
            $amountToPay = $data['new_plan'][1]['payable_amount'] * 100;  // 100 = 1 rupees
            $user = Auth::user();
            $api = new Api(getAdminRazorpayKey(), getAdminRazorpaySecret());
            $orderData = [
                'receipt'  => $plan->id,
                'amount'   => $amountToPay,
                'currency' => getSubscriptionPlanCurrencyCode(strtoupper($plan->currency_id)),
                'notes'    => [
                    'email'           => $user->email,
                    'name'            => $user->name,
                    'subscription_id' => $subscription->id,
                    'coupon_code'     => $input['coupon_code'],
                ],
            ];

            $razorpayOrder = $api->order->create($orderData);
            $data['id'] = $razorpayOrder->id;
            $data['amount'] = $amountToPay;
            $data['name'] = $user->name;
            $data['email'] = $user->email;
            $data['planId'] = $subscription->id;

            return $this->sendResponse($data['id'], 'data');
        } catch (HttpException $ex) {
            throw new UnprocessableEntityHttpException($ex->getMessage());
        }
    }

    public function failed(): \Illuminate\Http\JsonResponse
    {
        return $this->sendError(__('messages.error.payment_failed'));
    }

    public function success(Request $request)
    {
        try {
            DB::beginTransaction();
            $api = new Api(getAdminRazorpayKey(), getAdminRazorpaySecret());
            $payment = $api->payment->fetch($request->razorpay_payment_id);
            $subscriptionPlanId = $payment['notes']['subscription_id'];
            Subscription::findOrFail($subscriptionPlanId)->update(['status' => Subscription::ACTIVE]);
            $userId = Subscription::whereId($payment->notes->subscription_id)->value('user_id');
            $user = \App\Models\User::whereId($userId)->first();
            $paymentAmount = $payment->amount / 100;
            // De-Active all others subscription
            Subscription::whereUserId($user->id)
                ->where('id', '!=', $subscriptionPlanId)
                ->update([
                    'status' => Subscription::DISABLE,
                ]);

            $couponCode = $payment->notes->coupon_code ?? null;
            $transaction = Transaction::create([
                'tenant_id'     => $user->tenant_id,
                'user_id'       => $user->id,
                'payment_mode'  => Subscription::TYPE_RAZORPAY,
                'price_of_plan' => $paymentAmount,
                'coupon_code'   => $couponCode,
                'status'        => Subscription::ACTIVE,
                'date'          => Carbon::now()->toDateString(),
            ]);
            
            if (!empty($couponCode)) {
                $incrementUsedCount = CouponCode::whereCode($couponCode)->first();
                $incrementUsedCount->increment('used', 1);
            }
            // updating the transaction id on the subscription table
            $subscription = Subscription::with('plan')->findOrFail($subscriptionPlanId);
            $subscription->update(['transaction_id' => $transaction->id]);
            sendMailOnPayment($user->name, $user->email, $subscription->plan->name, $paymentAmount);
            DB::commit();

            return $this->sendSuccess(__('messages.success.plan_subscribed'));
        } catch (\Exception $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
