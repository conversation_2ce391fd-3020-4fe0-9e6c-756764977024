<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\CreateDigitalBusinessCardRequest;
use App\Http\Resources\DigitalBusinessCardCollection;
use App\Http\Resources\DigitalBusinessCardResource;
use App\Models\Link;
use App\Models\PlanFeatures;
use App\Models\Role;
use App\Models\User;
use App\Repositories\DigitalBusinessCardRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DigitalBusinessCardAPIController extends AppBaseController
{
    /** @var DigitalBusinessCardRepository */
    private $digitalBusinessCardRepo;

    public function __construct(DigitalBusinessCardRepository $digitalBusinessCardRepository)
    {
        $this->digitalBusinessCardRepo = $digitalBusinessCardRepository;
    }

    /**
     * @param  Request  $request
     * @return DigitalBusinessCardCollection
     */
    public function index(Request $request)
    {
        $perPage = getPageSize($request);
        $users = $this->digitalBusinessCardRepo->paginate($perPage);
        DigitalBusinessCardResource::usingWithCollection();

        return new DigitalBusinessCardCollection($users);
    }

    /**
     * @param  CreateDigitalBusinessCardRequest  $request
     * @return DigitalBusinessCardResource|JsonResponse
     */
    public function store(CreateDigitalBusinessCardRequest $request)
    {
        $input = $request->all();
        $ulrAlias = $input['url_alias'];
        $existAlias = Link::whereUrlAlias($ulrAlias)->exists();

        if ($existAlias) {
            return $this->sendError('The URL alias has been already in shorten URL.');
        }

        $user = User::whereTenantId($input['tenant_id'])->first();

        if (empty($user)) {
            return $this->sendError('User not found.');
        }

        $plan = null;
        $planFeature = null;
        if (! \Auth::user()->hasRole(Role::ADMIN)) {
            $plan = getUserCurrentActiveSubscription($user->id)->plan;

            $planFeature = PlanFeatures::wherePlanId($plan->id)->where('allow_create_business_card', true)->first();
        }

        if (empty($planFeature) && ! \Auth::user()->hasRole(Role::ADMIN)) {
            return $this->sendError('This user not allow create digital business card, Please upgrade your plan.');
        }

        if (! \Auth::user()->hasRole(Role::ADMIN) && $planFeature->business_card_limit <= getUserDigitalBusinessCardRecordsCount($input['tenant_id'])) {
            if (! \Auth::user()->hasRole(Role::ADMIN)) {
                return $this->sendError('Your assign digital business card limit is exceed, Please upgrade your plan.');
            }
        }

        $digitalBusinessCard = $this->digitalBusinessCardRepo->store($input);

        return new DigitalBusinessCardResource($digitalBusinessCard);
    }

    /**
     * Undocumented function
     *
     * @param  mixed  $id
     * @return JsonResponse
     */
    public function changeStatus($id)
    {
        $digitalBusinessCard = $this->digitalBusinessCardRepo->find($id);
        $digitalBusinessCard->update(['status' => ! $digitalBusinessCard->status]);

        return $this->sendSuccess('Digital business card status updated successfully.');
    }

    /**
     * @param  mixed  $id
     * @return DigitalBusinessCardResource
     */
    public function show($id)
    {
        $digitalBusinessCard = $this->digitalBusinessCardRepo->find($id);

        return new DigitalBusinessCardResource($digitalBusinessCard);
    }

    /**
     * @param  Request  $request
     * @param  mixed  $id
     * @return JsonResponse
     */
    public function update(Request $request, $id)
    {
        $updateInput = $request->all();
        $updateSocialLink = $this->digitalBusinessCardRepo->updateAdminSocialLink($updateInput, $id);

        return $this->sendResponse($updateSocialLink, 'Business card social links updated successfully.');
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function destroy($id)
    {
        $digitalBusinessCard = $this->digitalBusinessCardRepo->find($id);
        $digitalBusinessCard->socialLinks()->delete();
        $digitalBusinessCard->delete();

        return $this->sendSuccess('Digital business card deleted successfully.');
    }
}
