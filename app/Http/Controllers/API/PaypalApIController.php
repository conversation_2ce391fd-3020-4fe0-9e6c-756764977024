<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\CouponCode;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use App\Repositories\SubscriptionRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\DB;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Throwable;

class PaypalApIController extends AppBaseController
{
    /** @var SubscriptionRepository */
    private SubscriptionRepository $subscriptionRepository;

    /**
     * @param SubscriptionRepository $subscriptionRepository
     */
    public function __construct(SubscriptionRepository $subscriptionRepository)
    {
        $this->subscriptionRepository = $subscriptionRepository;
        
        $liveMode = getSettingValue('paypal_payment_mode');

        if ($liveMode) {
            $live = true;
            config([
                'paypal.mode'                  => 'live',
                'paypal.live.client_id'        => getPaypalClientId($live),
                'paypal.live.client_secret'    => getPaypalSecret($live),
            ]);
        }else {
            $live = false;
            config([
                'paypal.mode'                  => 'sandbox',
                'paypal.sandbox.client_id'     => getPaypalClientId($live),
                'paypal.sandbox.client_secret' => getPaypalSecret($live),
            ]);
        }
    }

    /**
     * @param Request $request
     * @throws Throwable
     * @return JsonResponse
     */
    public function onBoard(Request $request): JsonResponse
    {
        $input = $request->all();
        $plan = Plan::whereId($input['plan_id'])->first();
        $data = $this->subscriptionRepository->getPlan($input);
        $subscription = $this->subscriptionRepository->createSubscription($input['plan_id']);
        if (! in_array(strtoupper(getSubscriptionPlanCurrencyCode($plan->currency_id)), getPayPalSupportedCurrencies())) {
            return $this->sendError(getSubscriptionPlanCurrencyCode($plan->currency_id).' is not currently supported.');
        }
        
        try {
            $provider = new PayPalClient;
            $provider->getAccessToken();

            $data = [
                'intent'              => 'CAPTURE',
                'purchase_units'      => [
                    [
                        'reference_id' => $subscription->id,
                        'amount'       => [
                            'value'         => $data['new_plan'][1]['payable_amount'],
                            'currency_code' => getSubscriptionPlanCurrencyCode($plan->currency_id),
                        ],
                    ],
                ],
                'application_context' => [
                    'cancel_url' => route('paypal.failed', $subscription->id),
                    'return_url' => route('paypal.success', ['coupon_code' => $input['coupon_code']]),
                ],
            ];
            $order = $provider->createOrder($data);   
        }catch (Exception $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }

        return $this->sendResponse($order, 'data');
    }

    /**
     * @param $id
     * @return Redirector|Application|RedirectResponse
     */
    public function failed($id): Redirector|Application|RedirectResponse
    {
        Subscription::findOrFail($id)->delete();

        return redirect(url("/#/app/manage-subscription"));
    }

    /**
     * @param Request $request
     * @throws Throwable
     * @return Application|Redirector|RedirectResponse
     */
    public function success(Request $request)
    {
        $provider = new PayPalClient();
        $provider->getAccessToken();
        $token = $request->get('token');

        try {
            DB::beginTransaction();
            $response = $provider->capturePaymentOrder($token);
            $subscriptionId = $response['purchase_units'][0]['reference_id'];
            $subscription = Subscription::with('plan')->where('id', $subscriptionId)->firstOrFail();
            $subscription->update(['status' => Subscription::ACTIVE]);
            $userId = Subscription::whereId($subscriptionId)->value('user_id');
            $user = User::whereId($userId)->first();
            $paymentAmount = $subscription->price_of_plan;
            Subscription::whereUserId($user->id)
                ->where('id', '!=', $subscriptionId)
                ->update([
                    'status' => Subscription::DISABLE,
                ]);

            $couponCode = $request->get('coupon_code');
            $transaction = Transaction::create([
                'tenant_id'     => $user->tenant_id,
                'user_id'       => $user->id,
                'payment_mode'  => Subscription::TYPE_PAYPAL,
                'price_of_plan' => $paymentAmount,
                'coupon_code'   => $request->get('coupon_code'),
                'status'        => Subscription::ACTIVE,
                'date'          => Carbon::now()->toDateString(),
            ]);

            if (!empty($couponCode)) {
                $incrementUsedCount = CouponCode::whereCode($couponCode)->first();
                $incrementUsedCount->increment('used', 1);
            }
            // updating the transaction id on the subscription table
            $subscription = Subscription::with('plan')->findOrFail($subscriptionId);
            $subscription->update(['transaction_id' => $transaction->id]);
            sendMailOnPayment($user->name, $user->email, $subscription->plan->name, $paymentAmount);
            DB::commit();

            return redirect(url("/#/app/manage-subscription"));
        } catch (HttpException $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
