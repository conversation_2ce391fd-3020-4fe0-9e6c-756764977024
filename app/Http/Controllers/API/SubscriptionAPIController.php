<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\CouponCode;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Repositories\SubscriptionRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SubscriptionAPIController extends AppBaseController
{
    /** @var SubscriptionRepository */
    private SubscriptionRepository $subscriptionRepository;

    /**
     * @param SubscriptionRepository $subscriptionRepository
     */
    public function __construct(SubscriptionRepository $subscriptionRepository)
    {
        $this->subscriptionRepository = $subscriptionRepository;
    }

    public function index(): JsonResponse
    {
        $subscriptionPlans = $this->subscriptionRepository->subscriptionPlans();

        return $this->sendResponse($subscriptionPlans, 'data');
    }

    public function choosePaymentType(Request $request): JsonResponse
    {
        $input = $request->all();
        if (!empty($input['coupon_code'])) {
            $couponCode = CouponCode::whereCode($input['coupon_code'])->first();
            if ($couponCode == null) {
                return $this->sendError(__('messages.error.invalid_coupon_code'));
            }
            if ($couponCode->used >= $couponCode->how_many_can_use) {
                return $this->sendError(__('messages.error.coupon_code_is_not_available'));
            }
        }
        $planData = $this->subscriptionRepository->getPlan($input);

        return $this->sendResponse($planData, 'data');
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function pay(Request $request): JsonResponse
    {
        $input = $request->all();
        $subscription = $this->subscriptionRepository->purchaseSubscription($input);
        if ($subscription->status == Subscription::ACTIVE) {
            return $this->sendSuccess(__('messages.success.plan_subscribed'));
        }

        return $this->sendSuccess(__('messages.success.wait_for_admin_approval'));
    }

    public function subscriptions(Request $request): JsonResponse
    {
        $perPage = getPageSize($request);
        $subscriptionIds = Subscription::where(function ($q) {
            $q->whereHas('transaction', function ($query) {
                $query->where('is_manual_payment', '!=', 2);
            })->orWhere('transaction_id', null);
        })->authenticated()->pluck('id')->toArray();

        $subscriptions = $this->subscriptionRepository->with('plan.currency')->authenticated()
            ->whereIn('id', $subscriptionIds)->paginate($perPage);

        return $this->sendResponse($subscriptions, 'data');
    }

    public function subscribedUserPlans(Request $request): JsonResponse
    {
        $perPage = getPageSize($request);
        $filterStatus = $request->get('plan_status');

        $query = $this->subscriptionRepository->whereHas('transaction', function(Builder $q) {
            $q->where('is_manual_payment', Transaction::APPROVED);
        })->orWhere('transaction_id',null);

        $subscribedIds = $query->pluck('id')->toArray();

        if(isset($request->filter['search'])) {
            $search = $request->filter['search'];
            $subscriptions = Subscription::with(['plan', 'user','transaction'])
                ->whereHas('plan', function(Builder $q) use ($search) {
                $q->where('name','like', '%'.$search.'%');
            })->whereIn('id', $subscribedIds);

        }else {
            $subscriptions = Subscription::with(['plan', 'user','transaction'])->whereIn('id', $subscribedIds);
        }

        if (isset($filterStatus)) {
            $status = $filterStatus == 1 ? 1 : 0;
            $subscriptions->where('status', $status);
        }

        $subscriptions = $subscriptions->paginate($perPage);

        return $this->sendResponse($subscriptions, 'data');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateSubscriptionEndDate(Request $request)
    {
        $input = $request->all();
        $this->subscriptionRepository->updateSubscriptionEndDate($input);

        return $this->sendSuccess(__('messages.success.subscription_date_updated'));
    }

    /**
     * @return JsonResponse
     */
    public function remainingSubscriptionDays(): JsonResponse
    {
        $settings = $this->subscriptionRepository->remainingSubscriptionDays();

        return $this->sendResponse($settings, __('messages.success.subscription_date_retrieved'));
    }

    /**
     * @return JsonResponse
     */
    public function publicSubscriptionPlans(): JsonResponse
    {
        $plans = Plan::with(['planFeatures', 'currency'])->get();

        return $this->sendResponse($plans, 'data');
    }

    /**
     * @param $id
     * @param $userID
     * @return JsonResponse
     */
    public function changeStatus($id, $userID)
    {
        $subscription = Subscription::whereId($id)->whereUserId($userID)->first();
        Subscription::whereUserId($userID)->update(['status' => false]);

        $subscription->update(['status' => !$subscription->status]);

        return $this->sendSuccess('Status updated successfully.');
    }
}
