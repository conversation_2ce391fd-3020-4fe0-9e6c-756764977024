<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreLinkRequest;
use App\Http\Requests\UpdateLinkRequest;
use App\Http\Resources\LinkCollection;
use App\Http\Resources\LinkResource;
use App\Models\DigitalBusinessCard;
use App\Models\Link;
use App\Repositories\LinkRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Factory;
use Illuminate\View\View;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class LinkAPIController extends AppBaseController
{
    /** @var LinkRepository */
    private $linkRepository;

    /**
     * @param LinkRepository $linkRepository
     */
    public function __construct(LinkRepository $linkRepository)
    {
        $this->linkRepository = $linkRepository;
        $this->middleware('checkLinkLimit')->only('store');
    }

    /**
     * @param Request $request
     *
     * @return LinkCollection
     */
    public function index(Request $request): LinkCollection
    {
        $perPage = getPageSize($request);
        $links = $this->linkRepository->tenant()->paginate($perPage);
        LinkResource::usingWithCollection();

        return new LinkCollection($links);
    }

    /**
     * @param StoreLinkRequest $request
     *
     * @return LinkResource|JsonResponse
     */
    public function store(StoreLinkRequest $request)
    {
        $input = $request->all();
        $ulrAlias = $input['url_alias'];
        $existAlias = DigitalBusinessCard::whereUrlAlias($ulrAlias)->exists();

        if ($existAlias) {
            return $this->sendError('The URL alias has been already in digital business card.');
        }

        $link = $this->linkRepository->storeLink($input);

        return new LinkResource($link);
    }

    /**
     * @param $id
     *
     * @return LinkResource
     */
    public function show($id): LinkResource
    {
        $link = Link::whereId($id)->first();

        return new LinkResource($link);
    }

    /**
     * @param $id
     * @param UpdateLinkRequest $request
     * @return LinkResource|JsonResponse
     */
    public function update($id, UpdateLinkRequest $request)
    {
        $input = $request->all();
        $ulrAlias = $input['url_alias'];
        $existAlias = DigitalBusinessCard::whereUrlAlias($ulrAlias)->exists();

        if ($existAlias) {
            return $this->sendError('The URL alias has been already in digital business card.');
        }

        $link = $this->linkRepository->updateLink($input, $id);

        return new LinkResource($link);
    }

    /**
     * @param Link $link
     * @return JsonResponse
     */
    public function destroy(Link $link): JsonResponse
    {
        $this->linkRepository->delete($link->id);

        return $this->sendSuccess(__('messages.success.link_deleted'));
    }

    /**
     * @param $alias
     * @return Application|RedirectResponse|Redirector|Factory|View
     */
    public function showPublicAlias($alias)
    {
        $link = Link::whereUrlAlias($alias)->first();
        $digitalBusinessCard = DigitalBusinessCard::with('socialLinks')->whereUrlAlias($alias)->first();

        if (!empty($link)) {
            return redirect($link->destination_url);
        } elseif (!empty($digitalBusinessCard)) {

            if (!$digitalBusinessCard->status) {
                return abort(404);
            }

            $templateId = $digitalBusinessCard->template_id;
            $socialLinks = $digitalBusinessCard->socialLinks()->get();

            return view('templates.template_' . $templateId, compact('digitalBusinessCard', 'socialLinks'));
        }

        return abort(404);
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function analytics($id): JsonResponse
    {
        $link = Link::find($id);
        $digitalBusinessCard = DigitalBusinessCard::find($id);

        if (request()->segment('2') == 'link') {
            $modelId = $link->id;
            $modelType = Link::class;
        } elseif (request()->segment('2') == 'business-card') {
            $modelId = $digitalBusinessCard->id;
            $modelType = DigitalBusinessCard::class;
        } else {
            return $this->sendError('This URL Alias record not found.');
        }

        $data = $this->linkRepository->analyticsData($modelId, $modelType);

        return $this->sendResponse($data, 'data');
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function chartData(Request $request): JsonResponse
    {
        try {
            $input = $request->all();
            $data = $this->linkRepository->chartData($input);

            return $this->sendResponse($data, 'Visitors fetch successfully.');
        } catch (Exception $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @return JsonResponse
     */
    public function mostVisitedLink(): JsonResponse
    {
        $tenantId = Auth::user()->tenant_id;
        $links = Link::whereTenantId($tenantId)->withCount(['analytics' => function ($q) {
            $q->whereBetween('created_at', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()]);
        }])->orderBy('analytics_count', 'desc')->limit(5)->get();

        $data = [];
        foreach ($links as $link) {
            $data[] = [
                'name' => $link->name,
                'count' => $link->analytics_count,
            ];
        }

        return $this->sendResponse($data, 'data');
    }
}
