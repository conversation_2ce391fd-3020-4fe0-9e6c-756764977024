<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\MultiTenant;
use App\Models\Plan;
use App\Models\Role;
use App\Models\Subscription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class FacebookController
 */
class SocialAuthController extends AppBaseController
{
    public function socialLogin(Request $request, $provider): \Illuminate\Http\JsonResponse
    {
        $input = $request->all();

        try {
            DB::beginTransaction();

            $user = User::whereEmail($input['data']['email'])->first();
            if (empty($user)) {
                $userData['name'] = $input['data']['name'];
                $userData['email'] = $input['data']['email'];
                $userData['email_verified_at'] = Carbon::now();
                $userData['password'] = bcrypt(Str::random(40));
                $userData['status'] = true;
                $userData['language'] = 'en';
                $tenant = MultiTenant::create(['tenant_username' => $userData['name']]);
                $userData['tenant_id'] = $tenant->id;
                $user = User::create($userData)->assignRole(Role::USER);
                $user->addMediaFromUrl($input['data']['picture'])->toMediaCollection(User::PATH, config('app.media_disc'));

                $plan = Plan::whereIsDefault(true)->first();
                $trialDays = $plan->trial_days;
                $subscription = [
                    'user_id'       => $user->id,
                    'plan_id'       => $plan->id,
                    'price_of_plan' => $plan->price,
                    'plan_type'     => $plan->type,
                    'start_date'    => Carbon::now(),
                    'end_date'      => Carbon::now()->addDays($trialDays),
                    'trial_ends_at' => Carbon::now()->addDays($trialDays),
                    'status'        => Subscription::ACTIVE,
                ];
                Subscription::create($subscription);
            }
            DB::commit();
            $token = $user->createToken('token')->plainTextToken;

            return response()->json([
                'data'    => [
                    'token' => $token,
                    'user'  => $user,
                ],
                'message' => 'Logged in successfully.',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
