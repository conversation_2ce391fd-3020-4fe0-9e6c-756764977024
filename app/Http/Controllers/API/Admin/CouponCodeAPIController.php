<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreCouponCodeRequest;
use App\Http\Requests\UpdateCouponCodeRequest;
use App\Http\Resources\CouponCodeCollection;
use App\Http\Resources\CouponCodeResource;
use App\Models\CouponCode;
use App\Models\Transaction;
use App\Repositories\CouponCodeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CouponCodeAPIController extends AppBaseController
{

    /** @var CouponCodeRepository */
    private CouponCodeRepository $couponCodeRepository;

    /**
     * @param CouponCodeRepository $couponCodeRepository
     */
    public function __construct(CouponCodeRepository $couponCodeRepository)
    {
        $this->couponCodeRepository = $couponCodeRepository;
    }

    /**
     * @param Request $request
     *
     * @return CouponCodeCollection
     */
    public function index(Request $request): CouponCodeCollection
    {
        $perPage = getPageSize($request);
        $couponCodes = $this->couponCodeRepository->paginate($perPage);
        CouponCodeResource::usingWithCollection();

        return new CouponCodeCollection($couponCodes);
    }

    /**
     * @param StoreCouponCodeRequest $request
     *
     * @return CouponCodeResource
     */
    public function store(StoreCouponCodeRequest $request): CouponCodeResource
    {
        $input = $request->all();
        $project = $this->couponCodeRepository->storeCouponCode($input);

        return new CouponCodeResource($project);
    }

    /**
     * @param $id
     *
     * @return CouponCodeResource
     */
    public function show($id): CouponCodeResource
    {
        $couponCode = $this->couponCodeRepository->find($id);

        return new CouponCodeResource($couponCode);
    }

    /**
     * @param $id
     * @param UpdateCouponCodeRequest $request
     *
     * @return CouponCodeResource
     */
    public function update($id, UpdateCouponCodeRequest $request): CouponCodeResource
    {
        $input = $request->all();
        $couponCode = $this->couponCodeRepository->updateCouponCode($input, $id);

        return new CouponCodeResource($couponCode);
    }

    /**
     * @param CouponCode $couponCode
     *
     * @return JsonResponse
     */
    public function destroy(CouponCode $couponCode): JsonResponse
    {
        $findCouponCode = Transaction::whereCouponCode($couponCode->code)->exists();
        if ($findCouponCode) {
            return $this->sendError(__('messages.error.coupon_code_used_by_user'));
        }
        $this->couponCodeRepository->delete($couponCode->id);

        return $this->sendSuccess(__('messages.success.coupon_code_deleted'));
    }
}
