<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreCurrencyRequest;
use App\Http\Requests\UpdateCurrencyRequest;
use App\Http\Resources\CurrencyCollection;
use App\Http\Resources\CurrencyResource;
use App\Models\Currency;
use App\Models\Plan;
use App\Repositories\CurrencyRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CurrencyAPIController extends AppBaseController
{
    /** @var CurrencyRepository */
    private CurrencyRepository $currencyRepository;

    /**
     * @param CurrencyRepository $currencyRepository
     */
    public function __construct(CurrencyRepository $currencyRepository)
    {
        $this->currencyRepository = $currencyRepository;
    }

    /**
     * @param Request $request
     *
     * @return CurrencyCollection
     */
    public function index(Request $request): CurrencyCollection
    {
        $perPage = getPageSize($request);
        $currencies = $this->currencyRepository->paginate($perPage);
        CurrencyResource::usingWithCollection();

        return new CurrencyCollection($currencies);
    }

    /**
     * @param StoreCurrencyRequest $request
     *
     * @return CurrencyResource
     */
    public function store(StoreCurrencyRequest $request): CurrencyResource
    {
        $input = $request->all();
        $currency = $this->currencyRepository->storeCurrency($input);

        return new CurrencyResource($currency);
    }

    /**
     * @param $id
     *
     * @return CurrencyResource
     */
    public function show($id): CurrencyResource
    {
        $currency = $this->currencyRepository->find($id);

        return new CurrencyResource($currency);
    }

    /**
     * @param $id
     * @param UpdateCurrencyRequest $request
     *
     * @return CurrencyResource
     */
    public function update($id, UpdateCurrencyRequest $request): CurrencyResource
    {
        $input = $request->all();
        $currency = $this->currencyRepository->updateCurrency($input, $id);

        return new CurrencyResource($currency);
    }

    /**
     * @param Currency $currency
     *
     * @return JsonResponse
     */
    public function destroy(Currency $currency): JsonResponse
    {
        $result = canDelete(Plan::class, 'currency_id', $currency->id);
        if ($result || getSettingValue('currency') == $currency->id) {
            return $this->sendError(__('messages.error.currency_used_somewhere_else'));
        }
        $this->currencyRepository->delete($currency->id);

        return $this->sendSuccess(__('messages.success.currency_deleted'));
    }
}
