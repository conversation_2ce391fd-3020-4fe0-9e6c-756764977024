<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Resources\TransactionCollection;
use App\Http\Resources\TransactionResource;
use App\Models\Transaction;
use App\Repositories\TransactionRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TransactionAPIController extends AppBaseController
{
    /** @var TransactionRepository */
    private TransactionRepository $transactionRepository;

    /**
     * @param TransactionRepository $transactionRepository
     */
    public function __construct(TransactionRepository $transactionRepository)
    {
        $this->transactionRepository = $transactionRepository;
    }

    public function index(Request $request): TransactionCollection
    {
        $perPage = getPageSize($request);
        $transactions = $this->transactionRepository->paginate($perPage);
        TransactionResource::usingWithCollection();

        return new TransactionCollection($transactions);
    }

    public function show($id): JsonResponse
    {
        $transaction = Transaction::with(['subscription.plan', 'subscription.user'])->findOrFail($id);

        return $this->sendResponse($transaction, 'data');
    }

    public function cashPayments(Request $request): JsonResponse
    {
        $perPage = getPageSize($request);
        $cashPayments = $this->transactionRepository->with(['subscription.plan.currency', 'subscription.user'])
            ->manualType()->paginate($perPage);

        return $this->sendResponse($cashPayments, 'data');
    }

    public function cashPaymentDetails($id)
    {
        $cashPayment = $this->transactionRepository->with(['subscription.plan.currency', 'subscription.user'])->find($id);

        return $this->sendResponse($cashPayment, 'data');
    }

    public function changePaymentStatus(Request $request): JsonResponse
    {
        $input = $request->all();
        $this->transactionRepository->changePaymentStatus($input);

        return $this->sendSuccess(__('messages.success.payment_status_updated'));
    }

    /**
     * @return JsonResponse
     */
    public function getWeekEarnings(): JsonResponse
    {
        $count = 7;
        $days = [];
        $date = Carbon::tomorrow();
        for ($i = 0; $i < $count; $i++) {
            $days[] = $date->subDay()->format('Y-m-d');
        }
        $day['days'] = array_reverse($days);
        $earnings = Transaction::whereStatus(Transaction::APPROVED)->whereBetween('date', [$day['days'][0], $day['days'][6]])
            ->orderBy('date', 'desc')
            ->groupBy('date')
            ->get([
                DB::raw('DATE_FORMAT(date,"%Y-%m-%d") as week'),
                DB::raw('SUM(price_of_plan) as price_of_plan'),
            ])->keyBy('week');
        $period = CarbonPeriod::create($day['days'][0], $day['days'][6]);
        $data['dates'] = array_map(function ($datePeriod) {
            return $datePeriod->format('Y-m-d');
        }, iterator_to_array($period));

        $data['earning'] = array_map(function ($datePeriod) use ($earnings) {
            $week = $datePeriod->format('Y-m-d');

            return $earnings->has($week) ? $earnings->get($week)->price_of_plan : 0;
        }, iterator_to_array($period));

        return $this->sendResponse($data, 'Week of Earning Retrieved Successfully');
    }
}
