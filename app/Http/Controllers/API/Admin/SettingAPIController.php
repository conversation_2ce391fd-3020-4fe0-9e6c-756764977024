<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\UpdateCaptchaSettingRequest;
use App\Http\Requests\UpdateGoogleLoginSettingRequest;
use App\Http\Requests\UpdateMailSettingRequest;
use App\Http\Requests\UpdateMainSettingRequest;
use App\Http\Requests\UpdatePaypalSettingRequest;
use App\Http\Requests\UpdateRazorpaySettingRequest;
use App\Http\Requests\UpdateStripeSettingRequest;
use App\Repositories\SettingRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class SettingAPIController extends AppBaseController
{
    /** @var SettingRepository */
    private SettingRepository $settingRepository;

    /**
     * @param SettingRepository $settingRepository
     */
    public function __construct(SettingRepository $settingRepository)
    {
        $this->settingRepository = $settingRepository;
    }

    public function mainSetting(): JsonResponse
    {
        $mainSetting = $this->settingRepository->getMainSetting();

        return $this->sendResponse($mainSetting, __('messages.success.main_setting_retrieved'));
    }

    public function updateMainSetting(UpdateMainSettingRequest $request): JsonResponse
    {
        $input = $request->all();
        $mainSetting = $this->settingRepository->updateMainSetting($input);

        return $this->sendResponse($mainSetting, __('messages.success.main_setting_updated'));
    }

    public function paypalSetting(): JsonResponse
    {
        $paypalSetting = $this->settingRepository->getPaypalSetting();

        return $this->sendResponse($paypalSetting, __('messages.success.paypal_data_retrieved'));
    }

    public function updatePaypalSetting(UpdatePaypalSettingRequest $request): JsonResponse
    {
        $input = $request->all();
        $paypalSetting = $this->settingRepository->updatePaypalSetting($input);

        return $this->sendResponse($paypalSetting, __('messages.success.paypal_data_updated'));
    }

    public function stripeSetting(): JsonResponse
    {
        $stripeSetting = $this->settingRepository->getStripeSetting();

        return $this->sendResponse($stripeSetting, __('messages.success.stripe_data_retrieved'));
    }

    public function updateStripeSetting(UpdateStripeSettingRequest $request): JsonResponse
    {
        $input = $request->all();
        $stripeSetting = $this->settingRepository->updateStripeSetting($input);

        return $this->sendResponse($stripeSetting, __('messages.success.stripe_data_updated'));
    }

    public function razorpaySetting(): JsonResponse
    {
        $razorpaySetting = $this->settingRepository->getRazorpaySetting();

        return $this->sendResponse($razorpaySetting, __('messages.success.razorpay_setting_retrieved'));
    }

    public function updateRazorpaySetting(UpdateRazorpaySettingRequest $request): JsonResponse
    {
        $input = $request->all();
        $razorpaySetting = $this->settingRepository->updateRazorpaySetting($input);

        return $this->sendResponse($razorpaySetting, __('messages.success.razorpay_setting_updated'));
    }

    public function captchaSetting(): JsonResponse
    {
        $captchaSetting = $this->settingRepository->getCaptchaSetting();

        return $this->sendResponse($captchaSetting, __('messages.success.captcha_data_retrieved'));
    }

    public function updateCaptchaSetting(UpdateCaptchaSettingRequest $request): JsonResponse
    {
        $input = $request->all();
        $captchaSetting = $this->settingRepository->updateCaptchaSetting($input);

        return $this->sendResponse($captchaSetting, __('messages.success.captcha_data_updated'));
    }

    public function adsSetting(): JsonResponse
    {
        $adsSetting = $this->settingRepository->getAdsSetting();

        return $this->sendResponse($adsSetting, __('messages.success.ads_data_retrieved'));
    }

    public function updateAdsSetting(Request $request): JsonResponse
    {
        $input = $request->all();
        $adsSetting = $this->settingRepository->updateAdsSetting($input);

        return $this->sendResponse($adsSetting, __('messages.success.ads_data_updated'));
    }

    public function socialSetting(): JsonResponse
    {
        $socialSetting = $this->settingRepository->getSocialSetting();

        return $this->sendResponse($socialSetting, __('messages.success.social_setting_data_retrieved'));
    }

    public function updateSocialSetting(Request $request): JsonResponse
    {
        $input = $request->all();
        $socialSetting = $this->settingRepository->updateSocialSetting($input);

        return $this->sendResponse($socialSetting, __('messages.success.social_setting_data_updated'));
    }

    public function mailSetting(): JsonResponse
    {
        $mailSetting = $this->settingRepository->getMailSetting();

        return $this->sendResponse($mailSetting, __('messages.success.main_setting_retrieved'));
    }

    public function updateMailSetting(UpdateMailSettingRequest $request): JsonResponse
    {
        $input = $request->all();
        $mailSetting = $this->settingRepository->updateMailSetting($input);

        return $this->sendResponse($mailSetting, __('messages.success.main_setting_updated'));
    }

    public function customStyleSetting(): JsonResponse
    {
        $customStyleSetting = $this->settingRepository->getCustomStyleSetting();

        return $this->sendResponse($customStyleSetting, __('messages.success.custom_style_setting_retrieved'));
    }

    public function updateCustomStyleSetting(Request $request): JsonResponse
    {
        $input = $request->all();
        $customStyleSetting = $this->settingRepository->updateCustomStyleSetting($input);

        return $this->sendResponse($customStyleSetting, __('messages.success.custom_style_setting_updated'));
    }

    public function announcementSetting(): JsonResponse
    {
        $announcementSetting = $this->settingRepository->getAnnouncementSetting();

        return $this->sendResponse($announcementSetting, __('messages.success.announcement_setting_retrieved'));
    }

    public function updateAnnouncementSetting(Request $request): JsonResponse
    {
        $input = $request->all();
        $announcementSetting = $this->settingRepository->updateAnnouncementSetting($input);

        return $this->sendResponse($announcementSetting, __('messages.success.announcement_setting_updated'));
    }

    public function emailNotificationSetting(): JsonResponse
    {
        $emailNotification = $this->settingRepository->getEmailNotificationSetting();

        return $this->sendResponse($emailNotification, __('messages.success.email_notification_retrieved'));
    }

    public function updateEmailNotificationSetting(Request $request): JsonResponse
    {
        $input = $request->all();
        $emailNotification = $this->settingRepository->updateEmailNotificationSetting($input);

        return $this->sendResponse($emailNotification, __('messages.success.email_notification_updated'));
    }

    /**
     * @return JsonResponse
     */
    public function clearCache(): JsonResponse
    {
        Artisan::call('cache:clear');

        return $this->sendSuccess(__('messages.success.cache_clear_successfully'));
    }

    /**
     * @return JsonResponse
     */
    public function getFrontSettingsValue(): JsonResponse
    {
        $settings = $this->settingRepository->getFrontSettingsValue();

        return $this->sendResponse($settings, __('messages.success.setting_value_retrieved'));
    }

    public function googleLoginSetting(): JsonResponse
    {
        $googleLogin = $this->settingRepository->getGoogleLoginSetting();

        return $this->sendResponse($googleLogin, __('messages.success.google_login_setting_retrieved'));
    }

    public function updateGoogleLoginSetting(UpdateGoogleLoginSettingRequest $request): JsonResponse
    {
        $input = $request->all();
        $googleLogin = $this->settingRepository->updateGoogleLoginSetting($input);

        return $this->sendResponse($googleLogin, __('messages.success.google_login_setting_updated'));
    }

    public function config()
    {
        $composerFile = file_get_contents('../composer.json');
        $composerData = json_decode($composerFile, true);
        $currentVersion = isset($composerData['version']) ? $composerData['version'] : '';

        return $this->sendResponse(['version' => $currentVersion], __('messages.success.config_retrieved'));
    }
}
