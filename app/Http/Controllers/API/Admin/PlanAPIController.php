<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StorePlanRequest;
use App\Http\Requests\UpdatePlanRequest;
use App\Http\Resources\PlanCollection;
use App\Http\Resources\PlanResource;
use App\Models\Plan;
use App\Models\Transaction;
use App\Repositories\PlanRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PlanAPIController extends AppBaseController
{
    /** @var PlanRepository */
    private PlanRepository $planRepository;

    /**
     * @param PlanRepository $planRepository
     */
    public function __construct(PlanRepository $planRepository)
    {
        $this->planRepository = $planRepository;
    }

    /**
     * @param Request $request
     *
     *
     * @return PlanCollection
     */
    public function index(Request $request): PlanCollection
    {
        $perPage = getPageSize($request);
        $plans = $this->planRepository->paginate($perPage);
        PlanResource::usingWithCollection();

        return new PlanCollection($plans);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StorePlanRequest $request
     * @return PlanResource
     */
    public function store(StorePlanRequest $request): PlanResource
    {
        $input = $request->all();
        $plan = $this->planRepository->storePlan($input);
        PlanResource::usingWithCollection();

        return new PlanResource($plan);
    }

    /**
     * @param $id
     *
     * @return PlanResource
     */
    public function show($id): PlanResource
    {
        $plan = $this->planRepository->find($id);

        return new PlanResource($plan);
    }

    /**
     * @param $id
     * @param UpdatePlanRequest $request
     *
     * @return PlanResource
     */
    public function update($id, UpdatePlanRequest $request): PlanResource
    {
        $input = $request->all();
        $plan = $this->planRepository->updatePlan($input, $id);

        return new PlanResource($plan);
    }

    /**
     * @param Plan $plan
     *
     * @return JsonResponse
     */
    public function destroy(Plan $plan): JsonResponse
    {
        $this->planRepository->delete($plan->id);

        return $this->sendSuccess(__('messages.success.plan_deleted'));
    }


    public function makePlanDefault($id): JsonResponse
    {
        $defaultPlan = Plan::where('is_default', 1)->first();
        $defaultPlan->update(['is_default' => 0]);
        $plan = Plan::findOrFail($id);
        if ($plan->trial_days == 0) {
            $plan->trial_days = Plan::TRAIL_DAYS;
        }
        $plan->is_default = 1;
        $plan->save();

        return $this->sendSuccess(__('messages.success.default_plan_changed'));
    }
}
