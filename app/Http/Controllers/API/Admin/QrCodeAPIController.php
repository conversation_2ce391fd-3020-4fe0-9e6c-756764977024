<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreQrCodeRequest;
use App\Http\Resources\QrCodeCollection;
use App\Http\Resources\QrCodeResource;
use App\Models\User;
use App\Repositories\QrCodeRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class QrCodeAPIController extends AppBaseController
{
    /** @var QrCodeRepository */
    private QrCodeRepository $qrCodeRepository;

    /**
     * @param QrCodeRepository $qrCodeRepository
     */
    public function __construct(QrCodeRepository $qrCodeRepository)
    {
        $this->qrCodeRepository = $qrCodeRepository;
    }

    /**
     * @param Request $request
     *
     * @return QrCodeCollection
     */
    public function index(Request $request): QrCodeCollection
    {
        $perPage = getPageSize($request);
        $qrCodes = $this->qrCodeRepository->paginate($perPage);
        QrCodeResource::usingWithCollection();

        return new QrCodeCollection($qrCodes);
    }

    /**
     * @param $id
     *
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $this->qrCodeRepository->delete($id);

        return $this->sendSuccess(__('messages.success.qr_code_deleted'));
    }

    /**
     * @param StoreQrCodeRequest $request
     *
     * @return QrCodeResource
     */
    public function store(StoreQrCodeRequest $request): QrCodeResource
    {
        $input = $request->all();
        $qrCode = $this->qrCodeRepository->storeQrCode($input);

        return new QrCodeResource($qrCode);
    }

    public function projectList(Request $request)
    {
        $projectList = $this->qrCodeRepository->projectList($request->tenant_id);
        QrCodeResource::usingWithCollection();

        return new QrCodeCollection($projectList);
    }

    public function qrCodeTypes(Request $request)
    {
        $qrCodeTypes = $this->qrCodeRepository->qrCodeTypes($request->tenant_id);

        return $this->sendResponse($qrCodeTypes, 'Qr Code types retrieved successfully.');
    }
}
