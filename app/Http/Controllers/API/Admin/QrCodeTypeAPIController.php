<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Resources\QrCodeResource;
use App\Http\Resources\QrCodeTypeCollection;
use App\Http\Resources\QrCodeTypeResource;
use App\Repositories\QrCodeTypeRepository;
use Illuminate\Http\Request;

class QrCodeTypeAPIController extends AppBaseController
{
    /** @var QrCodeTypeRepository */
    private QrCodeTypeRepository $qrCodeTypeRepo;
    
    public function __construct(QrCodeTypeRepository $qrCodeTypeRepository)
    {
        $this->qrCodeTypeRepo = $qrCodeTypeRepository;
    }
    
    public function index(Request $request)
    {
        $perPage = getPageSize($request);
        $qrCodeTypes = $this->qrCodeTypeRepo->paginate($perPage);
        QrCodeResource::usingWithCollection();
        
        return new QrCodeTypeCollection($qrCodeTypes);
    }

    /**
     * @param Request $request
     * @return QrCodeTypeResource
     */
    public function update(Request $request)
    {
        $updateInput = $request->all();
        $qrCodeType = $this->qrCodeTypeRepo->find($updateInput['id']);
        unset($updateInput['id']);
        $qrCodeType->update($updateInput);
        
        return new QrCodeTypeResource($qrCodeType);
    }
}
