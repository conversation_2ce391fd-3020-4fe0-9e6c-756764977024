<?php

namespace App\Http\Controllers\API\Admin;

use Carbon\Carbon;
use App\Models\Link;
use App\Models\Role;
use App\Models\User;
use App\Models\QrCode;
use App\Models\Project;
use App\Models\Transaction;
use App\Models\Subscription;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\AppBaseController;
use Illuminate\Contracts\Database\Query\Builder;

class DashboardAPIController extends AppBaseController
{
    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data['qr_codes'] = QrCode::count();
        $data['links'] = Link::count();
        $data['projects'] = Project::count();
        $data['total_earning'] = Transaction::whereStatus(Transaction::APPROVED)->sum('price_of_plan');
        $todayDate = Carbon::now()->toDateString();
        $data['today_earning'] = Transaction::where('date',$todayDate)->whereStatus(Transaction::APPROVED)->sum('price_of_plan');
        $data['total_subscriptions'] = Subscription::with('transaction')
                                        ->whereHas('transaction', function(Builder $q) {
                                            $q->where('is_manual_payment', Transaction::APPROVED);
                                        })->orWhere('transaction_id', null)->count();
        $data['active_subscriptions'] = Subscription::whereStatus(Subscription::ACTIVE)->count();
        $data['users'] = User::whereHas('roles', function ($q) {
            $q->where('name', Role::USER);
        })->count();
        $data['latest_users'] = [];
        $latestUsers = User::whereHas('roles', function ($q) {
            $q->where('name', Role::USER);
        })->latest()->take(5)->get();
        foreach ($latestUsers as $user) {
            $data['latest_users'][] = $user->prepareAttributes();
        }

        return $this->sendResponse($data,__('messages.success.admin_data_retrieved'));
    }
}
