<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StorePageRequest;
use App\Http\Requests\UpdatePageRequest;
use App\Http\Resources\PageCollection;
use App\Http\Resources\PageResource;
use App\Models\Page;
use App\Repositories\PageRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PageAPIController extends AppBaseController
{
    /** @var PageRepository */
    private PageRepository $PageRepository;

    /**
     * @param PageRepository $PageRepository
     */
    public function __construct(PageRepository $PageRepository)
    {
        $this->PageRepository = $PageRepository;
    }

    /**
     * @param Request $request
     *
     * @return PageCollection
     */
    public function index(Request $request): PageCollection
    {
        $perPage = getPageSize($request);
        $pages = $this->PageRepository->paginate($perPage);
        PageResource::usingWithCollection();

        return new PageCollection($pages);
    }

    /**
     * @param StorePageRequest $request
     *
     * @return PageResource
     */
    public function store(StorePageRequest $request): PageResource
    {
        $input = $request->all();
        $page = $this->PageRepository->storePage($input);

        return new PageResource($page);
    }

    /**
     * @param $id
     *
     * @return PageResource
     */
    public function show($id): PageResource
    {
        $page = $this->PageRepository->find($id);

        return new PageResource($page);
    }

    /**
     * @param $id
     * @param UpdatePageRequest $request
     *
     * @return PageResource
     */
    public function update($id, UpdatePageRequest $request): PageResource
    {
        $input = $request->all();
        $page = $this->PageRepository->updatePage($input, $id);

        return new PageResource($page);
    }

    /**
     * @param Page $page
     * @return JsonResponse
     */
    public function destroy(Page $page): JsonResponse
    {
        $this->PageRepository->delete($page->id);

        return $this->sendSuccess(__('Page deleted successfully.'));
    }

    /**
     *
     * @return JsonResponse
     */
    public function getPages(): JsonResponse
    {
        $pages = $this->PageRepository->get();

        return $this->sendResponse($pages, 'data');
    }

    /**
     * @param $slug
     *
     * @return JsonResponse
     */
    public function getPage($slug): JsonResponse
    {
        $page = $this->PageRepository->where('slug', $slug)->get();

        return $this->sendResponse($page, 'data');
    }
}
