<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreLinkRequest;
use App\Http\Resources\LinkCollection;
use App\Http\Resources\LinkResource;
use App\Models\Analytic;
use App\Models\DigitalBusinessCard;
use App\Models\Link;
use App\Models\User;
use App\Repositories\LinkRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Stripe\Card;

class LinkAPIController extends AppBaseController
{
    /** @var LinkRepository */
    private LinkRepository $linkRepository;

    /**
     * @param LinkRepository $linkRepository
     */
    public function __construct(LinkRepository $linkRepository)
    {
        $this->linkRepository = $linkRepository;
    }

    /**
     * @param Request $request
     *
     * @return LinkCollection
     */
    public function index(Request $request): LinkCollection
    {
        $perPage = getPageSize($request);
        $links = $this->linkRepository->paginate($perPage);
        LinkResource::usingWithCollection();

        return new LinkCollection($links);
    }

    /**
     * @param $id
     *
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $this->linkRepository->delete($id);

        return $this->sendSuccess(__('messages.success.link_deleted'));
    }

    /**
     * @param StoreLinkRequest $request
     *
     * @return LinkResource|JsonResponse
     */
    public function store(StoreLinkRequest $request)
    {
        $input = $request->all();
        $ulrAlias = $input['url_alias'];
        $existAlias = DigitalBusinessCard::whereUrlAlias($ulrAlias)->exists();

        if ($existAlias) {
            return $this->sendError('The URL alias has been already in digital business card.');
        }
        
        $link = $this->linkRepository->storeLink($input);

        return new LinkResource($link);
    }

    /**
     * @return JsonResponse
     */
    public function mostVisitedLink(): JsonResponse
    {
        $links = Link::withCount(['analytics'=> function($q) {
            $q->whereBetween('created_at', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()]);
        }])->orderBy('analytics_count', 'desc')->limit(5)->get();

        $data = [];
        foreach ($links as $link) {
            $data[] = [
                'name' => $link->name,
                'count' => $link->analytics_count,
            ];
        }

        return $this->sendResponse($data, 'data');
    }
}
