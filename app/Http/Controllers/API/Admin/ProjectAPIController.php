<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\CreateAdminProjectRequest;
use App\Http\Resources\ProjectCollection;
use App\Http\Resources\ProjectResource;
use App\Repositories\ProjectRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProjectAPIController extends AppBaseController
{
    /** @var ProjectRepository */
    private ProjectRepository $projectRepository;

    /**
     * @param ProjectRepository $projectRepository
     */
    public function __construct(ProjectRepository $projectRepository)
    {
        $this->projectRepository = $projectRepository;
    }

    /**
     * @param Request $request
     *
     * @return ProjectCollection
     */
    public function index(Request $request): ProjectCollection
    {
        $perPage = getPageSize($request);
        $projects = $this->projectRepository->paginate($perPage);
        ProjectResource::usingWithCollection();

        return new ProjectCollection($projects);
    }

    /**
     * @param CreateAdminProjectRequest $request
     * @return ProjectResource
     */
    public function store(CreateAdminProjectRequest $request): ProjectResource
    {
        $input = $request->all();
        $project = $this->projectRepository->storeAdminProject($input);

        return new ProjectResource($project);
    }

    /**
     * @param $id
     *
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $this->projectRepository->delete($id);

        return $this->sendSuccess(__('messages.success.project_deleted'));
    }
}
