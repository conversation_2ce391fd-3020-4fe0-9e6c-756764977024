<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\Link;
use App\Models\Project;
use App\Models\QrCode;
use Illuminate\Http\JsonResponse;

class DashboardAPIController extends AppBaseController
{
    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data['qr_codes'] = QrCode::whereTenantId(getLoginUserTenantId())->count();
        $data['links'] = Link::whereTenantId(getLoginUserTenantId())->count();
        $data['projects'] = Project::whereTenantId(getLoginUserTenantId())->count();
        $data['latest_qrcodes'] = [];
        $latestQrCodes = QrCode::whereTenantId(getLoginUserTenantId())->latest()->take(5)->get();
        foreach ($latestQrCodes as $qrCode) {
            $data['latest_qrcodes'][] = $qrCode->prepareAttributes();
        }

        return $this->sendResponse($data, 'user dashboard data retrieved successfully');
    }
}
