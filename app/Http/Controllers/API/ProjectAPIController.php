<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreProjectRequest;
use App\Http\Requests\UpdateProjectRequest;
use App\Http\Resources\ProjectCollection;
use App\Http\Resources\ProjectResource;
use App\Models\Project;
use App\Repositories\ProjectRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectAPIController extends AppBaseController
{
    /** @var ProjectRepository */
    private ProjectRepository $projectRepository;

    /**
     * @param ProjectRepository $projectRepository
     */
    public function __construct(ProjectRepository $projectRepository)
    {
        $this->projectRepository = $projectRepository;
        $this->middleware('checkProjectLimit')->only('store');
    }

    /**
     * @param Request $request
     *
     * @return ProjectCollection
     */
    public function index(Request $request): ProjectCollection
    {
        $perPage = getPageSize($request);
        $projects = $this->projectRepository->tenant()->paginate($perPage);
        ProjectResource::usingWithCollection();

        return new ProjectCollection($projects);
    }

    /**
     * @param StoreProjectRequest $request
     *
     * @return ProjectResource
     */
    public function store(StoreProjectRequest $request): ProjectResource
    {
        $input = $request->all();
        $project = $this->projectRepository->storeUserProject($input);

        return new ProjectResource($project);
    }

    /**
     * @param $id
     * @return ProjectResource
     */
    public function show($id): ProjectResource
    {
        $project = $this->projectRepository->find($id);

        return new ProjectResource($project);
    }

    /**
     * @param $id
     * @param UpdateProjectRequest $request
     *
     * @return ProjectResource
     */
    public function update($id, UpdateProjectRequest $request): ProjectResource
    {
        $input = $request->all();
        $project = $this->projectRepository->updateProject($input, $id);

        return new ProjectResource($project);
    }

    /**
     * @param Project $project
     *
     * @return JsonResponse
     */
    public function destroy(Project $project): JsonResponse
    {
        $this->projectRepository->delete($project->id);

        return $this->sendSuccess(__('messages.success.project_deleted'));
    }
}
