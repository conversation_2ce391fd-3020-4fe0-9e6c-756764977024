<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreContactUsRequest;
use App\Http\Requests\StoreSubscriberRequest;
use App\Jobs\ContactUsEmailJob;
use App\Models\ContactUs;
use App\Repositories\ContactUsRepository;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ContactUsAPIController extends AppBaseController
{
    /** @var ContactUsRepository */
    private ContactUsRepository $contactUsRepository;

    /**
     * @param ContactUsRepository $contactUsRepository
     */
    public function __construct(ContactUsRepository $contactUsRepository)
    {
        $this->contactUsRepository = $contactUsRepository;
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = getPageSize($request);

        $messages = $this->contactUsRepository->paginate($perPage);

        return $this->sendResponse($messages, 'data');
    }

    public function store(StoreContactUsRequest $request): JsonResponse
    {
        $input = $request->all();
        verifyCaptcha();
        $contactDetails = ContactUs::create($input);
        dispatch(new ContactUsEmailJob($input, $contactDetails->email));

        return $this->sendResponse($contactDetails, __('messages.success.message_send_successfully'));
    }
    
    public function show($id)
    {
        $contactDetails = ContactUs::findOrFail($id);
        $contactDetails->update(['view' => isset($contactDetails->view) ?? true]);

        return $this->sendResponse($contactDetails, __('messages.success.message_view_successfully'));
    }
    
    public function destroy(Request $request): JsonResponse
    {
        ContactUs::findOrFail($request->id)->delete();

        return $this->sendSuccess(__('messages.success.message_deleted'));
    }
}
