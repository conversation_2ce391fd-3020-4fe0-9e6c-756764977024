<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreSubscriberRequest;
use App\Models\Subscriber;
use App\Repositories\SubscriberRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SubscriberAPIController extends AppBaseController
{
    /** @var SubscriberRepository */
    private SubscriberRepository $subscriberRepository;

    /**
     * @param SubscriberRepository $subscriberRepository
     */
    public function __construct(SubscriberRepository $subscriberRepository)
    {
        $this->subscriberRepository = $subscriberRepository;
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = getPageSize($request);

        $subscribers = $this->subscriberRepository->paginate($perPage);

        return $this->sendResponse($subscribers, 'data');
    }

    /**
     * @param StoreSubscriberRequest $request
     *
     * @return JsonResponse
     */
    public function store(StoreSubscriberRequest $request): JsonResponse
    {
        $input = $request->all();
        Subscriber::create($input);

        return $this->sendResponse($input['email'], __('messages.success.subscribed_successfully'));
    }

    /**
     * @param Subscriber $subscriber
     *
     * @return JsonResponse
     */
    public function destroy(Subscriber $subscriber): JsonResponse
    {
        $subscriber->delete();

        return $this->sendSuccess(__('messages.success.subscriber_deleted'));
    }
}
