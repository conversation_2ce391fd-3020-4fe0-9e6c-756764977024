<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\UpdateFeaturesRequest;
use App\Http\Requests\UpdateFrontCmsRequest;
use App\Http\Requests\UpdateTermConditionsRequest;
use App\Http\Resources\FeatureCollection;
use App\Http\Resources\FeatureResource;
use App\Models\Feature;
use App\Models\FrontCMS;
use App\Models\Setting;
use App\Models\SubFeature;
use App\Repositories\FeatureRepository;
use App\Repositories\SubFeatureRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class FrontCmsAPIController extends AppBaseController
{
    private FeatureRepository $featureRepository;
    private SubFeatureRepository $subFeatureRepository;

    /**
     * @param FeatureRepository $featureRepository
     * @param SubFeatureRepository $subFeatureRepository
     */
    public function __construct(FeatureRepository $featureRepository, SubFeatureRepository $subFeatureRepository)
    {
        $this->featureRepository = $featureRepository;
        $this->subFeatureRepository = $subFeatureRepository;
    }

    public function index(): JsonResponse
    {
        $frontCms = FrontCMS::all();

        return $this->sendResponse($frontCms, 'data');
    }

    public function updateFrontCMS(UpdateFrontCmsRequest $request): JsonResponse
    {
        $input = $request->all();
        $frontCms = FrontCMS::find($input['id']);
        
        $frontCms->update($input);
        if (!empty($input['image'])) {
            $frontCms->clearMediaCollection(FrontCMS::IMAGE);
            $frontCms->media()->delete();
            $frontCms->addMedia($input['image'])->toMediaCollection(FrontCMS::IMAGE, config('app.media_disc'));
        }

        return $this->sendResponse($frontCms, __('messages.success.front_cms_updated'));
    }

    /**
     * @return JsonResponse
     */
    public function subFeatures(): JsonResponse
    {
        $subFeatures = $this->subFeatureRepository->get();

        return $this->sendResponse($subFeatures, 'data');
    }

    public function updateSubFeatures(UpdateFrontCmsRequest $request): JsonResponse
    {
        $input = $request->all();
        $subFeatures = SubFeature::find($input['id']);
        $subFeatures->update($input);
        if (!empty($input['image'])) {
            $subFeatures->clearMediaCollection(FrontCMS::IMAGE);
            $subFeatures->media()->delete();
            $subFeatures->addMedia($input['image'])->toMediaCollection(FrontCMS::IMAGE, config('app.media_disc'));
        }

        return $this->sendResponse($subFeatures, __('messages.success.sub_feature_updated'));
    }

    /**
     * @return FeatureCollection
     */
    public function features()
    {
        $features = $this->featureRepository->get();
        FeatureResource::usingWithCollection();

        return new FeatureCollection($features);
    }

    /**
     * @param UpdateFeaturesRequest $request
     * @return JsonResponse
     */
    public function updateFeatures(UpdateFeaturesRequest $request): JsonResponse
    {
        $input = $request->all();
        if (isset($input['sub_title'])) {
            $input['sub_title'] = json_encode($input['sub_title']);
        }
        
        $features = Feature::find($input['id']);
        $features->update($input);
        
        if (!empty($input['image'])) {
            $features->clearMediaCollection(FrontCMS::IMAGE);
            $features->media()->delete();
            $features->addMedia($input['image'])->toMediaCollection(FrontCMS::IMAGE, config('app.media_disc'));
        }

        return $this->sendResponse($features, __('messages.success.feature_updated'));
    }

    /**
     * @return JsonResponse
     */
    public function termConditions()
    {
        $termConditions = Setting::whereIn('key', ['term_conditions', 'privacy_policy'])->pluck('value',
            'key')->toArray();

        return $this->sendResponse($termConditions, 'data');
    }

    /**
     * @param UpdateTermConditionsRequest $request
     * @return JsonResponse
     */
    public function UpdateTermConditions(UpdateTermConditionsRequest $request): JsonResponse
    {
        $input = $request->all();
        $termConditions = Arr::only($input, ['term_conditions', 'privacy_policy']);
        foreach ($termConditions as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $this->sendResponse($termConditions, __('messages.success.feature_updated'));
    }
}
