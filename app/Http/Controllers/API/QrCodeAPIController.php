<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\StoreQrCodeRequest;
use App\Http\Requests\UpdateQrCodeRequest;
use App\Http\Resources\QrCodeCollection;
use App\Http\Resources\QrCodeResource;
use App\Models\QrCode;
use App\Models\User;
use App\Repositories\QrCodeRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class QrCodeAPIController extends AppBaseController
{
    /** @var QrCodeRepository */
    private QrCodeRepository $qrCodeRepository;

    /**
     * @param QrCodeRepository $qrCodeRepository
     */
    public function __construct(QrCodeRepository $qrCodeRepository)
    {
        $this->qrCodeRepository = $qrCodeRepository;
        $this->middleware('checkQrCodeLimit')->only('store');
        $this->middleware('checkQrCodeTypeLimit')->only(['store', 'update']);
    }

    /**
     * @param Request $request
     *
     * @return QrCodeCollection|Response
     */
    public function index(Request $request): QrCodeCollection|Response
    {
        $perPage = getPageSize($request);
        $qrCodes = $this->qrCodeRepository->tenant()->paginate($perPage);
        QrCodeResource::usingWithCollection();

        return new QrCodeCollection($qrCodes);
    }

    /**
     * @param StoreQrCodeRequest $request
     *
     * @return QrCodeResource
     */
    public function store(StoreQrCodeRequest $request): QrCodeResource
    {
        $input = $request->all();
        $qrCode = $this->qrCodeRepository->storeQrCode($input);

        return new QrCodeResource($qrCode);
    }

    /**
     * @param $id
     *
     * @return QrCodeResource
     */
    public function show($id): QrCodeResource
    {
        $qrCode = $this->qrCodeRepository->find($id);

        return new QrCodeResource($qrCode);
    }

    /**
     * @param UpdateQrCodeRequest $request
     * @param QrCode $qrCode
     *
     * @return QrCodeResource
     */
    public function update($id, UpdateQrCodeRequest $request): QrCodeResource
    {
        $input = $request->all();
        $qrCode = $this->qrCodeRepository->updateQrCode($input, $id);

        return new QrCodeResource($qrCode);
    }

    /**
     * @param QrCode $qrCode
     *
     * @return JsonResponse
     */
    public function destroy(QrCode $qrCode): JsonResponse
    {
        $this->qrCodeRepository->delete($qrCode->id);

        return $this->sendSuccess(__('messages.success.qr_code_deleted'));
    }

    /**
     * @return QrCodeCollection
     */
    public function projectList(): QrCodeCollection
    {
        $projectList = $this->qrCodeRepository->projectList();
        QrCodeResource::usingWithCollection();

        return new QrCodeCollection($projectList);
    }

    public function qrCodeTypes(): JsonResponse
    {
        $qrCodeTypes = $this->qrCodeRepository->qrCodeTypes();

        return $this->sendResponse($qrCodeTypes, 'Qr Code types retrieved successfully.');
    }
}
