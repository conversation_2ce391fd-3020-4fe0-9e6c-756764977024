<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\CouponCode;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Repositories\SubscriptionRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Checkout\Session;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class StripeAPIController extends AppBaseController
{
    /** @var SubscriptionRepository */
    private SubscriptionRepository $subscriptionRepository;

    /**
     * @param SubscriptionRepository $subscriptionRepository
     */
    public function __construct(SubscriptionRepository $subscriptionRepository)
    {
        $this->subscriptionRepository = $subscriptionRepository;
    }

    public function onBoard(Request $request): array|JsonResponse
    {
        $input = $request->all();
        $data = $this->subscriptionRepository->getPlan($input);
        $subscription = $this->subscriptionRepository->createSubscription($input['plan_id']);
        if (!isset($data['current_plan'])) {
            return $data;
        }

        $result = $this->manageStripeData(
            $data['new_plan'],
            [
                'amountToPay' => $data['new_plan'][1]['payable_amount'], 'sub_id' => $subscription->id,
                'coupon_code' => $input['coupon_code'],
            ]
        );

        return $this->sendResponse($result, 'data');
    }

    public function manageStripeData($plan, $data)
    {
        $amountToPay = $data['amountToPay'];
        $subscriptionId = $data['sub_id'];
        if ($plan[0]->currency_id != null && in_array(getSubscriptionPlanCurrencyCode($plan[0]->currency_id), zeroDecimalCurrencies(), true)) {
            $planAmount = (float) $amountToPay;
        } else {
            $planAmount = (float) $amountToPay * 100;
        }

        setAdminStripeApiKey();
        try {
            $session = Session::create([
                'payment_method_types' => ['card'],
                'customer_email'       => Auth::user()->email,
                'line_items'           => [
                    [
                        'price_data' => [
                            'product_data' => [
                                'name'        => $plan[0]['name'],
                                'description' => 'Subscribing for the plan named '.$plan[0]['name'],
                            ],
                            'unit_amount'  => $planAmount,
                            'currency'     => getSubscriptionPlanCurrencyCode($plan[0]['currency_id']),
                        ],
                        'quantity'   => 1,
                    ],
                ],
                'client_reference_id'  => $subscriptionId,
                'metadata'             => [
                    'payment_type'  => request()->get('payment_type'),
                    'amount'        => $planAmount,
                    'plan_currency' => getSubscriptionPlanCurrencyCode($plan[0]['currency_id']),
                    'coupon_code'   => $data['coupon_code'],
                ],
                'mode'                 => 'payment',
                'success_url'          => url('api/stripe-payment-success').'?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url'           => url('api/stripe-payment-failed', $subscriptionId),
            ]);

            return [
                'sessionId'  => $session['id'],
                'stripe_key' => getStripeKey(),
            ];
        } catch (Exception $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    public function failed($id)
    {
        Subscription::findOrFail($id)->delete();

        return redirect(url("/#/app/manage-subscription"));
    }

    public function success(Request $request)
    {
        try {
            DB::beginTransaction();
            $stripeKey = getSettingValue('stripe_secret') ?? config('services.stripe.secret_key');
            // New Plan Subscribe
            $stripe = new \Stripe\StripeClient($stripeKey);
            $sessionData = $stripe->checkout->sessions->retrieve(
                $request->session_id,
                []
            );
            $userId = Subscription::whereId($sessionData->client_reference_id)->value('user_id');
            $user = \App\Models\User::whereId($userId)->first();
            // where, $sessionData->client_reference_id = the subscription id
            Subscription::findOrFail($sessionData->client_reference_id)->update(['status' => Subscription::ACTIVE]);
            // De-Active all other subscriptions

            Subscription::whereUserId($user->id)
                ->where('id', '!=', $sessionData->client_reference_id)
                ->update([
                    'status' => Subscription::DISABLE,
                ]);

            $paymentAmount = null;
            if ($sessionData->metadata->plan_currency != null && getSubscriptionPlanCurrencyCode($sessionData->metadata->plan_currency)) {
                $paymentAmount = $sessionData->amount_total;
            } else {
                $paymentAmount = $sessionData->amount_total / 100;
            }
            
            $couponCode = $sessionData->metadata->coupon_code;
            $transaction = Transaction::create([
                'tenant_id'     => $user->tenant_id,
                'user_id'       => $user->id,
                'payment_mode'  => Subscription::TYPE_STRIPE,
                'price_of_plan' => $paymentAmount,
                'coupon_code'   => $couponCode,
                'status'        => Subscription::ACTIVE,
                'date'          => Carbon::now()->toDateString(),
            ]);
            if (!empty($couponCode)) {
                $incrementUsedCount = CouponCode::whereCode($couponCode)->first();
                $incrementUsedCount->increment('used', 1);
            }
            $subscription = Subscription::findOrFail($sessionData->client_reference_id);
            $subscription->update(['transaction_id' => $transaction->id]);
            sendMailOnPayment($user->name, $user->email, $subscription->plan->name, $paymentAmount);
            DB::commit();

            return redirect(url("/#/app/manage-subscription"));
        } catch (\Exception $e) {
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
