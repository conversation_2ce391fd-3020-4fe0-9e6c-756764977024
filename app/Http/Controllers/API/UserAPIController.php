<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Http\Requests\ChangePasswordRequest;
use App\Http\Requests\CreateUserRequest;
use App\Http\Requests\UpdateUserProfileRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Resources\UserCollection;
use App\Http\Resources\UserResource;
use App\Models\Link;
use App\Models\QrCode;
use App\Models\User;
use App\Repositories\LinkRepository;
use App\Repositories\QrCodeRepository;
use App\Repositories\UserRepository;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

/**
 * Class UserAPIController
 */
class UserAPIController extends AppBaseController
{
    /** @var UserRepository */
    private UserRepository $userRepository;
    private QrCodeRepository $qrCodeRepository;
    private LinkRepository $linkRepository;

    /**
     * @param UserRepository $userRepository
     * @param QrCodeRepository $qrCodeRepository
     * @param LinkRepository $linkRepository
     */
    public function __construct(UserRepository $userRepository, QrCodeRepository $qrCodeRepository, LinkRepository $linkRepository) {
        $this->userRepository = $userRepository;
        $this->qrCodeRepository = $qrCodeRepository;
        $this->linkRepository = $linkRepository;
    }

    /**
     * @param Request $request
     *
     * @return UserCollection
     */
    public function index(Request $request): UserCollection
    {
        $perPage = getPageSize($request);
        $users = $this->userRepository->getUsers($perPage);
        UserResource::usingWithCollection();

        return new UserCollection($users);
    }

    /**
     * @param CreateUserRequest $request
     *
     * @return UserResource
     */
    public function store(CreateUserRequest $request): UserResource
    {
        $input = $request->all();
        $user = $this->userRepository->storeUser($input);

        return new UserResource($user);
    }

    public function show($id)
    {
        $details['user'] = User::with('subscription.plan')->whereId($id)->get();

        return $this->sendResponse($details, "User details retrieved successfully");
    }

    public function getQrCodes($id, Request $request): JsonResponse
    {
        $perPage = getPageSize($request);
        $tenantId = User::whereId($id)->first()->tenant_id;
        $qeCodes = $this->qrCodeRepository->with('project')->whereTenantId($tenantId)->paginate($perPage);

        return $this->sendResponse($qeCodes, "data");
    }

    public function getLinks($id, Request $request): JsonResponse
    {
        $perPage = getPageSize($request);
        $tenantId = User::whereId($id)->first()->tenant_id;
        $links = $this->linkRepository->whereTenantId($tenantId)->paginate($perPage);

        return $this->sendResponse($links, "data");
    }

    /**
     * @param $id
     * @param UpdateUserRequest $request
     *
     * @return UserResource|JsonResponse
     */
    public function update($id, UpdateUserRequest $request): UserResource|JsonResponse
    {
        $input = $request->all();
        $user = $this->userRepository->updateUser($input, $id);

        return new UserResource($user);
    }

    /**
     * @param User $user
     *
     * @return JsonResponse
     */
    public function destroy(User $user): JsonResponse
    {
        $subject = "Account Deleted at ".getSettingValue('title');
        $userData['data'] = "<h1>Hello <span>$user->name</span>,</h1><br>"."Your account has been deleted by admin.".'<br><br><br><p>Regards</p><p>'.getSettingValue('title').'</p>';
        $adminData['data'] = '<h1>Dear <span>'.getLoginAdminName().',</span></h1><br><strong>'.$user->name.'</strong>'."'s account has been deleted successfully.".'<br><br><br><p>Regards</p><p>'.getSettingValue('title').'</p>';

        if (getSettingValue('delete_user')) {
            sendMailToUser($user->email, $subject, $userData, 'emails.mail-sender');
            sendMailToAdmin($subject, $adminData);
        }

        $user->subscriptions()->delete();
        $user->transactions()->delete();
        $this->userRepository->delete($user->id);
        $this->userRepository->deleteUserRelatedRecords($user->tenant_id);

        return $this->sendSuccess(__('messages.success.user_deleted'));
    }

    public function editProfile(): JsonResponse
    {
        $user = Auth::user();

        return $this->sendResponse($user, 'data');
    }

    public function updateProfile(UpdateUserProfileRequest $request): JsonResponse
    {
        $input = $request->all();
        $updateUser = $this->userRepository->updateUserProfile($input);

        return $this->sendResponse($updateUser, 'data');
    }

    /**
     * @param ChangePasswordRequest $request
     *
     * @return JsonResponse
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $input = $request->all();
        try {
            $this->userRepository->changePassword($input);

            return $this->sendSuccess(__('messages.success.password_updated'));
        } catch (\Exception $e) {

            return $this->sendError($e->getMessage());
        }
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function emailVerified($id): JsonResponse
    {
        User::whereId($id)->update(['email_verified_at' => Carbon::now()]);

        return $this->sendSuccess(__('messages.success.email_verified'));
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function updateStatus($id): JsonResponse
    {
        $user = User::findOrFail($id);
        $user->update(['status' => !$user->status]);

        return $this->sendSuccess(__('messages.success.user_status_updated'));
    }

    public function deleteAccount(Request $request): JsonResponse
    {
        $user = Auth::user();
        $this->userRepository->deleteUserRelatedRecords($user->tenant_id);
        if (!Hash::check($request->password, $user->password)) {
            return $this->sendError('Current password is invalid.');
        }
        $subject = "Account Deleted at ".getSettingValue('title');
        $userData['data'] = "<h1>Hello </h1>".'<strong>'.$user->name.'</strong><br>'.", your account has been deleted successfully.".'<br><p>Regards</p><p>'.getSettingValue('title').'</p>';
        $adminData['data'] = '<h1>Dear </h1><br><strong>'.$user->name.'</strong>'." deleted their account from ".getSettingValue('title').'<br><p>Regards</p><p>'.getSettingValue('title').'</p>';
        if (getSettingValue('delete_user')) {
            sendMailToUser($user->email, $subject, $userData, 'emails.mail-sender');
            sendMailToAdmin($subject, $adminData);
        }
        $user->delete();

        return $this->sendSuccess(__('messages.success.account_deleted'));
    }

    /**
     * @param ChangePasswordRequest $request
     *
     * @return JsonResponse
     */
    public function changeLoginPassword(Request $request): JsonResponse
    {
        $input = $request->all();

        $this->userRepository->changeLoginPassword($input);

        return $this->sendSuccess(__('messages.success.password_updated'));
    }

    public function getAllUsers(): JsonResponse
    {
        $users = User::whereRelation('roles', 'name', '!=', 'admin')->orderBy('id', 'DESC')->get();

        return $this->sendResponse($users, "data");
    }

    public function updateLanguage(Request $request): JsonResponse
    {
        $language = $request->get('language');
        $user = Auth::user();
        $user->update([
            'language' => $language,
        ]);

        return $this->sendResponse($user->language, __('messages.success.language_updated'));
    }
}
