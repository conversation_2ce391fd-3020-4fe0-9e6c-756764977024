<?php

namespace App\Http;

use App\Http\Middleware\AllowUserRegistration;
use App\Http\Middleware\Analytics;
use App\Http\Middleware\CheckLinkLimit;
use App\Http\Middleware\CheckProjectLimit;
use App\Http\Middleware\CheckQrCodeLimit;
use App\Http\Middleware\CheckQrCodeTypeLimit;
use App\Http\Middleware\CheckSubscription;
use App\Http\Middleware\CheckUserStatus;
use App\Http\Middleware\MultiTenantMiddleware;
use App\Http\Middleware\TemplateLanguageMiddleware;
use App\Http\Middleware\XSS;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:60,1',
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \App\Http\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
        'multi_tenant' => MultiTenantMiddleware::class,
        'check_subscription' => CheckSubscription::class,
        'checkUserStatus' => CheckUserStatus::class,
        'analytics' => Analytics::class,
        'checkLinkLimit' => CheckLinkLimit::class,
        'checkQrCodeLimit' => CheckQrCodeLimit::class,
        'checkProjectLimit' => CheckProjectLimit::class,
        'checkQrCodeTypeLimit' => CheckQrCodeTypeLimit::class,
        'checkAllowUserRegistration' => AllowUserRegistration::class,
        'xss' => XSS::class,
        'templateLanguage' => TemplateLanguageMiddleware::class,
    ];
}
