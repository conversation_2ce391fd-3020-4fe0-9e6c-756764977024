<?php

namespace App\Http\Requests;

use App\Models\Plan;
use App\Models\PlanFeatures;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;

class UpdatePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $planRules = Plan::$rules;
        $planRules['name'] = 'required';

        return Arr::collapse([$planRules, PlanFeatures::$rules]);
    }

    /**
     *
     * @return array
     */
    public function messages(): array
    {
        return PlanFeatures::$messages;
    }
}
