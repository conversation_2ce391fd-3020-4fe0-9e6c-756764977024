<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateGoogleLoginSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'google_client_id' => 'required_if:enable_google_login,1',
        ];
    }

    /**
     * @return string[]
     */
    public function messages()
    {
        return [
            'google_client_id'     => 'Google client id is required.',
        ];
    }
}
