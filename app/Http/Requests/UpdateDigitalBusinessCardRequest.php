<?php

namespace App\Http\Requests;

use App\Models\DigitalBusinessCard;
use Illuminate\Foundation\Http\FormRequest;

class UpdateDigitalBusinessCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = DigitalBusinessCard::$rules;
        $rules['url_alias'] = 'required|unique:business_cards,url_alias,'.$this->route('digital_business_card')->id;
            
        return $rules;
    }
}
