<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRazorpaySettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'razorpay_key'    => 'required_if:enable_razorpay_payments,1',
            'razorpay_secret' => 'required_if:enable_razorpay_payments,1',
        ];
    }

    /**
     * @return string[]
     */
    public function messages()
    {
        return [
            'razorpay_key'    => 'Razorpay Key is required.',
            'razorpay_secret' => 'Razorpay Secret Key is required.',
        ];
    }
}
