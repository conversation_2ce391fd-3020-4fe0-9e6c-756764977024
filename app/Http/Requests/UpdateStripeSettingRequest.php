<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateStripeSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'stripe_Key'    => 'required_if:enable_stripe_payments,1',
            'stripe_secret' => 'required_if:enable_stripe_payments,1',
        ];
    }

    /**
     * @return string[]
     */
    public function messages()
    {
        return [
            'stripe_Key'    => 'Stripe Key is required.',
            'stripe_secret' => 'Stripe Secret Key is required.',
        ];
    }
}
