<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMailSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'from_name'      => 'required',
            'from_email'     => 'required|email:filter',
            'host'           => 'required',
            'encryption'     => 'required',
            'port'           => 'required',
            'email_username' => 'required',
            'email_password' => 'required',
        ];
    }
}
