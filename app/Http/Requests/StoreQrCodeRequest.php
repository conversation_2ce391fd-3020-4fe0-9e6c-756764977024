<?php

namespace App\Http\Requests;

use App\Models\QrCode;
use App\Models\Role;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreQrCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = QrCode::$rules;
        if (Auth::user()->hasRole(Role::ADMIN)) {
            $rules['tenant_id'] = 'required';
        }

        return $rules;
    }

    /**
     * @return array|string[]
     */
    public function messages()
    {
        $messages = [];
        if (Auth::user()->hasRole(Role::ADMIN)) {
            $messages['tenant_id.required'] = 'User field is required';
        }
        
        $messages['project_id.exists'] = 'The selected Group ID is invalid.';

        return $messages;
    }
}
