<?php

namespace App\Http\Requests;

use App\Models\DigitalBusinessCard;
use Illuminate\Foundation\Http\FormRequest;

class CreateDigitalBusinessCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return DigitalBusinessCard::$rules;
    }

    /**
     * @return string[]
     */
    public function messages()
    {
        return [
            'url_alias.unique'    => 'The business card URL alias has been already taken.'
        ];
    }
}
