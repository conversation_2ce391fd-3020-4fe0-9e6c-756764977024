<?php

namespace App\Http\Requests;

use App\Models\Plan;
use App\Models\PlanFeatures;
use App\Models\PlanFefatures;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;

class StorePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     *
     * @return array
     */
    public function rules(): array
    {
        return Arr::collapse([Plan::$rules, PlanFeatures::$rules]);
    }

    /**
     *
     * @return array
     */
    public function messages(): array
    {
        return PlanFeatures::$messages;
    }
}
