<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFacebookLoginSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'facebook_app_id'     => 'required_if:enable_facebook_login,1',
        ];
    }

    /**
     * @return string[]
     */
    public function messages()
    {
        return [
            'facebook_app_id'     => 'Facebook app id is required.',
        ];
    }
}
