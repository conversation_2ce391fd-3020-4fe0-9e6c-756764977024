<?php

namespace App\Http\Requests;

use App\Models\QrCode;
use Illuminate\Foundation\Http\FormRequest;

class UpdateQrCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = QrCode::$rules;
        $rules['name'] = 'required|unique:qr_codes,name,'.$this->route('id');

        return $rules;
    }
}
