<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        $rules = User::$rules;
        $rules['email'] = 'required|email|unique:users,email,'.$this->route('id');
        $rules['role_id'] = 'integer|exists:roles,id';
        $rules['password'] = 'nullable';
        $rules['confirm_password'] = 'required_with:password|same:password';

        return $rules;
    }
}
