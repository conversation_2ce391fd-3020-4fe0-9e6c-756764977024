<?php

namespace App\Http\Requests;

use App\Models\Link;
use Illuminate\Foundation\Http\FormRequest;

class UpdateLinkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = Link::$rules;
        $rules['name'] = 'required|unique:links,name,'.$this->route('id');
        $rules['url_alias'] = 'required|unique:links,url_alias,'.$this->route('id');

        return $rules;
    }

    /**
     * @return array|string[]
     */
    public function messages()
    {
        return Link::$messages;
    }
}
