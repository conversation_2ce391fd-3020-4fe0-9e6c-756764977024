<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePaypalSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'paypal_payment_mode' => 'required_if:enable_paypal_payments,1',
            'paypal_client_id'    => 'required_if:enable_paypal_payments,1',
            'paypal_secret'       => 'required_if:enable_paypal_payments,1',
        ];
    }

    /**
     * @return string[]
     */
    public function messages()
    {
        return [
            'paypal_client_id.required_if' => 'Paypal Client Id is required.',
            'paypal_secret.required_if'    => 'Paypal Secret Key is required.',
        ];
    }
}
