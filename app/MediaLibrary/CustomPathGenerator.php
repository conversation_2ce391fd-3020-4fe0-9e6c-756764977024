<?php

namespace App\MediaLibrary;

use App\Models\DigitalBusinessCard;
use App\Models\QrCode;
use App\Models\QrCodeType;
use App\Models\SocialLink;
use App\Models\User;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\Support\PathGenerator\PathGenerator;

/**
 * Class CustomPathGenerator
 * @package App\MediaLibrary
 */
class CustomPathGenerator implements PathGenerator
{
    /**
     * @param Media $media
     *
     * @return string
     */
    public function getPath(Media $media): string
    {
        $path = '{PARENT_DIR}'.DIRECTORY_SEPARATOR.$media->id.DIRECTORY_SEPARATOR;
        switch ($media->collection_name) {
            case User::PATH;
                return str_replace('{PARENT_DIR}', User::PATH, $path);
            case QrCode::PATH;
                return str_replace('{PARENT_DIR}', QrCode::PATH, $path);
            case QrCodeType::PATH;
                return str_replace('{PARENT_DIR}', QrCodeType::PATH, $path);
            case DigitalBusinessCard::PROFILE_PATH:
                return str_replace('{PARENT_DIR}', DigitalBusinessCard::PROFILE_PATH, $path);
            case DigitalBusinessCard::COVER_PATH:
                return str_replace('{PARENT_DIR}', DigitalBusinessCard::COVER_PATH, $path);
            case SocialLink::PATH:
                    return str_replace('{PARENT_DIR}', SocialLink::PATH, $path);
            case 'default' :
                return '';
        }
    }

    /**
     * @param Media $media
     *
     * @return string
     */
    public function getPathForConversions(Media $media): string
    {
        return $this->getPath($media).'thumbnails/';
    }

    /**
     * @param Media $media
     *
     * @return string
     */
    public function getPathForResponsiveImages(Media $media): string
    {
        return $this->getPath($media).'rs-images/';
    }
}
