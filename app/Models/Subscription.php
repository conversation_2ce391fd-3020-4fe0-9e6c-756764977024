<?php

namespace App\Models;

use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\Subscription
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $plan_id
 * @property float|null $price_of_plan
 * @property int $plan_type
 * @property Carbon $start_date
 * @property Carbon $end_date
 * @property Carbon|null $trial_ends_at
 * @property int $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Plan|null $plan
 * @property-read User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription query()
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription wherePlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription wherePlanType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription wherePriceOfPlan($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereTrialEndsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereUserId($value)
 * @mixin \Eloquent
 * @property int|null $transaction_id
 * @property-read Transaction|null $transaction
 * @method static \Illuminate\Database\Eloquent\Builder|Subscription whereTransactionId($value)
 */
class Subscription extends Model
{
    use HasFactory, HasJsonResourcefulData;

    public const ACTIVE = 1;

    public const DISABLE = 0;

    public const TYPE_STRIPE = 1;

    public const TYPE_PAYPAL = 2;

    public const TYPE_RAZORPAY = 3;

    public const TYPE_MANUAL = 4;

    const PAYMENT_TYPES = [
        self::TYPE_STRIPE   => 'Stripe',
        self::TYPE_PAYPAL   => 'PayPal',
        self::TYPE_RAZORPAY => 'Razorpay',
        self::TYPE_MANUAL   => 'Manual',
    ];

    const STATUS_ARR = [
        self::ACTIVE  => 'Active',
        self::DISABLE => 'Disable',
    ];

    public const MONTHLY = 1;
    public const WEEKLY = 2;
    public const ANNUALLY = 3;

    const TYPE_ARR = [
        self::MONTHLY  => 'Monthly',
        self::WEEKLY   => 'Weekly',
        self::ANNUALLY => 'Annually',
    ];

    protected $fillable = [
        'user_id', 'plan_id', 'price_of_plan', 'plan_type', 'start_date', 'end_date',
        'trial_ends_at', 'status', 'transaction_id',
    ];

    /**
     * @var string[]
     */
    protected $casts = [
        'user_id'        => 'integer',
        'plan_id'        => 'integer',
        'start_date'     => 'datetime',
        'end_date'       => 'datetime',
        'trial_ends_at'  => 'datetime',
        'status'         => 'boolean',
        'price_of_plan'  => 'double',
        'plan_type'      => 'integer',
        'transaction_id' => 'integer',
    ];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'user_id'       => $this->user_id,
            'payment_mode'  => $this->transaction->payment_mode,
            'price_of_plan' => $this->price_of_plan,
            'plan_id'       => $this->plan_id,
            'plan_type'     => $this->plan_type,
            'start_date'    => $this->start_date,
            'end_date'      => $this->end_date,
        ];

        return $fields;
    }

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }

    protected $appends = ['payment_request', 'payable_amount'];

    /**
     * @return BelongsTo
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'plan_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    public function isExpired(): bool
    {
        $now = Carbon::now();

        // this means the subscription is ended.
        return (!empty($this->trial_ends_at) && $this->trial_ends_at < $now) || $this->end_date < $now;
    }

    /**
     * @param Builder $query
     */
    public function scopeAuthenticated(Builder $query): void
    {
        $query->where('user_id', getLoginUserId());
    }

    /**
     * @param Builder $query
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('status', self::ACTIVE);
    }

    public function getPaymentRequestAttribute()
    {
        return $this->transaction->is_manual_payment ?? null;
    }

    public function getPayableAmountAttribute()
    {
        return $this->transaction->price_of_plan ?? null;
    }
}
