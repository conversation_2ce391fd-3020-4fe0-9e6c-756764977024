<?php

namespace App\Models;

use Illuminate\Support\Carbon;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\PlanFeatures
 *
 * @property int $id
 * @property int $plan_id
 * @property int $qr_code_limit
 * @property int $links_limit
 * @property int $projects_limit
 * @property string $qr_code_types
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Plan|null $plans
 * @property int $allow_create_business_card
 * @property float|null $business_card_limit
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures query()
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereLinksLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures wherePlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereProjectsLimit($value)
 * @method static Builder|PlanFeatures whereQrCodeLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereQrCodeTypes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereAllowCreateBusinessCard($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlanFeatures whereBusinessCardLimit($value)
 * @mixin Eloquent
 */
class PlanFeatures extends Model
{
    use HasFactory;

    protected $table = "plan_features";

    protected $fillable = [
        'plan_id',
        'qr_code_limit',
        'links_limit',
        'projects_limit',
        'qr_code_types',
        'allow_create_business_card',
        'business_card_limit',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'plan_id'                     => 'integer',
        'qr_code_limit'               => 'integer',
        'links_limit'                 => 'integer',
        'projects_limit'              => 'integer',
        'qr_code_types'               => 'string',
        'allow_create_business_card'  => 'boolean',
        'business_card_limit'         => 'double',
    ];

    public const TEXT = 1;
    public const PHONE = 2;
    public const EMAIL = 3;
    public const FACE_TIME = 4;
    public const WIFI = 5;
    public const CRYPTO = 6;
    public const PAYPAL = 7;
    public const URL = 8;
    public const SMS = 9;
    public const WHATSAPP = 10;
    public const LOCATION = 11;
    public const EVENT = 12;
    public const VCARD = 13;

    const QR_CODE_TYPE = [
        self::TEXT      => 'Text',
        self::PHONE     => 'Phone',
        self::EMAIL     => 'Email',
        self::FACE_TIME => 'Facetime',
        self::WIFI      => 'WiFi',
        self::CRYPTO    => 'Crypto',
        self::PAYPAL    => 'PayPal',
        self::URL       => 'URL',
        self::SMS       => 'SMS',
        self::WHATSAPP  => 'Whatsapp',
        self::LOCATION  => 'Location',
        self::EVENT     => 'Event',
        self::VCARD     => 'Vcard',
    ];

    public static array $rules = [
        'qr_code_limit'         => 'required',
        'links_limit'           => 'required',
        'projects_limit'        => 'required',
        'qr_code_types'         => 'required',
        'business_card_limit'    => 'required_if:allow_create_business_card,1',
    ];

    public static array  $messages = [
        'qr_code_types' => 'The QR Code types field is required.'
    ];

    function plans()
    {
        return $this->belongsTo(Plan::class);
    }
}
