<?php

namespace App\Models;

use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\Transaction
 *
 * @property int $id
 * @property string $tenant_id
 * @property int $user_id
 * @property int $payment_mode
 * @property float $price_of_plan
 * @property int $status
 * @property int $is_manual_payment
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction query()
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereIsManualPayment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction wherePaymentMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction wherePriceOfPlan($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereUserId($value)
 * @mixin Eloquent
 * @property string|null $coupon_code
 * @property-read \App\Models\Subscription|null $subscription
 * @method static \Illuminate\Database\Eloquent\Builder|Transaction whereCouponCode($value)
 */
class Transaction extends Model
{
    use HasFactory, HasJsonResourcefulData;

    public $fillable = ['tenant_id', 'user_id', 'payment_mode', 'price_of_plan', 'coupon_code', 'status', 'date'];

    public const PENDING = 0;
    public const APPROVED = 1;
    public const DENIED = 2;

    /**
     * @var string[]
     */
    protected $casts = [
        'tenant_id'         => 'string',
        'user_id'           => 'integer',
        'payment_mode'      => 'integer',
        'price_of_plan'     => 'double',
        'status'            => 'boolean',
        'is_manual_payment' => 'integer',
    ];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'id'                => $this->id,
            'plan_name'         => !empty($this->subscription->plan) ? $this->subscription->plan->name : null,
            'payment_date'      => $this->created_at,
            'is_manual_payment' => $this->is_manual_payment,
            'payment_type'      => $this->payment_mode,
            'amount'            => $this->price_of_plan,
            'plan_amount'       => !empty($this->subscription->price_of_plan) ? $this->subscription->price_of_plan : null,
            'status'            => $this->status,
            'coupon_code'       => $this->coupon_code,
            'user_id'           => !empty($this->subscription->user->id) ? $this->subscription->user->id : null,
            'user_name'         => !empty($this->subscription->user->name) ? $this->subscription->user->name : null,
            'user_email'        => !empty($this->subscription->user->email) ? $this->subscription->user->email : null,
            'user_image'        => !empty($this->subscription->user->image_url) ? $this->subscription->user->image_url : null,
            'currency_id'       => !empty($this->subscription->plan->currency->id) ? $this->subscription->plan->currency->id : null,
            'currency_symbol'   => !empty($this->subscription->plan->currency->symbol) ? $this->subscription->plan->currency->symbol : null,
        ];

        return $fields;
    }

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }

    /**
     * @return HasOne
     */
    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class, 'transaction_id', 'id');
    }

    /**
     * @param Builder $query
     */
    public function scopeManualType(Builder $query): void
    {
        $query->wherePaymentMode(Subscription::TYPE_MANUAL);
    }
}
