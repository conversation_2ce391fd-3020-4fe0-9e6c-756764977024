<?php

namespace App\Models;

use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\Link
 *
 * @property int $id
 * @property string $tenant_id
 * @property string $destination_url
 * @property string $url_alias
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read User|null $user
 * @method static Builder|Link newModelQuery()
 * @method static Builder|Link newQuery()
 * @method static Builder|Link query()
 * @method static Builder|Link whereCreatedAt($value)
 * @method static Builder|Link whereDestinationUrl($value)
 * @method static Builder|Link whereId($value)
 * @method static Builder|Link whereTenantId($value)
 * @method static Builder|Link whereUpdatedAt($value)
 * @method static Builder|Link whereUrlAlias($value)
 * @mixin Eloquent
 */
class Link extends Model implements HasMedia
{
    use HasFactory, HasJsonResourcefulData, InteractsWithMedia;

    public const PATH = 'qrcode_of_link';
    protected $fillable = [
        'destination_url',
        'url_alias',
        'tenant_id',
        'name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'name'            => 'string',
        'destination_url' => 'string',
        'url_alias'       => 'string',
        'tenant_id'       => 'string',
    ];

    public static array $rules = [
        'name'            => 'required|unique:links,name',
        'destination_url' => 'required|url',
        'url_alias'       => 'required|unique:links,url_alias',
    ];

    public static array $messages = [
        'url_alias.unique'    => 'The URL alias has been already taken.',
        'destination_url.url' => 'The destination URL must be a valid URL.',
    ];

    protected $appends = ['image_url'];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'name'            => $this->name,
            'destination_url' => $this->destination_url,
            'url_alias'       => $this->url_alias,
            'user_id'         => $this->user->id,
            'user_name'       => $this->user->name,
            'user_email'      => $this->user->email,
            'user_image'      => $this->user->image_url,
            'created_at'      => $this->created_at,
            'updated_at'      => $this->updated_at,
            'qrcode_image'    => $this->image_url,
        ];

        return $fields;
    }

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'tenant_id', 'tenant_id');
    }

    /**
     * @param Builder $query
     */
    public function scopeTenant(Builder $query): void
    {
        $query->where('tenant_id', Auth::user()->tenant_id);
    }

    /**
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::PATH)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return '';
    }
    
    public function analytics(): MorphMany
    {
        return $this->morphMany(Analytic::class, 'model');
    }
}
