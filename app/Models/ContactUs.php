<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContactUs extends BaseModel implements JsonResourceful
{
    use HasFactory, HasJsonResourcefulData;

    public $table = 'contact_us';
    protected $fillable = [
        'name',
        'email',
        'subject',
        'message',
        'view',
    ];

    function prepareLinks()
    {
        return [];
    }

    function prepareAttributes()
    {
        return [];
    }
}
