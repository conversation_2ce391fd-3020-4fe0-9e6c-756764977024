<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\Analytic
 *
 * @property int $id
 * @property string|null $session
 * @property string|null $uri
 * @property string|null $source
 * @property string|null $country
 * @property string|null $browser
 * @property string|null $device
 * @property string|null $operating_system
 * @property string|null $ip
 * @property string|null $language
 * @property string|null $meta
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic query()
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereBrowser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereDevice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereMeta($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereOperatingSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereSession($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Analytic whereUri($value)
 * @mixin Eloquent
 */
class Analytic extends Model
{
    use HasFactory;

    protected $table = 'analytics';

    protected $fillable = [
        'session',
        'uri',
        'source',
        'country',
        'browser',
        'device',
        'operating_system',
        'ip',
        'language',
        'meta',
        'model_id',
        'model_type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'session'          => 'string',
        'uri'              => 'string',
        'source'           => 'string',
        'country'          => 'string',
        'browser'          => 'string',
        'device'           => 'string',
        'operating_system' => 'string',
        'ip'               => 'string',
        'language'         => 'string',
        'meta'             => 'string',
    ];
}
