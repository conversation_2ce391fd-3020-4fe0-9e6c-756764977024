<?php

namespace App\Models;

use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Plan
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property int $order
 * @property int $trial_days
 * @property int $type
 * @property int $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\PlanFeatures|null $planFeatures
 * @method static \Illuminate\Database\Eloquent\Builder|Plan newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Plan newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Plan query()
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereTrialDays($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property float $price
 * @property int $currency_id
 * @property bool $is_default
 * @property-read Currency $currency
 * @property-read mixed $is_active
 * @property-read Collection|Subscription[] $subscription
 * @property-read int|null $subscription_count
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Plan wherePrice($value)
 */
class Plan extends Model
{
    use HasFactory, HasJsonResourcefulData;

    protected $fillable = [
        'name',
        'price',
        'currency_id',
        'order',
        'trial_days',
        'type',
        'is_default',
        'status',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'name'        => 'string',
        'price'       => 'double',
        'order'       => 'integer',
        'currency_id' => 'integer',
        'trial_days'  => 'integer',
        'type'        => 'integer',
        'is_default'  => 'boolean',
    ];

    public const DISABLE = 0;
    public const ACTIVE = 1;
    public const TRAIL_DAYS = 7;

    public const MONTHLY = 1;
    public const ANNUALLY = 2;

    const TYPE_ARR = [
        self::MONTHLY  => 'Monthly',
        self::ANNUALLY => 'Annually',
    ];

    public static array $rules = [
        'name'        => 'required',
        'price'       => 'required',
        'currency_id' => 'required|exists:currencies,id',
        'order'       => 'required',
        'type'        => 'required',
    ];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'name'                        => $this->name,
            'price'                       => $this->price,
            'description'                 => $this->description,
            'currency_id'                 => $this->currency_id,
            'currency_name'               => $this->currency->name,
            'currency_symbol'             => $this->currency->symbol,
            'order'                       => $this->order,
            'trial_days'                  => $this->trial_days,
            'type'                        => $this->type,
            'is_default'                  => $this->is_default,
            'status'                      => $this->status,
            'plan_id'                     => $this->planFeatures->plan_id ?? null,
            'qr_code_limit'               => $this->planFeatures->qr_code_limit ?? null,
            'links_limit'                 => $this->planFeatures->links_limit ?? null,
            'projects_limit'              => $this->planFeatures->projects_limit ?? null,
            'allow_create_business_card'  => $this->planFeatures->allow_create_business_card == 1 ? 1 : 0,
            'business_card_limit'         => $this->planFeatures->business_card_limit ?? null,
            'qr_code_types'               => $this->planFeatures->qr_code_types ?? null,
            'total_user'                  => $this->subscription->count(),
            'is_active'                   => $this->getIsActiveAttribute(),
            'created_at'                  => $this->created_at,
            'updated_at'                  => $this->updated_at,
        ];

        return $fields;
    }

    protected $appends = ['is_active'];

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }

    function planFeatures()
    {
        return $this->hasOne(PlanFeatures::class, 'plan_id', 'id');
    }

    public function subscription()
    {
        return $this->hasMany(Subscription::class)->where('status', Subscription::ACTIVE);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    public function getIsActiveAttribute()
    {
        $subscription = Subscription::whereUserId(getLoginUserId())->whereStatus(Subscription::ACTIVE)->first();
        if (isset($subscription->user_id)) {
            if ($subscription->user_id == getLoginUserId()) {
                return $subscription->plan_id;
            }
        }

        return null;
    }
}
