<?php

namespace App\Models;

use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\CouponCode
 *
 * @property int $id
 * @property string $name
 * @property int $type
 * @property string $code
 * @property float|null $discount
 * @property int|null $quantity
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode query()
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereDiscount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CouponCode whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class CouponCode extends Model
{
    use HasFactory, HasJsonResourcefulData;

    public const DISCOUNT = 1;
    public const FIXED = 2;

    const TYPE_ARR = [
        self::DISCOUNT => 'Discount',
        self::FIXED    => 'Fixed',
    ];

    protected $fillable = [
        'name',
        'type',
        'code',
        'discount',
        'how_many_can_use',
        'used',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'name'             => 'string',
        'type'             => 'integer',
        'code'             => 'string',
        'discount'         => 'double',
        'how_many_can_use' => 'integer',
    ];

    public static array $rules = [
        'name'             => 'required|unique:coupon_codes,name',
        'code'             => 'required|unique:coupon_codes,code',
        'type'             => 'required',
        'discount'         => 'required',
        'how_many_can_use' => 'required',
    ];

    public static array $messages = [
        'how_many_can_use.required' => 'Enter how many people can use this coupon',
    ];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        return [
            'name'             => $this->name,
            'type'             => $this->type,
            'code'             => $this->code,
            'discount'         => $this->discount,
            'how_many_can_use' => $this->how_many_can_use,
            'used'             => $this->used,
            'created_at'       => $this->created_at,
            'updated_at'       => $this->updated_at,
        ];
    }

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }
}
