<?php

namespace App\Models;

use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Page extends Model
{
    use HasFactory, HasJsonResourcefulData;

    protected $fillable = [
        'title',
        'meta_title',
        'meta_description',
        'description',
        'visibility',
        'slug',
    ];

    public static array $rules = [
        'title'            => 'required|unique:pages',
        'meta_title'       => 'required',
        'meta_description' => 'required',
        'description'      => 'required',
    ];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        return [
            'title'            => $this->title,
            'meta_title'       => $this->meta_title,
            'meta_description' => $this->meta_description,
            'description'      => $this->description,
            'slug'             => $this->slug,
            'visibility'       => $this->visibility,
            'created_at'       => $this->created_at,
            'updated_at'       => $this->updated_at,
        ];
    }

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }
}
