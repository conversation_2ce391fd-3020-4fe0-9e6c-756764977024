<?php

namespace App\Models;

use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\QrCode
 *
 * @property int $id
 * @property string $name
 * @property int|null $project_id
 * @property string $tenant_id
 * @property int $type
 * @property int $style
 * @property int $foreground_type
 * @property int|null $foreground_gradient_style
 * @property string|null $foreground_color
 * @property string|null $foreground_color1
 * @property string|null $foreground_color2
 * @property string $background_color
 * @property string $background_transparency
 * @property int $custom_eye_color
 * @property string|null $eye_inner_color
 * @property string|null $eye_outer_color
 * @property int $size
 * @property int $margin_size
 * @property int $error_correction
 * @property array $extra_data
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Project|null $project
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode query()
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereBackgroundColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereBackgroundTransparency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereCustomEyeColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereErrorCorrection($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereExtraData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereEyeInnerColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereEyeOuterColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereForegroundColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereForegroundColor1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereForegroundColor2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereForegroundGradientStyle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereForegroundType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereMarginSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereProjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereStyle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QrCode whereUpdatedAt($value)
 * @mixin Eloquent
 */
class QrCode extends Model implements HasMedia
{
    use HasFactory, HasJsonResourcefulData, InteractsWithMedia;

    public const PATH = 'qr_code_images';
    protected $fillable = [
        'name',
        'project_id',
        'type',
        'style',
        'foreground_type',
        'foreground_gradient_style',
        'foreground_color',
        'foreground_color1',
        'foreground_color2',
        'background_color',
        'background_transparency',
        'custom_eye_color',
        'eye_inner_color',
        'eye_outer_color',
        'size',
        'margin_size',
        'error_correction',
        'extra_data',
        'tenant_id',
    ];

    public const NO = 0;
    public const YES = 1;

    public const TEXT = 1;
    public const URL = 2;
    public const PHONE = 3;
    public const SMS = 4;
    public const EMAIL = 5;
    public const WHATSAPP = 6;
    public const FACE_TIME = 7;
    public const LOCATION = 8;
    public const  WIFI = 9;
    public const  EVENT = 10;
    public const CRYPTO = 11;
    public const VCARD = 12;
    public const  PAYPAL = 13;

    const QR_CODE_TYPE = [
        self::TEXT      => 'Text',
        self::PHONE     => 'Phone',
        self::EMAIL     => 'Email',
        self::FACE_TIME => 'Facetime',
        self::WIFI      => 'WiFi',
        self::CRYPTO    => 'Crypto',
        self::PAYPAL    => 'PayPal',
        self::URL       => 'URL',
        self::SMS       => 'SMS',
        self::WHATSAPP  => 'Whatsapp',
        self::LOCATION  => 'Location',
        self::EVENT     => 'Event',
        self::VCARD     => 'Vcard',
    ];

    public const SQUARE = 1;
    public const ROUND = 2;
    public const DOT = 3;

    const QR_CODE_STYLE = [
        self::SQUARE => 'Square',
        self::ROUND  => 'Round',
        self::DOT    => 'Dot',
    ];

    public const COLOR = 1;
    public const GRADIENT = 2;

    const FOREGROUND_TYPE = [
        self::COLOR    => 'Color',
        self::GRADIENT => 'Gradient',
    ];

    public const HORIZONTAL = 1;
    public const RADIAL = 2;

    const FOREGROUND_GRADIENT_STYLE = [
        self::HORIZONTAL => 'Horizontal',
        self::RADIAL     => 'Radial',
    ];

    public static array $rules = [
        'name'       => 'required|unique:qr_codes',
        'project_id' => 'nullable|exists:projects,id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'name'                      => "string",
        'project_id'                => "integer",
        'type'                      => "integer",
        'style'                     => "integer",
        'foreground_type'           => "integer",
        'foreground_gradient_style' => "integer",
        'foreground_color1'         => "string",
        'foreground_color2'         => "string",
        'background_color'          => "string",
        'background_transparency'   => "string",
        'custom_eye_color'          => "integer",
        'eye_inner_color'           => "string",
        'eye_outer_color'           => "string",
        'size'                      => "integer",
        'margin_size'               => "integer",
        'error_correction'          => "integer",
        'extra_data'                => "array",
        'tenant_id'                 => "string",
    ];

    /**
     * @return array
     */
    public function prepareAttributes(): array
    {
        return [
            'id'                        => $this->id,
            'name'                      => $this->name,
            'project_id'                => $this->project_id,
            'project_name'              => $this->project->name ?? null,
            'type'                      => $this->type,
            'type_name'                 => self::QR_CODE_TYPE[$this->type],
            'style'                     => $this->style,
            'foreground_type'           => $this->foreground_type,
            'foreground_gradient_style' => $this->foreground_gradient_style,
            'foreground_color'          => $this->foreground_color,
            'foreground_color1'         => $this->foreground_color1,
            'foreground_color2'         => $this->foreground_color2,
            'background_color'          => $this->background_color,
            'background_transparency'   => $this->background_transparency,
            'custom_eye_color'          => $this->custom_eye_color,
            'eye_inner_color'           => $this->eye_inner_color,
            'eye_outer_color'           => $this->eye_outer_color,
            'size'                      => $this->size,
            'margin_size'               => $this->margin_size,
            'error_correction'          => $this->error_correction,
            'user_id'                   => $this->user->id,
            'user_name'                 => $this->user->name,
            'user_email'                => $this->user->email,
            'user_image'                => $this->user->image_url,
            'image'                     => $this->image_url,
            'extra_data'                => $this->extra_data,
            'created_at'                => $this->created_at,
            'updated_at'                => $this->updated_at,
        ];
    }

    protected $appends = ['type_name', 'image_url'];

    /**
     * @return array
     */
    public function prepareLinks(): array
    {
        return [];
    }

    /**
     * @return BelongsTo
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'tenant_id', 'tenant_id');
    }

    public function getTypeNameAttribute()
    {
        return self::QR_CODE_TYPE[$this->type];
    }

    /**
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::PATH)->first();

        return $media->getFullUrl();
    }

    /**
     * @param Builder $query
     */
    public function scopeTenant(Builder $query): void
    {
        $query->where('tenant_id', Auth::user()->tenant_id);
    }
}
