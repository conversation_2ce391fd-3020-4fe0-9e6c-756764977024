<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Feature extends Model implements HasMedia, JsonResourceful
{
    use HasFactory, InteractsWithMedia, HasJsonResourcefulData;

    protected $fillable = ['title', 'sub_title', 'description'];
    protected $appends = ['image_url'];

    public static array $rules = [
        'title'       => 'required',
//        'sub_title'   => 'required',
        'description' => 'required',
    ];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'title'       => $this->title,
            'sub_titles'  => json_decode($this->sub_title),
            'description' => $this->description,
            'image'       => $this->image_url,
        ];

        return $fields;
    }

    /**
     * @return array
     */
    function prepareLinks()
    {
        return [];
    }

    /**
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(FrontCMS::IMAGE)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return asset('default-images/feature-img.png');

        return defaultFrontCmsImage();
    }
}
