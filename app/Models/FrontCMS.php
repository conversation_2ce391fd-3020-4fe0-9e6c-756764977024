<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class FrontCMS extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $table = 'front_cms';
    protected $fillable = ['title', 'description'];
    public const IMAGE = 'front_cms_images';

    public static array $rules = [
        'title'       => 'required',
        'description' => 'required',
    ];

    protected $appends = ['image_url'];
    /**
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::IMAGE)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return defaultFrontCmsImage();
    }
}
