<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\SocialLink
 *
 * @property int $id
 * @property string $link
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read mixed $icon
 * @property-read MediaCollection|Media[] $media
 * @property-read int|null $media_count
 * @method static Builder|SocialLink newModelQuery()
 * @method static Builder|SocialLink newQuery()
 * @method static Builder|SocialLink query()
 * @method static Builder|SocialLink whereCreatedAt($value)
 * @method static Builder|SocialLink whereId($value)
 * @method static Builder|SocialLink whereLink($value)
 * @method static Builder|SocialLink whereUpdatedAt($value)
 * @mixin Eloquent
 * @property int|null $default_link
 * @method static Builder|SocialLink whereDefaultLink($value)
 */
class SocialLink extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;
    const PATH = 'icon';

    protected $table = 'social_links';

    protected $fillable = [
        'link',
        'default_link',
    ];

    protected $appends = ['icon'];

    protected $casts = [
        'link' => 'string',
        'default_link' => 'integer',
    ];

    public function getIconAttribute()
    {
        /** @var Media $media */
        $media = $this->getMedia(self::PATH)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return asset('default-images/avatar.png');
    }
}
