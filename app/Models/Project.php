<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\Project
 *
 * @property int $id
 * @property string $name
 * @property string $color
 * @property string $tenant_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read QrCode|null $project
 * @method static Builder|Project newModelQuery()
 * @method static Builder|Project newQuery()
 * @method static Builder|Project query()
 * @method static Builder|Project whereColor($value)
 * @method static Builder|Project whereCreatedAt($value)
 * @method static Builder|Project whereId($value)
 * @method static Builder|Project whereName($value)
 * @method static Builder|Project whereTenantId($value)
 * @method static Builder|Project whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Project extends BaseModel implements JsonResourceful
{
    use HasFactory, HasJsonResourcefulData;

    protected $fillable = ['name', 'color', 'tenant_id'];

    public static array $rules = [
        'name' => 'required|unique:projects',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'name'      => 'string',
        'color'     => 'string',
        'tenant_id' => 'string',
    ];

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'name'       => $this->name,
            'color'      => $this->color,
            'user_id'    => $this->user->id,
            'user_name'  => $this->user->name,
            'user_email' => $this->user->email,
            'user_image' => $this->user->image_url,
            'link_count' => $this->user->link_count,
            'qr_code_count' => $this->user->qr_code_count,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];

        return $fields;
    }

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }

    /**
     * @return HasOne
     */
    public function qrCode(): HasOne
    {
        return $this->hasOne(QrCode::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'tenant_id', 'tenant_id');
    }
    
    /**
     * @param Builder $query
     */
    public function scopeTenant(Builder $query): void
    {
        $query->where('tenant_id', Auth::user()->tenant_id);
    }
}
