<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Traits\HasJsonResourcefulData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class SubFeature extends Model implements HasMedia, JsonResourceful
{
    use HasFactory, InteractsWithMedia, HasJsonResourcefulData;

    protected $fillable = ['title', 'description'];
    protected $appends = ['image_url'];

    /**
     * @return array
     */
    function prepareLinks()
    {
        return [];
    }

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        return [];
    }
    
    /**
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(FrontCMS::IMAGE)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return defaultFrontCmsImage();
    }
}
