<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\DigitalBusinessCard
 *
 * @property int $id
 * @property string $url_alias
 * @property string $name
 * @property string $job_title
 * @property string $company
 * @property string $phone
 * @property string $email
 * @property string $website
 * @property int $status
 * @property int $template_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read string $cover_image
 * @property-read string $profile_image
 * @property-read MediaCollection|Media[] $media
 * @property-read int|null $media_count
 * @method static Builder|DigitalBusinessCard newModelQuery()
 * @method static Builder|DigitalBusinessCard newQuery()
 * @method static Builder|DigitalBusinessCard query()
 * @method static Builder|DigitalBusinessCard whereCompany($value)
 * @method static Builder|DigitalBusinessCard whereCreatedAt($value)
 * @method static Builder|DigitalBusinessCard whereEmail($value)
 * @method static Builder|DigitalBusinessCard whereId($value)
 * @method static Builder|DigitalBusinessCard whereJobTitle($value)
 * @method static Builder|DigitalBusinessCard whereName($value)
 * @method static Builder|DigitalBusinessCard wherePhone($value)
 * @method static Builder|DigitalBusinessCard whereStatus($value)
 * @method static Builder|DigitalBusinessCard whereTemplateId($value)
 * @method static Builder|DigitalBusinessCard whereUpdatedAt($value)
 * @method static Builder|DigitalBusinessCard whereUrlAlias($value)
 * @method static Builder|DigitalBusinessCard whereWebsite($value)
 * @mixin Eloquent
 * @property string $tenant_id
 * @method static Builder|DigitalBusinessCard whereTenantId($value)
 * @property-read string $profile_url_base64
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\SocialLink[] $socialLinks
 * @property-read int|null $social_links_count
 * @property-read \App\Models\User $user
 * @method static Builder|DigitalBusinessCard tenant()
 */
class DigitalBusinessCard extends Model implements HasMedia, JsonResourceful
{
    use HasFactory, InteractsWithMedia, HasJsonResourcefulData;

    const PROFILE_PATH = 'profiles';

    const COVER_PATH = 'covers';

    /**
     * @var string
     */
    protected $table = 'business_cards';

    /**
     * @var string[]
     */
    protected $fillable = [
        'url_alias',
        'name',
        'job_title',
        'company',
        'phone',
        'email',
        'website',
        'status',
        'template_id',
        'tenant_id',
    ];

    protected $appends = [
        'profile_image',
        'cover_image',
        'profile_url_base64',
    ];

    protected $casts = [
        'url_alias' => 'string',
        'name' => 'string',
        'job_title' => 'string',
        'company' => 'string',
        'phone' => 'string',
        'email' => 'string',
        'website' => 'string',
        'status' => 'boolean',
        'template_id' => 'integer',
        'tenant_id' => 'string',
    ];

    public static $rules = [
        'url_alias' => 'required|unique:business_cards,url_alias',
        'name' => 'required',
        'job_title' => 'required',
        'company' => 'required',
        'phone' => 'required',
        'email' => 'required|email:filter',
        'website' => 'required',
        'profile_image' => 'nullable|mimes:jpg,bmp,png,apng,avif,jpeg',
        'cover_image' => 'nullable|mimes:jpg,bmp,png,apng,avif,jpeg',
    ];

    /**
     * @return string
     */
    public function getProfileImageAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::PROFILE_PATH)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return asset('default-images/avatar.png');
    }

    public function getProfileUrlBase64Attribute(): string
    {
        $url = asset('default-images/avatar.png');

        /** @var Media $media */
        $media = $this->getMedia(self::PROFILE_PATH)->first();
        if ($media !== null) {
            $url = $media->getFullUrl();
        }

        return base64_encode(file_get_contents($url));
    }

    /**
     * @return string
     */
    public function getCoverImageAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::COVER_PATH)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return asset('default-images/avatar.png');
    }

    /**
     * @return array
     */
    public function prepareLinks(): array
    {
        return [
            "self" => route('digital-business-cards.show', $this->id),
        ];
    }

    /**
     * @return array
     */
    public function prepareAttributes(): array
    {
        $fields = [
            'id' => $this->id,
            'name' => $this->name,
            'url_alias' => $this->url_alias,
            'job_title' => $this->job_title,
            'company' => $this->company,
            'phone' => $this->phone,
            'email' => $this->email,
            'website' => $this->website,
            'template_id' => $this->template_id,
            'profile_image' => $this->profile_image,
            'cover_image' => $this->cover_image,
            'status' => $this->status,
            'tenant_id' => $this->tenant_id,
            'user_id' => !empty($this->user) ? $this->user->id : null,
            'user_name' => !empty($this->user) ? $this->user->name : null,
            'socialLinks' => !empty($this->socialLinks) ? $this->socialLinks : [],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];

        return $fields;
    }

    /**
     * @param Builder $query
     */
    public function scopeTenant(Builder $query): void
    {
        $query->where('tenant_id', Auth::user()->tenant_id);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id', 'tenant_id');
    }

    public function socialLinks()
    {
        return $this->belongsToMany(SocialLink::class, 'business_card_social_links', 'business_card_id', 'social_link_id')->withTimestamps();
    }
}
