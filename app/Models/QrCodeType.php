<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\QrCodeType
 *
 * @property int $id
 * @property string $title
 * @property string|null $description
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read string $image
 * @method static \Database\Factories\QrCodeTypeFactory factory(...$parameters)
 * @method static Builder|QrCodeType newModelQuery()
 * @method static Builder|QrCodeType newQuery()
 * @method static Builder|QrCodeType query()
 * @method static Builder|QrCodeType whereCreatedAt($value)
 * @method static Builder|QrCodeType whereDescription($value)
 * @method static Builder|QrCodeType whereId($value)
 * @method static Builder|QrCodeType whereTitle($value)
 * @method static Builder|QrCodeType whereUpdatedAt($value)
 * @mixin Eloquent
 */
class QrCodeType extends Model implements HasMedia, JsonResourceful
{
    use HasFactory, InteractsWithMedia, HasJsonResourcefulData;
    
    const PATH = 'image';
    const JSON_API_TYPE = 'QR Code Types';

    /**
     * @var string
     */
    protected $table = 'qr_code_types';

    /**
     * @var string[]
     */
    protected $fillable = [
        'title',
        'description',
    ];

    /**
     * @var string[]
     */
    protected $appends = ['image'];

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [];
    }

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'id'          => $this->id,
            'title'       => $this->title,
            'description' => $this->description,
            'image'       => $this->image,
            'created_at'  => $this->created_at,
            'updated_at'  => $this->updated_at,
        ];

        return $fields;
    }
    
    public function getImageAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::PATH)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return asset('default-images/avatar.png');
    }
}
