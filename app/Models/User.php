<?php

namespace App\Models;

use App\Models\Contracts\JsonResourceful;
use App\Notifications\ResetPasswordNotification;
use App\Traits\HasJsonResourcefulData;
use App\Traits\Multitenantable;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Traits\HasRoles;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * App\Models\User
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $tenant_id
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read string $image_url
 * @property-read MediaCollection|Media[] $media
 * @property-read int|null $media_count
 * @property-read DatabaseNotificationCollection|DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection|Permission[] $permissions
 * @property-read int|null $permissions_count
 * @property-read Collection|\Spatie\Permission\Models\Role[] $roles
 * @property-read int|null $roles_count
 * @property-read MultiTenant|null $tenant
 * @property-read Collection|PersonalAccessToken[] $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory(...$parameters)
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static Builder|User permission($permissions)
 * @method static Builder|User query()
 * @method static Builder|User role($roles, $guard = null)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereName($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereTenantId($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @mixin Eloquent
 */
class User extends Authenticatable implements HasMedia, JsonResourceful, MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, HasJsonResourcefulData, BelongsToTenant, Multitenantable, InteractsWithMedia;

    public const PATH = 'user_profile';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'email_verified_at',
        'status',
        'tenant_id',
        'language',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'name'              => 'string',
        'email'             => 'string',
        'password'          => 'string',
        'email_verified_at' => 'datetime',
        'tenant_id'         => 'string',
        'language'          => 'string',
        'status'            => 'boolean',
    ];

    protected $appends = ['image_url', 'qr_code_count', 'link_count'];

    /**
     *
     *
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::PATH)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return asset('default-images/avatar.png');
    }

    /**
     * @return array
     */
    function prepareLinks(): array
    {
        return [
            "self" => route('users.show', $this->id),
        ];
    }

    /**
     * @return array
     */
    function prepareAttributes(): array
    {
        $fields = [
            'id'                => $this->id,
            'name'              => $this->name,
            'email'             => $this->email,
            'created_at'        => $this->created_at,
            'updated_at'        => $this->updated_at,
            'role_id'           => $this->roles->first()->id ?? null,
            'role_name'         => $this->roles->first()->display_name ?? null,
            'image'             => $this->image_url,
            'status'            => $this->status,
            'plan'              => !empty($this->subscription->plan->name) ?  $this->subscription->plan->name : null,
            'plan_id'           => !empty($this->subscription->plan_id) ? $this->subscription->plan_id : null,
            'email_verified_at' => $this->email_verified_at,
            'tenant_id'         => $this->tenant_id,
            'link_count'        => $this->link_count,
            'qr_code_count'     => $this->qr_code_count,
        ];

        return $fields;
    }

    /**
     * @var array|string[]
     */
    public static array $rules = [
        'name'             => 'required',
        'email'            => 'required|email|unique:users',
        'password'         => 'required',
        'confirm_password' => 'required|same:password',
    ];

    /**
     * @return mixed
     */
    public function subscription(): mixed
    {
        return $this->hasOne(Subscription::class)->whereStatus(Subscription::ACTIVE);
    }

    /**
     * @return mixed
     */
    public function subscriptions(): mixed
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * @return mixed
     */
    public function transactions(): mixed
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * @param string $token
     */
    public function sendPasswordResetNotification($token)
    {
        $url = url('/#/reset-password/'.$token);

        $this->notify(new ResetPasswordNotification($url));
    }

    /**
     * @return int
     */
    public function getQrCodeCountAttribute(): int
    {
        return QrCode::whereTenantId($this->tenant_id)->count();
    }

    /**
     * @return int
     */
    public function getLinkCountAttribute(): int
    {
        return Link::whereTenantId($this->tenant_id)->count();
    }
}
