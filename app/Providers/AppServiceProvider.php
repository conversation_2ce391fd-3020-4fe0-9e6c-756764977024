<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (strpos(php_sapi_name(), 'cli') != 0) {
            if (!empty(getSettingValue('host'))) {
                config(['mail.mailers.smtp.host' => getSettingValue('host')]);
                config(['mail.mailers.smtp.port' => getSettingValue('port')]);
                config(['mail.mailers.smtp.username' => getSettingValue('email_username')]);
                config(['mail.mailers.smtp.password' => getSettingValue('email_password')]);
                config(['mail.mailers.smtp.encryption' => getSettingValue('encryption')]);
                config(['mail.from.name' => getSettingValue('from_name')]);
                config(['mail.from.address' => getSettingValue('from_email')]);
            }
        }
    }
}
