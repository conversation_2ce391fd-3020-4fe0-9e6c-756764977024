<?php

namespace App\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\Filters\Filter;

/**
 * Class CustomSearchFilter
 */
class CustomSearchFilter implements Filter
{
    public $searchableFields;

    /**
     * @param $searchableFields
     */
    public function __construct($searchableFields)
    {
        $this->searchableFields = $searchableFields;
        $filterSearchFields = request()->get('filter')['search_fields'] ?? [];
        if (!empty($filterSearchFields)) {
            $this->searchableFields = explode(',', $filterSearchFields);
        }
    }

    /**
     * @param Builder $query
     * @param $value
     * @param string $property
     *
     * @return Builder
     */
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        $customQuery = $query;

        if (is_array($value)) {
            foreach ($this->searchableFields as $key => $searchableField) {
                if(Str::contains($searchableField, '.')) {
                    $relation = explode('.', $searchableField)[0] ?? '';
                    $field = explode('.', $searchableField)[1] ?? '';
                    foreach ($value as $string) {
                        $customQuery->orWhereHas($relation, function ($q) use ($field, $string) {
                            $q->where($field, 'LIKE', '%'.$string.'%');
                        });
                    }
                    unset($this->searchableFields[$key]);
                } else {
                    foreach ($value as $string) {
                        $customQuery->orWhere($searchableField, 'LIKE', '%'.$string.'%');
                    }
                }
                
            }
        } else {
            foreach ($this->searchableFields as $key => $searchableField) {
                if(Str::contains($searchableField,'.')) {
                    $relation = substr_count($searchableField,'.') == 1
                        ? (explode('.', $searchableField)[0] ?? '')
                        : (explode('.', $searchableField)[0] . '.' . explode('.', $searchableField)[1] ?? '');
                    $field = explode('.', $searchableField)[2] ?? (explode('.', $searchableField)[1] ?? '');
                    $customQuery->orWhereHas($relation, function ($q) use ($field, $value) {
                        $q->where($field, 'LIKE', '%'.$value.'%');
                    });
                    unset($this->searchableFields[$key]);
                } else {
                    $customQuery->orWhere($searchableField, 'LIKE', '%'.$value.'%');
                }
            }
        }

        return $customQuery;
    }
}
