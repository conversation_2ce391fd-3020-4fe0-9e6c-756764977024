<?php

namespace App\Repositories;

use App\Models\Setting;
use Illuminate\Support\Arr;

/**
 * Class QrCodeRepository
 */
class SettingRepository extends BaseRepository
{
    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Setting::class;
    }

    public function getMainSetting(): array
    {
        $mainSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($mainSetting, [
            'title', 'logo', 'favicon', 'enable_new_users_registration', 'plan_expire_notification', 'currency', 'email', 'phone', 'address'
        ]);
    }

    public function updateMainSetting($input): array
    {
        $mainSetting = Arr::only($input, [
            'title', 'logo', 'favicon', 'enable_new_users_registration', 'plan_expire_notification', 'currency', 'email', 'phone', 'address'
        ]);

        foreach ($mainSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        if (isset($input['logo'])) {
            /** @var Setting $setting */
            $setting = Setting::where('key', '=', 'logo')->first();
            $mainSetting['logo'] = $this->uploadSettingImages($setting, $input['logo']);
        }
        if (isset($input['favicon'])) {
            /** @var Setting $setting */
            $setting = Setting::where('key', '=', 'favicon')->first();
            $mainSetting['favicon'] = $this->uploadSettingImages($setting, $input['favicon']);
        }

        return $mainSetting;
    }

    public function uploadSettingImages($setting, $value)
    {
        $setting->clearMediaCollection(Setting::PATH);
        $media = $setting->addMedia($value)->toMediaCollection(Setting::PATH, config('app.media_disc'));
        $setting = $setting->refresh();
        $setting->update(['value' => $media->getFullUrl()]);

        return $media->getFullUrl();
    }

    public function getPaypalSetting(): array
    {
        $paypalSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($paypalSetting, [
            'enable_paypal_payments', 'paypal_client_id', 'paypal_secret', 'paypal_payment_mode',
        ]);
    }

    public function updatePaypalSetting($input): array
    {
        $input['paypal_payment_mode'] = strtolower($input['paypal_payment_mode']);
        $paypalSetting = Arr::only($input, [
            'enable_paypal_payments', 'paypal_client_id', 'paypal_secret', 'paypal_payment_mode',
        ]);

        foreach ($paypalSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $paypalSetting;
    }

    public function getStripeSetting(): array
    {
        $stripeSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($stripeSetting, [
            'enable_stripe_payments', 'stripe_Key', 'stripe_secret', 'stripe_payment_mode',
        ]);
    }

    public function updateStripeSetting($input): array
    {
        $stripeSetting = Arr::only($input, [
            'enable_stripe_payments', 'stripe_Key', 'stripe_secret', 'stripe_payment_mode',
        ]);

        foreach ($stripeSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $stripeSetting;
    }

    public function getRazorpaySetting(): array
    {
        $razorpaySetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($razorpaySetting, [
            'enable_razorpay_payments', 'razorpay_key', 'razorpay_secret', 'razorpay_payment_mode',
        ]);
    }

    public function updateRazorpaySetting($input): array
    {
        $razorpaySetting = Arr::only($input, [
            'enable_razorpay_payments', 'razorpay_key', 'razorpay_secret', 'razorpay_payment_mode',
        ]);

        foreach ($razorpaySetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $razorpaySetting;
    }

    public function getCaptchaSetting(): array
    {
        $captchaSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($captchaSetting, [
            'captcha_key', 'captcha_on_the_lost_password', 'captcha_on_register_login', 'captcha_on_contact_page',
            'captcha_site_key', 'captcha_secret_key',
        ]);
    }

    public function updateCaptchaSetting($input): array
    {
        $captchaSetting = Arr::only($input, [
            'captcha_key', 'captcha_on_the_lost_password', 'captcha_on_register_login', 'captcha_on_contact_page',
            'captcha_site_key', 'captcha_secret_key',
        ]);

        foreach ($captchaSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $captchaSetting;
    }

    public function getAdsSetting(): array
    {
        $adsSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($adsSetting, [
            'ads_header', 'ads_footer',
        ]);
    }

    public function updateAdsSetting($input): array
    {
        $adsSetting = Arr::only($input, [
            'ads_header', 'ads_footer',
        ]);

        foreach ($adsSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $adsSetting;
    }

    public function getSocialSetting(): array
    {
        $socialSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($socialSetting, [
            'youtube_link', 'facebook_link', 'twitter_link', 'instagram_link', 'tiktok_link', 'linked_in_link',
        ]);
    }

    public function updateSocialSetting($input): array
    {
        $socialSetting = Arr::only($input, [
            'youtube_link', 'facebook_link', 'twitter_link', 'instagram_link', 'tiktok_link', 'linked_in_link',
        ]);

        foreach ($socialSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $socialSetting;
    }

    public function getMailSetting(): array
    {
        $mailSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($mailSetting, [
            'from_name', 'from_email', 'host', 'encryption', 'port', 'email_username',
            'email_password',
        ]);
    }

    public function updateMailSetting($input): array
    {
        $mailSetting = Arr::only($input, [
            'from_name', 'from_email', 'host', 'encryption', 'port', 'email_username',
            'email_password',
        ]);

        foreach ($mailSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $mailSetting;
    }

    public function getCustomStyleSetting(): array
    {
        $customStyleSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($customStyleSetting, [
            'custom_js', 'custom_css',
        ]);
    }

    public function updateCustomStyleSetting($input): array
    {
        $customStyleSetting = Arr::only($input, [
            'custom_js', 'custom_css',
        ]);

        foreach ($customStyleSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $customStyleSetting;
    }

    public function getAnnouncementSetting(): array
    {
        $announcementSetting = Setting::pluck('value', 'key')->toArray();

        return Arr::only($announcementSetting, [
            'user_announcement_content', 'user_text_color', 'user_bg_color', 'announcement_enabled',
        ]);
    }

    public function updateAnnouncementSetting($input): array
    {
        $announcementSetting = Arr::only($input, [
            'user_announcement_content', 'user_text_color', 'user_bg_color', 'announcement_enabled',
        ]);

        foreach ($announcementSetting as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $announcementSetting;
    }

    public function getEmailNotificationSetting(): array
    {
        $emailNotification = Setting::pluck('value', 'key')->toArray();

        return Arr::only($emailNotification, [
            'emails_to_be_notified', 'new_user', 'delete_user', 'new_payment',
            'new_custom_domain', 'contact_page_emails',
        ]);
    }

    public function updateEmailNotificationSetting($input): array
    {
        $emailNotification = Arr::only($input, [
            'emails_to_be_notified', 'new_user', 'delete_user', 'new_payment',
            'new_custom_domain', 'contact_page_emails',
        ]);

        foreach ($emailNotification as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $emailNotification;
    }

    public function getFrontSettingsValue(): array
    {
        $keyName = [
            'currency', 'title', 'logo', 'favicon', 'announcement_enabled', 'google_client_id', 'enable_google_login',
            'facebook_app_id', 'enable_facebook_login', 'phone', 'email', 'address', 'captcha_site_key', 'captcha_secret_key',
            'captcha_on_the_lost_password', 'captcha_on_register_login', 'captcha_on_contact_page',
        ];

        return Setting::whereIn('key', $keyName)->pluck('value', 'key')->toArray();
    }

    public function getGoogleLoginSetting(): array
    {
        $googleLogin = Setting::pluck('value', 'key')->toArray();

        return Arr::only($googleLogin, [
            'google_client_id', 'enable_google_login',
        ]);
    }

    public function updateGoogleLoginSetting($input): array
    {
        $googleLogin = Arr::only($input, [
            'google_client_id', 'enable_google_login',
        ]);

        foreach ($googleLogin as $key => $value) {
            Setting::where('key', '=', $key)->first()->update(['value' => $value]);
        }

        return $googleLogin;
    }
}
