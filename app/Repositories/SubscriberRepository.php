<?php

namespace App\Repositories;

use App\Models\Subscriber;

/**
 * Class SubscriberRepository
 */
class SubscriberRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
      'email', 'created_at'
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Subscriber::class;
    }
}
