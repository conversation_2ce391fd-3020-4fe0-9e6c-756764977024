<?php

namespace App\Repositories;

use App\Models\CouponCode;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class CouponCodeRepository
 */
class CouponCodeRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'type',
        'code',
        'discount',
        'how_many_can_use',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return CouponCode::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storeCouponCode($input): mixed
    {
        try {
            DB::beginTransaction();
            $project = $this->create($input);
            DB::commit();

            return $project;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @param $id
     *
     * @return mixed
     */
    public function updateCouponCode($input, $id): mixed
    {
        try {
            DB::beginTransaction();
            $project = $this->update($input, $id);
            DB::commit();

            return $project;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
