<?php

namespace App\Repositories;

use App\Models\Analytic;
use App\Models\DigitalBusinessCard;
use App\Models\Link;
use App\Models\Role;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class LinkRepository
 */
class LinkRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'user.name',
        'destination_url',
        'url_alias',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Link::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storeLink($input): mixed
    {
        try {
            DB::beginTransaction();
            if (Auth::user()->hasRole(Role::USER)) {
                $input['tenant_id'] = Auth::user()->tenant_id;
            }
            $link = $this->create($input);
            $link->addMediaFromBase64($input['qr_image'])->toMediaCollection(Link::PATH, config('app.media_disc'));
            DB::commit();

            return $link;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @param $id
     *
     * @return mixed
     */
    public function updateLink($input, $id): mixed
    {
        try {
            DB::beginTransaction();
            $link = $this->update($input, $id);
            if (!empty($input['qr_image'])) {
                $link->clearMediaCollection(Link::PATH);
                $link->media()->delete();
                $link->addMediaFromBase64($input['qr_image'])->toMediaCollection(Link::PATH, config('app.media_disc'));
            }
            DB::commit();

            return $link;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $modelId
     * @param $modelType
     * @return array
     */
    public function analyticsData($modelId, $modelType): array
    {
        $analytics = Analytic::where('model_id', $modelId)->where('model_type', $modelType)->get();
        
        if ($analytics->count() > 0) {
            $DataCount = $analytics->count();
            $percentage = 100 / $DataCount;
            $browser = $analytics->groupBy('browser');
            $data = [];
            foreach ($browser as $key => $item) {
                $browser_record[$key]['count'] = $item->count();
                $browser_record[$key]['per'] = $item->count() * $percentage;
            }

            $browser_data = collect($browser_record)->sortBy('count')->reverse()->toArray();

            $data['browser'] = $browser_data;

            $device = $analytics->groupBy('device');

            foreach ($device as $key => $item) {
                $device_record[$key]['count'] = $item->count();
                $device_record[$key]['per'] = $item->count() * $percentage;
            }

            $device_data = collect($device_record)->sortBy('count')->reverse()->toArray();

            $data['device'] = $device_data;

            $country = $analytics->groupBy('country');

            foreach ($country as $key => $item) {
                $country_record[$key]['count'] = $item->count();
                $country_record[$key]['per'] = $item->count() * $percentage;
            }

            $country_data = collect($country_record)->sortBy('count')->reverse()->toArray();

            $data['country'] = $country_data;

            $operating_system = $analytics->groupBy('operating_system');

            foreach ($operating_system as $key => $item) {
                $operating_record[$key]['count'] = $item->count();
                $operating_record[$key]['per'] = $item->count() * $percentage;
            }

            $operating_data = collect($operating_record)->sortBy('count')->reverse()->toArray();

            $data['operating_system'] = $operating_data;

            $language = $analytics->groupBy('language');

            foreach ($language as $key => $item) {
                $language_record[$key]['count'] = $item->count();
                $language_record[$key]['per'] = $item->count() * $percentage;
            }

            $language_data = collect($language_record)->sortBy('count')->reverse()->toArray();

            $data['language'] = $language_data;

            if ($modelType == Link::class) {
                $data['link_id'] = $modelId;
            }else {
                $data['business_card_id'] = $modelId;
            }

            return $data;
        }
        
        $data['noRecord'] = 'No Data Available';

        return $data;
    }

    /**
     * @param $input
     * @return array
     */
    public function chartData($input): array
    {
        $startDate = isset($input['start_date']) ? Carbon::parse($input['start_date']) : '';
        $endDate = isset($input['end_date']) ? Carbon::parse($input['end_date']) : '';
        $data = [];

        if (!empty($input['business_card_id'])) {
            $analytics = Analytic::where('model_id', $input['business_card_id'])->where('model_type', DigitalBusinessCard::class);
        }else {
            $analytics = Analytic::where('model_id', $input['link_id'])->where('model_type', Link::class);
        }
        
        $visitor = $analytics->addSelect([DB::raw('DAY(created_at) as day,created_at')])
            ->addSelect([DB::raw('Month(created_at) as month,created_at')])
            ->addSelect([DB::raw('YEAR(created_at) as year,created_at')])
            ->orderBy('created_at')
            ->get();
        
        $period = CarbonPeriod::create($startDate, $endDate);

        foreach ($period as $date) {
            $data['totalVisitorCount'][] = $visitor->where('day', $date->format('d'))->where('month',
                $date->format('m'))->count();
            $data['weeklyLabels'][] = $date->format('d-m-y');
        }

        return $data;
    }
}
