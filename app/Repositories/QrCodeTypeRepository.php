<?php

namespace App\Repositories;

use App\Models\QrCodeType;

/**
 * Class QrCodeTypeRepository
 */
class QrCodeTypeRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'title',
        'description',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return QrCodeType::class;
    }

}
