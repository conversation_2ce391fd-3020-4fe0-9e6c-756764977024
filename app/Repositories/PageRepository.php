<?php

namespace App\Repositories;

use App\Models\Page;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class PageRepository
 */
class PageRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'title',
        'meta_title',
        'meta_description',
        'description',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Page::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storePage($input): mixed
    {
        try {
            DB::beginTransaction();
            $page = $this->create($input);
            DB::commit();

            return $page;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @param $id
     *
     * @return mixed
     */
    public function updatePage($input, $id): mixed
    {
        try {
            DB::beginTransaction();
            $page = $this->update($input, $id);
            DB::commit();

            return $page;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
