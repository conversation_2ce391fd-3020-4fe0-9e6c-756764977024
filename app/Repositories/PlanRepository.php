<?php

namespace App\Repositories;

use App\Models\Plan;
use App\Models\PlanFeatures;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class PlanRepository
 */
class PlanRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'price',
        'created_at'
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Plan::class;
    }

    public function storePlan($input)
    {
        try {
            DB::beginTransaction();
            $planInputArray = Arr::only($input,
                ['name', 'price', 'currency_id', 'order', 'trial_days', 'type', 'description']);
            $planFeaturesInputArray = Arr::only($input, [
                'plan_id', 'qr_code_limit', 'links_limit', 'projects_limit', 'allow_create_business_card',
                'qr_code_types','business_card_limit'
            ]);

            $plan = $this->create($planInputArray);
            $planFeaturesInputArray['plan_id'] = $plan->id;
            $planFeaturesInputArray['allow_create_business_card'] = $planFeaturesInputArray['allow_create_business_card'] == 1 ? true : false;
            PlanFeatures::create($planFeaturesInputArray);

            DB::commit();

            return $plan;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    public function updatePlan($input, $id)
    {
        try {
            DB::beginTransaction();
            $planInputArray = Arr::only($input,
                ['name', 'price', 'currency_id', 'order', 'trial_days', 'type', 'description']);
            $planFeaturesInputArray = Arr::only($input, [
                'plan_id', 'qr_code_limit', 'links_limit', 'projects_limit', 'allow_create_business_card',
                'qr_code_types','business_card_limit'
            ]);

            $plan = Plan::findOrFail($id);
            $plan->update($planInputArray, ['id' => $id]);
            $planFeaturesInputArray['allow_create_business_card'] = $planFeaturesInputArray['allow_create_business_card'] == 1 ? true : false;
            $plan->planFeatures->update($planFeaturesInputArray, ['id' => $plan->planFeatures->id]);

            DB::commit();

            return $plan;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
