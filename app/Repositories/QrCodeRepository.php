<?php

namespace App\Repositories;

use App\Models\Project;
use App\Models\QrCode;
use App\Models\Role;
use App\Models\Subscription;
use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class QrCodeRepository
 */
class QrCodeRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'user.name',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return QrCode::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storeQrCode($input): mixed
    {
        try {
            DB::beginTransaction();
            if (Auth::user()->hasRole(Role::USER)) {
                $input['tenant_id'] = Auth::user()->tenant_id;
            }
            qrCodeTypeRelatedInputs($input);
            $qrCode = $this->create($input);

            $qrCode->addMediaFromBase64($input['image'])->toMediaCollection(QrCode::PATH, config('app.media_disc'));

            DB::commit();

            return $qrCode;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @param $id
     *
     * @return mixed
     */
    public function updateQrCode($input, $id): mixed
    {
        try {
            DB::beginTransaction();
            qrCodeTypeRelatedInputs($input);
            $qrCode = $this->update($input, $id);

            if (!empty($input['image'])) {
                $qrCode->clearMediaCollection(QrCode::PATH);
                $qrCode->media()->delete();
                $qrCode->addMediaFromBase64($input['image'])->toMediaCollection(QrCode::PATH, config('app.media_disc'));
            }
            DB::commit();

            return $qrCode;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    public function projectList($tenantId = null): Collection
    {
        $projects = Project::whereTenantId(Auth::user()->tenant_id)->get();
        if (Auth::user()->hasRole(Role::ADMIN)) {
            $projects = Project::whereTenantId($tenantId)->get();
        }

        return $projects;
    }

    public function qrCodeTypes($tenantId = null)
    {
        $qrCodeTypes = [];
        if (Auth::user()->hasRole(Role::USER)) {
            $checkQrCodeType = Arr::only(QrCode::QR_CODE_TYPE,
                explode(',', currentActiveSubscription()->plan->planFeatures->qr_code_types));
            foreach ($checkQrCodeType as $key) {
                $qrCodeTypes[] = $key;
            }
        }

        if (Auth::user()->hasRole(Role::ADMIN)) {
            $userId = User::whereTenantId($tenantId)->first()->id;
            $currentActivePlan = Subscription::with('plan')
                ->whereStatus(Subscription::ACTIVE)
                ->whereUserId($userId)
                ->first();

            $checkQrCodeType = Arr::only(QrCode::QR_CODE_TYPE,
                explode(',', $currentActivePlan->plan->planFeatures->qr_code_types));
            foreach ($checkQrCodeType as $key) {
                $qrCodeTypes[] = $key;
            }
        }

        return $qrCodeTypes;
    }
}
