<?php

namespace App\Repositories;

use App\Models\Subscription;
use App\Models\Transaction;

/**
 * Class TransactionRepository
 */
class TransactionRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'subscription.user.name',
        'subscription.plan.name',
        'price_of_plan',
        'created_at',
    ];
    
    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Transaction::class;
    }

    public function changePaymentStatus($input): void
    {
        $transaction = Transaction::with('subscription')->findOrFail($input['id']);

        if ($input['status'] == Transaction::APPROVED) {
            $subscription = $transaction->subscription;

            Transaction::whereId($transaction->id)->update([
                'is_manual_payment' => $input['status'],
                'status'            => Subscription::ACTIVE,
            ]);

            Subscription::find($subscription->id)->update(['status' => Subscription::ACTIVE]);

            Subscription::whereUserId($subscription->user_id)
                ->where('id', '!=', $subscription->id)
                ->update([
                    'status' => Subscription::DISABLE,
                ]);

            $subject = "Subscription Activated";
            $data['data'] = "<h1>Hello <span>".$subscription->user->name."</span>,</h1><br>"."Your ".'<strong>'.
                $subscription->plan->name.'</strong>'." plan activated successfully.".'<br><br><p>Regards</p><p>'.getSettingValue('title').'</p>';
            if (getSettingValue('new_payment')) {
                sendMailToUser($subscription->user->email, $subject, $data, 'emails.mail-sender');
            }
        }

        if ($input['status'] == Transaction::DENIED) {
            Transaction::whereId($transaction->id)->update([
                'is_manual_payment' => $input['status'],
                'status'            => Subscription::DISABLE,
            ]);
        }
    }
}
