<?php

namespace App\Repositories;

use App\Models\ContactUs;

/**
 * Class ContactUsRepository
 */
class ContactUsRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
      'name', 
      'email', 
      'subject', 
      'message', 
      'view', 
      'created_at'
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return ContactUs::class;
    }
}
