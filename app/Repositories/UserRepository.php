<?php

namespace App\Repositories;

use App\Models\DigitalBusinessCard;
use App\Models\Link;
use App\Models\MultiTenant;
use App\Models\Plan;
use App\Models\Project;
use App\Models\QrCode;
use App\Models\Role;
use App\Models\Subscription;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class UserRepository
 */
class UserRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'email',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return User::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storeUser($input): mixed
    {
        try {
            DB::beginTransaction();
            $input['password'] = Hash::make($input['password']);
            $tenant = MultiTenant::create(['tenant_username' => $input['name']]);
            $input['tenant_id'] = $tenant->id;
            if (!Auth::check()){
                verifyCaptcha();
            }
            $user = $this->create($input);
            $user->assignRole(Role::USER);
            if (!empty($input['image'])) {
                $user->addMedia($input['image'])->toMediaCollection(User::PATH, config('app.media_disc'));
            }

            $adminSubject = "New User Account";
            $appName = getSettingValue('title');
            $adminData['data'] = "<h1>Dear <span>".getLoginAdminName().",</span></h1><br><p>User account has been created with this name ".'<strong>'.$user->name.'</strong>.</p><br><br><p>Regards</p><p>'.$appName.'</p>';
            $userSubject = "Welcome ".$user->name." to ".$appName;
            $userData['data'] = $user;

            if (getSettingValue('new_user')) {
                sendMailToUser($user->email, $userSubject, $userData, 'emails.registration_mail');
                sendMailToAdmin($adminSubject, $adminData);
            }

            $plan = Plan::where('is_default', 1)->first();
            $trialDays = $plan->trial_days;
            $subscription = [
                'user_id'       => $user->id,
                'plan_id'       => $plan->id,
                'price_of_plan' => $plan->price,
                'plan_type'     => $plan->type,
                'start_date'    => Carbon::now(),
                'end_date'      => Carbon::now()->addDays($trialDays),
                'trial_ends_at' => Carbon::now()->addDays($trialDays),
                'status'        => Subscription::ACTIVE,
            ];
            Subscription::create($subscription);

            DB::commit();

            return $user;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @param $id
     *
     * @return mixed
     */
    public function updateUser($input, $id): mixed
    {
        try {
            DB::beginTransaction();
            $user = User::findOrFail($id);

            if (!empty($input['password'])) {
                $input['password'] = Hash::make($input['password']);
                $user->update($input);
            } else {
                $user->name = $input['name'];
                $user->email = $input['email'];
                $user->save();
            }

            if (!empty($input['image'])) {
                $user->clearMediaCollection(User::PATH);
                $user->media()->delete();
                $user->addMedia($input['image'])->toMediaCollection(User::PATH, config('app.media_disc'));
            }

            if (isset($input['role_id'])) {
                $user->syncRoles($input['role_id']);
            }
            DB::commit();

            return $user;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $perPage
     *
     * @return mixed
     */
    public function getUsers($perPage): mixed
    {
        return $this->whereRelation('roles', 'name', '!=', 'admin')->paginate($perPage);
    }

    public function updateUserProfile($input)
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            $user->update($input);

            if (!empty($input['image'])) {
                $user->clearMediaCollection(User::PATH);
                $user->media()->delete();
                $user->addMedia($input['image'])->toMediaCollection(User::PATH, config('app.media_disc'));
            }
            DB::commit();

            return $user;
        } catch (Exception $e) {
            DB::rollBack();

            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param array $input
     *
     * @return User
     */
    public function changePassword(array $input): User
    {
        /** @var User $user */
        $user = Auth::user();

        if (!Hash::check($input['current_password'], $user->password)) {
            throw new UnprocessableEntityHttpException(__('messages.error.current_password_invalid'));
        }

        $input['password'] = Hash::make($input['new_password']);
        $user->update($input);

        return $user;
    }

    /**
     * @param array $input
     *
     * @return User
     */
    public function changeLoginPassword(array $input): User
    {
        /** @var User $user */
        $user = Auth::user();
        $input['password'] = Hash::make($input['password']);
        $input['email_verified_at'] = Carbon::now();
        $user->update($input);

        return $user;
    }

    public function deleteUserRelatedRecords($id): void
    {
        Project::whereTenantId($id)->delete();
        Link::whereTenantId($id)->delete();
        QrCode::whereTenantId($id)->delete();
        DigitalBusinessCard::whereTenantId($id)->delete();
    }

}
