<?php

namespace App\Repositories;

use App\Models\Project;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class ProjectRepository
 */
class ProjectRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'user.name',
        'color',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Project::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storeUserProject($input): mixed
    {
        try {
            DB::beginTransaction();
            $input['tenant_id'] = Auth::user()->tenant_id;
            $project = $this->create($input);
            DB::commit();

            return $project;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storeAdminProject($input): mixed
    {
        try {
            DB::beginTransaction();
            $project = $this->create($input);
            DB::commit();

            return $project;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @param $id
     *
     * @return mixed
     */
    public function updateProject($input, $id): mixed
    {
        try {
            DB::beginTransaction();
            $project = $this->update($input, $id);
            DB::commit();

            return $project;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }
}
