<?php

namespace App\Repositories;

use App\Models\CouponCode;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Transaction;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class SubscriptionRepository
 */
class SubscriptionRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'plan.name',
        'user.name',
        'start_date',
        'end_date',
        'created_at',
        'price_of_plan',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Subscription::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function purchaseSubscription($input): mixed
    {
        try {
            DB::beginTransaction();
            $plan = Plan::findOrFail($input['plan_id']);
            $data = $this->getPlan($input);
            $newPlanDays = daysOfPlan($plan);
            $startsAt = Carbon::now();
            $endsAt = $startsAt->copy()->addDays($newPlanDays);
            $status = $data['new_plan'][1]['payable_amount'] > 0 ? Subscription::DISABLE : Subscription::ACTIVE;
            $user = Auth::user();
            $subscriptionInput = [
                'user_id'       => $user->id,
                'plan_id'       => $plan->id,
                'price_of_plan' => $plan->price,
                'plan_type'     => $plan->type,
                'start_date'    => $startsAt,
                'end_date'      => $endsAt,
                'status'        => $status,
            ];

            $subscription = Subscription::create($subscriptionInput);
            if ($status == Subscription::ACTIVE) {
                Subscription::whereUserId(Auth::id())->where('id', '!=', $subscription->id)
                    ->update(['status' => Subscription::DISABLE]);
            }

            $transaction = '';
            if ($status == Subscription::DISABLE) {
                $transaction = Transaction::create([
                    'tenant_id'     => $user->tenant_id,
                    'user_id'       => $user->id,
                    'payment_mode'  => Subscription::TYPE_MANUAL,
                    'price_of_plan' => $data['new_plan'][1]['payable_amount'],
                    'date'          => Carbon::now()->toDateString(),
                ]);
                $subject = 'Request for plan activation';
                $data['data'] = '<h1>Dear <span>'.getLoginAdminName().'</span>,</h1><br><strong>'.$user->name.'</strong>'." has send request for activate ".
                    '<strong>'.$data['new_plan'][0]['name'].'</strong>'." plan.".'<br><br><p>Regards</p><p>'.getSettingValue('title').'</p>';
                if (getSettingValue('new_payment')) {
                    sendMailToAdmin($subject, $data);
                }
                if (!empty($input['coupon_code'])) {
                    $couponCode = CouponCode::whereCode($input['coupon_code'])->first();
                    $couponCode->increment('used', 1);
                    $transaction->update(['coupon_code' => $couponCode->code]);
                }
            }

            $subscription->update(['transaction_id' => $transaction->id ?? null]);
            DB::commit(); 

            return $subscription;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @return array
     */
    public function getPlan($input): array
    {
        // Current Subscription/Plan
        $currentSubscription = currentActiveSubscription();
        $startsAt = Carbon::now()->format('jS M, Y');
        $currentSubscriptionStartsAt = Carbon::parse($currentSubscription->start_date)->format('jS M, Y');
        $currentSubscriptionEndsAt = Carbon::parse($currentSubscription->end_date)->format('jS M, Y');
        $totalDays = Carbon::parse($currentSubscription->start_date)->diffInDays($currentSubscription->end_date);
        $usedDays = Carbon::parse($currentSubscription->start_date)->diffInDays($startsAt);
        $remainingDays = $totalDays - $usedDays;
        $currentPlan = $currentSubscription->plan;
        $perDayPrice = round($currentPlan->price / daysOfPlan($currentPlan), 2);
        if (checkIfPlanIsInTrial($currentPlan)) {
            $remainingBalance = 0.00;
            $usedBalance = 0.00;
        } else {
            $remainingBalance = $currentPlan->price - ($perDayPrice * $usedDays);
            $usedBalance = $currentPlan->price - $remainingBalance;
        }

        // New Plan
        $newPlan = Plan::whereId($input['plan_id'])->first();
        $newPlanDays = daysOfPlan($newPlan);
        $endsAt = Carbon::now()->addDays($newPlanDays)->format('jS M, Y');
        $amountToPay = 0;
        $remainingBalanceFromPrvPlan = 0;
        if (!$currentSubscription->isExpired() && !checkIfPlanIsInTrial($currentSubscription)) {

            // checking if the current active subscription plan has the same price and type in order to process the calculation for the peroration
            $planPrice = $currentPlan->price;
            $planType = $currentPlan->type;
            if ($planPrice != $currentSubscription->price_of_plan || $planType != $currentSubscription->plan_type) {
                $planPrice = $currentSubscription->price_of_plan;
            }

            $days = daysOfPlan($currentPlan);
            $perDayPrice = round($planPrice / $days, 2);

            $remainingBalanceFromPrvPlan = round($planPrice - ($perDayPrice * $usedDays), 2);
            if ($remainingBalanceFromPrvPlan <= $currentSubscription->price_of_plan) { // adjust the amount in plan
                $amountToPay = round($newPlan->price - $remainingBalanceFromPrvPlan, 2);
            }

            if ($remainingBalanceFromPrvPlan > $amountToPay) {
                $amountToPay = $newPlan->price - $remainingBalanceFromPrvPlan;
            }
        } else {
            $amountToPay = $newPlan->price;
            $remainingBalanceFromPrvPlan = 0;
        }
        $stripe = getSettingValue('enable_stripe_payments') || (env('STRIPE_KEY') && env('STRIPE_SECRET')) ? 1 : 0;
        $paypal = getSettingValue('enable_paypal_payments') || (env('PAYPAL_SANDBOX_CLIENT_ID') &&
            env('PAYPAL_SANDBOX_CLIENT_SECRET')) ? 1 : 0;
        $razorpay = getSettingValue('enable_razorpay_payments') || (env('RAZOR_KEY') && env('RAZOR_SECRET')) ? 1 : 0;
        $couponCode = '';
        if (!empty($input['coupon_code'])) {
            $amountToPay = $this->applyCouponCode($input['coupon_code'], $newPlan->price) - $remainingBalanceFromPrvPlan;
            $couponCode = CouponCode::whereCode($input['coupon_code'])->first();
        }
        $amountToPay = $amountToPay < 0 ?  0 : $amountToPay;
       
        $amountToPay = str_replace(',', '', number_format($amountToPay, 2));
        $usedBalance = number_format($usedBalance, 2);

        return [
            'current_plan' => [
                $currentPlan, [
                    'starts_at'         => $currentSubscriptionStartsAt,
                    'expires_on'        => $currentSubscriptionEndsAt,
                    'used_days'         => $usedDays,
                    'remaining_days'    => $remainingDays,
                    'used_balance'      => $usedBalance,
                    'remaining_balance' => $remainingBalance,
                    'currency_symbol'   => $currentPlan->currency->symbol,
                ],
            ],
            'new_plan'       => [
                $newPlan, [
                    'starts_at'                      => $startsAt,
                    'expires_on'                     => $endsAt,
                    'total_days'                     => daysOfPlan($newPlan),
                    'remaining_balance_of_prev_plan' => $remainingBalanceFromPrvPlan,
                    'payable_amount'                 => $amountToPay,
                    'currency_symbol'                => $newPlan->currency->symbol,
                ],
            ],
            'payment_enable' => [
                'stripe'   => $stripe,
                'paypal'   => $paypal,
                'razorpay' => $razorpay,
            ],
            'discount'       => [
                'coupon_code' => $couponCode,
            ],
        ];
    }

    /**
     * @return array
     */
    public function subscriptionPlans()
    {
        $plans = Plan::with('planFeatures')->get();
        $subscription = currentActiveSubscription();
        $userHasPlan = $subscription !== null && $subscription->end_date >= Carbon::now() ?
            Subscription::ACTIVE : Subscription::DISABLE;
        $remainingTrialDays = Carbon::parse($subscription->start_date)->diffInDays($subscription->trial_ends_at);

        $subscriptionPlans = [];
        foreach ($plans as $plan) {
            $subscriptionPlans[] = $plan->prepareAttributes();
        }

        return [
            $subscriptionPlans,
            'user_has_plan'        => $userHasPlan,
            'starts_at'            => $subscription->start_date,
            'ends_at'              => $subscription->end_date,
            'remaining_trial_days' => $remainingTrialDays,
        ];
    }

    /**
     * @param $planId
     * @return mixed
     */
    public function createSubscription($planId)
    {
        $plan = Plan::find($planId);
        $newPlanDays = daysOfPlan($plan);
        $startsAt = Carbon::now();
        $endsAt = $startsAt->copy()->addDays($newPlanDays);

        $subscriptionInput = [
            'user_id'       => Auth::id(),
            'plan_id'       => $plan->id,
            'price_of_plan' => $plan->price,
            'plan_type'     => $plan->type,
            'start_date'    => $startsAt,
            'end_date'      => $endsAt,
        ];

        return Subscription::create($subscriptionInput);
    }

    /**
     * @param $input
     */
    public function updateSubscriptionEndDate($input): void
    {
        Subscription::whereId($input['subscription_id'])->update(['end_date' => $input['end_date']]);
    }

    /**
     * @param $code
     * @param $amountToPay
     * @return float|HigherOrderBuilderProxy|int|mixed|null
     */
    public function applyCouponCode($code, $amountToPay)
    {
        try {
            $couponCode = CouponCode::whereCode($code)->first();
            if ($couponCode->type == CouponCode::DISCOUNT) {
                $amountToPay = $amountToPay - ($amountToPay * $couponCode->discount) / 100;
            } else {
                $amountToPay -= $couponCode->discount;
            }

            return $amountToPay;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @return array
     */
    public function remainingSubscriptionDays(): array
    {
        $subscription = currentActiveSubscription();
        $endDate = !empty($subscription->trial_ends_at) ? 'trial_ends_at' : 'end_date';
        $expirePlanDays = Carbon::parse($subscription->start_date)->diffInDays($subscription->$endDate);
        $paymentRequest = Transaction::wherePaymentMode(Subscription::TYPE_MANUAL)->whereUserId(getLoginUserId())
            ->whereIsManualPayment(Transaction::PENDING)->exists();

        $data['plan_expire_notification'] = $expirePlanDays <= getSettingValue('plan_expire_notification') ? 1 : 0;
        $data['remaining_days_of_subscription'] = $expirePlanDays;
        $data['user_status'] = Auth::user()->status;
        $data['plan_expired'] = currentActiveSubscription()->isExpired();
        $data['allow_create_business_card'] = currentActiveSubscription()->plan->planFeatures->allow_create_business_card;
        $data['has_manual_request'] = $paymentRequest ? 1 : 0;

        return $data;
    }
}
