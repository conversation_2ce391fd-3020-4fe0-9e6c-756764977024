<?php

namespace App\Repositories;

use App\Models\DigitalBusinessCard;
use App\Models\Role;
use App\Models\SocialLink;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 *
 * Class DigitalBusinessCardRepository
 */
class DigitalBusinessCardRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'job_title',
        'company',
        'phone',
        'email',
        'website',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable(): array
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return DigitalBusinessCard::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function store($input): mixed
    {
        try {
            DB::beginTransaction();
            $input['status'] = $input['status'] == 1 ? true : false;

            if (Auth::user()->hasRole(Role::USER)) {
                $input['tenant_id'] = Auth::user()->tenant_id;
            }

            $digitalBusinessCard = $this->create($input);

            if (!empty($input['profile_image'])) {
                $digitalBusinessCard->addMedia($input['profile_image'])->toMediaCollection(DigitalBusinessCard::PROFILE_PATH, config('app.media_disc'));
            }

            if (!empty($input['cover_image'])) {
                $digitalBusinessCard->addMedia($input['cover_image'])->toMediaCollection(DigitalBusinessCard::COVER_PATH, config('app.media_disc'));
            }

            DB::commit();

            return $digitalBusinessCard;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $updateInput
     * @param $id
     * @return mixed
     */
    public function updateDigitalBusinessCard($updateInput, $id): mixed
    {
        try {
            DB::beginTransaction();
            $updateInput['status'] = $updateInput['status'] == 1 ? true : false;
            $updateDigitalBusinessCard = $this->update($updateInput, $id);

            if (!empty($updateInput['profile_image'])) {
                $updateDigitalBusinessCard->clearMediaCollection(DigitalBusinessCard::PROFILE_PATH);
                $updateDigitalBusinessCard->addMedia($updateInput['profile_image'])->toMediaCollection(DigitalBusinessCard::PROFILE_PATH, config('app.media_disc'));
            }

            if (!empty($updateInput['cover_image'])) {
                $updateDigitalBusinessCard->clearMediaCollection(DigitalBusinessCard::COVER_PATH);
                $updateDigitalBusinessCard->addMedia($updateInput['cover_image'])->toMediaCollection(DigitalBusinessCard::COVER_PATH, config('app.media_disc'));
            }

            DB::commit();

            return $updateDigitalBusinessCard;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param mixed $input
     * @return mixed
     */
    public function storeSocialLinks($input)
    {
        try {
            DB::beginTransaction();

            $socialLinksInput = Arr::except($input['social_links'], 'business_card_id');
            $socialLinksIds = [];

            if (isset($socialLinksInput) && !empty($socialLinksInput)) {
                foreach ($socialLinksInput as $key => $val) {

                    if (isset($val['id']) && !empty($val['id'])) {
                        $updateSocialLink = SocialLink::whereId($val['id'])->first();

                        if (!empty($updateSocialLink)) {
                            if ($val['icon'] != null && str_contains($val['icon'], 'base64')) {
                                $updateSocialLink->clearMediaCollection(SocialLink::PATH);
                                $updateSocialLink->media()->delete();
                                $updateSocialLink->addMediaFromBase64($val['icon'])->toMediaCollection(SocialLink::PATH, config('app.media_disc'));
                            }
                        }

                        $updateSocialLink->update(['link' => $val['link']]);

                        array_push($socialLinksIds, $updateSocialLink->id);
                    } else {
                        $socialLink = SocialLink::create($val);

                        if ($val['icon'] != null && str_contains($val['icon'], 'base64')) {
                            $socialLink->addMediaFromBase64($val['icon'])->toMediaCollection(SocialLink::PATH, config('app.media_disc'));
                        }

                        array_push($socialLinksIds, $socialLink->id);
                    }
                }
            }

            $businesCard = $this->find($input['business_card_id']);

            $businesCard->socialLinks()->sync($socialLinksIds);
            $socialLinks = $businesCard->socialLinks()->get();

            DB::commit();

            return $socialLinks;
        } catch (Exception $ex) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($ex->getMessage());
        }
    }

    /**
     * @param mixed $updateInput
     * @param mixed $id
     * @return mixed
     */
    public function updateAdminSocialLink($updateInput, $id)
    {
        try {
            DB::beginTransaction();
            $tenantId = $updateInput['tenant_id'];

            $socialLinksInput = Arr::except($updateInput['social_links'], 'tenant_id');
            $socialLinksIds = [];

            if (isset($socialLinksInput) && !empty($socialLinksInput)) {
                foreach ($socialLinksInput as $key => $val) {

                    if (isset($val['id']) && !empty($val['id'])) {
                        $updateSocialLink = SocialLink::whereId($val['id'])->first();

                        if (!empty($updateSocialLink)) {
                            if ($val['icon'] != null && str_contains($val['icon'], 'base64')) {
                                $updateSocialLink->clearMediaCollection(SocialLink::PATH);
                                $updateSocialLink->media()->delete();
                                $updateSocialLink->addMediaFromBase64($val['icon'])->toMediaCollection(SocialLink::PATH, config('app.media_disc'));
                            }
                        }

                        $updateSocialLink->update(['link' => $val['link']]);

                        array_push($socialLinksIds, $updateSocialLink->id);
                    } else {
                        $socialLink = SocialLink::create($val);

                        if ($val['icon'] != null && str_contains($val['icon'], 'base64')) {
                            $socialLink->addMediaFromBase64($val['icon'])->toMediaCollection(SocialLink::PATH, config('app.media_disc'));
                        }

                        array_push($socialLinksIds, $socialLink->id);
                    }
                }
            }

            $businesCard = $this->where('tenant_id', $tenantId)->find($id);

            $businesCard->socialLinks()->sync($socialLinksIds);
            $socialLinks = $businesCard->socialLinks()->get();

            DB::commit();

            return $socialLinks;
        } catch (Exception $ex) {
            DB::rollBack();

            throw new UnprocessableEntityHttpException($ex->getMessage());
        }
    }
}
