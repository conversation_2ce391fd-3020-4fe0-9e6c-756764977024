<?php

namespace App\Repositories;

use App\Models\Currency;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Class CurrencyRepository
 */
class CurrencyRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name',
        'code',
        'symbol',
        'created_at',
    ];

    /**
     * Return searchable fields
     *
     * @return array
     */
    public function getFieldsSearchable()
    {
        return $this->fieldSearchable;
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Currency::class;
    }

    /**
     * @param $input
     * @return mixed
     */
    public function storeCurrency($input): mixed
    {
        try {
            DB::beginTransaction();
            $currency = $this->create($input);
            DB::commit();

            return $currency;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

    /**
     * @param $input
     * @param $id
     *
     * @return mixed
     */
    public function updateCurrency($input, $id): mixed
    {
        try {
            DB::beginTransaction();
            $currency = $this->update($input, $id);
            DB::commit();

            return $currency;
        } catch (Exception $e) {
            DB::rollBack();
            throw new UnprocessableEntityHttpException($e->getMessage());
        }
    }

}
