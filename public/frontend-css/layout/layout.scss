@import "variable.scss";

header {
    .navbar {
        padding: 25px 0 !important;
        @media (max-width: 991px) {
            padding: 20px 0 !important;
        }
        .navbar-brand {
            .navbar-logo {
                max-height: 40px !important;
                height: 40px !important;
            }
        }
        .navbar-toggler {
            @media (max-width: 991px) {
                box-shadow: none !important;
            }
            .navbar-toggler-icon {
                background-image: none !important;
                height: 26px !important;
                width: 26px !important;
                position: relative !important;
                margin: auto !important;
                cursor: pointer !important;
                .icon-bar {
                    position: absolute !important;
                    height: 2px !important;
                    background-color: $dark !important;
                    width: 26px !important;
                    display: block !important;
                    border-radius: 2px !important;
                    margin-top: 4px !important;
                    transition: 0.35s ease all !important;
                }
                .top-bar {
                    top: 0px !important;
                }
                .middle-bar {
                    top: 7px !important;
                    opacity: 1 !important;
                    width: 20px !important;
                }
                .bottom-bar {
                    top: 14px !important;
                }
                &.open {
                    .top-bar {
                        top: 7px !important;
                        animation: rotatedown 0.3s forwards !important;
                        animation-delay: 0.3s !important;
                    }
                    .middle-bar {
                        opacity: 0 !important;
                        width: 0% !important;
                    }
                    .bottom-bar {
                        top: 7px !important;
                        animation: rotateup 0.3s forwards !important;
                        animation-delay: 0.3s !important;
                    }
                }
            }
            &:focus {
                box-shadow: none !important;
            }
        }
        .navbar-collapse {
            .navbar-nav {
                .nav-item {
                    .nav-link {
                        padding: 0 20px !important;
                        font-size: 18px !important;
                        color: $dark !important;
                        @media (max-width: 991px) {
                            padding: 0 0 5px 0 !important;
                        }
                    }
                }
            }
            @media (max-width: 991px) {
                position: absolute !important;
                width: calc(100% - 24px) !important;
                top: 100% !important;
                background: #fff !important;
                left: 0 !important;
                right: 0 !important;
                margin: auto !important;
                max-width: 696px !important;
                border-radius: 0.625rem !important;
                box-shadow: 0 0 20px rgb(173 181 189 / 38%) !important;
                z-index: 1024 !important;
                padding: 20px !important;
            }
            @media (max-width: 767px) {
                max-width: 516px !important;
            }
        }
    }
}

@keyframes rotatedown {
    from {
        transform: rotate(0deg) !important;
    }
    to {
        transform: rotate(-45deg) !important;
    }
}
@keyframes rotateup {
    from {
        transform: rotate(0deg) !important;
    }
    to {
        transform: rotate(45deg) !important;
    }
}

footer {
    .footer-logo {
        @media (max-width: 919px) {
            justify-content: center;
        }
        .footer-logo-img {
            max-height: 40px !important;
            height: 40px !important;
        }
    }
}
footer > :nth-child(1) > :nth-child(1) > :nth-child(2){
    @media (max-width: 919px) {
        text-align: center;
    }
}


