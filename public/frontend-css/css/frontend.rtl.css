/*!**********************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!./resources/qr-builder/src/assets/scss/frontend/frontend.scss ***!
  \**********************************************************************************************************************************************************************************/
@font-face {
  font-family: "Circular Std" !important;
  font-style: normal !important;
  font-weight: 400 !important;
  src: url(../c1441dbff704baa78817.otf) format("truetype") !important;
  font-display: swap !important;
}
@font-face {
  font-family: "Circular Std" !important;
  font-style: normal !important;
  font-weight: 500 !important;
  src: url(../d19dcc38a149cb85c5d8.otf) format("truetype") !important;
  font-display: swap !important;
}
@font-face {
  font-family: "Circular Std" !important;
  font-style: normal !important;
  font-weight: 700 !important;
  src: url(../c35da9cdcefd0d8b8f1c.otf) format("truetype") !important;
  font-display: swap !important;
}
body {
  font-weight: 500 !important;
}

.bg_secondary {
  background-color: #281a77 !important;
}

.zIndex-3 {
  z-index: 3 !important;
}

.border-radius-10 {
  border-radius: 10px !important;
}

.mt-40 {
  margin-top: 40px;
}
@media (max-width: 575px) {
  .mt-40 {
    margin-top: 30px;
  }
}

.mt-20 {
  margin-top: 20px !important;
}
@media (max-width: 575px) {
  .mt-20 {
    margin-top: 10px !important;
  }
}

.mt-100 {
  margin-top: 100px !important;
}
@media (max-width: 575px) {
  .mt-100 {
    margin-top: 75px !important;
  }
}

.mt-30 {
  margin-top: 30px !important;
}
@media (max-width: 575px) {
  .mt-30 {
    margin-top: 20px !important;
  }
}

.ls-1 {
  letter-spacing: 1.5px !important;
}

.px-100 {
  padding-right: 100px !important;
  padding-left: 100px !important;
}

.pt-100 {
  padding-top: 100px !important;
}
@media (max-width: 991px) {
  .pt-100 {
    padding-top: 50px !important;
  }
}

.pb-100 {
  padding-bottom: 100px !important;
}
@media (max-width: 991px) {
  .pb-100 {
    padding-bottom: 50px !important;
  }
}

.pt-40 {
  padding-top: 40px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.p-40 {
  padding: 40px !important;
}

.pt-60 {
  padding-top: 60px !important;
}
@media (max-width: 991px) {
  .pt-60 {
    padding-top: 50px !important;
  }
}

.pb-60 {
  padding-bottom: 60px !important;
}
@media (max-width: 991px) {
  .pb-60 {
    padding-bottom: 50px !important;
  }
}

.mb-40 {
  margin-bottom: 40px !important;
}
@media (max-width: 991px) {
  .mb-40 {
    margin-bottom: 30px !important;
  }
}

.mb-50 {
  margin-bottom: 50px !important;
}
@media (max-width: 991px) {
  .mb-50 {
    margin-bottom: 40px !important;
  }
}

.fs-14 {
  font-size: 14px !important;
}

.fs-16 {
  font-size: 16px !important;
}
@media (max-width: 575px) {
  .fs-16 {
    font-size: 14px !important;
  }
}

.fs-18 {
  font-size: 18px !important;
}
@media (max-width: 575px) {
  .fs-18 {
    font-size: 16px !important;
  }
}

.fs-20 {
  font-size: 20px !important;
}
@media (max-width: 991px) {
  .fs-20 {
    font-size: 18px !important;
  }
}

.fs-24 {
  font-size: 24px !important;
}
@media (max-width: 991px) {
  .fs-24 {
    font-size: 22px !important;
  }
}
@media (max-width: 575px) {
  .fs-24 {
    font-size: 20px !important;
  }
}

.fs-28 {
  font-size: 28px !important;
}
@media (max-width: 991px) {
  .fs-28 {
    font-size: 26px !important;
  }
}
@media (max-width: 575px) {
  .fs-28 {
    font-size: 24px !important;
  }
}

.fs-30 {
  font-size: 30px !important;
}
@media (max-width: 991px) {
  .fs-30 {
    font-size: 26px !important;
  }
}
@media (max-width: 575px) {
  .fs-30 {
    font-size: 22px !important;
  }
}

.fs-34 {
  font-size: 34px !important;
}
@media (max-width: 991px) {
  .fs-34 {
    font-size: 30px !important;
  }
}
@media (max-width: 575px) {
  .fs-34 {
    font-size: 26px !important;
  }
}

.fw-700 {
  font-weight: 700 !important;
}

.fw-100 {
  font-weight: 100 !important;
}

.fw-600 {
  font-weight: 600 !important;
}

.fw-500 {
  font-weight: 500 !important;
}

.fw-4 {
  font-weight: 400 !important;
}

a {
  text-decoration: none !important;
}

h1,
.fs-54 {
  font-size: 54px !important;
  line-height: 64px !important;
}
@media (max-width: 991px) {
  h1,
  .fs-54 {
    font-size: 40px !important;
    line-height: 50px !important;
  }
}
@media (max-width: 480px) {
  h1,
  .fs-54 {
    font-size: 33px !important;
    line-height: 43px !important;
  }
}

h2,
.fs-40 {
  font-size: 40px !important;
}
@media (max-width: 991px) {
  h2,
  .fs-40 {
    font-size: 36px !important;
  }
}
@media (max-width: 575px) {
  h2,
  .fs-40 {
    font-size: 30px !important;
  }
}

.object-fit-cover {
  object-fit: cover !important;
}

.img-fluid {
  max-width: 100% !important;
  height: auto !important;
}

.text-gray-100 {
  color: #f5f5f5 !important;
}

.bg-gray-100 {
  background-color: #f5f5f5 !important;
}

.text-gray-200 {
  color: #7f7c89 !important;
}

.text-gray-300 {
  color: #dddddd !important;
}

.text-secondary {
  color: #281a77 !important;
}

.text-primary {
  color: #ff9900 !important;
}

.bg_secondary {
  background-color: #281a77 !important;
}

.bg_primary {
  background-color: #ff9900 !important;
}

.bg-secondary-light {
  background-color: rgba(40, 26, 119, 0.1254901961) !important;
}

.shadow {
  box-shadow: 0px 3px 25px rgba(0, 0, 0, 0.1) !important;
}

.plan-btn {
  padding: 10px 30px !important;
  font-weight: 500 !important;
  box-shadow: none !important;
  background-color: transparent !important;
}
.plan-btn:focus {
  box-shadow: none !important;
}

.btn {
  border-radius: 25px !important;
  padding: 10px 30px !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}
.btn:focus {
  box-shadow: none !important;
}

.btn-primary-light {
  border: 1px solid #ff9900 !important;
  background-color: transparent !important;
  color: #ffffff !important;
  box-shadow: none !important;
}
.btn-primary-light:hover, .btn-primary-light:focus, .btn-primary-light:active .btn-primary-light.active {
  box-shadow: none !important;
  background-color: #ff9900 !important;
  color: #ffffff !important;
  border: 1px solid #ff9900 !important;
}

.btn-primary {
  border: 1px solid #ff9900 !important;
  background-color: #ff9900 !important;
  color: #ffffff !important;
  box-shadow: none !important;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active {
  box-shadow: none !important;
  background-color: transparent !important;
  color: #ffffff !important;
  border: 1px solid #ff9900 !important;
}

.auth_pages .btn-primary:hover {
  color: #ff9900 !important;
}

.form-check-input[type=checkbox] {
  border-radius: 2px !important;
}

.form-check-input:checked {
  background-color: #281a77 !important;
  border-color: #281a77 !important;
}

.form-check-input:focus {
  box-shadow: none !important;
  border: 1px solid #281a77 !important;
}

.subscribe-section {
  position: relative;
  z-index: 2;
  transform: translateY(55px) !important;
}
.subscribe-section .subscribe-box {
  padding: 45px 60px !important;
  border-radius: 20px !important;
}
@media (max-width: 991px) {
  .subscribe-section .subscribe-box {
    padding: 40px 50px !important;
  }
}
@media (max-width: 575px) {
  .subscribe-section .subscribe-box {
    padding: 25px 30px !important;
  }
}
.subscribe-section .subscribe-box .form-control {
  padding: 10px 20px !important;
  height: 50px !important;
  background-color: #ffffff !important;
  font-size: 18px !important;
  color: #7f7c89 !important;
  border-radius: 10px !important;
  border: 0 !important;
}
.subscribe-section .subscribe-box .form-control:focus {
  box-shadow: none !important;
}
.subscribe-section .subscribe-box .form-control.subscribe-input {
  padding-left: 160px !important;
}
@media (max-width: 575px) {
  .subscribe-section .subscribe-box .form-control.subscribe-input {
    padding-left: 20px !important;
  }
}
.subscribe-section .subscribe-box .subscribe-btn {
  background-color: #ff9900 !important;
  color: #ffffff !important;
  border: 1px solid #ff9900 !important;
  border-radius: 10px 0 0 10px !important;
  font-size: 18px !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
}
@media (max-width: 575px) {
  .subscribe-section .subscribe-box .subscribe-btn {
    position: relative !important;
    display: flex !important;
    margin: 20px auto 0 auto !important;
    border-radius: 10px !important;
  }
}

.terms_condition,
.privacy_policy {
  background-image: radial-gradient(circle farthest-corner at 10% 20%, rgba(166, 239, 253, 0.25) 0%, rgba(166, 239, 253, 0.25) 90.1%);
  z-index: 0;
}
.terms_condition .dangerously_set_html,
.privacy_policy .dangerously_set_html {
  width: 90% !important;
  padding: 20px !important;
  text-align: justify !important;
}
.terms_condition .dangerously_set_html > p,
.privacy_policy .dangerously_set_html > p {
  font-weight: 200 !important;
}
.terms_condition .dangerously_set_html > p > strong,
.privacy_policy .dangerously_set_html > p > strong {
  font-weight: bold !important;
}
.terms_condition .dangerously_set_html > ul,
.terms_condition .dangerously_set_html > * > ul,
.terms_condition .dangerously_set_html > * > * > ul,
.privacy_policy .dangerously_set_html > ul,
.privacy_policy .dangerously_set_html > * > ul,
.privacy_policy .dangerously_set_html > * > * > ul {
  margin-right: 2rem !important;
  font-weight: 200 !important;
}
.terms_condition .dangerously_set_html > ul > strong,
.terms_condition .dangerously_set_html > ul strong,
.terms_condition .dangerously_set_html > ul > li strong,
.terms_condition .dangerously_set_html > * > ul > strong,
.terms_condition .dangerously_set_html > * > ul strong,
.terms_condition .dangerously_set_html > * > ul > li strong,
.terms_condition .dangerously_set_html > * > * > ul > strong,
.terms_condition .dangerously_set_html > * > * > ul strong,
.terms_condition .dangerously_set_html > * > * > ul > li strong,
.privacy_policy .dangerously_set_html > ul > strong,
.privacy_policy .dangerously_set_html > ul strong,
.privacy_policy .dangerously_set_html > ul > li strong,
.privacy_policy .dangerously_set_html > * > ul > strong,
.privacy_policy .dangerously_set_html > * > ul strong,
.privacy_policy .dangerously_set_html > * > ul > li strong,
.privacy_policy .dangerously_set_html > * > * > ul > strong,
.privacy_policy .dangerously_set_html > * > * > ul strong,
.privacy_policy .dangerously_set_html > * > * > ul > li strong {
  font-weight: bold !important;
}
.terms_condition .dangerously_set_html > ol,
.terms_condition .dangerously_set_html > * > ol,
.terms_condition .dangerously_set_html > * > * > ol,
.privacy_policy .dangerously_set_html > ol,
.privacy_policy .dangerously_set_html > * > ol,
.privacy_policy .dangerously_set_html > * > * > ol {
  font-weight: 200 !important;
}
.terms_condition .dangerously_set_html > ol > strong,
.terms_condition .dangerously_set_html > ol strong,
.terms_condition .dangerously_set_html > ol > li strong,
.terms_condition .dangerously_set_html > * > ol > strong,
.terms_condition .dangerously_set_html > * > ol strong,
.terms_condition .dangerously_set_html > * > ol > li strong,
.terms_condition .dangerously_set_html > * > * > ol > strong,
.terms_condition .dangerously_set_html > * > * > ol strong,
.terms_condition .dangerously_set_html > * > * > ol > li strong,
.privacy_policy .dangerously_set_html > ol > strong,
.privacy_policy .dangerously_set_html > ol strong,
.privacy_policy .dangerously_set_html > ol > li strong,
.privacy_policy .dangerously_set_html > * > ol > strong,
.privacy_policy .dangerously_set_html > * > ol strong,
.privacy_policy .dangerously_set_html > * > ol > li strong,
.privacy_policy .dangerously_set_html > * > * > ol > strong,
.privacy_policy .dangerously_set_html > * > * > ol strong,
.privacy_policy .dangerously_set_html > * > * > ol > li strong {
  font-weight: bold !important;
}
.terms_condition p,
.terms_condition ul,
.terms_condition ol,
.privacy_policy p,
.privacy_policy ul,
.privacy_policy ol {
  font-size: 17.5px !important;
  line-height: 1.625 !important;
  font-weight: 300 !important;
}
.terms_condition p.mb-0,
.terms_condition ul.mb-0,
.terms_condition ol.mb-0,
.privacy_policy p.mb-0,
.privacy_policy ul.mb-0,
.privacy_policy ol.mb-0 {
  margin-bottom: 0 !important;
}
.terms_condition .feature-bg,
.privacy_policy .feature-bg {
  position: absolute;
  z-index: -1;
}
.terms_condition .feature-bg.vector-1,
.privacy_policy .feature-bg.vector-1 {
  right: 0;
  top: 0px;
  left: 0;
  z-index: -1;
}
.terms_condition .feature-bg.vector-2,
.privacy_policy .feature-bg.vector-2 {
  left: 0;
  top: 100rem;
  z-index: -1;
}
.terms_condition .feature-bg.contact_side_vector1,
.privacy_policy .feature-bg.contact_side_vector1 {
  left: 3.125rem;
  top: 50%;
  z-index: -1 !important;
}
.terms_condition .qrcode-bg,
.privacy_policy .qrcode-bg {
  position: absolute;
  z-index: -1;
}
.terms_condition .qrcode-bg.vector,
.privacy_policy .qrcode-bg.vector {
  right: 3rem;
  top: 45rem;
}
.terms_condition .qrcode-bg.vector-1,
.privacy_policy .qrcode-bg.vector-1 {
  right: 0;
  left: 0;
  bottom: 100rem;
}
.terms_condition .qrcode-bg.vector-2,
.privacy_policy .qrcode-bg.vector-2 {
  left: 0;
  right: 0;
  bottom: 0px;
}
.terms_condition .qrpersonal-bg,
.privacy_policy .qrpersonal-bg {
  position: absolute;
  z-index: -1;
}
@media (max-width: 991px) {
  .terms_condition .qrpersonal-bg img,
  .privacy_policy .qrpersonal-bg img {
    width: 70%;
  }
}
@media (max-width: 575px) {
  .terms_condition .qrpersonal-bg img,
  .privacy_policy .qrpersonal-bg img {
    width: 50%;
  }
}
.terms_condition .qrpersonal-bg.vector-1,
.privacy_policy .qrpersonal-bg.vector-1 {
  left: 0;
  top: 175rem;
}
.terms_condition .qrpersonal-bg.vector-2,
.privacy_policy .qrpersonal-bg.vector-2 {
  right: 0;
  bottom: 175rem;
}
.terms_condition .background,
.privacy_policy .background {
  background-color: #fff !important;
  background: #fff !important;
}

.bg_dark_blue {
  background-color: #281a77 !important;
}

.hero-section {
  z-index: 2;
}
.hero-section .hero-img {
  max-width: 510px !important;
  width: 100% !important;
}

.feature-section .feature-bg {
  position: absolute;
  z-index: -1;
}
.feature-section .feature-bg.vector-1 {
  right: 0;
  top: -220px;
  z-index: 1;
}
@media (max-width: 1199px) {
  .feature-section .feature-bg.vector-1 {
    top: -150px;
  }
}
@media (max-width: 768px) {
  .feature-section .feature-bg.vector-1 {
    top: -100px;
  }
}
@media (max-width: 575px) {
  .feature-section .feature-bg.vector-1 {
    top: -50px;
  }
}
.feature-section .feature-bg.vector-2 {
  left: 0;
  bottom: -130px;
  z-index: 1;
}
@media (max-width: 1199px) {
  .feature-section .feature-bg.vector-2 {
    bottom: -50px;
  }
}
@media (max-width: 575px) {
  .feature-section .feature-bg.vector-2 {
    bottom: -20px;
  }
}
.feature-section .feature-section-container {
  position: relative;
  z-index: 2;
}
.feature-section .feature-img {
  max-width: 480px !important;
  width: 100% !important;
}
.feature-section .feature-content .check-list-img {
  width: 20px !important;
  margin-left: 15px !important;
}

.qr-personal-section {
  position: relative;
  z-index: 2;
}
.qr-personal-section .qrpersonal-bg {
  position: absolute;
  z-index: 0;
}
@media (max-width: 991px) {
  .qr-personal-section .qrpersonal-bg img {
    width: 70%;
  }
}
@media (max-width: 575px) {
  .qr-personal-section .qrpersonal-bg img {
    width: 50%;
  }
}
.qr-personal-section .qrpersonal-bg.vector-1 {
  right: 0;
  top: -50px;
}
@media (max-width: 991px) {
  .qr-personal-section .qrpersonal-bg.vector-1 {
    top: -35px;
  }
}
@media (max-width: 575px) {
  .qr-personal-section .qrpersonal-bg.vector-1 {
    top: -25px;
  }
}
.qr-personal-section .qrpersonal-bg.vector-2 {
  left: 0;
  bottom: -58px;
}
@media (max-width: 991px) {
  .qr-personal-section .qrpersonal-bg.vector-2 {
    bottom: -42px;
  }
}
@media (max-width: 575px) {
  .qr-personal-section .qrpersonal-bg.vector-2 {
    bottom: -28px;
  }
}
.qr-personal-section .qr-personal-img {
  max-width: 460px !important;
  width: 100% !important;
}
.qr-personal-section .qr-personal-content .check-list-img {
  width: 20px !important;
  margin-left: 15px !important;
}

.qr-code-types-section .qrcode-bg {
  position: absolute;
}
.qr-code-types-section .qrcode-bg.vector {
  right: 35px;
  bottom: 160px;
}
.qr-code-types-section .qrcode-bg.vector-1 {
  right: 0;
  top: 75px;
}
.qr-code-types-section .qrcode-bg.vector-2 {
  left: 0;
  bottom: -150px;
}
.qr-code-types-section .qr-types-card {
  height: 100% !important;
  border: 0 !important;
  border-radius: 20px !important;
  padding: 25px !important;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.5s !important;
}
.qr-code-types-section .qr-types-card:hover, .qr-code-types-section .qr-types-card:focus, .qr-code-types-section .qr-types-card.active, .qr-code-types-section .qr-types-card:active {
  background-color: #281a77 !important;
}
.qr-code-types-section .qr-types-card:hover .card-body, .qr-code-types-section .qr-types-card:focus .card-body, .qr-code-types-section .qr-types-card.active .card-body, .qr-code-types-section .qr-types-card:active .card-body {
  color: #ffffff !important;
}
.qr-code-types-section .qr-types-card:hover .type-btn, .qr-code-types-section .qr-types-card:focus .type-btn, .qr-code-types-section .qr-types-card.active .type-btn, .qr-code-types-section .qr-types-card:active .type-btn {
  color: #ff9900 !important;
}
.qr-code-types-section .qr-types-card .card-img-top {
  width: auto !important;
  height: 45px !important;
}
.qr-code-types-section .qr-types-card .card-body {
  padding-top: 30px !important;
  padding-bottom: 20px !important;
  color: #29282f !important;
}
.qr-code-types-section .qr-types-card .type-btn {
  width: 20px !important;
  height: 20px !important;
  font-size: 20px !important;
  display: flex !important;
  color: #281a77 !important;
  cursor: pointer !important;
}

.get-in-touch-section .feature-bg {
  position: absolute;
  z-index: -1;
}
.get-in-touch-section .feature-bg.vector-1 {
  right: 0;
  top: -220px;
  z-index: 1;
}
@media (max-width: 1199px) {
  .get-in-touch-section .feature-bg.vector-1 {
    top: -150px;
  }
}
@media (max-width: 768px) {
  .get-in-touch-section .feature-bg.vector-1 {
    top: -100px;
  }
}
@media (max-width: 575px) {
  .get-in-touch-section .feature-bg.vector-1 {
    top: -50px;
  }
}
.get-in-touch-section .feature-bg.contact_side_vector1 {
  left: 3.125rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1 !important;
}
.get-in-touch-section .contact-icon {
  width: 19px;
  height: 19px;
  min-width: 19px;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}
.get-in-touch-section .contact_info_section {
  height: 650px !important;
  overflow: hidden;
}
.get-in-touch-section .contact_big_circle {
  height: 269px !important;
  width: 269px !important;
  position: absolute;
  bottom: -4vh;
  left: -4vh;
}
.get-in-touch-section .contact_small_circle {
  height: 140px !important;
  width: 140px !important;
  position: absolute;
  bottom: 12vh;
  left: 12vh;
}
.get-in-touch-section .contact-form {
  border-radius: 10px;
  padding: 25px;
  position: relative;
  z-index: 2;
}
@media (max-width: 575px) {
  .get-in-touch-section .contact-form {
    padding: 15px;
  }
}
.get-in-touch-section .contact-form .contact_left_top {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}
.get-in-touch-section .contact-form .contact_right_top {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
}
.get-in-touch-section .contact-form .contact_left_bottom {
  position: absolute;
  right: 0;
  z-index: -1;
  bottom: 0;
}
.get-in-touch-section .contact-form .contact_right_bottom {
  position: absolute;
  bottom: 0;
  z-index: -1;
  left: 0;
}
.get-in-touch-section .contact-form form .form_label {
  font-weight: 500;
  font-size: 18px;
  color: #29282f;
  margin-bottom: 10px;
}
.get-in-touch-section .contact-form form .form-control {
  padding: 15px;
  height: 50px;
  background-color: white;
  border: 1px solid #dddddd;
  border-radius: 10px;
  color: #7f7c89;
}
@media (max-width: 575px) {
  .get-in-touch-section .contact-form form .form-control {
    height: 50px;
  }
}
.get-in-touch-section .contact-form form .form-control::placeholder {
  color: #7f7c89;
  font-size: 16px;
}
.get-in-touch-section .contact-form form .form-control:focus {
  outline: none;
  box-shadow: none;
}
.get-in-touch-section .contact-form form .btn-primary {
  border-radius: 10px !important;
  background-color: #281a77 !important;
  border-color: #281a77 !important;
  height: 60px !important;
  font-size: 18px;
  font-weight: 500;
}
.get-in-touch-section .contact-form form .btn-primary:hover, .get-in-touch-section .contact-form form .btn-primary:focus, .get-in-touch-section .contact-form form .btn-primary.active, .get-in-touch-section .contact-form form .btn-primary:active {
  background-color: transparent !important;
  border-color: #281a77 !important;
  color: #281a77 !important;
}

header .navbar {
  padding: 25px 0 !important;
}
@media (max-width: 991px) {
  header .navbar {
    padding: 20px 0 !important;
  }
}
header .navbar .navbar-brand .navbar-logo {
  max-height: 50px !important;
  height: 50px !important;
}
@media (max-width: 991px) {
  header .navbar .navbar-toggler {
    box-shadow: none !important;
  }
}
header .navbar .navbar-toggler .navbar-toggler-icon {
  background-image: none !important;
  height: 26px !important;
  width: 26px !important;
  position: relative !important;
  margin: auto !important;
  cursor: pointer !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .icon-bar {
  position: absolute !important;
  height: 2px !important;
  background-color: #ffffff !important;
  width: 26px !important;
  display: block !important;
  border-radius: 2px !important;
  margin-top: 4px !important;
  transition: 0.35s ease all !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .top-bar {
  top: 0px !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .middle-bar {
  top: 7px !important;
  opacity: 1 !important;
  width: 20px !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .bottom-bar {
  top: 14px !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon.open .top-bar {
  top: 7px !important;
  animation: rotateDown 0.3s forwards !important;
  animation-delay: 0.3s !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon.open .middle-bar {
  opacity: 0 !important;
  width: 0% !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon.open .bottom-bar {
  top: 7px !important;
  animation: rotateUp 0.3s forwards !important;
  animation-delay: 0.3s !important;
}
header .navbar .navbar-toggler:focus {
  box-shadow: none !important;
}
@media (max-width: 991px) {
  header .navbar .navbar-collapse {
    position: absolute !important;
    width: calc(100% - 24px) !important;
    top: 100% !important;
    background-color: #281a77 !important;
    left: 12px !important;
    margin: auto !important;
    max-width: 300px !important;
    border-radius: 0.625rem !important;
    box-shadow: 0 0 20px rgba(173, 181, 189, 0.38) !important;
    z-index: 1024 !important;
    padding: 20px !important;
  }
}
header .navbar .navbar-collapse .navbar-nav .nav-item {
  padding: 0 20px !important;
}
@media (max-width: 991px) {
  header .navbar .navbar-collapse .navbar-nav .nav-item {
    padding: 0 0 5px 0 !important;
    width: fit-content;
  }
}
header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link {
  padding: 0 !important;
  font-size: 18px !important;
  color: #ffffff !important;
  position: relative !important;
}
header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link:after {
  position: absolute !important;
  content: "" !important;
  width: 0 !important;
  height: 2px !important;
  bottom: -5px !important;
  margin: 0 auto !important;
  right: 0 !important;
  left: 0 !important;
  border-radius: 5px !important;
  background-color: #ff9900 !important;
  transition: 0.5s !important;
}
header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link.active:after, header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link:hover:after {
  width: 50px !important;
}

@keyframes rotateDown {
  from {
    transform: rotate(0deg) !important;
  }
  to {
    transform: rotate(45deg) !important;
  }
}
@keyframes rotateUp {
  from {
    transform: rotate(0deg) !important;
  }
  to {
    transform: rotate(-45deg) !important;
  }
}
footer {
  z-index: 1;
}
footer .footer-vector {
  right: 60px;
  top: -25px;
}
footer .footer-logo .footer-logo-img {
  max-height: 50px !important;
  height: 50px !important;
}

.pricing-plan-section .plan-card {
  margin: 0 5px;
  border: 2px solid #e4e3e9;
  border-radius: 20px;
  box-shadow: none !important;
}
.pricing-plan-section .plan-card:hover, .pricing-plan-section .plan-card:focus, .pricing-plan-section .plan-card.active {
  border: 2px solid #ff9900;
}
.pricing-plan-section .plan-card:hover .card-body .choose-plan-btn, .pricing-plan-section .plan-card:focus .card-body .choose-plan-btn, .pricing-plan-section .plan-card.active .card-body .choose-plan-btn {
  color: #ffffff !important;
  background-color: #281a77 !important;
}
.pricing-plan-section .plan-card .card-body {
  font-weight: 500;
  padding: 20px;
}
.pricing-plan-section .plan-card .card-body .most_popular {
  width: 55% !important;
}
.pricing-plan-section .plan-card .card-body #annual {
  display: none;
}
.pricing-plan-section .plan-card .card-body .price {
  color: #281a77;
}
.pricing-plan-section .plan-card .card-body .price span {
  color: #7f7c89;
}
.pricing-plan-section .plan-card .card-body .list li {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 400;
}
.pricing-plan-section .plan-card .card-body .list li .plan_check_icon_container {
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  color: white !important;
  margin-left: 8px !important;
}
.pricing-plan-section .plan-card .card-body .list li .plan_check_icon_container svg {
  width: 12px !important;
  height: 12px !important;
}
.pricing-plan-section .plan-card .card-body .list li span {
  font-weight: 500;
}
.pricing-plan-section .plan-card .card-body .choose-plan-btn {
  color: #281a77;
  box-shadow: 0px 2px 5px #afafaf !important;
  border: none !important;
  outline: none !important;
}
.pricing-plan-section .switch-field {
  width: 260px;
  height: 50px;
  position: relative;
  display: flex;
  padding: 4px;
  position: relative;
  background-color: #ffffff;
  line-height: 3rem;
  border-radius: 3rem;
  border: 1px solid #f5f5f5;
  border-radius: 25px;
  box-shadow: 0 10px 20px #f5f5f5;
}
@media (max-width: 575px) {
  .pricing-plan-section .switch-field {
    height: 50px;
  }
}
.pricing-plan-section .switch-field input {
  visibility: hidden;
  position: absolute;
  top: 0;
}
.pricing-plan-section .switch-field label {
  width: 50%;
  padding: 0;
  margin: 0;
  font-weight: 500;
  color: #29282f;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
}
.pricing-plan-section .switch-wrapper {
  position: absolute;
  top: 3px;
  bottom: 0;
  width: 48%;
  z-index: 3;
  transition: transform 0.5s cubic-bezier(0.77, 0, 0.175, 1);
}
.pricing-plan-section .switch {
  border-radius: 3rem;
  background-color: #ff9900;
  height: 42px;
}
.pricing-plan-section .switch div {
  width: 100%;
  text-align: center;
  opacity: 0;
  display: block;
  color: #ffffff;
  font-weight: 500;
  transition: opacity 0.2s cubic-bezier(0.77, 0, 0.175, 1) 0.125s;
  will-change: opacity;
  position: absolute;
  top: -3px;
  right: 0;
}
.pricing-plan-section .switch-field input:nth-of-type(1):checked ~ .switch-wrapper {
  transform: translateX(0%);
}
.pricing-plan-section .switch-field input:nth-of-type(2):checked ~ .switch-wrapper {
  transform: translateX(-100%);
}
.pricing-plan-section .switch-field input:nth-of-type(1):checked ~ .switch-wrapper .switch div:nth-of-type(1) {
  opacity: 1;
}
.pricing-plan-section .switch-field input:nth-of-type(2):checked ~ .switch-wrapper .switch div:nth-of-type(2) {
  opacity: 1;
}

.body {
  background-color: #eff3f7 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  height: 100vh !important;
}
@media (max-width: 768px) {
  .body {
    height: 100% !important;
  }
}

.flex-column-fluid {
  flex: 1 0 auto !important;
}

.width-540 {
  max-width: 540px !important;
  width: 100% !important;
}

.shadow-md {
  box-shadow: 0 5px 20px rgba(173, 181, 189, 0.2) !important;
}

.image {
  display: inline-block !important;
  flex-shrink: 0 !important;
  -o-object-fit: cover !important;
  object-fit: cover !important;
  position: relative !important;
}

.logo-fix-size {
  max-height: 60px !important;
  max-width: 120px !important;
  width: 100% !important;
  -o-object-fit: cover !important;
  object-fit: cover !important;
}

.sign-in-form {
  padding: 40px 20px !important;
  border-radius: 0.938rem !important;
}
@media (max-width: 576px) {
  .sign-in-form {
    padding: 25px !important;
  }
}
.sign-in-form form .form-group label {
  font-size: 16px !important;
  margin-bottom: 8px !important;
  font-weight: 400 !important;
}
.sign-in-form form .form-group .form-control {
  background-color: #fff !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.313rem !important;
  color: #6c757d !important;
  font-size: 15px !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  padding: 0.688rem 0.938rem !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
  width: 100% !important;
}
.sign-in-form form .form-group .form-control:focus {
  outline: 0 !important;
  box-shadow: none !important;
}
.sign-in-form form .btn-primary:hover, .sign-in-form form .btn-primary:focus, .sign-in-form form .btn-primary.active, .sign-in-form form .btn-primary:active {
  color: #ff9900 !important;
}

.required:after {
  color: #f62947 !important;
  content: "*" !important;
  font-size: inherit !important;
  font-weight: 700 !important;
  position: relative !important;
}

/* Slider */
.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}
.slick-slider .slick-track {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
.slick-list:focus {
  outline: none;
}

.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-track {
  position: relative;
  top: 0;
  right: 0;
  display: block;
  margin-right: auto;
  margin-left: auto;
}
.slick-track:before {
  display: table;
  content: "";
}
.slick-track:after {
  display: table;
  content: "";
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}
.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: right;
  height: 100%;
  min-height: 1px;
}
.slick-slide img {
  display: block;
}

[dir=rtl] .slick-slide {
  float: left;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}

.toast-card__toast-title {
  font-size: 20px !important;
}
