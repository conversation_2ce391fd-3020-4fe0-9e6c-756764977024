/*!*************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!./resources/qr-builder/src/assets/scss/frontend/frontheader.scss ***!
  \*************************************************************************************************************************************************************************************/
header .navbar {
  padding: 25px 0 !important;
}
@media (max-width: 991px) {
  header .navbar {
    padding: 20px 0 !important;
  }
}
header .navbar .navbar-brand .navbar-logo {
  max-height: 50px !important;
  height: 50px !important;
}
@media (max-width: 991px) {
  header .navbar .navbar-toggler {
    box-shadow: none !important;
  }
}
header .navbar .navbar-toggler .navbar-toggler-icon {
  background-image: none !important;
  height: 26px !important;
  width: 26px !important;
  position: relative !important;
  margin: auto !important;
  cursor: pointer !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .icon-bar {
  position: absolute !important;
  height: 2px !important;
  background-color: #ffffff !important;
  width: 26px !important;
  display: block !important;
  border-radius: 2px !important;
  margin-top: 4px !important;
  transition: 0.35s ease all !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .top-bar {
  top: 0px !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .middle-bar {
  top: 7px !important;
  opacity: 1 !important;
  width: 20px !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon .bottom-bar {
  top: 14px !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon.open .top-bar {
  top: 7px !important;
  animation: rotateDown 0.3s forwards !important;
  animation-delay: 0.3s !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon.open .middle-bar {
  opacity: 0 !important;
  width: 0% !important;
}
header .navbar .navbar-toggler .navbar-toggler-icon.open .bottom-bar {
  top: 7px !important;
  animation: rotateUp 0.3s forwards !important;
  animation-delay: 0.3s !important;
}
header .navbar .navbar-toggler:focus {
  box-shadow: none !important;
}
@media (max-width: 991px) {
  header .navbar .navbar-collapse {
    position: absolute !important;
    width: calc(100% - 24px) !important;
    top: 100% !important;
    background-color: #281a77 !important;
    right: 12px !important;
    margin: auto !important;
    max-width: 300px !important;
    border-radius: 0.625rem !important;
    box-shadow: 0 0 20px rgba(173, 181, 189, 0.38) !important;
    z-index: 1024 !important;
    padding: 20px !important;
  }
}
header .navbar .navbar-collapse .navbar-nav .nav-item {
  padding: 0 20px !important;
}
@media (max-width: 991px) {
  header .navbar .navbar-collapse .navbar-nav .nav-item {
    padding: 0 0 5px 0 !important;
    width: fit-content;
  }
}
header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link {
  padding: 0 !important;
  font-size: 18px !important;
  color: #ffffff !important;
  position: relative !important;
}
header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link:after {
  position: absolute !important;
  content: "" !important;
  width: 0 !important;
  height: 2px !important;
  bottom: -5px !important;
  margin: 0 auto !important;
  left: 0 !important;
  right: 0 !important;
  border-radius: 5px !important;
  background-color: #ff9900 !important;
  transition: 0.5s !important;
}
header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link.active:after, header .navbar .navbar-collapse .navbar-nav .nav-item .nav-link:hover:after {
  width: 50px !important;
}

@keyframes rotateDown {
  from {
    transform: rotate(0deg) !important;
  }
  to {
    transform: rotate(-45deg) !important;
  }
}
@keyframes rotateUp {
  from {
    transform: rotate(0deg) !important;
  }
  to {
    transform: rotate(45deg) !important;
  }
}
footer {
  z-index: 1;
}
footer .footer-vector {
  left: 60px;
  top: -25px;
}
footer .footer-logo .footer-logo-img {
  max-height: 50px !important;
  height: 50px !important;
}
