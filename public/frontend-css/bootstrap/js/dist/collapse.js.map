{"version": 3, "file": "collapse.js", "sources": ["../src/util/index.js", "../src/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: null\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(null|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let actives = []\n    let activesData\n\n    if (this._config.parent) {\n      const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._config.parent).filter(elem => !children.includes(elem)) // remove children if greater depth\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives.length) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Collapse.getInstance(tempActiveData) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    actives.forEach(elemActive => {\n      if (container !== elemActive) {\n        Collapse.getOrCreateInstance(elemActive, { toggle: false }).hide()\n      }\n\n      if (!activesData) {\n        Data.set(elemActive, DATA_KEY, null)\n      }\n    })\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    for (let i = 0; i < triggerArrayLength; i++) {\n      const trigger = this._triggerArray[i]\n      const elem = getElementFromSelector(trigger)\n\n      if (elem && !this._isShown(elem)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    SelectorEngine.find(SELECTOR_DATA_TOGGLE, this._config.parent).filter(elem => !children.includes(elem))\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        if (selected) {\n          this._addAriaAndCollapsedClass([element], this._isShown(selected))\n        }\n      })\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const _config = {}\n      if (typeof config === 'string' && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n"], "names": ["toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "<PERSON><PERSON><PERSON>", "toggle", "parent", "DefaultType", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "CLASS_NAME_DEEPER_CHILDREN", "CLASS_NAME_HORIZONTAL", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "SELECTOR_DATA_TOGGLE", "Collapse", "BaseComponent", "constructor", "_isTransitioning", "_config", "_getConfig", "_triggerArray", "toggleList", "SelectorEngine", "find", "i", "len", "elem", "filterElement", "filter", "foundElem", "_element", "_selector", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "actives", "activesData", "children", "container", "findOne", "tempActiveData", "getInstance", "startEvent", "EventHandler", "trigger", "defaultPrevented", "elemActive", "getOrCreateInstance", "Data", "set", "dimension", "_getDimension", "classList", "remove", "add", "style", "complete", "capitalizedDimension", "slice", "scrollSize", "_queueCallback", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contains", "Manipulator", "getDataAttributes", "Boolean", "selected", "trigger<PERSON><PERSON>y", "isOpen", "setAttribute", "each", "data", "on", "event", "target", "tagName", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "selectorElements"], "mappings": ";;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;EAOA,MAAMA,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAyBA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMU,sBAAsB,GAAGX,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAiCA,MAAMW,SAAS,GAAGnB,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACoB,MAAX,KAAsB,WAA1B,EAAuC;EACrCpB,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACqB,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGtB,GAAG,IAAI;EACxB,MAAImB,SAAS,CAACnB,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAACoB,MAAJ,GAAapB,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACuB,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOP,QAAQ,CAACC,aAAT,CAAuBjB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMwB,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAId,SAAS,CAACc,KAAD,CAAlB,GAA4B,SAA5B,GAAwClC,MAAM,CAACkC,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;EA+DA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMO,MAAM,GAAGhC,OAAO,IAAI;EACxB;EACAA,EAAAA,OAAO,CAACiC,YAAR;EACD,CAHD;;EAKA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAAC1B,QAAQ,CAAC4B,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOH,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMI,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIhC,QAAQ,CAACiC,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACvB,MAA/B,EAAuC;EACrCP,MAAAA,QAAQ,CAACkC,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAAChB,OAA1B,CAAkCkB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMI,kBAAkB,GAAGC,MAAM,IAAI;EACnCN,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMO,CAAC,GAAGb,SAAS,EAAnB;EACA;;EACA,QAAIa,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;ECvOA;EACA;EACA;EACA;EACA;EACA;EAgBA;EACA;EACA;EACA;EACA;;EAEA,MAAMH,IAAI,GAAG,UAAb;EACA,MAAMM,QAAQ,GAAG,aAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IADM;EAEdC,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,MAAMC,WAAW,GAAG;EAClBF,EAAAA,MAAM,EAAE,SADU;EAElBC,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,MAAME,UAAU,GAAI,OAAMN,SAAU,EAApC;EACA,MAAMO,WAAW,GAAI,QAAOP,SAAU,EAAtC;EACA,MAAMQ,UAAU,GAAI,OAAMR,SAAU,EAApC;EACA,MAAMS,YAAY,GAAI,SAAQT,SAAU,EAAxC;EACA,MAAMU,oBAAoB,GAAI,QAAOV,SAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMU,eAAe,GAAG,MAAxB;EACA,MAAMC,mBAAmB,GAAG,UAA5B;EACA,MAAMC,qBAAqB,GAAG,YAA9B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EACA,MAAMC,0BAA0B,GAAI,WAAUH,mBAAoB,KAAIA,mBAAoB,EAA1F;EACA,MAAMI,qBAAqB,GAAG,qBAA9B;EAEA,MAAMC,KAAK,GAAG,OAAd;EACA,MAAMC,MAAM,GAAG,QAAf;EAEA,MAAMC,gBAAgB,GAAG,sCAAzB;EACA,MAAMC,oBAAoB,GAAG,6BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,QAAN,SAAuBC,8BAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAAC/E,OAAD,EAAUmB,MAAV,EAAkB;EAC3B,UAAMnB,OAAN;EAEA,SAAKgF,gBAAL,GAAwB,KAAxB;EACA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgB/D,MAAhB,CAAf;EACA,SAAKgE,aAAL,GAAqB,EAArB;EAEA,UAAMC,UAAU,GAAGC,+BAAc,CAACC,IAAf,CAAoBV,oBAApB,CAAnB;;EAEA,SAAK,IAAIW,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGJ,UAAU,CAACpE,MAAjC,EAAyCuE,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;EACrD,YAAME,IAAI,GAAGL,UAAU,CAACG,CAAD,CAAvB;EACA,YAAMtF,QAAQ,GAAGO,sBAAsB,CAACiF,IAAD,CAAvC;EACA,YAAMC,aAAa,GAAGL,+BAAc,CAACC,IAAf,CAAoBrF,QAApB,EACnB0F,MADmB,CACZC,SAAS,IAAIA,SAAS,KAAK,KAAKC,QADpB,CAAtB;;EAGA,UAAI5F,QAAQ,KAAK,IAAb,IAAqByF,aAAa,CAAC1E,MAAvC,EAA+C;EAC7C,aAAK8E,SAAL,GAAiB7F,QAAjB;;EACA,aAAKkF,aAAL,CAAmBvC,IAAnB,CAAwB6C,IAAxB;EACD;EACF;;EAED,SAAKM,mBAAL;;EAEA,QAAI,CAAC,KAAKd,OAAL,CAAarB,MAAlB,EAA0B;EACxB,WAAKoC,yBAAL,CAA+B,KAAKb,aAApC,EAAmD,KAAKc,QAAL,EAAnD;EACD;;EAED,QAAI,KAAKhB,OAAL,CAAatB,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;EACF,GA/BkC;;;EAmCjB,aAAPD,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJT,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAzCkC;;;EA6CnCU,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKsC,QAAL,EAAJ,EAAqB;EACnB,WAAKC,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKnB,gBAAL,IAAyB,KAAKiB,QAAL,EAA7B,EAA8C;EAC5C;EACD;;EAED,QAAIG,OAAO,GAAG,EAAd;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKpB,OAAL,CAAarB,MAAjB,EAAyB;EACvB,YAAM0C,QAAQ,GAAGjB,+BAAc,CAACC,IAAf,CAAoBf,0BAApB,EAAgD,KAAKU,OAAL,CAAarB,MAA7D,CAAjB;EACAwC,MAAAA,OAAO,GAAGf,+BAAc,CAACC,IAAf,CAAoBX,gBAApB,EAAsC,KAAKM,OAAL,CAAarB,MAAnD,EAA2D+B,MAA3D,CAAkEF,IAAI,IAAI,CAACa,QAAQ,CAAClG,QAAT,CAAkBqF,IAAlB,CAA3E,CAAV,CAFuB;EAGxB;;EAED,UAAMc,SAAS,GAAGlB,+BAAc,CAACmB,OAAf,CAAuB,KAAKV,SAA5B,CAAlB;;EACA,QAAIM,OAAO,CAACpF,MAAZ,EAAoB;EAClB,YAAMyF,cAAc,GAAGL,OAAO,CAACd,IAAR,CAAaG,IAAI,IAAIc,SAAS,KAAKd,IAAnC,CAAvB;EACAY,MAAAA,WAAW,GAAGI,cAAc,GAAG5B,QAAQ,CAAC6B,WAAT,CAAqBD,cAArB,CAAH,GAA0C,IAAtE;;EAEA,UAAIJ,WAAW,IAAIA,WAAW,CAACrB,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,UAAM2B,UAAU,GAAGC,6BAAY,CAACC,OAAb,CAAqB,KAAKhB,QAA1B,EAAoC/B,UAApC,CAAnB;;EACA,QAAI6C,UAAU,CAACG,gBAAf,EAAiC;EAC/B;EACD;;EAEDV,IAAAA,OAAO,CAAC7E,OAAR,CAAgBwF,UAAU,IAAI;EAC5B,UAAIR,SAAS,KAAKQ,UAAlB,EAA8B;EAC5BlC,QAAAA,QAAQ,CAACmC,mBAAT,CAA6BD,UAA7B,EAAyC;EAAEpD,UAAAA,MAAM,EAAE;EAAV,SAAzC,EAA4DuC,IAA5D;EACD;;EAED,UAAI,CAACG,WAAL,EAAkB;EAChBY,QAAAA,qBAAI,CAACC,GAAL,CAASH,UAAT,EAAqBxD,QAArB,EAA+B,IAA/B;EACD;EACF,KARD;;EAUA,UAAM4D,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKvB,QAAL,CAAcwB,SAAd,CAAwBC,MAAxB,CAA+BlD,mBAA/B;;EACA,SAAKyB,QAAL,CAAcwB,SAAd,CAAwBE,GAAxB,CAA4BlD,qBAA5B;;EAEA,SAAKwB,QAAL,CAAc2B,KAAd,CAAoBL,SAApB,IAAiC,CAAjC;;EAEA,SAAKnB,yBAAL,CAA+B,KAAKb,aAApC,EAAmD,IAAnD;;EACA,SAAKH,gBAAL,GAAwB,IAAxB;;EAEA,UAAMyC,QAAQ,GAAG,MAAM;EACrB,WAAKzC,gBAAL,GAAwB,KAAxB;;EAEA,WAAKa,QAAL,CAAcwB,SAAd,CAAwBC,MAAxB,CAA+BjD,qBAA/B;;EACA,WAAKwB,QAAL,CAAcwB,SAAd,CAAwBE,GAAxB,CAA4BnD,mBAA5B,EAAiDD,eAAjD;;EAEA,WAAK0B,QAAL,CAAc2B,KAAd,CAAoBL,SAApB,IAAiC,EAAjC;EAEAP,MAAAA,6BAAY,CAACC,OAAb,CAAqB,KAAKhB,QAA1B,EAAoC9B,WAApC;EACD,KATD;;EAWA,UAAM2D,oBAAoB,GAAGP,SAAS,CAAC,CAAD,CAAT,CAAapF,WAAb,KAA6BoF,SAAS,CAACQ,KAAV,CAAgB,CAAhB,CAA1D;EACA,UAAMC,UAAU,GAAI,SAAQF,oBAAqB,EAAjD;;EAEA,SAAKG,cAAL,CAAoBJ,QAApB,EAA8B,KAAK5B,QAAnC,EAA6C,IAA7C;;EACA,SAAKA,QAAL,CAAc2B,KAAd,CAAoBL,SAApB,IAAkC,GAAE,KAAKtB,QAAL,CAAc+B,UAAd,CAA0B,IAA9D;EACD;;EAED1B,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKlB,gBAAL,IAAyB,CAAC,KAAKiB,QAAL,EAA9B,EAA+C;EAC7C;EACD;;EAED,UAAMU,UAAU,GAAGC,6BAAY,CAACC,OAAb,CAAqB,KAAKhB,QAA1B,EAAoC7B,UAApC,CAAnB;;EACA,QAAI2C,UAAU,CAACG,gBAAf,EAAiC;EAC/B;EACD;;EAED,UAAMK,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKvB,QAAL,CAAc2B,KAAd,CAAoBL,SAApB,IAAkC,GAAE,KAAKtB,QAAL,CAAciC,qBAAd,GAAsCX,SAAtC,CAAiD,IAArF;EAEAnF,IAAAA,MAAM,CAAC,KAAK6D,QAAN,CAAN;;EAEA,SAAKA,QAAL,CAAcwB,SAAd,CAAwBE,GAAxB,CAA4BlD,qBAA5B;;EACA,SAAKwB,QAAL,CAAcwB,SAAd,CAAwBC,MAAxB,CAA+BlD,mBAA/B,EAAoDD,eAApD;;EAEA,UAAM4D,kBAAkB,GAAG,KAAK5C,aAAL,CAAmBnE,MAA9C;;EACA,SAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwC,kBAApB,EAAwCxC,CAAC,EAAzC,EAA6C;EAC3C,YAAMsB,OAAO,GAAG,KAAK1B,aAAL,CAAmBI,CAAnB,CAAhB;EACA,YAAME,IAAI,GAAG9E,sBAAsB,CAACkG,OAAD,CAAnC;;EAEA,UAAIpB,IAAI,IAAI,CAAC,KAAKQ,QAAL,CAAcR,IAAd,CAAb,EAAkC;EAChC,aAAKO,yBAAL,CAA+B,CAACa,OAAD,CAA/B,EAA0C,KAA1C;EACD;EACF;;EAED,SAAK7B,gBAAL,GAAwB,IAAxB;;EAEA,UAAMyC,QAAQ,GAAG,MAAM;EACrB,WAAKzC,gBAAL,GAAwB,KAAxB;;EACA,WAAKa,QAAL,CAAcwB,SAAd,CAAwBC,MAAxB,CAA+BjD,qBAA/B;;EACA,WAAKwB,QAAL,CAAcwB,SAAd,CAAwBE,GAAxB,CAA4BnD,mBAA5B;;EACAwC,MAAAA,6BAAY,CAACC,OAAb,CAAqB,KAAKhB,QAA1B,EAAoC5B,YAApC;EACD,KALD;;EAOA,SAAK4B,QAAL,CAAc2B,KAAd,CAAoBL,SAApB,IAAiC,EAAjC;;EAEA,SAAKU,cAAL,CAAoBJ,QAApB,EAA8B,KAAK5B,QAAnC,EAA6C,IAA7C;EACD;;EAEDI,EAAAA,QAAQ,CAACjG,OAAO,GAAG,KAAK6F,QAAhB,EAA0B;EAChC,WAAO7F,OAAO,CAACqH,SAAR,CAAkBW,QAAlB,CAA2B7D,eAA3B,CAAP;EACD,GApKkC;;;EAwKnCe,EAAAA,UAAU,CAAC/D,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGuC,OADI;EAEP,SAAGuE,4BAAW,CAACC,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFI;EAGP,SAAG1E;EAHI,KAAT;EAKAA,IAAAA,MAAM,CAACwC,MAAP,GAAgBwE,OAAO,CAAChH,MAAM,CAACwC,MAAR,CAAvB,CANiB;;EAOjBxC,IAAAA,MAAM,CAACyC,MAAP,GAAgB7C,UAAU,CAACI,MAAM,CAACyC,MAAR,CAA1B;EACA3C,IAAAA,eAAe,CAACgC,IAAD,EAAO9B,MAAP,EAAe0C,WAAf,CAAf;EACA,WAAO1C,MAAP;EACD;;EAEDiG,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKvB,QAAL,CAAcwB,SAAd,CAAwBW,QAAxB,CAAiCxD,qBAAjC,IAA0DC,KAA1D,GAAkEC,MAAzE;EACD;;EAEDqB,EAAAA,mBAAmB,GAAG;EACpB,QAAI,CAAC,KAAKd,OAAL,CAAarB,MAAlB,EAA0B;EACxB;EACD;;EAED,UAAM0C,QAAQ,GAAGjB,+BAAc,CAACC,IAAf,CAAoBf,0BAApB,EAAgD,KAAKU,OAAL,CAAarB,MAA7D,CAAjB;EACAyB,IAAAA,+BAAc,CAACC,IAAf,CAAoBV,oBAApB,EAA0C,KAAKK,OAAL,CAAarB,MAAvD,EAA+D+B,MAA/D,CAAsEF,IAAI,IAAI,CAACa,QAAQ,CAAClG,QAAT,CAAkBqF,IAAlB,CAA/E,EACGlE,OADH,CACWvB,OAAO,IAAI;EAClB,YAAMoI,QAAQ,GAAGzH,sBAAsB,CAACX,OAAD,CAAvC;;EAEA,UAAIoI,QAAJ,EAAc;EACZ,aAAKpC,yBAAL,CAA+B,CAAChG,OAAD,CAA/B,EAA0C,KAAKiG,QAAL,CAAcmC,QAAd,CAA1C;EACD;EACF,KAPH;EAQD;;EAEDpC,EAAAA,yBAAyB,CAACqC,YAAD,EAAeC,MAAf,EAAuB;EAC9C,QAAI,CAACD,YAAY,CAACrH,MAAlB,EAA0B;EACxB;EACD;;EAEDqH,IAAAA,YAAY,CAAC9G,OAAb,CAAqBkE,IAAI,IAAI;EAC3B,UAAI6C,MAAJ,EAAY;EACV7C,QAAAA,IAAI,CAAC4B,SAAL,CAAeC,MAAf,CAAsBhD,oBAAtB;EACD,OAFD,MAEO;EACLmB,QAAAA,IAAI,CAAC4B,SAAL,CAAeE,GAAf,CAAmBjD,oBAAnB;EACD;;EAEDmB,MAAAA,IAAI,CAAC8C,YAAL,CAAkB,eAAlB,EAAmCD,MAAnC;EACD,KARD;EASD,GAtNkC;;;EA0Nb,SAAflF,eAAe,CAACjC,MAAD,EAAS;EAC7B,WAAO,KAAKqH,IAAL,CAAU,YAAY;EAC3B,YAAMvD,OAAO,GAAG,EAAhB;;EACA,UAAI,OAAO9D,MAAP,KAAkB,QAAlB,IAA8B,YAAYU,IAAZ,CAAiBV,MAAjB,CAAlC,EAA4D;EAC1D8D,QAAAA,OAAO,CAACtB,MAAR,GAAiB,KAAjB;EACD;;EAED,YAAM8E,IAAI,GAAG5D,QAAQ,CAACmC,mBAAT,CAA6B,IAA7B,EAAmC/B,OAAnC,CAAb;;EAEA,UAAI,OAAO9D,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsH,IAAI,CAACtH,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAEDsH,QAAAA,IAAI,CAACtH,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;EA3OkC;EA8OrC;EACA;EACA;EACA;EACA;;;AAEAyF,+BAAY,CAAC8B,EAAb,CAAgBjI,QAAhB,EAA0ByD,oBAA1B,EAAgDU,oBAAhD,EAAsE,UAAU+D,KAAV,EAAiB;EACrF;EACA,MAAIA,KAAK,CAACC,MAAN,CAAaC,OAAb,KAAyB,GAAzB,IAAiCF,KAAK,CAACG,cAAN,IAAwBH,KAAK,CAACG,cAAN,CAAqBD,OAArB,KAAiC,GAA9F,EAAoG;EAClGF,IAAAA,KAAK,CAACI,cAAN;EACD;;EAED,QAAM9I,QAAQ,GAAGO,sBAAsB,CAAC,IAAD,CAAvC;EACA,QAAMwI,gBAAgB,GAAG3D,+BAAc,CAACC,IAAf,CAAoBrF,QAApB,CAAzB;EAEA+I,EAAAA,gBAAgB,CAACzH,OAAjB,CAAyBvB,OAAO,IAAI;EAClC6E,IAAAA,QAAQ,CAACmC,mBAAT,CAA6BhH,OAA7B,EAAsC;EAAE2D,MAAAA,MAAM,EAAE;EAAV,KAAtC,EAAyDA,MAAzD;EACD,GAFD;EAGD,CAZD;EAcA;EACA;EACA;EACA;EACA;EACA;;EAEAd,kBAAkB,CAACgC,QAAD,CAAlB;;;;;;;;"}