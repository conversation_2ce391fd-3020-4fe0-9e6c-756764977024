{"version": 3, "file": "dropdown.js", "sources": ["../src/util/index.js", "../src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      this._createPopper(parent)\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper(parent) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n    if (isDisplayStatic) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n    }\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Dropdown.getInstance(toggles[i])\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._isShown()) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (event.key === ESCAPE_KEY) {\n      instance.hide()\n      return\n    }\n\n    if (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY) {\n      if (!isActive) {\n        instance.show()\n      }\n\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n"], "names": ["toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getComputedStyle", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "isRTL", "documentElement", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "index", "indexOf", "listLength", "Math", "max", "min", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_DATA_TOGGLE", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "<PERSON><PERSON><PERSON>", "offset", "boundary", "reference", "display", "popperConfig", "autoClose", "DefaultType", "Dropdown", "BaseComponent", "constructor", "_popper", "_config", "_getConfig", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "toggle", "_isShown", "hide", "show", "_element", "relatedTarget", "showEvent", "EventHandler", "trigger", "defaultPrevented", "parent", "getParentFromElement", "Manipulator", "setDataAttribute", "_createPopper", "closest", "concat", "children", "elem", "on", "focus", "setAttribute", "add", "_completeHide", "dispose", "destroy", "update", "hideEvent", "off", "remove", "removeDataAttribute", "getDataAttributes", "getBoundingClientRect", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "find", "modifier", "enabled", "createPopper", "SelectorEngine", "next", "_getPlacement", "parentDropdown", "parentNode", "isEnd", "_getOffset", "map", "val", "Number", "parseInt", "popperData", "defaultBsPopperConfig", "placement", "options", "_selectMenuItem", "key", "target", "items", "filter", "each", "data", "getOrCreateInstance", "clearMenus", "event", "button", "type", "toggles", "i", "len", "context", "getInstance", "<PERSON><PERSON><PERSON>", "isMenuTarget", "tagName", "clickEvent", "dataApiKeydownHandler", "isActive", "preventDefault", "stopPropagation", "getToggleButton", "matches", "prev", "instance"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;EAOA,MAAMA,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAiCA,MAAMU,SAAS,GAAGlB,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACmB,MAAX,KAAsB,WAA1B,EAAuC;EACrCnB,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACoB,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGrB,GAAG,IAAI;EACxB,MAAIkB,SAAS,CAAClB,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAACmB,MAAJ,GAAanB,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACsB,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAON,QAAQ,CAACC,aAAT,CAAuBjB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMuB,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAId,SAAS,CAACc,KAAD,CAAlB,GAA4B,SAA5B,GAAwCjC,MAAM,CAACiC,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAcA,MAAMO,SAAS,GAAG/B,OAAO,IAAI;EAC3B,MAAI,CAACW,SAAS,CAACX,OAAD,CAAV,IAAuBA,OAAO,CAACgC,cAAR,GAAyBjB,MAAzB,KAAoC,CAA/D,EAAkE;EAChE,WAAO,KAAP;EACD;;EAED,SAAOkB,gBAAgB,CAACjC,OAAD,CAAhB,CAA0BkC,gBAA1B,CAA2C,YAA3C,MAA6D,SAApE;EACD,CAND;;EAQA,MAAMC,UAAU,GAAGnC,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACa,QAAR,KAAqBuB,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAIrC,OAAO,CAACsC,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAOvC,OAAO,CAACwC,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAOxC,OAAO,CAACwC,QAAf;EACD;;EAED,SAAOxC,OAAO,CAACyC,YAAR,CAAqB,UAArB,KAAoCzC,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;;EAuCA,MAAMwC,IAAI,GAAG,MAAM,EAAnB;;EAeA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAACnC,QAAQ,CAACqC,IAAT,CAAcL,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOG,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMG,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIxC,QAAQ,CAACyC,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAAChC,MAA/B,EAAuC;EACrCN,MAAAA,QAAQ,CAAC0C,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAACzB,OAA1B,CAAkC2B,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAeA,MAAMI,KAAK,GAAG,MAAM5C,QAAQ,CAAC6C,eAAT,CAAyBC,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnCT,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMU,CAAC,GAAGf,SAAS,EAAnB;EACA;;EACA,QAAIe,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;EAoDA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMG,oBAAoB,GAAG,CAACC,IAAD,EAAOC,aAAP,EAAsBC,aAAtB,EAAqCC,cAArC,KAAwD;EACnF,MAAIC,KAAK,GAAGJ,IAAI,CAACK,OAAL,CAAaJ,aAAb,CAAZ,CADmF;;EAInF,MAAIG,KAAK,KAAK,CAAC,CAAf,EAAkB;EAChB,WAAOJ,IAAI,CAAC,CAACE,aAAD,IAAkBC,cAAlB,GAAmCH,IAAI,CAACpD,MAAL,GAAc,CAAjD,GAAqD,CAAtD,CAAX;EACD;;EAED,QAAM0D,UAAU,GAAGN,IAAI,CAACpD,MAAxB;EAEAwD,EAAAA,KAAK,IAAIF,aAAa,GAAG,CAAH,GAAO,CAAC,CAA9B;;EAEA,MAAIC,cAAJ,EAAoB;EAClBC,IAAAA,KAAK,GAAG,CAACA,KAAK,GAAGE,UAAT,IAAuBA,UAA/B;EACD;;EAED,SAAON,IAAI,CAACO,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASL,KAAT,EAAgBE,UAAU,GAAG,CAA7B,CAAZ,CAAD,CAAX;EACD,CAjBD;;ECpSA;EACA;EACA;EACA;EACA;EACA;EAqBA;EACA;EACA;EACA;EACA;;EAEA,MAAMb,IAAI,GAAG,UAAb;EACA,MAAMiB,QAAQ,GAAG,aAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EAEA,MAAMC,UAAU,GAAG,QAAnB;EACA,MAAMC,SAAS,GAAG,OAAlB;EACA,MAAMC,OAAO,GAAG,KAAhB;EACA,MAAMC,YAAY,GAAG,SAArB;EACA,MAAMC,cAAc,GAAG,WAAvB;EACA,MAAMC,kBAAkB,GAAG,CAA3B;;EAEA,MAAMC,cAAc,GAAG,IAAI3D,MAAJ,CAAY,GAAEwD,YAAa,IAAGC,cAAe,IAAGJ,UAAW,EAA3D,CAAvB;EAEA,MAAMO,UAAU,GAAI,OAAMT,SAAU,EAApC;EACA,MAAMU,YAAY,GAAI,SAAQV,SAAU,EAAxC;EACA,MAAMW,UAAU,GAAI,OAAMX,SAAU,EAApC;EACA,MAAMY,WAAW,GAAI,QAAOZ,SAAU,EAAtC;EACA,MAAMa,oBAAoB,GAAI,QAAOb,SAAU,GAAEC,YAAa,EAA9D;EACA,MAAMa,sBAAsB,GAAI,UAASd,SAAU,GAAEC,YAAa,EAAlE;EACA,MAAMc,oBAAoB,GAAI,QAAOf,SAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMe,eAAe,GAAG,MAAxB;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EACA,MAAMC,kBAAkB,GAAG,SAA3B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EAEA,MAAMC,oBAAoB,GAAG,6BAA7B;EACA,MAAMC,aAAa,GAAG,gBAAtB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMC,sBAAsB,GAAG,6DAA/B;EAEA,MAAMC,aAAa,GAAGlD,KAAK,KAAK,SAAL,GAAiB,WAA5C;EACA,MAAMmD,gBAAgB,GAAGnD,KAAK,KAAK,WAAL,GAAmB,SAAjD;EACA,MAAMoD,gBAAgB,GAAGpD,KAAK,KAAK,YAAL,GAAoB,cAAlD;EACA,MAAMqD,mBAAmB,GAAGrD,KAAK,KAAK,cAAL,GAAsB,YAAvD;EACA,MAAMsD,eAAe,GAAGtD,KAAK,KAAK,YAAL,GAAoB,aAAjD;EACA,MAAMuD,cAAc,GAAGvD,KAAK,KAAK,aAAL,GAAqB,YAAjD;EAEA,MAAMwD,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CADM;EAEdC,EAAAA,QAAQ,EAAE,iBAFI;EAGdC,EAAAA,SAAS,EAAE,QAHG;EAIdC,EAAAA,OAAO,EAAE,SAJK;EAKdC,EAAAA,YAAY,EAAE,IALA;EAMdC,EAAAA,SAAS,EAAE;EANG,CAAhB;EASA,MAAMC,WAAW,GAAG;EAClBN,EAAAA,MAAM,EAAE,yBADU;EAElBC,EAAAA,QAAQ,EAAE,kBAFQ;EAGlBC,EAAAA,SAAS,EAAE,yBAHO;EAIlBC,EAAAA,OAAO,EAAE,QAJS;EAKlBC,EAAAA,YAAY,EAAE,wBALI;EAMlBC,EAAAA,SAAS,EAAE;EANO,CAApB;EASA;EACA;EACA;EACA;EACA;;EAEA,MAAME,QAAN,SAAuBC,8BAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACvH,OAAD,EAAUkB,MAAV,EAAkB;EAC3B,UAAMlB,OAAN;EAEA,SAAKwH,OAAL,GAAe,IAAf;EACA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBxG,MAAhB,CAAf;EACA,SAAKyG,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;EACD,GARkC;;;EAYjB,aAAPjB,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEqB,aAAXO,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD;;EAEc,aAAJxD,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAtBkC;;;EA0BnCmE,EAAAA,MAAM,GAAG;EACP,WAAO,KAAKC,QAAL,KAAkB,KAAKC,IAAL,EAAlB,GAAgC,KAAKC,IAAL,EAAvC;EACD;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAI/F,UAAU,CAAC,KAAKgG,QAAN,CAAV,IAA6B,KAAKH,QAAL,CAAc,KAAKL,KAAnB,CAAjC,EAA4D;EAC1D;EACD;;EAED,UAAMS,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKD;EADA,KAAtB;EAIA,UAAME,SAAS,GAAGC,6BAAY,CAACC,OAAb,CAAqB,KAAKJ,QAA1B,EAAoC1C,UAApC,EAAgD2C,aAAhD,CAAlB;;EAEA,QAAIC,SAAS,CAACG,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMC,MAAM,GAAGpB,QAAQ,CAACqB,oBAAT,CAA8B,KAAKP,QAAnC,CAAf,CAfK;;EAiBL,QAAI,KAAKN,SAAT,EAAoB;EAClBc,MAAAA,4BAAW,CAACC,gBAAZ,CAA6B,KAAKjB,KAAlC,EAAyC,QAAzC,EAAmD,MAAnD;EACD,KAFD,MAEO;EACL,WAAKkB,aAAL,CAAmBJ,MAAnB;EACD,KArBI;EAwBL;EACA;EACA;;;EACA,QAAI,kBAAkBhI,QAAQ,CAAC6C,eAA3B,IACF,CAACmF,MAAM,CAACK,OAAP,CAAezC,mBAAf,CADH,EACwC;EACtC,SAAG0C,MAAH,CAAU,GAAGtI,QAAQ,CAACqC,IAAT,CAAckG,QAA3B,EACG1H,OADH,CACW2H,IAAI,IAAIX,6BAAY,CAACY,EAAb,CAAgBD,IAAhB,EAAsB,WAAtB,EAAmCvG,IAAnC,CADnB;EAED;;EAED,SAAKyF,QAAL,CAAcgB,KAAd;;EACA,SAAKhB,QAAL,CAAciB,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEA,SAAKzB,KAAL,CAAWrF,SAAX,CAAqB+G,GAArB,CAAyBvD,eAAzB;;EACA,SAAKqC,QAAL,CAAc7F,SAAd,CAAwB+G,GAAxB,CAA4BvD,eAA5B;;EACAwC,IAAAA,6BAAY,CAACC,OAAb,CAAqB,KAAKJ,QAA1B,EAAoCzC,WAApC,EAAiD0C,aAAjD;EACD;;EAEDH,EAAAA,IAAI,GAAG;EACL,QAAI9F,UAAU,CAAC,KAAKgG,QAAN,CAAV,IAA6B,CAAC,KAAKH,QAAL,CAAc,KAAKL,KAAnB,CAAlC,EAA6D;EAC3D;EACD;;EAED,UAAMS,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKD;EADA,KAAtB;;EAIA,SAAKmB,aAAL,CAAmBlB,aAAnB;EACD;;EAEDmB,EAAAA,OAAO,GAAG;EACR,QAAI,KAAK/B,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAagC,OAAb;EACD;;EAED,UAAMD,OAAN;EACD;;EAEDE,EAAAA,MAAM,GAAG;EACP,SAAK5B,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKN,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAaiC,MAAb;EACD;EACF,GAhGkC;;;EAoGnCH,EAAAA,aAAa,CAAClB,aAAD,EAAgB;EAC3B,UAAMsB,SAAS,GAAGpB,6BAAY,CAACC,OAAb,CAAqB,KAAKJ,QAA1B,EAAoC5C,UAApC,EAAgD6C,aAAhD,CAAlB;;EACA,QAAIsB,SAAS,CAAClB,gBAAd,EAAgC;EAC9B;EACD,KAJ0B;EAO3B;;;EACA,QAAI,kBAAkB/H,QAAQ,CAAC6C,eAA/B,EAAgD;EAC9C,SAAGyF,MAAH,CAAU,GAAGtI,QAAQ,CAACqC,IAAT,CAAckG,QAA3B,EACG1H,OADH,CACW2H,IAAI,IAAIX,6BAAY,CAACqB,GAAb,CAAiBV,IAAjB,EAAuB,WAAvB,EAAoCvG,IAApC,CADnB;EAED;;EAED,QAAI,KAAK8E,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAagC,OAAb;EACD;;EAED,SAAK7B,KAAL,CAAWrF,SAAX,CAAqBsH,MAArB,CAA4B9D,eAA5B;;EACA,SAAKqC,QAAL,CAAc7F,SAAd,CAAwBsH,MAAxB,CAA+B9D,eAA/B;;EACA,SAAKqC,QAAL,CAAciB,YAAd,CAA2B,eAA3B,EAA4C,OAA5C;;EACAT,IAAAA,4BAAW,CAACkB,mBAAZ,CAAgC,KAAKlC,KAArC,EAA4C,QAA5C;EACAW,IAAAA,6BAAY,CAACC,OAAb,CAAqB,KAAKJ,QAA1B,EAAoC3C,YAApC,EAAkD4C,aAAlD;EACD;;EAEDV,EAAAA,UAAU,CAACxG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKqG,WAAL,CAAiBV,OADb;EAEP,SAAG8B,4BAAW,CAACmB,iBAAZ,CAA8B,KAAK3B,QAAnC,CAFI;EAGP,SAAGjH;EAHI,KAAT;EAMAF,IAAAA,eAAe,CAAC4C,IAAD,EAAO1C,MAAP,EAAe,KAAKqG,WAAL,CAAiBH,WAAhC,CAAf;;EAEA,QAAI,OAAOlG,MAAM,CAAC8F,SAAd,KAA4B,QAA5B,IAAwC,CAACrG,SAAS,CAACO,MAAM,CAAC8F,SAAR,CAAlD,IACF,OAAO9F,MAAM,CAAC8F,SAAP,CAAiB+C,qBAAxB,KAAkD,UADpD,EAEE;EACA;EACA,YAAM,IAAIlI,SAAJ,CAAe,GAAE+B,IAAI,CAAC9B,WAAL,EAAmB,gGAApC,CAAN;EACD;;EAED,WAAOZ,MAAP;EACD;;EAED2H,EAAAA,aAAa,CAACJ,MAAD,EAAS;EACpB,QAAI,OAAOuB,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAInI,SAAJ,CAAc,+DAAd,CAAN;EACD;;EAED,QAAIoI,gBAAgB,GAAG,KAAK9B,QAA5B;;EAEA,QAAI,KAAKV,OAAL,CAAaT,SAAb,KAA2B,QAA/B,EAAyC;EACvCiD,MAAAA,gBAAgB,GAAGxB,MAAnB;EACD,KAFD,MAEO,IAAI9H,SAAS,CAAC,KAAK8G,OAAL,CAAaT,SAAd,CAAb,EAAuC;EAC5CiD,MAAAA,gBAAgB,GAAGnJ,UAAU,CAAC,KAAK2G,OAAL,CAAaT,SAAd,CAA7B;EACD,KAFM,MAEA,IAAI,OAAO,KAAKS,OAAL,CAAaT,SAApB,KAAkC,QAAtC,EAAgD;EACrDiD,MAAAA,gBAAgB,GAAG,KAAKxC,OAAL,CAAaT,SAAhC;EACD;;EAED,UAAME,YAAY,GAAG,KAAKgD,gBAAL,EAArB;;EACA,UAAMC,eAAe,GAAGjD,YAAY,CAACkD,SAAb,CAAuBC,IAAvB,CAA4BC,QAAQ,IAAIA,QAAQ,CAAC3G,IAAT,KAAkB,aAAlB,IAAmC2G,QAAQ,CAACC,OAAT,KAAqB,KAAhG,CAAxB;EAEA,SAAK/C,OAAL,GAAewC,iBAAM,CAACQ,YAAP,CAAoBP,gBAApB,EAAsC,KAAKtC,KAA3C,EAAkDT,YAAlD,CAAf;;EAEA,QAAIiD,eAAJ,EAAqB;EACnBxB,MAAAA,4BAAW,CAACC,gBAAZ,CAA6B,KAAKjB,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD;EACD;EACF;;EAEDK,EAAAA,QAAQ,CAAChI,OAAO,GAAG,KAAKmI,QAAhB,EAA0B;EAChC,WAAOnI,OAAO,CAACsC,SAAR,CAAkBC,QAAlB,CAA2BuD,eAA3B,CAAP;EACD;;EAED8B,EAAAA,eAAe,GAAG;EAChB,WAAO6C,+BAAc,CAACC,IAAf,CAAoB,KAAKvC,QAAzB,EAAmC/B,aAAnC,EAAkD,CAAlD,CAAP;EACD;;EAEDuE,EAAAA,aAAa,GAAG;EACd,UAAMC,cAAc,GAAG,KAAKzC,QAAL,CAAc0C,UAArC;;EAEA,QAAID,cAAc,CAACtI,SAAf,CAAyBC,QAAzB,CAAkCyD,kBAAlC,CAAJ,EAA2D;EACzD,aAAOW,eAAP;EACD;;EAED,QAAIiE,cAAc,CAACtI,SAAf,CAAyBC,QAAzB,CAAkC0D,oBAAlC,CAAJ,EAA6D;EAC3D,aAAOW,cAAP;EACD,KATa;;;EAYd,UAAMkE,KAAK,GAAG7I,gBAAgB,CAAC,KAAK0F,KAAN,CAAhB,CAA6BzF,gBAA7B,CAA8C,eAA9C,EAA+D3B,IAA/D,OAA0E,KAAxF;;EAEA,QAAIqK,cAAc,CAACtI,SAAf,CAAyBC,QAAzB,CAAkCwD,iBAAlC,CAAJ,EAA0D;EACxD,aAAO+E,KAAK,GAAGtE,gBAAH,GAAsBD,aAAlC;EACD;;EAED,WAAOuE,KAAK,GAAGpE,mBAAH,GAAyBD,gBAArC;EACD;;EAEDqB,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKK,QAAL,CAAcW,OAAd,CAAuB,IAAG5C,iBAAkB,EAA5C,MAAmD,IAA1D;EACD;;EAED6E,EAAAA,UAAU,GAAG;EACX,UAAM;EAAEjE,MAAAA;EAAF,QAAa,KAAKW,OAAxB;;EAEA,QAAI,OAAOX,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAACxG,KAAP,CAAa,GAAb,EAAkB0K,GAAlB,CAAsBC,GAAG,IAAIC,MAAM,CAACC,QAAP,CAAgBF,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAOnE,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAOsE,UAAU,IAAItE,MAAM,CAACsE,UAAD,EAAa,KAAKjD,QAAlB,CAA3B;EACD;;EAED,WAAOrB,MAAP;EACD;;EAEDoD,EAAAA,gBAAgB,GAAG;EACjB,UAAMmB,qBAAqB,GAAG;EAC5BC,MAAAA,SAAS,EAAE,KAAKX,aAAL,EADiB;EAE5BP,MAAAA,SAAS,EAAE,CAAC;EACVzG,QAAAA,IAAI,EAAE,iBADI;EAEV4H,QAAAA,OAAO,EAAE;EACPxE,UAAAA,QAAQ,EAAE,KAAKU,OAAL,CAAaV;EADhB;EAFC,OAAD,EAMX;EACEpD,QAAAA,IAAI,EAAE,QADR;EAEE4H,QAAAA,OAAO,EAAE;EACPzE,UAAAA,MAAM,EAAE,KAAKiE,UAAL;EADD;EAFX,OANW;EAFiB,KAA9B,CADiB;;EAkBjB,QAAI,KAAKtD,OAAL,CAAaR,OAAb,KAAyB,QAA7B,EAAuC;EACrCoE,MAAAA,qBAAqB,CAACjB,SAAtB,GAAkC,CAAC;EACjCzG,QAAAA,IAAI,EAAE,aAD2B;EAEjC4G,QAAAA,OAAO,EAAE;EAFwB,OAAD,CAAlC;EAID;;EAED,WAAO,EACL,GAAGc,qBADE;EAEL,UAAI,OAAO,KAAK5D,OAAL,CAAaP,YAApB,KAAqC,UAArC,GAAkD,KAAKO,OAAL,CAAaP,YAAb,CAA0BmE,qBAA1B,CAAlD,GAAqG,KAAK5D,OAAL,CAAaP,YAAtH;EAFK,KAAP;EAID;;EAEDsE,EAAAA,eAAe,CAAC;EAAEC,IAAAA,GAAF;EAAOC,IAAAA;EAAP,GAAD,EAAkB;EAC/B,UAAMC,KAAK,GAAGlB,+BAAc,CAACJ,IAAf,CAAoB/D,sBAApB,EAA4C,KAAKqB,KAAjD,EAAwDiE,MAAxD,CAA+D7J,SAA/D,CAAd;;EAEA,QAAI,CAAC4J,KAAK,CAAC5K,MAAX,EAAmB;EACjB;EACD,KAL8B;EAQ/B;;;EACAmD,IAAAA,oBAAoB,CAACyH,KAAD,EAAQD,MAAR,EAAgBD,GAAG,KAAKrG,cAAxB,EAAwC,CAACuG,KAAK,CAACvL,QAAN,CAAesL,MAAf,CAAzC,CAApB,CAAqFvC,KAArF;EACD,GAhQkC;;;EAoQb,SAAfpF,eAAe,CAAC7C,MAAD,EAAS;EAC7B,WAAO,KAAK2K,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGzE,QAAQ,CAAC0E,mBAAT,CAA6B,IAA7B,EAAmC7K,MAAnC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAI,OAAO4K,IAAI,CAAC5K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED4K,MAAAA,IAAI,CAAC5K,MAAD,CAAJ;EACD,KAZM,CAAP;EAaD;;EAEgB,SAAV8K,UAAU,CAACC,KAAD,EAAQ;EACvB,QAAIA,KAAK,KAAKA,KAAK,CAACC,MAAN,KAAiB7G,kBAAjB,IAAwC4G,KAAK,CAACE,IAAN,KAAe,OAAf,IAA0BF,KAAK,CAACR,GAAN,KAAcvG,OAArF,CAAT,EAAyG;EACvG;EACD;;EAED,UAAMkH,OAAO,GAAG3B,+BAAc,CAACJ,IAAf,CAAoBlE,oBAApB,CAAhB;;EAEA,SAAK,IAAIkG,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,OAAO,CAACrL,MAA9B,EAAsCsL,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,YAAME,OAAO,GAAGlF,QAAQ,CAACmF,WAAT,CAAqBJ,OAAO,CAACC,CAAD,CAA5B,CAAhB;;EACA,UAAI,CAACE,OAAD,IAAYA,OAAO,CAAC9E,OAAR,CAAgBN,SAAhB,KAA8B,KAA9C,EAAqD;EACnD;EACD;;EAED,UAAI,CAACoF,OAAO,CAACvE,QAAR,EAAL,EAAyB;EACvB;EACD;;EAED,YAAMI,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEmE,OAAO,CAACpE;EADH,OAAtB;;EAIA,UAAI8D,KAAJ,EAAW;EACT,cAAMQ,YAAY,GAAGR,KAAK,CAACQ,YAAN,EAArB;EACA,cAAMC,YAAY,GAAGD,YAAY,CAACrM,QAAb,CAAsBmM,OAAO,CAAC5E,KAA9B,CAArB;;EACA,YACE8E,YAAY,CAACrM,QAAb,CAAsBmM,OAAO,CAACpE,QAA9B,KACCoE,OAAO,CAAC9E,OAAR,CAAgBN,SAAhB,KAA8B,QAA9B,IAA0C,CAACuF,YAD5C,IAECH,OAAO,CAAC9E,OAAR,CAAgBN,SAAhB,KAA8B,SAA9B,IAA2CuF,YAH9C,EAIE;EACA;EACD,SATQ;;;EAYT,YAAIH,OAAO,CAAC5E,KAAR,CAAcpF,QAAd,CAAuB0J,KAAK,CAACP,MAA7B,MAA0CO,KAAK,CAACE,IAAN,KAAe,OAAf,IAA0BF,KAAK,CAACR,GAAN,KAAcvG,OAAzC,IAAqD,qCAAqCtD,IAArC,CAA0CqK,KAAK,CAACP,MAAN,CAAaiB,OAAvD,CAA9F,CAAJ,EAAoK;EAClK;EACD;;EAED,YAAIV,KAAK,CAACE,IAAN,KAAe,OAAnB,EAA4B;EAC1B/D,UAAAA,aAAa,CAACwE,UAAd,GAA2BX,KAA3B;EACD;EACF;;EAEDM,MAAAA,OAAO,CAACjD,aAAR,CAAsBlB,aAAtB;EACD;EACF;;EAE0B,SAApBM,oBAAoB,CAAC1I,OAAD,EAAU;EACnC,WAAOQ,sBAAsB,CAACR,OAAD,CAAtB,IAAmCA,OAAO,CAAC6K,UAAlD;EACD;;EAE2B,SAArBgC,qBAAqB,CAACZ,KAAD,EAAQ;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkBrK,IAAlB,CAAuBqK,KAAK,CAACP,MAAN,CAAaiB,OAApC,IACFV,KAAK,CAACR,GAAN,KAAcxG,SAAd,IAA4BgH,KAAK,CAACR,GAAN,KAAczG,UAAd,KAC1BiH,KAAK,CAACR,GAAN,KAAcrG,cAAd,IAAgC6G,KAAK,CAACR,GAAN,KAActG,YAA/C,IACC8G,KAAK,CAACP,MAAN,CAAa5C,OAAb,CAAqB1C,aAArB,CAF0B,CAD1B,GAIF,CAACd,cAAc,CAAC1D,IAAf,CAAoBqK,KAAK,CAACR,GAA1B,CAJH,EAImC;EACjC;EACD;;EAED,UAAMqB,QAAQ,GAAG,KAAKxK,SAAL,CAAeC,QAAf,CAAwBuD,eAAxB,CAAjB;;EAEA,QAAI,CAACgH,QAAD,IAAab,KAAK,CAACR,GAAN,KAAczG,UAA/B,EAA2C;EACzC;EACD;;EAEDiH,IAAAA,KAAK,CAACc,cAAN;EACAd,IAAAA,KAAK,CAACe,eAAN;;EAEA,QAAI7K,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAED,UAAM8K,eAAe,GAAG,KAAKC,OAAL,CAAa/G,oBAAb,IAAqC,IAArC,GAA4CsE,+BAAc,CAAC0C,IAAf,CAAoB,IAApB,EAA0BhH,oBAA1B,EAAgD,CAAhD,CAApE;EACA,UAAMiH,QAAQ,GAAG/F,QAAQ,CAAC0E,mBAAT,CAA6BkB,eAA7B,CAAjB;;EAEA,QAAIhB,KAAK,CAACR,GAAN,KAAczG,UAAlB,EAA8B;EAC5BoI,MAAAA,QAAQ,CAACnF,IAAT;EACA;EACD;;EAED,QAAIgE,KAAK,CAACR,GAAN,KAActG,YAAd,IAA8B8G,KAAK,CAACR,GAAN,KAAcrG,cAAhD,EAAgE;EAC9D,UAAI,CAAC0H,QAAL,EAAe;EACbM,QAAAA,QAAQ,CAAClF,IAAT;EACD;;EAEDkF,MAAAA,QAAQ,CAAC5B,eAAT,CAAyBS,KAAzB;;EACA;EACD;;EAED,QAAI,CAACa,QAAD,IAAab,KAAK,CAACR,GAAN,KAAcxG,SAA/B,EAA0C;EACxCoC,MAAAA,QAAQ,CAAC2E,UAAT;EACD;EACF;;EAvXkC;EA0XrC;EACA;EACA;EACA;EACA;;;AAEA1D,+BAAY,CAACY,EAAb,CAAgBzI,QAAhB,EAA0BmF,sBAA1B,EAAkDO,oBAAlD,EAAwEkB,QAAQ,CAACwF,qBAAjF;AACAvE,+BAAY,CAACY,EAAb,CAAgBzI,QAAhB,EAA0BmF,sBAA1B,EAAkDQ,aAAlD,EAAiEiB,QAAQ,CAACwF,qBAA1E;AACAvE,+BAAY,CAACY,EAAb,CAAgBzI,QAAhB,EAA0BkF,oBAA1B,EAAgD0B,QAAQ,CAAC2E,UAAzD;AACA1D,+BAAY,CAACY,EAAb,CAAgBzI,QAAhB,EAA0BoF,oBAA1B,EAAgDwB,QAAQ,CAAC2E,UAAzD;AACA1D,+BAAY,CAACY,EAAb,CAAgBzI,QAAhB,EAA0BkF,oBAA1B,EAAgDQ,oBAAhD,EAAsE,UAAU8F,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAACc,cAAN;EACA1F,EAAAA,QAAQ,CAAC0E,mBAAT,CAA6B,IAA7B,EAAmChE,MAAnC;EACD,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEAvE,kBAAkB,CAAC6D,QAAD,CAAlB;;;;;;;;"}