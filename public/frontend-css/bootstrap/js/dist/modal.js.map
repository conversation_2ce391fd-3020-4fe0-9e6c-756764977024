{"version": 3, "file": "modal.js", "sources": ["../src/util/index.js", "../src/util/scrollbar.js", "../src/util/backdrop.js", "../src/util/focustrap.js", "../src/util/component-functions.js", "../src/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.append(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport { typeCheckConfig } from './index'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nclass FocusTrap {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  activate() {\n    const { trapElement, autofocus } = this._config\n\n    if (this._isActive) {\n      return\n    }\n\n    if (autofocus) {\n      trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n\n  _handleFocusin(event) {\n    const { target } = event\n    const { trapElement } = this._config\n\n    if (target === document || target === trapElement || trapElement.contains(target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const { classList, scrollHeight, style } = this._element\n    const isModalOverflowing = scrollHeight > document.documentElement.clientHeight\n\n    // return if the following background transition hasn't yet completed\n    if ((!isModalOverflowing && style.overflowY === 'hidden') || classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      style.overflowY = 'hidden'\n    }\n\n    classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        this._queueCallback(() => {\n          style.overflowY = ''\n        }, this._dialog)\n      }\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking moddal toggler while another one is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen) {\n    Modal.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n"], "names": ["MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "isRTL", "documentElement", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "target", "removeEventListener", "setTimeout", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "ScrollBarHelper", "constructor", "_element", "getWidth", "documentWidth", "clientWidth", "Math", "abs", "innerWidth", "hide", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "style", "overflow", "styleProp", "scrollbarWidth", "manipulationCallBack", "_applyManipulationCallback", "reset", "_resetElementAttributes", "actualValue", "Manipulator", "setDataAttribute", "getDataAttribute", "removeProperty", "removeDataAttribute", "callBack", "SelectorEngine", "find", "isOverflowing", "<PERSON><PERSON><PERSON>", "className", "isAnimated", "rootElement", "clickCallback", "DefaultType", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "Backdrop", "_config", "_getConfig", "_isAppended", "show", "_append", "_getElement", "add", "_emulateAnimation", "remove", "dispose", "backdrop", "createElement", "append", "EventHandler", "on", "off", "trapElement", "autofocus", "DATA_KEY", "EVENT_KEY", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_KEY", "TAB_NAV_FORWARD", "TAB_NAV_BACKWARD", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "focus", "event", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "focusableC<PERSON><PERSON>n", "key", "shift<PERSON>ey", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "preventDefault", "closest", "instance", "getOrCreateInstance", "DATA_API_KEY", "ESCAPE_KEY", "keyboard", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_CLICK_DATA_API", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "OPEN_SELECTOR", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_TOGGLE", "Modal", "BaseComponent", "_dialog", "findOne", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_isShown", "_ignoreBackdropClick", "_isTransitioning", "_scrollBar", "toggle", "relatedTarget", "showEvent", "trigger", "defaultPrevented", "_isAnimated", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "one", "_showBackdrop", "_showElement", "hideEvent", "_queueCallback", "_hideModal", "htmlElement", "handleUpdate", "Boolean", "getDataAttributes", "modalBody", "parentNode", "display", "removeAttribute", "setAttribute", "scrollTop", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "currentTarget", "scrollHeight", "isModalOverflowing", "clientHeight", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "each", "data", "allReadyOpen", "getInstance"], "mappings": ";;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAGA,MAAMA,uBAAuB,GAAG,IAAhC;EACA,MAAMC,cAAc,GAAG,eAAvB;;EAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,MAAMU,gCAAgC,GAAGX,OAAO,IAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAI;EAAEY,IAAAA,kBAAF;EAAsBC,IAAAA;EAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAA9C;EAEA,QAAMgB,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;EACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;EAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAdiD;;;EAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACN,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAO,EAAAA,eAAe,GAAGA,eAAe,CAACP,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACW,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EvB,uBAAtF;EACD,CArBD;;EAuBA,MAAM8B,oBAAoB,GAAGpB,OAAO,IAAI;EACtCA,EAAAA,OAAO,CAACqB,aAAR,CAAsB,IAAIC,KAAJ,CAAU/B,cAAV,CAAtB;EACD,CAFD;;EAIA,MAAMgC,SAAS,GAAG9B,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAAC+B,MAAX,KAAsB,WAA1B,EAAuC;EACrC/B,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACgC,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGjC,GAAG,IAAI;EACxB,MAAI8B,SAAS,CAAC9B,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAAC+B,MAAJ,GAAa/B,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACkC,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOlB,QAAQ,CAACC,aAAT,CAAuBjB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMmC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAId,SAAS,CAACc,KAAD,CAAlB,GAA4B,SAA5B,GAAwC7C,MAAM,CAAC6C,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAcA,MAAMO,SAAS,GAAG3C,OAAO,IAAI;EAC3B,MAAI,CAACuB,SAAS,CAACvB,OAAD,CAAV,IAAuBA,OAAO,CAAC4C,cAAR,GAAyBjB,MAAzB,KAAoC,CAA/D,EAAkE;EAChE,WAAO,KAAP;EACD;;EAED,SAAOZ,gBAAgB,CAACf,OAAD,CAAhB,CAA0B6C,gBAA1B,CAA2C,YAA3C,MAA6D,SAApE;EACD,CAND;;EAQA,MAAMC,UAAU,GAAG9C,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACyB,QAAR,KAAqBsB,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAIhD,OAAO,CAACiD,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAOlD,OAAO,CAACmD,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAOnD,OAAO,CAACmD,QAAf;EACD;;EAED,SAAOnD,OAAO,CAACoD,YAAR,CAAqB,UAArB,KAAoCpD,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;EAyCA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMmD,MAAM,GAAGrD,OAAO,IAAI;EACxB;EACAA,EAAAA,OAAO,CAACsD,YAAR;EACD,CAHD;;EAKA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAa1C,MAAnB;;EAEA,MAAI0C,MAAM,IAAI,CAAC/C,QAAQ,CAACgD,IAAT,CAAcL,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOI,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAME,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAInD,QAAQ,CAACoD,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAAC/B,MAA/B,EAAuC;EACrClB,MAAAA,QAAQ,CAACqD,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAACxB,OAA1B,CAAkC0B,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAeA,MAAMI,KAAK,GAAG,MAAMvD,QAAQ,CAACwD,eAAT,CAAyBC,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnCT,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMU,CAAC,GAAGd,SAAS,EAAnB;EACA;;EACA,QAAIc,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EAiBA,MAAMG,OAAO,GAAGjB,QAAQ,IAAI;EAC1B,MAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;EAClCA,IAAAA,QAAQ;EACT;EACF,CAJD;;EAMA,MAAMkB,sBAAsB,GAAG,CAAClB,QAAD,EAAWmB,iBAAX,EAA8BC,iBAAiB,GAAG,IAAlD,KAA2D;EACxF,MAAI,CAACA,iBAAL,EAAwB;EACtBH,IAAAA,OAAO,CAACjB,QAAD,CAAP;EACA;EACD;;EAED,QAAMqB,eAAe,GAAG,CAAxB;EACA,QAAMC,gBAAgB,GAAGvE,gCAAgC,CAACoE,iBAAD,CAAhC,GAAsDE,eAA/E;EAEA,MAAIE,MAAM,GAAG,KAAb;;EAEA,QAAMC,OAAO,GAAG,CAAC;EAAEC,IAAAA;EAAF,GAAD,KAAgB;EAC9B,QAAIA,MAAM,KAAKN,iBAAf,EAAkC;EAChC;EACD;;EAEDI,IAAAA,MAAM,GAAG,IAAT;EACAJ,IAAAA,iBAAiB,CAACO,mBAAlB,CAAsC/F,cAAtC,EAAsD6F,OAAtD;EACAP,IAAAA,OAAO,CAACjB,QAAD,CAAP;EACD,GARD;;EAUAmB,EAAAA,iBAAiB,CAACjB,gBAAlB,CAAmCvE,cAAnC,EAAmD6F,OAAnD;EACAG,EAAAA,UAAU,CAAC,MAAM;EACf,QAAI,CAACJ,MAAL,EAAa;EACX/D,MAAAA,oBAAoB,CAAC2D,iBAAD,CAApB;EACD;EACF,GAJS,EAIPG,gBAJO,CAAV;EAKD,CA3BD;;EC9PA;EACA;EACA;EACA;EACA;EACA;EAMA,MAAMM,sBAAsB,GAAG,mDAA/B;EACA,MAAMC,uBAAuB,GAAG,aAAhC;;EAEA,MAAMC,eAAN,CAAsB;EACpBC,EAAAA,WAAW,GAAG;EACZ,SAAKC,QAAL,GAAgBnF,QAAQ,CAACgD,IAAzB;EACD;;EAEDoC,EAAAA,QAAQ,GAAG;EACT;EACA,UAAMC,aAAa,GAAGrF,QAAQ,CAACwD,eAAT,CAAyB8B,WAA/C;EACA,WAAOC,IAAI,CAACC,GAAL,CAASnF,MAAM,CAACoF,UAAP,GAAoBJ,aAA7B,CAAP;EACD;;EAEDK,EAAAA,IAAI,GAAG;EACL,UAAMC,KAAK,GAAG,KAAKP,QAAL,EAAd;;EACA,SAAKQ,gBAAL,GAFK;;;EAIL,SAAKC,qBAAL,CAA2B,KAAKV,QAAhC,EAA0C,cAA1C,EAA0DW,eAAe,IAAIA,eAAe,GAAGH,KAA/F,EAJK;;;EAML,SAAKE,qBAAL,CAA2Bd,sBAA3B,EAAmD,cAAnD,EAAmEe,eAAe,IAAIA,eAAe,GAAGH,KAAxG;;EACA,SAAKE,qBAAL,CAA2Bb,uBAA3B,EAAoD,aAApD,EAAmEc,eAAe,IAAIA,eAAe,GAAGH,KAAxG;EACD;;EAEDC,EAAAA,gBAAgB,GAAG;EACjB,SAAKG,qBAAL,CAA2B,KAAKZ,QAAhC,EAA0C,UAA1C;;EACA,SAAKA,QAAL,CAAca,KAAd,CAAoBC,QAApB,GAA+B,QAA/B;EACD;;EAEDJ,EAAAA,qBAAqB,CAACrG,QAAD,EAAW0G,SAAX,EAAsB/C,QAAtB,EAAgC;EACnD,UAAMgD,cAAc,GAAG,KAAKf,QAAL,EAAvB;;EACA,UAAMgB,oBAAoB,GAAG7G,OAAO,IAAI;EACtC,UAAIA,OAAO,KAAK,KAAK4F,QAAjB,IAA6B9E,MAAM,CAACoF,UAAP,GAAoBlG,OAAO,CAAC+F,WAAR,GAAsBa,cAA3E,EAA2F;EACzF;EACD;;EAED,WAAKJ,qBAAL,CAA2BxG,OAA3B,EAAoC2G,SAApC;;EACA,YAAMJ,eAAe,GAAGzF,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiC2G,SAAjC,CAAxB;EACA3G,MAAAA,OAAO,CAACyG,KAAR,CAAcE,SAAd,IAA4B,GAAE/C,QAAQ,CAAC3C,MAAM,CAACC,UAAP,CAAkBqF,eAAlB,CAAD,CAAqC,IAA3E;EACD,KARD;;EAUA,SAAKO,0BAAL,CAAgC7G,QAAhC,EAA0C4G,oBAA1C;EACD;;EAEDE,EAAAA,KAAK,GAAG;EACN,SAAKC,uBAAL,CAA6B,KAAKpB,QAAlC,EAA4C,UAA5C;;EACA,SAAKoB,uBAAL,CAA6B,KAAKpB,QAAlC,EAA4C,cAA5C;;EACA,SAAKoB,uBAAL,CAA6BxB,sBAA7B,EAAqD,cAArD;;EACA,SAAKwB,uBAAL,CAA6BvB,uBAA7B,EAAsD,aAAtD;EACD;;EAEDe,EAAAA,qBAAqB,CAACxG,OAAD,EAAU2G,SAAV,EAAqB;EACxC,UAAMM,WAAW,GAAGjH,OAAO,CAACyG,KAAR,CAAcE,SAAd,CAApB;;EACA,QAAIM,WAAJ,EAAiB;EACfC,MAAAA,4BAAW,CAACC,gBAAZ,CAA6BnH,OAA7B,EAAsC2G,SAAtC,EAAiDM,WAAjD;EACD;EACF;;EAEDD,EAAAA,uBAAuB,CAAC/G,QAAD,EAAW0G,SAAX,EAAsB;EAC3C,UAAME,oBAAoB,GAAG7G,OAAO,IAAI;EACtC,YAAMqC,KAAK,GAAG6E,4BAAW,CAACE,gBAAZ,CAA6BpH,OAA7B,EAAsC2G,SAAtC,CAAd;;EACA,UAAI,OAAOtE,KAAP,KAAiB,WAArB,EAAkC;EAChCrC,QAAAA,OAAO,CAACyG,KAAR,CAAcY,cAAd,CAA6BV,SAA7B;EACD,OAFD,MAEO;EACLO,QAAAA,4BAAW,CAACI,mBAAZ,CAAgCtH,OAAhC,EAAyC2G,SAAzC;EACA3G,QAAAA,OAAO,CAACyG,KAAR,CAAcE,SAAd,IAA2BtE,KAA3B;EACD;EACF,KARD;;EAUA,SAAKyE,0BAAL,CAAgC7G,QAAhC,EAA0C4G,oBAA1C;EACD;;EAEDC,EAAAA,0BAA0B,CAAC7G,QAAD,EAAWsH,QAAX,EAAqB;EAC7C,QAAIhG,SAAS,CAACtB,QAAD,CAAb,EAAyB;EACvBsH,MAAAA,QAAQ,CAACtH,QAAD,CAAR;EACD,KAFD,MAEO;EACLuH,MAAAA,+BAAc,CAACC,IAAf,CAAoBxH,QAApB,EAA8B,KAAK2F,QAAnC,EAA6C1D,OAA7C,CAAqDqF,QAArD;EACD;EACF;;EAEDG,EAAAA,aAAa,GAAG;EACd,WAAO,KAAK7B,QAAL,KAAkB,CAAzB;EACD;;EA/EmB;;ECdtB;EACA;EACA;EACA;EACA;EACA;EAKA,MAAM8B,SAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,gBADG;EAEdjF,EAAAA,SAAS,EAAE,IAFG;EAEG;EACjBkF,EAAAA,UAAU,EAAE,KAHE;EAIdC,EAAAA,WAAW,EAAE,MAJC;EAIO;EACrBC,EAAAA,aAAa,EAAE;EALD,CAAhB;EAQA,MAAMC,aAAW,GAAG;EAClBJ,EAAAA,SAAS,EAAE,QADO;EAElBjF,EAAAA,SAAS,EAAE,SAFO;EAGlBkF,EAAAA,UAAU,EAAE,SAHM;EAIlBC,EAAAA,WAAW,EAAE,kBAJK;EAKlBC,EAAAA,aAAa,EAAE;EALG,CAApB;EAOA,MAAMxD,MAAI,GAAG,UAAb;EACA,MAAM0D,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAMC,eAAe,GAAI,gBAAe5D,MAAK,EAA7C;;EAEA,MAAM6D,QAAN,CAAe;EACbzC,EAAAA,WAAW,CAAC7D,MAAD,EAAS;EAClB,SAAKuG,OAAL,GAAe,KAAKC,UAAL,CAAgBxG,MAAhB,CAAf;EACA,SAAKyG,WAAL,GAAmB,KAAnB;EACA,SAAK3C,QAAL,GAAgB,IAAhB;EACD;;EAED4C,EAAAA,IAAI,CAAC5E,QAAD,EAAW;EACb,QAAI,CAAC,KAAKyE,OAAL,CAAa1F,SAAlB,EAA6B;EAC3BkC,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACA;EACD;;EAED,SAAK6E,OAAL;;EAEA,QAAI,KAAKJ,OAAL,CAAaR,UAAjB,EAA6B;EAC3BxE,MAAAA,MAAM,CAAC,KAAKqF,WAAL,EAAD,CAAN;EACD;;EAED,SAAKA,WAAL,GAAmBzF,SAAnB,CAA6B0F,GAA7B,CAAiCT,iBAAjC;;EAEA,SAAKU,iBAAL,CAAuB,MAAM;EAC3B/D,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACD,KAFD;EAGD;;EAEDuC,EAAAA,IAAI,CAACvC,QAAD,EAAW;EACb,QAAI,CAAC,KAAKyE,OAAL,CAAa1F,SAAlB,EAA6B;EAC3BkC,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACA;EACD;;EAED,SAAK8E,WAAL,GAAmBzF,SAAnB,CAA6B4F,MAA7B,CAAoCX,iBAApC;;EAEA,SAAKU,iBAAL,CAAuB,MAAM;EAC3B,WAAKE,OAAL;EACAjE,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACD,KAHD;EAID,GAtCY;;;EA0Cb8E,EAAAA,WAAW,GAAG;EACZ,QAAI,CAAC,KAAK9C,QAAV,EAAoB;EAClB,YAAMmD,QAAQ,GAAGtI,QAAQ,CAACuI,aAAT,CAAuB,KAAvB,CAAjB;EACAD,MAAAA,QAAQ,CAACnB,SAAT,GAAqB,KAAKS,OAAL,CAAaT,SAAlC;;EACA,UAAI,KAAKS,OAAL,CAAaR,UAAjB,EAA6B;EAC3BkB,QAAAA,QAAQ,CAAC9F,SAAT,CAAmB0F,GAAnB,CAAuBV,iBAAvB;EACD;;EAED,WAAKrC,QAAL,GAAgBmD,QAAhB;EACD;;EAED,WAAO,KAAKnD,QAAZ;EACD;;EAED0C,EAAAA,UAAU,CAACxG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG6F,SADI;EAEP,UAAI,OAAO7F,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAFO,KAAT,CADiB;;EAOjBA,IAAAA,MAAM,CAACgG,WAAP,GAAqBpG,UAAU,CAACI,MAAM,CAACgG,WAAR,CAA/B;EACAlG,IAAAA,eAAe,CAAC2C,MAAD,EAAOzC,MAAP,EAAekG,aAAf,CAAf;EACA,WAAOlG,MAAP;EACD;;EAED2G,EAAAA,OAAO,GAAG;EACR,QAAI,KAAKF,WAAT,EAAsB;EACpB;EACD;;EAED,SAAKF,OAAL,CAAaP,WAAb,CAAyBmB,MAAzB,CAAgC,KAAKP,WAAL,EAAhC;;EAEAQ,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKT,WAAL,EAAhB,EAAoCP,eAApC,EAAqD,MAAM;EACzDtD,MAAAA,OAAO,CAAC,KAAKwD,OAAL,CAAaN,aAAd,CAAP;EACD,KAFD;EAIA,SAAKQ,WAAL,GAAmB,IAAnB;EACD;;EAEDO,EAAAA,OAAO,GAAG;EACR,QAAI,CAAC,KAAKP,WAAV,EAAuB;EACrB;EACD;;EAEDW,IAAAA,6BAAY,CAACE,GAAb,CAAiB,KAAKxD,QAAtB,EAAgCuC,eAAhC;;EAEA,SAAKvC,QAAL,CAAciD,MAAd;;EACA,SAAKN,WAAL,GAAmB,KAAnB;EACD;;EAEDK,EAAAA,iBAAiB,CAAChF,QAAD,EAAW;EAC1BkB,IAAAA,sBAAsB,CAAClB,QAAD,EAAW,KAAK8E,WAAL,EAAX,EAA+B,KAAKL,OAAL,CAAaR,UAA5C,CAAtB;EACD;;EA/FY;;EC/Bf;EACA;EACA;EACA;EACA;EACA;EAMA,MAAMF,SAAO,GAAG;EACd0B,EAAAA,WAAW,EAAE,IADC;EACK;EACnBC,EAAAA,SAAS,EAAE;EAFG,CAAhB;EAKA,MAAMtB,aAAW,GAAG;EAClBqB,EAAAA,WAAW,EAAE,SADK;EAElBC,EAAAA,SAAS,EAAE;EAFO,CAApB;EAKA,MAAM/E,MAAI,GAAG,WAAb;EACA,MAAMgF,UAAQ,GAAG,cAAjB;EACA,MAAMC,WAAS,GAAI,IAAGD,UAAS,EAA/B;EACA,MAAME,aAAa,GAAI,UAASD,WAAU,EAA1C;EACA,MAAME,iBAAiB,GAAI,cAAaF,WAAU,EAAlD;EAEA,MAAMG,OAAO,GAAG,KAAhB;EACA,MAAMC,eAAe,GAAG,SAAxB;EACA,MAAMC,gBAAgB,GAAG,UAAzB;;EAEA,MAAMC,SAAN,CAAgB;EACdnE,EAAAA,WAAW,CAAC7D,MAAD,EAAS;EAClB,SAAKuG,OAAL,GAAe,KAAKC,UAAL,CAAgBxG,MAAhB,CAAf;EACA,SAAKiI,SAAL,GAAiB,KAAjB;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACD;;EAEDC,EAAAA,QAAQ,GAAG;EACT,UAAM;EAAEZ,MAAAA,WAAF;EAAeC,MAAAA;EAAf,QAA6B,KAAKjB,OAAxC;;EAEA,QAAI,KAAK0B,SAAT,EAAoB;EAClB;EACD;;EAED,QAAIT,SAAJ,EAAe;EACbD,MAAAA,WAAW,CAACa,KAAZ;EACD;;EAEDhB,IAAAA,6BAAY,CAACE,GAAb,CAAiB3I,QAAjB,EAA2B+I,WAA3B,EAXS;;EAYTN,IAAAA,6BAAY,CAACC,EAAb,CAAgB1I,QAAhB,EAA0BgJ,aAA1B,EAAyCU,KAAK,IAAI,KAAKC,cAAL,CAAoBD,KAApB,CAAlD;EACAjB,IAAAA,6BAAY,CAACC,EAAb,CAAgB1I,QAAhB,EAA0BiJ,iBAA1B,EAA6CS,KAAK,IAAI,KAAKE,cAAL,CAAoBF,KAApB,CAAtD;EAEA,SAAKJ,SAAL,GAAiB,IAAjB;EACD;;EAEDO,EAAAA,UAAU,GAAG;EACX,QAAI,CAAC,KAAKP,SAAV,EAAqB;EACnB;EACD;;EAED,SAAKA,SAAL,GAAiB,KAAjB;EACAb,IAAAA,6BAAY,CAACE,GAAb,CAAiB3I,QAAjB,EAA2B+I,WAA3B;EACD,GAhCa;;;EAoCdY,EAAAA,cAAc,CAACD,KAAD,EAAQ;EACpB,UAAM;EAAE9E,MAAAA;EAAF,QAAa8E,KAAnB;EACA,UAAM;EAAEd,MAAAA;EAAF,QAAkB,KAAKhB,OAA7B;;EAEA,QAAIhD,MAAM,KAAK5E,QAAX,IAAuB4E,MAAM,KAAKgE,WAAlC,IAAiDA,WAAW,CAACnG,QAAZ,CAAqBmC,MAArB,CAArD,EAAmF;EACjF;EACD;;EAED,UAAMkF,QAAQ,GAAG/C,+BAAc,CAACgD,iBAAf,CAAiCnB,WAAjC,CAAjB;;EAEA,QAAIkB,QAAQ,CAAC5I,MAAT,KAAoB,CAAxB,EAA2B;EACzB0H,MAAAA,WAAW,CAACa,KAAZ;EACD,KAFD,MAEO,IAAI,KAAKF,oBAAL,KAA8BH,gBAAlC,EAAoD;EACzDU,MAAAA,QAAQ,CAACA,QAAQ,CAAC5I,MAAT,GAAkB,CAAnB,CAAR,CAA8BuI,KAA9B;EACD,KAFM,MAEA;EACLK,MAAAA,QAAQ,CAAC,CAAD,CAAR,CAAYL,KAAZ;EACD;EACF;;EAEDG,EAAAA,cAAc,CAACF,KAAD,EAAQ;EACpB,QAAIA,KAAK,CAACM,GAAN,KAAcd,OAAlB,EAA2B;EACzB;EACD;;EAED,SAAKK,oBAAL,GAA4BG,KAAK,CAACO,QAAN,GAAiBb,gBAAjB,GAAoCD,eAAhE;EACD;;EAEDtB,EAAAA,UAAU,CAACxG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG6F,SADI;EAEP,UAAI,OAAO7F,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAFO,KAAT;EAIAF,IAAAA,eAAe,CAAC2C,MAAD,EAAOzC,MAAP,EAAekG,aAAf,CAAf;EACA,WAAOlG,MAAP;EACD;;EAtEa;;EC/BhB;EACA;EACA;EACA;EACA;EACA;;EAKA,MAAM6I,oBAAoB,GAAG,CAACC,SAAD,EAAYC,MAAM,GAAG,MAArB,KAAgC;EAC3D,QAAMC,UAAU,GAAI,gBAAeF,SAAS,CAACpB,SAAU,EAAvD;EACA,QAAMlF,IAAI,GAAGsG,SAAS,CAACrG,IAAvB;EAEA2E,EAAAA,6BAAY,CAACC,EAAb,CAAgB1I,QAAhB,EAA0BqK,UAA1B,EAAuC,qBAAoBxG,IAAK,IAAhE,EAAqE,UAAU6F,KAAV,EAAiB;EACpF,QAAI,CAAC,GAAD,EAAM,MAAN,EAAc/J,QAAd,CAAuB,KAAK2K,OAA5B,CAAJ,EAA0C;EACxCZ,MAAAA,KAAK,CAACa,cAAN;EACD;;EAED,QAAIlI,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAED,UAAMuC,MAAM,GAAG7E,sBAAsB,CAAC,IAAD,CAAtB,IAAgC,KAAKyK,OAAL,CAAc,IAAG3G,IAAK,EAAtB,CAA/C;EACA,UAAM4G,QAAQ,GAAGN,SAAS,CAACO,mBAAV,CAA8B9F,MAA9B,CAAjB,CAVoF;;EAapF6F,IAAAA,QAAQ,CAACL,MAAD,CAAR;EACD,GAdD;EAeD,CAnBD;;ECVA;EACA;EACA;EACA;EACA;EACA;EAmBA;EACA;EACA;EACA;EACA;;EAEA,MAAMtG,IAAI,GAAG,OAAb;EACA,MAAMgF,QAAQ,GAAG,UAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAM6B,YAAY,GAAG,WAArB;EACA,MAAMC,UAAU,GAAG,QAAnB;EAEA,MAAM1D,OAAO,GAAG;EACdoB,EAAAA,QAAQ,EAAE,IADI;EAEduC,EAAAA,QAAQ,EAAE,IAFI;EAGdpB,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,MAAMlC,WAAW,GAAG;EAClBe,EAAAA,QAAQ,EAAE,kBADQ;EAElBuC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBpB,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMqB,UAAU,GAAI,OAAM/B,SAAU,EAApC;EACA,MAAMgC,oBAAoB,GAAI,gBAAehC,SAAU,EAAvD;EACA,MAAMiC,YAAY,GAAI,SAAQjC,SAAU,EAAxC;EACA,MAAMkC,UAAU,GAAI,OAAMlC,SAAU,EAApC;EACA,MAAMmC,WAAW,GAAI,QAAOnC,SAAU,EAAtC;EACA,MAAMoC,YAAY,GAAI,SAAQpC,SAAU,EAAxC;EACA,MAAMqC,mBAAmB,GAAI,gBAAerC,SAAU,EAAtD;EACA,MAAMsC,qBAAqB,GAAI,kBAAiBtC,SAAU,EAA1D;EACA,MAAMuC,qBAAqB,GAAI,kBAAiBvC,SAAU,EAA1D;EACA,MAAMwC,uBAAuB,GAAI,oBAAmBxC,SAAU,EAA9D;EACA,MAAMyC,oBAAoB,GAAI,QAAOzC,SAAU,GAAE4B,YAAa,EAA9D;EAEA,MAAMc,eAAe,GAAG,YAAxB;EACA,MAAMjE,eAAe,GAAG,MAAxB;EACA,MAAMC,eAAe,GAAG,MAAxB;EACA,MAAMiE,iBAAiB,GAAG,cAA1B;EAEA,MAAMC,aAAa,GAAG,aAAtB;EACA,MAAMC,eAAe,GAAG,eAAxB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMC,oBAAoB,GAAG,0BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBC,8BAApB,CAAkC;EAChC9G,EAAAA,WAAW,CAAC3F,OAAD,EAAU8B,MAAV,EAAkB;EAC3B,UAAM9B,OAAN;EAEA,SAAKqI,OAAL,GAAe,KAAKC,UAAL,CAAgBxG,MAAhB,CAAf;EACA,SAAK4K,OAAL,GAAelF,+BAAc,CAACmF,OAAf,CAAuBN,eAAvB,EAAwC,KAAKzG,QAA7C,CAAf;EACA,SAAKgH,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;EACA,SAAKC,UAAL,GAAkB,KAAKC,oBAAL,EAAlB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAKC,gBAAL,GAAwB,KAAxB;EACA,SAAKC,UAAL,GAAkB,IAAIzH,eAAJ,EAAlB;EACD,GAZ+B;;;EAgBd,aAAPiC,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJpD,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAtB+B;;;EA0BhC6I,EAAAA,MAAM,CAACC,aAAD,EAAgB;EACpB,WAAO,KAAKL,QAAL,GAAgB,KAAK7G,IAAL,EAAhB,GAA8B,KAAKqC,IAAL,CAAU6E,aAAV,CAArC;EACD;;EAED7E,EAAAA,IAAI,CAAC6E,aAAD,EAAgB;EAClB,QAAI,KAAKL,QAAL,IAAiB,KAAKE,gBAA1B,EAA4C;EAC1C;EACD;;EAED,UAAMI,SAAS,GAAGpE,6BAAY,CAACqE,OAAb,CAAqB,KAAK3H,QAA1B,EAAoC8F,UAApC,EAAgD;EAChE2B,MAAAA;EADgE,KAAhD,CAAlB;;EAIA,QAAIC,SAAS,CAACE,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKR,QAAL,GAAgB,IAAhB;;EAEA,QAAI,KAAKS,WAAL,EAAJ,EAAwB;EACtB,WAAKP,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKC,UAAL,CAAgBhH,IAAhB;;EAEA1F,IAAAA,QAAQ,CAACgD,IAAT,CAAcR,SAAd,CAAwB0F,GAAxB,CAA4BuD,eAA5B;;EAEA,SAAKwB,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEA1E,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKuD,OAArB,EAA8BV,uBAA9B,EAAuD,MAAM;EAC3D9C,MAAAA,6BAAY,CAAC2E,GAAb,CAAiB,KAAKjI,QAAtB,EAAgCmG,qBAAhC,EAAuD5B,KAAK,IAAI;EAC9D,YAAIA,KAAK,CAAC9E,MAAN,KAAiB,KAAKO,QAA1B,EAAoC;EAClC,eAAKqH,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKa,aAAL,CAAmB,MAAM,KAAKC,YAAL,CAAkBV,aAAlB,CAAzB;EACD;;EAEDlH,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAK6G,QAAN,IAAkB,KAAKE,gBAA3B,EAA6C;EAC3C;EACD;;EAED,UAAMc,SAAS,GAAG9E,6BAAY,CAACqE,OAAb,CAAqB,KAAK3H,QAA1B,EAAoC2F,UAApC,CAAlB;;EAEA,QAAIyC,SAAS,CAACR,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKR,QAAL,GAAgB,KAAhB;;EACA,UAAMnF,UAAU,GAAG,KAAK4F,WAAL,EAAnB;;EAEA,QAAI5F,UAAJ,EAAgB;EACd,WAAKqF,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKS,eAAL;;EACA,SAAKC,eAAL;;EAEA,SAAKd,UAAL,CAAgBxC,UAAhB;;EAEA,SAAK1E,QAAL,CAAc3C,SAAd,CAAwB4F,MAAxB,CAA+BX,eAA/B;;EAEAgB,IAAAA,6BAAY,CAACE,GAAb,CAAiB,KAAKxD,QAAtB,EAAgCiG,mBAAhC;EACA3C,IAAAA,6BAAY,CAACE,GAAb,CAAiB,KAAKsD,OAAtB,EAA+BV,uBAA/B;;EAEA,SAAKiC,cAAL,CAAoB,MAAM,KAAKC,UAAL,EAA1B,EAA6C,KAAKtI,QAAlD,EAA4DiC,UAA5D;EACD;;EAEDiB,EAAAA,OAAO,GAAG;EACR,KAAChI,MAAD,EAAS,KAAK4L,OAAd,EACGxK,OADH,CACWiM,WAAW,IAAIjF,6BAAY,CAACE,GAAb,CAAiB+E,WAAjB,EAA8B3E,SAA9B,CAD1B;;EAGA,SAAKoD,SAAL,CAAe9D,OAAf;;EACA,SAAKgE,UAAL,CAAgBxC,UAAhB;;EACA,UAAMxB,OAAN;EACD;;EAEDsF,EAAAA,YAAY,GAAG;EACb,SAAKV,aAAL;EACD,GA/G+B;;;EAmHhCb,EAAAA,mBAAmB,GAAG;EACpB,WAAO,IAAIzE,QAAJ,CAAa;EAClBzF,MAAAA,SAAS,EAAE0L,OAAO,CAAC,KAAKhG,OAAL,CAAaU,QAAd,CADA;EACyB;EAC3ClB,MAAAA,UAAU,EAAE,KAAK4F,WAAL;EAFM,KAAb,CAAP;EAID;;EAEDV,EAAAA,oBAAoB,GAAG;EACrB,WAAO,IAAIjD,SAAJ,CAAc;EACnBT,MAAAA,WAAW,EAAE,KAAKzD;EADC,KAAd,CAAP;EAGD;;EAED0C,EAAAA,UAAU,CAACxG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG6F,OADI;EAEP,SAAGT,4BAAW,CAACoH,iBAAZ,CAA8B,KAAK1I,QAAnC,CAFI;EAGP,UAAI,OAAO9D,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAHO,KAAT;EAKAF,IAAAA,eAAe,CAAC2C,IAAD,EAAOzC,MAAP,EAAekG,WAAf,CAAf;EACA,WAAOlG,MAAP;EACD;;EAEDiM,EAAAA,YAAY,CAACV,aAAD,EAAgB;EAC1B,UAAMxF,UAAU,GAAG,KAAK4F,WAAL,EAAnB;;EACA,UAAMc,SAAS,GAAG/G,+BAAc,CAACmF,OAAf,CAAuBL,mBAAvB,EAA4C,KAAKI,OAAjD,CAAlB;;EAEA,QAAI,CAAC,KAAK9G,QAAL,CAAc4I,UAAf,IAA6B,KAAK5I,QAAL,CAAc4I,UAAd,CAAyB/M,QAAzB,KAAsCsB,IAAI,CAACC,YAA5E,EAA0F;EACxF;EACAvC,MAAAA,QAAQ,CAACgD,IAAT,CAAcwF,MAAd,CAAqB,KAAKrD,QAA1B;EACD;;EAED,SAAKA,QAAL,CAAca,KAAd,CAAoBgI,OAApB,GAA8B,OAA9B;;EACA,SAAK7I,QAAL,CAAc8I,eAAd,CAA8B,aAA9B;;EACA,SAAK9I,QAAL,CAAc+I,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAK/I,QAAL,CAAc+I,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAK/I,QAAL,CAAcgJ,SAAd,GAA0B,CAA1B;;EAEA,QAAIL,SAAJ,EAAe;EACbA,MAAAA,SAAS,CAACK,SAAV,GAAsB,CAAtB;EACD;;EAED,QAAI/G,UAAJ,EAAgB;EACdxE,MAAAA,MAAM,CAAC,KAAKuC,QAAN,CAAN;EACD;;EAED,SAAKA,QAAL,CAAc3C,SAAd,CAAwB0F,GAAxB,CAA4BT,eAA5B;;EAEA,UAAM2G,kBAAkB,GAAG,MAAM;EAC/B,UAAI,KAAKxG,OAAL,CAAa6B,KAAjB,EAAwB;EACtB,aAAK4C,UAAL,CAAgB7C,QAAhB;EACD;;EAED,WAAKiD,gBAAL,GAAwB,KAAxB;EACAhE,MAAAA,6BAAY,CAACqE,OAAb,CAAqB,KAAK3H,QAA1B,EAAoC+F,WAApC,EAAiD;EAC/C0B,QAAAA;EAD+C,OAAjD;EAGD,KATD;;EAWA,SAAKY,cAAL,CAAoBY,kBAApB,EAAwC,KAAKnC,OAA7C,EAAsD7E,UAAtD;EACD;;EAED8F,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKX,QAAT,EAAmB;EACjB9D,MAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKvD,QAArB,EAA+BkG,qBAA/B,EAAsD3B,KAAK,IAAI;EAC7D,YAAI,KAAK9B,OAAL,CAAaiD,QAAb,IAAyBnB,KAAK,CAACM,GAAN,KAAcY,UAA3C,EAAuD;EACrDlB,UAAAA,KAAK,CAACa,cAAN;EACA,eAAK7E,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,KAAKkC,OAAL,CAAaiD,QAAd,IAA0BnB,KAAK,CAACM,GAAN,KAAcY,UAA5C,EAAwD;EAC7D,eAAKyD,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO;EACL5F,MAAAA,6BAAY,CAACE,GAAb,CAAiB,KAAKxD,QAAtB,EAAgCkG,qBAAhC;EACD;EACF;;EAED8B,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKZ,QAAT,EAAmB;EACjB9D,MAAAA,6BAAY,CAACC,EAAb,CAAgBrI,MAAhB,EAAwB8K,YAAxB,EAAsC,MAAM,KAAK8B,aAAL,EAA5C;EACD,KAFD,MAEO;EACLxE,MAAAA,6BAAY,CAACE,GAAb,CAAiBtI,MAAjB,EAAyB8K,YAAzB;EACD;EACF;;EAEDsC,EAAAA,UAAU,GAAG;EACX,SAAKtI,QAAL,CAAca,KAAd,CAAoBgI,OAApB,GAA8B,MAA9B;;EACA,SAAK7I,QAAL,CAAc+I,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAK/I,QAAL,CAAc8I,eAAd,CAA8B,YAA9B;;EACA,SAAK9I,QAAL,CAAc8I,eAAd,CAA8B,MAA9B;;EACA,SAAKxB,gBAAL,GAAwB,KAAxB;;EACA,SAAKN,SAAL,CAAezG,IAAf,CAAoB,MAAM;EACxB1F,MAAAA,QAAQ,CAACgD,IAAT,CAAcR,SAAd,CAAwB4F,MAAxB,CAA+BqD,eAA/B;;EACA,WAAK6C,iBAAL;;EACA,WAAK5B,UAAL,CAAgBpG,KAAhB;;EACAmC,MAAAA,6BAAY,CAACqE,OAAb,CAAqB,KAAK3H,QAA1B,EAAoC6F,YAApC;EACD,KALD;EAMD;;EAEDqC,EAAAA,aAAa,CAAClK,QAAD,EAAW;EACtBsF,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKvD,QAArB,EAA+BiG,mBAA/B,EAAoD1B,KAAK,IAAI;EAC3D,UAAI,KAAK8C,oBAAT,EAA+B;EAC7B,aAAKA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,UAAI9C,KAAK,CAAC9E,MAAN,KAAiB8E,KAAK,CAAC6E,aAA3B,EAA0C;EACxC;EACD;;EAED,UAAI,KAAK3G,OAAL,CAAaU,QAAb,KAA0B,IAA9B,EAAoC;EAClC,aAAK5C,IAAL;EACD,OAFD,MAEO,IAAI,KAAKkC,OAAL,CAAaU,QAAb,KAA0B,QAA9B,EAAwC;EAC7C,aAAK+F,0BAAL;EACD;EACF,KAfD;;EAiBA,SAAKlC,SAAL,CAAepE,IAAf,CAAoB5E,QAApB;EACD;;EAED6J,EAAAA,WAAW,GAAG;EACZ,WAAO,KAAK7H,QAAL,CAAc3C,SAAd,CAAwBC,QAAxB,CAAiC+E,eAAjC,CAAP;EACD;;EAED6G,EAAAA,0BAA0B,GAAG;EAC3B,UAAMd,SAAS,GAAG9E,6BAAY,CAACqE,OAAb,CAAqB,KAAK3H,QAA1B,EAAoC4F,oBAApC,CAAlB;;EACA,QAAIwC,SAAS,CAACR,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAM;EAAEvK,MAAAA,SAAF;EAAagM,MAAAA,YAAb;EAA2BxI,MAAAA;EAA3B,QAAqC,KAAKb,QAAhD;EACA,UAAMsJ,kBAAkB,GAAGD,YAAY,GAAGxO,QAAQ,CAACwD,eAAT,CAAyBkL,YAAnE,CAP2B;;EAU3B,QAAK,CAACD,kBAAD,IAAuBzI,KAAK,CAAC2I,SAAN,KAAoB,QAA5C,IAAyDnM,SAAS,CAACC,QAAV,CAAmBiJ,iBAAnB,CAA7D,EAAoG;EAClG;EACD;;EAED,QAAI,CAAC+C,kBAAL,EAAyB;EACvBzI,MAAAA,KAAK,CAAC2I,SAAN,GAAkB,QAAlB;EACD;;EAEDnM,IAAAA,SAAS,CAAC0F,GAAV,CAAcwD,iBAAd;;EACA,SAAK8B,cAAL,CAAoB,MAAM;EACxBhL,MAAAA,SAAS,CAAC4F,MAAV,CAAiBsD,iBAAjB;;EACA,UAAI,CAAC+C,kBAAL,EAAyB;EACvB,aAAKjB,cAAL,CAAoB,MAAM;EACxBxH,UAAAA,KAAK,CAAC2I,SAAN,GAAkB,EAAlB;EACD,SAFD,EAEG,KAAK1C,OAFR;EAGD;EACF,KAPD,EAOG,KAAKA,OAPR;;EASA,SAAK9G,QAAL,CAAcsE,KAAd;EACD,GA5Q+B;EA+QhC;EACA;;;EAEAwD,EAAAA,aAAa,GAAG;EACd,UAAMwB,kBAAkB,GAAG,KAAKtJ,QAAL,CAAcqJ,YAAd,GAA6BxO,QAAQ,CAACwD,eAAT,CAAyBkL,YAAjF;;EACA,UAAMvI,cAAc,GAAG,KAAKuG,UAAL,CAAgBtH,QAAhB,EAAvB;;EACA,UAAMwJ,iBAAiB,GAAGzI,cAAc,GAAG,CAA3C;;EAEA,QAAK,CAACyI,iBAAD,IAAsBH,kBAAtB,IAA4C,CAAClL,KAAK,EAAnD,IAA2DqL,iBAAiB,IAAI,CAACH,kBAAtB,IAA4ClL,KAAK,EAAhH,EAAqH;EACnH,WAAK4B,QAAL,CAAca,KAAd,CAAoB6I,WAApB,GAAmC,GAAE1I,cAAe,IAApD;EACD;;EAED,QAAKyI,iBAAiB,IAAI,CAACH,kBAAtB,IAA4C,CAAClL,KAAK,EAAnD,IAA2D,CAACqL,iBAAD,IAAsBH,kBAAtB,IAA4ClL,KAAK,EAAhH,EAAqH;EACnH,WAAK4B,QAAL,CAAca,KAAd,CAAoB8I,YAApB,GAAoC,GAAE3I,cAAe,IAArD;EACD;EACF;;EAEDmI,EAAAA,iBAAiB,GAAG;EAClB,SAAKnJ,QAAL,CAAca,KAAd,CAAoB6I,WAApB,GAAkC,EAAlC;EACA,SAAK1J,QAAL,CAAca,KAAd,CAAoB8I,YAApB,GAAmC,EAAnC;EACD,GAnS+B;;;EAuSV,SAAf7K,eAAe,CAAC5C,MAAD,EAASuL,aAAT,EAAwB;EAC5C,WAAO,KAAKmC,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGjD,KAAK,CAACrB,mBAAN,CAA0B,IAA1B,EAAgCrJ,MAAhC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAI,OAAO2N,IAAI,CAAC3N,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2N,MAAAA,IAAI,CAAC3N,MAAD,CAAJ,CAAauL,aAAb;EACD,KAZM,CAAP;EAaD;;EArT+B;EAwTlC;EACA;EACA;EACA;EACA;;;AAEAnE,+BAAY,CAACC,EAAb,CAAgB1I,QAAhB,EAA0BwL,oBAA1B,EAAgDM,oBAAhD,EAAsE,UAAUpC,KAAV,EAAiB;EACrF,QAAM9E,MAAM,GAAG7E,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcJ,QAAd,CAAuB,KAAK2K,OAA5B,CAAJ,EAA0C;EACxCZ,IAAAA,KAAK,CAACa,cAAN;EACD;;EAED9B,EAAAA,6BAAY,CAAC2E,GAAb,CAAiBxI,MAAjB,EAAyBqG,UAAzB,EAAqC4B,SAAS,IAAI;EAChD,QAAIA,SAAS,CAACE,gBAAd,EAAgC;EAC9B;EACA;EACD;;EAEDtE,IAAAA,6BAAY,CAAC2E,GAAb,CAAiBxI,MAAjB,EAAyBoG,YAAzB,EAAuC,MAAM;EAC3C,UAAI9I,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,aAAKuH,KAAL;EACD;EACF,KAJD;EAKD,GAXD,EAPqF;;EAqBrF,QAAMwF,YAAY,GAAGlI,+BAAc,CAACmF,OAAf,CAAuBP,aAAvB,CAArB;;EACA,MAAIsD,YAAJ,EAAkB;EAChBlD,IAAAA,KAAK,CAACmD,WAAN,CAAkBD,YAAlB,EAAgCvJ,IAAhC;EACD;;EAED,QAAMsJ,IAAI,GAAGjD,KAAK,CAACrB,mBAAN,CAA0B9F,MAA1B,CAAb;EAEAoK,EAAAA,IAAI,CAACrC,MAAL,CAAY,IAAZ;EACD,CA7BD;EA+BAzC,oBAAoB,CAAC6B,KAAD,CAApB;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEArI,kBAAkB,CAACqI,KAAD,CAAlB;;;;;;;;"}