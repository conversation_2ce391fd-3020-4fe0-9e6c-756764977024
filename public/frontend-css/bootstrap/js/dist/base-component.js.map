{"version": 3, "file": "base-component.js", "sources": ["../src/util/index.js", "../src/base-component.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  executeAfterTransition,\n  getElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.1.3'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n"], "names": ["MILLISECONDS_MULTIPLIER", "TRANSITION_END", "getTransitionDurationFromElement", "element", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "execute", "callback", "executeAfterTransition", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "target", "removeEventListener", "addEventListener", "setTimeout", "VERSION", "BaseComponent", "constructor", "_element", "Data", "set", "DATA_KEY", "dispose", "remove", "EventHandler", "off", "EVENT_KEY", "Object", "getOwnPropertyNames", "for<PERSON>ach", "propertyName", "_queueCallback", "isAnimated", "getInstance", "get", "getOrCreateInstance", "config", "NAME", "Error"], "mappings": ";;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAGA,MAAMA,uBAAuB,GAAG,IAAhC;EACA,MAAMC,cAAc,GAAG,eAAvB;;EAkEA,MAAMC,gCAAgC,GAAGC,OAAO,IAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAI;EAAEC,IAAAA,kBAAF;EAAsBC,IAAAA;EAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBJ,OAAxB,CAA9C;EAEA,QAAMK,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;EACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;EAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAdiD;;;EAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACQ,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAP,EAAAA,eAAe,GAAGA,eAAe,CAACO,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACH,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EL,uBAAtF;EACD,CArBD;;EAuBA,MAAMa,oBAAoB,GAAGV,OAAO,IAAI;EACtCA,EAAAA,OAAO,CAACW,aAAR,CAAsB,IAAIC,KAAJ,CAAUd,cAAV,CAAtB;EACD,CAFD;;EAIA,MAAMe,SAAS,GAAGC,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACC,MAAX,KAAsB,WAA1B,EAAuC;EACrCD,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACE,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGH,GAAG,IAAI;EACxB,MAAID,SAAS,CAACC,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAACC,MAAJ,GAAaD,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACI,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOC,QAAQ,CAACC,aAAT,CAAuBN,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAsIA,MAAMO,OAAO,GAAGC,QAAQ,IAAI;EAC1B,MAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;EAClCA,IAAAA,QAAQ;EACT;EACF,CAJD;;EAMA,MAAMC,sBAAsB,GAAG,CAACD,QAAD,EAAWE,iBAAX,EAA8BC,iBAAiB,GAAG,IAAlD,KAA2D;EACxF,MAAI,CAACA,iBAAL,EAAwB;EACtBJ,IAAAA,OAAO,CAACC,QAAD,CAAP;EACA;EACD;;EAED,QAAMI,eAAe,GAAG,CAAxB;EACA,QAAMC,gBAAgB,GAAG5B,gCAAgC,CAACyB,iBAAD,CAAhC,GAAsDE,eAA/E;EAEA,MAAIE,MAAM,GAAG,KAAb;;EAEA,QAAMC,OAAO,GAAG,CAAC;EAAEC,IAAAA;EAAF,GAAD,KAAgB;EAC9B,QAAIA,MAAM,KAAKN,iBAAf,EAAkC;EAChC;EACD;;EAEDI,IAAAA,MAAM,GAAG,IAAT;EACAJ,IAAAA,iBAAiB,CAACO,mBAAlB,CAAsCjC,cAAtC,EAAsD+B,OAAtD;EACAR,IAAAA,OAAO,CAACC,QAAD,CAAP;EACD,GARD;;EAUAE,EAAAA,iBAAiB,CAACQ,gBAAlB,CAAmClC,cAAnC,EAAmD+B,OAAnD;EACAI,EAAAA,UAAU,CAAC,MAAM;EACf,QAAI,CAACL,MAAL,EAAa;EACXlB,MAAAA,oBAAoB,CAACc,iBAAD,CAApB;EACD;EACF,GAJS,EAIPG,gBAJO,CAAV;EAKD,CA3BD;;EC9PA;EACA;EACA;EACA;EACA;EACA;EASA;EACA;EACA;EACA;EACA;;EAEA,MAAMO,OAAO,GAAG,OAAhB;;EAEA,MAAMC,aAAN,CAAoB;EAClBC,EAAAA,WAAW,CAACpC,OAAD,EAAU;EACnBA,IAAAA,OAAO,GAAGiB,UAAU,CAACjB,OAAD,CAApB;;EAEA,QAAI,CAACA,OAAL,EAAc;EACZ;EACD;;EAED,SAAKqC,QAAL,GAAgBrC,OAAhB;EACAsC,IAAAA,qBAAI,CAACC,GAAL,CAAS,KAAKF,QAAd,EAAwB,KAAKD,WAAL,CAAiBI,QAAzC,EAAmD,IAAnD;EACD;;EAEDC,EAAAA,OAAO,GAAG;EACRH,IAAAA,qBAAI,CAACI,MAAL,CAAY,KAAKL,QAAjB,EAA2B,KAAKD,WAAL,CAAiBI,QAA5C;EACAG,IAAAA,6BAAY,CAACC,GAAb,CAAiB,KAAKP,QAAtB,EAAgC,KAAKD,WAAL,CAAiBS,SAAjD;EAEAC,IAAAA,MAAM,CAACC,mBAAP,CAA2B,IAA3B,EAAiCC,OAAjC,CAAyCC,YAAY,IAAI;EACvD,WAAKA,YAAL,IAAqB,IAArB;EACD,KAFD;EAGD;;EAEDC,EAAAA,cAAc,CAAC5B,QAAD,EAAWtB,OAAX,EAAoBmD,UAAU,GAAG,IAAjC,EAAuC;EACnD5B,IAAAA,sBAAsB,CAACD,QAAD,EAAWtB,OAAX,EAAoBmD,UAApB,CAAtB;EACD;EAED;;;EAEkB,SAAXC,WAAW,CAACpD,OAAD,EAAU;EAC1B,WAAOsC,qBAAI,CAACe,GAAL,CAASpC,UAAU,CAACjB,OAAD,CAAnB,EAA8B,KAAKwC,QAAnC,CAAP;EACD;;EAEyB,SAAnBc,mBAAmB,CAACtD,OAAD,EAAUuD,MAAM,GAAG,EAAnB,EAAuB;EAC/C,WAAO,KAAKH,WAAL,CAAiBpD,OAAjB,KAA6B,IAAI,IAAJ,CAASA,OAAT,EAAkB,OAAOuD,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAxD,CAApC;EACD;;EAEiB,aAAPrB,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJsB,IAAI,GAAG;EAChB,UAAM,IAAIC,KAAJ,CAAU,qEAAV,CAAN;EACD;;EAEkB,aAARjB,QAAQ,GAAG;EACpB,WAAQ,MAAK,KAAKgB,IAAK,EAAvB;EACD;;EAEmB,aAATX,SAAS,GAAG;EACrB,WAAQ,IAAG,KAAKL,QAAS,EAAzB;EACD;;EAjDiB;;;;;;;;"}