{"version": 3, "file": "tooltip.js", "sources": ["../src/util/index.js", "../src/util/sanitizer.js", "../src/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attributeName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const element = elements[i]\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    attributeList.forEach(attribute => {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport { DefaultAllowlist, sanitizeHtml } from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // A trick to recreate a tooltip in case a new title is given by using the NOT documented `data-bs-original-title`\n    // This will be removed later in favor of a `setContent` method\n    if (this.constructor.NAME === 'tooltip' && this.tip && this.getTitle() !== this.tip.querySelector(SELECTOR_TOOLTIP_INNER).innerHTML) {\n      this._disposePopper()\n      this.tip.remove()\n      this.tip = null\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = this._resolvePossibleFunction(this._config.customClass)\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW) {\n        tip.remove()\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      this._disposePopper()\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    const tip = element.children[0]\n    this.setContent(tip)\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n\n    this.tip = tip\n    return this.tip\n  }\n\n  setContent(tip) {\n    this._sanitizeAndSetContent(tip, this.getTitle(), SELECTOR_TOOLTIP_INNER)\n  }\n\n  _sanitizeAndSetContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!content && templateElement) {\n      templateElement.remove()\n      return\n    }\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(templateElement, content)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.append(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    const title = this._element.getAttribute('data-bs-original-title') || this._config.title\n\n    return this._resolvePossibleFunction(title)\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    return context || this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(content) {\n    return typeof content === 'function' ? content.call(this._element) : content\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${this._getBasicClassPrefix()}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const basicClassPrefixRegex = new RegExp(`(^|\\\\s)${this._getBasicClassPrefix()}\\\\S+`, 'g')\n    const tabClass = tip.getAttribute('class').match(basicClassPrefixRegex)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _getBasicClassPrefix() {\n    return CLASS_PREFIX\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n"], "names": ["MAX_UID", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "querySelector", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "findShadowRoot", "element", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "parentNode", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "isRTL", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "uriAttributes", "Set", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "includes", "has", "Boolean", "nodeValue", "regExp", "filter", "attributeRegex", "i", "len", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "elements", "concat", "querySelectorAll", "elementName", "remove", "attributeList", "attributes", "allowedAttributes", "removeAttribute", "innerHTML", "DATA_KEY", "EVENT_KEY", "CLASS_PREFIX", "DISALLOWED_ATTRIBUTES", "DefaultType", "animation", "template", "title", "trigger", "delay", "html", "selector", "placement", "offset", "container", "fallbackPlacements", "boundary", "customClass", "sanitize", "popperConfig", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_FADE", "CLASS_NAME_MODAL", "CLASS_NAME_SHOW", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "<PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "_popper", "_config", "_getConfig", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "toggle", "event", "context", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "classList", "contains", "dispose", "clearTimeout", "EventHandler", "off", "_element", "closest", "_hideModalHandler", "_disposePopper", "show", "style", "display", "Error", "isWithContent", "showEvent", "shadowRoot", "isInTheDom", "ownerDocument", "defaultPrevented", "getTitle", "tipId", "setAttribute", "add", "attachment", "_getAttachment", "_addAttachmentClass", "Data", "set", "append", "update", "createPopper", "_getPopperConfig", "_resolvePossibleFunction", "split", "children", "on", "complete", "prevHoverState", "isAnimated", "_queueCallback", "hide", "_cleanTipClass", "hideEvent", "createElement", "<PERSON><PERSON><PERSON><PERSON>", "_sanitizeAndSetContent", "content", "templateElement", "SelectorEngine", "findOne", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "getAttribute", "updateAttachment", "getOrCreateInstance", "<PERSON><PERSON><PERSON><PERSON>", "_getDelegateConfig", "_getOffset", "map", "val", "Number", "parseInt", "popperData", "defaultBsPopperConfig", "modifiers", "options", "enabled", "phase", "data", "_handlePopperPlacementChange", "onFirstUpdate", "_getBasicClassPrefix", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "type", "setTimeout", "relatedTarget", "dataAttributes", "Manipulator", "getDataAttributes", "dataAttr", "key", "basicClassPrefixRegex", "tabClass", "token", "trim", "tClass", "state", "popper", "destroy", "each"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMA,OAAO,GAAG,OAAhB;;EAKA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;;;EAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBZ,OAA3B,CAAV;EACD,GAFD,QAESa,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EA4EA,MAAMM,SAAS,GAAGb,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACc,MAAX,KAAsB,WAA1B,EAAuC;EACrCd,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACe,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGhB,GAAG,IAAI;EACxB,MAAIa,SAAS,CAACb,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAACc,MAAJ,GAAad,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACiB,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAON,QAAQ,CAACO,aAAT,CAAuBlB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMmB,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIf,SAAS,CAACe,KAAD,CAAlB,GAA4B,SAA5B,GAAwC7B,MAAM,CAAC6B,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAsCA,MAAMO,cAAc,GAAGC,OAAO,IAAI;EAChC,MAAI,CAACxB,QAAQ,CAACyB,eAAT,CAAyBC,YAA9B,EAA4C;EAC1C,WAAO,IAAP;EACD,GAH+B;;;EAMhC,MAAI,OAAOF,OAAO,CAACG,WAAf,KAA+B,UAAnC,EAA+C;EAC7C,UAAMC,IAAI,GAAGJ,OAAO,CAACG,WAAR,EAAb;EACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;EACD;;EAED,MAAIJ,OAAO,YAAYK,UAAvB,EAAmC;EACjC,WAAOL,OAAP;EACD,GAb+B;;;EAgBhC,MAAI,CAACA,OAAO,CAACM,UAAb,EAAyB;EACvB,WAAO,IAAP;EACD;;EAED,SAAOP,cAAc,CAACC,OAAO,CAACM,UAAT,CAArB;EACD,CArBD;;EAuBA,MAAMC,IAAI,GAAG,MAAM,EAAnB;;EAeA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAACjC,QAAQ,CAACmC,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOH,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMI,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIvC,QAAQ,CAACwC,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAAC/B,MAA/B,EAAuC;EACrCN,MAAAA,QAAQ,CAACyC,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAACvB,OAA1B,CAAkCyB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAeA,MAAMI,KAAK,GAAG,MAAM3C,QAAQ,CAACyB,eAAT,CAAyBmB,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnCR,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMS,CAAC,GAAGf,SAAS,EAAnB;EACA;;EACA,QAAIe,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;ECvOA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMG,aAAa,GAAG,IAAIC,GAAJ,CAAQ,CAC5B,YAD4B,EAE5B,MAF4B,EAG5B,MAH4B,EAI5B,UAJ4B,EAK5B,UAL4B,EAM5B,QAN4B,EAO5B,KAP4B,EAQ5B,YAR4B,CAAR,CAAtB;EAWA,MAAMC,sBAAsB,GAAG,gBAA/B;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,gEAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,MAAMC,gBAAgB,GAAG,CAACC,SAAD,EAAYC,oBAAZ,KAAqC;EAC5D,QAAMC,aAAa,GAAGF,SAAS,CAACG,QAAV,CAAmBtE,WAAnB,EAAtB;;EAEA,MAAIoE,oBAAoB,CAACG,QAArB,CAA8BF,aAA9B,CAAJ,EAAkD;EAChD,QAAIR,aAAa,CAACW,GAAd,CAAkBH,aAAlB,CAAJ,EAAsC;EACpC,aAAOI,OAAO,CAACT,gBAAgB,CAACtC,IAAjB,CAAsByC,SAAS,CAACO,SAAhC,KAA8CT,gBAAgB,CAACvC,IAAjB,CAAsByC,SAAS,CAACO,SAAhC,CAA/C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,QAAMC,MAAM,GAAGP,oBAAoB,CAACQ,MAArB,CAA4BC,cAAc,IAAIA,cAAc,YAAYpD,MAAxE,CAAf,CAX4D;;EAc5D,OAAK,IAAIqD,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGJ,MAAM,CAAC/D,MAA7B,EAAqCkE,CAAC,GAAGC,GAAzC,EAA8CD,CAAC,EAA/C,EAAmD;EACjD,QAAIH,MAAM,CAACG,CAAD,CAAN,CAAUpD,IAAV,CAAe2C,aAAf,CAAJ,EAAmC;EACjC,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD,CArBD;;EAuBO,MAAMW,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCjB,sBAAvC,CAFyB;EAG9BkB,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9BjB,EAAAA,CAAC,EAAE,EAlB2B;EAmB9BkB,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAC9D,MAAI,CAACF,UAAU,CAAClG,MAAhB,EAAwB;EACtB,WAAOkG,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,QAAMG,SAAS,GAAG,IAAIzE,MAAM,CAAC0E,SAAX,EAAlB;EACA,QAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;EACA,QAAMO,QAAQ,GAAG,GAAGC,MAAH,CAAU,GAAGH,eAAe,CAAC1E,IAAhB,CAAqB8E,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;EAEA,OAAK,IAAIzC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGsC,QAAQ,CAACzG,MAA/B,EAAuCkE,CAAC,GAAGC,GAA3C,EAAgDD,CAAC,EAAjD,EAAqD;EACnD,UAAMhD,OAAO,GAAGuF,QAAQ,CAACvC,CAAD,CAAxB;EACA,UAAM0C,WAAW,GAAG1F,OAAO,CAACwC,QAAR,CAAiBtE,WAAjB,EAApB;;EAEA,QAAI,CAACkB,MAAM,CAACC,IAAP,CAAY4F,SAAZ,EAAuBxC,QAAvB,CAAgCiD,WAAhC,CAAL,EAAmD;EACjD1F,MAAAA,OAAO,CAAC2F,MAAR;EAEA;EACD;;EAED,UAAMC,aAAa,GAAG,GAAGJ,MAAH,CAAU,GAAGxF,OAAO,CAAC6F,UAArB,CAAtB;EACA,UAAMC,iBAAiB,GAAG,GAAGN,MAAH,CAAUP,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACS,WAAD,CAAT,IAA0B,EAA1D,CAA1B;EAEAE,IAAAA,aAAa,CAACtG,OAAd,CAAsB+C,SAAS,IAAI;EACjC,UAAI,CAACD,gBAAgB,CAACC,SAAD,EAAYyD,iBAAZ,CAArB,EAAqD;EACnD9F,QAAAA,OAAO,CAAC+F,eAAR,CAAwB1D,SAAS,CAACG,QAAlC;EACD;EACF,KAJD;EAKD;;EAED,SAAO6C,eAAe,CAAC1E,IAAhB,CAAqBqF,SAA5B;EACD;;EC7HD;EACA;EACA;EACA;EACA;EACA;EAqBA;EACA;EACA;EACA;EACA;;EAEA,MAAMvE,IAAI,GAAG,SAAb;EACA,MAAMwE,QAAQ,GAAG,YAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,YAArB;EACA,MAAMC,qBAAqB,GAAG,IAAIpE,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B;EAEA,MAAMqE,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlBC,EAAAA,OAAO,EAAE,QAJS;EAKlBC,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlBC,EAAAA,QAAQ,EAAE,kBAPQ;EAQlBC,EAAAA,SAAS,EAAE,mBARO;EASlBC,EAAAA,MAAM,EAAE,yBATU;EAUlBC,EAAAA,SAAS,EAAE,0BAVO;EAWlBC,EAAAA,kBAAkB,EAAE,OAXF;EAYlBC,EAAAA,QAAQ,EAAE,kBAZQ;EAalBC,EAAAA,WAAW,EAAE,mBAbK;EAclBC,EAAAA,QAAQ,EAAE,SAdQ;EAelBjC,EAAAA,UAAU,EAAE,iBAfM;EAgBlBD,EAAAA,SAAS,EAAE,QAhBO;EAiBlBmC,EAAAA,YAAY,EAAE;EAjBI,CAApB;EAoBA,MAAMC,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAErG,KAAK,KAAK,MAAL,GAAc,OAHN;EAIpBsG,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAEvG,KAAK,KAAK,OAAL,GAAe;EALN,CAAtB;EAQA,MAAMwG,OAAO,GAAG;EACdrB,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,mCAFF,GAGA,QALI;EAMdE,EAAAA,OAAO,EAAE,aANK;EAOdD,EAAAA,KAAK,EAAE,EAPO;EAQdE,EAAAA,KAAK,EAAE,CARO;EASdC,EAAAA,IAAI,EAAE,KATQ;EAUdC,EAAAA,QAAQ,EAAE,KAVI;EAWdC,EAAAA,SAAS,EAAE,KAXG;EAYdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAZM;EAadC,EAAAA,SAAS,EAAE,KAbG;EAcdC,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAdN;EAedC,EAAAA,QAAQ,EAAE,iBAfI;EAgBdC,EAAAA,WAAW,EAAE,EAhBC;EAiBdC,EAAAA,QAAQ,EAAE,IAjBI;EAkBdjC,EAAAA,UAAU,EAAE,IAlBE;EAmBdD,EAAAA,SAAS,EAAE/B,gBAnBG;EAoBdkE,EAAAA,YAAY,EAAE;EApBA,CAAhB;EAuBA,MAAMQ,KAAK,GAAG;EACZC,EAAAA,IAAI,EAAG,OAAM3B,SAAU,EADX;EAEZ4B,EAAAA,MAAM,EAAG,SAAQ5B,SAAU,EAFf;EAGZ6B,EAAAA,IAAI,EAAG,OAAM7B,SAAU,EAHX;EAIZ8B,EAAAA,KAAK,EAAG,QAAO9B,SAAU,EAJb;EAKZ+B,EAAAA,QAAQ,EAAG,WAAU/B,SAAU,EALnB;EAMZgC,EAAAA,KAAK,EAAG,QAAOhC,SAAU,EANb;EAOZiC,EAAAA,OAAO,EAAG,UAASjC,SAAU,EAPjB;EAQZkC,EAAAA,QAAQ,EAAG,WAAUlC,SAAU,EARnB;EASZmC,EAAAA,UAAU,EAAG,aAAYnC,SAAU,EATvB;EAUZoC,EAAAA,UAAU,EAAG,aAAYpC,SAAU;EAVvB,CAAd;EAaA,MAAMqC,eAAe,GAAG,MAAxB;EACA,MAAMC,gBAAgB,GAAG,OAAzB;EACA,MAAMC,eAAe,GAAG,MAAxB;EAEA,MAAMC,gBAAgB,GAAG,MAAzB;EACA,MAAMC,eAAe,GAAG,KAAxB;EAEA,MAAMC,sBAAsB,GAAG,gBAA/B;EACA,MAAMC,cAAc,GAAI,IAAGL,gBAAiB,EAA5C;EAEA,MAAMM,gBAAgB,GAAG,eAAzB;EAEA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,cAAc,GAAG,QAAvB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,OAAN,SAAsBC,8BAAtB,CAAoC;EAClCC,EAAAA,WAAW,CAACrJ,OAAD,EAAUd,MAAV,EAAkB;EAC3B,QAAI,OAAOoK,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAIzJ,SAAJ,CAAc,8DAAd,CAAN;EACD;;EAED,UAAMG,OAAN,EAL2B;;EAQ3B,SAAKuJ,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAKC,OAAL,GAAe,IAAf,CAZ2B;;EAe3B,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgB3K,MAAhB,CAAf;EACA,SAAK4K,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;EACD,GApBiC;;;EAwBhB,aAAPpC,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJlG,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD;;EAEe,aAALmG,KAAK,GAAG;EACjB,WAAOA,KAAP;EACD;;EAEqB,aAAXvB,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD,GAtCiC;;;EA0ClC2D,EAAAA,MAAM,GAAG;EACP,SAAKT,UAAL,GAAkB,IAAlB;EACD;;EAEDU,EAAAA,OAAO,GAAG;EACR,SAAKV,UAAL,GAAkB,KAAlB;EACD;;EAEDW,EAAAA,aAAa,GAAG;EACd,SAAKX,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;EAEDY,EAAAA,MAAM,CAACC,KAAD,EAAQ;EACZ,QAAI,CAAC,KAAKb,UAAV,EAAsB;EACpB;EACD;;EAED,QAAIa,KAAJ,EAAW;EACT,YAAMC,OAAO,GAAG,KAAKC,4BAAL,CAAkCF,KAAlC,CAAhB;;EAEAC,MAAAA,OAAO,CAACX,cAAR,CAAuBa,KAAvB,GAA+B,CAACF,OAAO,CAACX,cAAR,CAAuBa,KAAvD;;EAEA,UAAIF,OAAO,CAACG,oBAAR,EAAJ,EAAoC;EAClCH,QAAAA,OAAO,CAACI,MAAR,CAAe,IAAf,EAAqBJ,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACK,MAAR,CAAe,IAAf,EAAqBL,OAArB;EACD;EACF,KAVD,MAUO;EACL,UAAI,KAAKM,aAAL,GAAqBC,SAArB,CAA+BC,QAA/B,CAAwCpC,eAAxC,CAAJ,EAA8D;EAC5D,aAAKiC,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;EAEDK,EAAAA,OAAO,GAAG;EACRC,IAAAA,YAAY,CAAC,KAAKvB,QAAN,CAAZ;EAEAwB,IAAAA,6BAAY,CAACC,GAAb,CAAiB,KAAKC,QAAL,CAAcC,OAAd,CAAsBtC,cAAtB,CAAjB,EAAwDC,gBAAxD,EAA0E,KAAKsC,iBAA/E;;EAEA,QAAI,KAAKtB,GAAT,EAAc;EACZ,WAAKA,GAAL,CAASnE,MAAT;EACD;;EAED,SAAK0F,cAAL;;EACA,UAAMP,OAAN;EACD;;EAEDQ,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKJ,QAAL,CAAcK,KAAd,CAAoBC,OAApB,KAAgC,MAApC,EAA4C;EAC1C,YAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAI,EAAE,KAAKC,aAAL,MAAwB,KAAKnC,UAA/B,CAAJ,EAAgD;EAC9C;EACD;;EAED,UAAMoC,SAAS,GAAGX,6BAAY,CAACvE,OAAb,CAAqB,KAAKyE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBzB,KAAjB,CAAuBG,IAA3D,CAAlB;EACA,UAAM6D,UAAU,GAAG7L,cAAc,CAAC,KAAKmL,QAAN,CAAjC;EACA,UAAMW,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKV,QAAL,CAAcY,aAAd,CAA4B7L,eAA5B,CAA4C4K,QAA5C,CAAqD,KAAKK,QAA1D,CADiB,GAEjBU,UAAU,CAACf,QAAX,CAAoB,KAAKK,QAAzB,CAFF;;EAIA,QAAIS,SAAS,CAACI,gBAAV,IAA8B,CAACF,UAAnC,EAA+C;EAC7C;EACD,KAjBI;EAoBL;;;EACA,QAAI,KAAKxC,WAAL,CAAiB5H,IAAjB,KAA0B,SAA1B,IAAuC,KAAKqI,GAA5C,IAAmD,KAAKkC,QAAL,OAAoB,KAAKlC,GAAL,CAAS/K,aAAT,CAAuB6J,sBAAvB,EAA+C5C,SAA1H,EAAqI;EACnI,WAAKqF,cAAL;;EACA,WAAKvB,GAAL,CAASnE,MAAT;EACA,WAAKmE,GAAL,GAAW,IAAX;EACD;;EAED,UAAMA,GAAG,GAAG,KAAKa,aAAL,EAAZ;EACA,UAAMsB,KAAK,GAAG9N,MAAM,CAAC,KAAKkL,WAAL,CAAiB5H,IAAlB,CAApB;EAEAqI,IAAAA,GAAG,CAACoC,YAAJ,CAAiB,IAAjB,EAAuBD,KAAvB;;EACA,SAAKf,QAAL,CAAcgB,YAAd,CAA2B,kBAA3B,EAA+CD,KAA/C;;EAEA,QAAI,KAAKrC,OAAL,CAAatD,SAAjB,EAA4B;EAC1BwD,MAAAA,GAAG,CAACc,SAAJ,CAAcuB,GAAd,CAAkB5D,eAAlB;EACD;;EAED,UAAM1B,SAAS,GAAG,OAAO,KAAK+C,OAAL,CAAa/C,SAApB,KAAkC,UAAlC,GAChB,KAAK+C,OAAL,CAAa/C,SAAb,CAAuB7I,IAAvB,CAA4B,IAA5B,EAAkC8L,GAAlC,EAAuC,KAAKoB,QAA5C,CADgB,GAEhB,KAAKtB,OAAL,CAAa/C,SAFf;;EAIA,UAAMuF,UAAU,GAAG,KAAKC,cAAL,CAAoBxF,SAApB,CAAnB;;EACA,SAAKyF,mBAAL,CAAyBF,UAAzB;;EAEA,UAAM;EAAErF,MAAAA;EAAF,QAAgB,KAAK6C,OAA3B;EACA2C,IAAAA,qBAAI,CAACC,GAAL,CAAS1C,GAAT,EAAc,KAAKT,WAAL,CAAiBpD,QAA/B,EAAyC,IAAzC;;EAEA,QAAI,CAAC,KAAKiF,QAAL,CAAcY,aAAd,CAA4B7L,eAA5B,CAA4C4K,QAA5C,CAAqD,KAAKf,GAA1D,CAAL,EAAqE;EACnE/C,MAAAA,SAAS,CAAC0F,MAAV,CAAiB3C,GAAjB;EACAkB,MAAAA,6BAAY,CAACvE,OAAb,CAAqB,KAAKyE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBzB,KAAjB,CAAuBK,QAA3D;EACD;;EAED,QAAI,KAAK0B,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAa+C,MAAb;EACD,KAFD,MAEO;EACL,WAAK/C,OAAL,GAAeL,iBAAM,CAACqD,YAAP,CAAoB,KAAKzB,QAAzB,EAAmCpB,GAAnC,EAAwC,KAAK8C,gBAAL,CAAsBR,UAAtB,CAAxC,CAAf;EACD;;EAEDtC,IAAAA,GAAG,CAACc,SAAJ,CAAcuB,GAAd,CAAkB1D,eAAlB;;EAEA,UAAMvB,WAAW,GAAG,KAAK2F,wBAAL,CAA8B,KAAKjD,OAAL,CAAa1C,WAA3C,CAApB;;EACA,QAAIA,WAAJ,EAAiB;EACf4C,MAAAA,GAAG,CAACc,SAAJ,CAAcuB,GAAd,CAAkB,GAAGjF,WAAW,CAAC4F,KAAZ,CAAkB,GAAlB,CAArB;EACD,KA/DI;EAkEL;EACA;EACA;;;EACA,QAAI,kBAAkBtO,QAAQ,CAACyB,eAA/B,EAAgD;EAC9C,SAAGuF,MAAH,CAAU,GAAGhH,QAAQ,CAACmC,IAAT,CAAcoM,QAA3B,EAAqCzN,OAArC,CAA6CU,OAAO,IAAI;EACtDgL,QAAAA,6BAAY,CAACgC,EAAb,CAAgBhN,OAAhB,EAAyB,WAAzB,EAAsCO,IAAtC;EACD,OAFD;EAGD;;EAED,UAAM0M,QAAQ,GAAG,MAAM;EACrB,YAAMC,cAAc,GAAG,KAAKzD,WAA5B;EAEA,WAAKA,WAAL,GAAmB,IAAnB;EACAuB,MAAAA,6BAAY,CAACvE,OAAb,CAAqB,KAAKyE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBzB,KAAjB,CAAuBI,KAA3D;;EAEA,UAAIkF,cAAc,KAAKvE,eAAvB,EAAwC;EACtC,aAAK+B,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF,KATD;;EAWA,UAAMyC,UAAU,GAAG,KAAKrD,GAAL,CAASc,SAAT,CAAmBC,QAAnB,CAA4BtC,eAA5B,CAAnB;;EACA,SAAK6E,cAAL,CAAoBH,QAApB,EAA8B,KAAKnD,GAAnC,EAAwCqD,UAAxC;EACD;;EAEDE,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAK1D,OAAV,EAAmB;EACjB;EACD;;EAED,UAAMG,GAAG,GAAG,KAAKa,aAAL,EAAZ;;EACA,UAAMsC,QAAQ,GAAG,MAAM;EACrB,UAAI,KAAKzC,oBAAL,EAAJ,EAAiC;EAC/B;EACD;;EAED,UAAI,KAAKf,WAAL,KAAqBf,gBAAzB,EAA2C;EACzCoB,QAAAA,GAAG,CAACnE,MAAJ;EACD;;EAED,WAAK2H,cAAL;;EACA,WAAKpC,QAAL,CAAcnF,eAAd,CAA8B,kBAA9B;;EACAiF,MAAAA,6BAAY,CAACvE,OAAb,CAAqB,KAAKyE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBzB,KAAjB,CAAuBE,MAA3D;;EAEA,WAAKuD,cAAL;EACD,KAdD;;EAgBA,UAAMkC,SAAS,GAAGvC,6BAAY,CAACvE,OAAb,CAAqB,KAAKyE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBzB,KAAjB,CAAuBC,IAA3D,CAAlB;;EACA,QAAI0F,SAAS,CAACxB,gBAAd,EAAgC;EAC9B;EACD;;EAEDjC,IAAAA,GAAG,CAACc,SAAJ,CAAcjF,MAAd,CAAqB8C,eAArB,EA3BK;EA8BL;;EACA,QAAI,kBAAkBjK,QAAQ,CAACyB,eAA/B,EAAgD;EAC9C,SAAGuF,MAAH,CAAU,GAAGhH,QAAQ,CAACmC,IAAT,CAAcoM,QAA3B,EACGzN,OADH,CACWU,OAAO,IAAIgL,6BAAY,CAACC,GAAb,CAAiBjL,OAAjB,EAA0B,WAA1B,EAAuCO,IAAvC,CADtB;EAED;;EAED,SAAKmJ,cAAL,CAAoBT,aAApB,IAAqC,KAArC;EACA,SAAKS,cAAL,CAAoBV,aAApB,IAAqC,KAArC;EACA,SAAKU,cAAL,CAAoBX,aAApB,IAAqC,KAArC;EAEA,UAAMoE,UAAU,GAAG,KAAKrD,GAAL,CAASc,SAAT,CAAmBC,QAAnB,CAA4BtC,eAA5B,CAAnB;;EACA,SAAK6E,cAAL,CAAoBH,QAApB,EAA8B,KAAKnD,GAAnC,EAAwCqD,UAAxC;;EACA,SAAK1D,WAAL,GAAmB,EAAnB;EACD;;EAEDiD,EAAAA,MAAM,GAAG;EACP,QAAI,KAAK/C,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa+C,MAAb;EACD;EACF,GAvOiC;;;EA2OlChB,EAAAA,aAAa,GAAG;EACd,WAAO/I,OAAO,CAAC,KAAKqJ,QAAL,EAAD,CAAd;EACD;;EAEDrB,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKb,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,UAAM9J,OAAO,GAAGxB,QAAQ,CAACgP,aAAT,CAAuB,KAAvB,CAAhB;EACAxN,IAAAA,OAAO,CAACgG,SAAR,GAAoB,KAAK4D,OAAL,CAAarD,QAAjC;EAEA,UAAMuD,GAAG,GAAG9J,OAAO,CAAC+M,QAAR,CAAiB,CAAjB,CAAZ;EACA,SAAKU,UAAL,CAAgB3D,GAAhB;EACAA,IAAAA,GAAG,CAACc,SAAJ,CAAcjF,MAAd,CAAqB4C,eAArB,EAAsCE,eAAtC;EAEA,SAAKqB,GAAL,GAAWA,GAAX;EACA,WAAO,KAAKA,GAAZ;EACD;;EAED2D,EAAAA,UAAU,CAAC3D,GAAD,EAAM;EACd,SAAK4D,sBAAL,CAA4B5D,GAA5B,EAAiC,KAAKkC,QAAL,EAAjC,EAAkDpD,sBAAlD;EACD;;EAED8E,EAAAA,sBAAsB,CAACnH,QAAD,EAAWoH,OAAX,EAAoB/G,QAApB,EAA8B;EAClD,UAAMgH,eAAe,GAAGC,+BAAc,CAACC,OAAf,CAAuBlH,QAAvB,EAAiCL,QAAjC,CAAxB;;EAEA,QAAI,CAACoH,OAAD,IAAYC,eAAhB,EAAiC;EAC/BA,MAAAA,eAAe,CAACjI,MAAhB;EACA;EACD,KANiD;;;EASlD,SAAKoI,iBAAL,CAAuBH,eAAvB,EAAwCD,OAAxC;EACD;;EAEDI,EAAAA,iBAAiB,CAAC/N,OAAD,EAAU2N,OAAV,EAAmB;EAClC,QAAI3N,OAAO,KAAK,IAAhB,EAAsB;EACpB;EACD;;EAED,QAAItB,SAAS,CAACiP,OAAD,CAAb,EAAwB;EACtBA,MAAAA,OAAO,GAAG9O,UAAU,CAAC8O,OAAD,CAApB,CADsB;;EAItB,UAAI,KAAK/D,OAAL,CAAajD,IAAjB,EAAuB;EACrB,YAAIgH,OAAO,CAACrN,UAAR,KAAuBN,OAA3B,EAAoC;EAClCA,UAAAA,OAAO,CAACgG,SAAR,GAAoB,EAApB;EACAhG,UAAAA,OAAO,CAACyM,MAAR,CAAekB,OAAf;EACD;EACF,OALD,MAKO;EACL3N,QAAAA,OAAO,CAACgO,WAAR,GAAsBL,OAAO,CAACK,WAA9B;EACD;;EAED;EACD;;EAED,QAAI,KAAKpE,OAAL,CAAajD,IAAjB,EAAuB;EACrB,UAAI,KAAKiD,OAAL,CAAazC,QAAjB,EAA2B;EACzBwG,QAAAA,OAAO,GAAG5I,YAAY,CAAC4I,OAAD,EAAU,KAAK/D,OAAL,CAAa3E,SAAvB,EAAkC,KAAK2E,OAAL,CAAa1E,UAA/C,CAAtB;EACD;;EAEDlF,MAAAA,OAAO,CAACgG,SAAR,GAAoB2H,OAApB;EACD,KAND,MAMO;EACL3N,MAAAA,OAAO,CAACgO,WAAR,GAAsBL,OAAtB;EACD;EACF;;EAED3B,EAAAA,QAAQ,GAAG;EACT,UAAMxF,KAAK,GAAG,KAAK0E,QAAL,CAAc+C,YAAd,CAA2B,wBAA3B,KAAwD,KAAKrE,OAAL,CAAapD,KAAnF;;EAEA,WAAO,KAAKqG,wBAAL,CAA8BrG,KAA9B,CAAP;EACD;;EAED0H,EAAAA,gBAAgB,CAAC9B,UAAD,EAAa;EAC3B,QAAIA,UAAU,KAAK,OAAnB,EAA4B;EAC1B,aAAO,KAAP;EACD;;EAED,QAAIA,UAAU,KAAK,MAAnB,EAA2B;EACzB,aAAO,OAAP;EACD;;EAED,WAAOA,UAAP;EACD,GA/TiC;;;EAmUlC9B,EAAAA,4BAA4B,CAACF,KAAD,EAAQC,OAAR,EAAiB;EAC3C,WAAOA,OAAO,IAAI,KAAKhB,WAAL,CAAiB8E,mBAAjB,CAAqC/D,KAAK,CAACgE,cAA3C,EAA2D,KAAKC,kBAAL,EAA3D,CAAlB;EACD;;EAEDC,EAAAA,UAAU,GAAG;EACX,UAAM;EAAExH,MAAAA;EAAF,QAAa,KAAK8C,OAAxB;;EAEA,QAAI,OAAO9C,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAACgG,KAAP,CAAa,GAAb,EAAkByB,GAAlB,CAAsBC,GAAG,IAAIC,MAAM,CAACC,QAAP,CAAgBF,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAO1H,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAO6H,UAAU,IAAI7H,MAAM,CAAC6H,UAAD,EAAa,KAAKzD,QAAlB,CAA3B;EACD;;EAED,WAAOpE,MAAP;EACD;;EAED+F,EAAAA,wBAAwB,CAACc,OAAD,EAAU;EAChC,WAAO,OAAOA,OAAP,KAAmB,UAAnB,GAAgCA,OAAO,CAAC3P,IAAR,CAAa,KAAKkN,QAAlB,CAAhC,GAA8DyC,OAArE;EACD;;EAEDf,EAAAA,gBAAgB,CAACR,UAAD,EAAa;EAC3B,UAAMwC,qBAAqB,GAAG;EAC5B/H,MAAAA,SAAS,EAAEuF,UADiB;EAE5ByC,MAAAA,SAAS,EAAE,CACT;EACErN,QAAAA,IAAI,EAAE,MADR;EAEEsN,QAAAA,OAAO,EAAE;EACP9H,UAAAA,kBAAkB,EAAE,KAAK4C,OAAL,CAAa5C;EAD1B;EAFX,OADS,EAOT;EACExF,QAAAA,IAAI,EAAE,QADR;EAEEsN,QAAAA,OAAO,EAAE;EACPhI,UAAAA,MAAM,EAAE,KAAKwH,UAAL;EADD;EAFX,OAPS,EAaT;EACE9M,QAAAA,IAAI,EAAE,iBADR;EAEEsN,QAAAA,OAAO,EAAE;EACP7H,UAAAA,QAAQ,EAAE,KAAK2C,OAAL,CAAa3C;EADhB;EAFX,OAbS,EAmBT;EACEzF,QAAAA,IAAI,EAAE,OADR;EAEEsN,QAAAA,OAAO,EAAE;EACP9O,UAAAA,OAAO,EAAG,IAAG,KAAKqJ,WAAL,CAAiB5H,IAAK;EAD5B;EAFX,OAnBS,EAyBT;EACED,QAAAA,IAAI,EAAE,UADR;EAEEuN,QAAAA,OAAO,EAAE,IAFX;EAGEC,QAAAA,KAAK,EAAE,YAHT;EAIErN,QAAAA,EAAE,EAAEsN,IAAI,IAAI,KAAKC,4BAAL,CAAkCD,IAAlC;EAJd,OAzBS,CAFiB;EAkC5BE,MAAAA,aAAa,EAAEF,IAAI,IAAI;EACrB,YAAIA,IAAI,CAACH,OAAL,CAAajI,SAAb,KAA2BoI,IAAI,CAACpI,SAApC,EAA+C;EAC7C,eAAKqI,4BAAL,CAAkCD,IAAlC;EACD;EACF;EAtC2B,KAA9B;EAyCA,WAAO,EACL,GAAGL,qBADE;EAEL,UAAI,OAAO,KAAKhF,OAAL,CAAaxC,YAApB,KAAqC,UAArC,GAAkD,KAAKwC,OAAL,CAAaxC,YAAb,CAA0BwH,qBAA1B,CAAlD,GAAqG,KAAKhF,OAAL,CAAaxC,YAAtH;EAFK,KAAP;EAID;;EAEDkF,EAAAA,mBAAmB,CAACF,UAAD,EAAa;EAC9B,SAAKzB,aAAL,GAAqBC,SAArB,CAA+BuB,GAA/B,CAAoC,GAAE,KAAKiD,oBAAL,EAA4B,IAAG,KAAKlB,gBAAL,CAAsB9B,UAAtB,CAAkC,EAAvG;EACD;;EAEDC,EAAAA,cAAc,CAACxF,SAAD,EAAY;EACxB,WAAOQ,aAAa,CAACR,SAAS,CAAC/G,WAAV,EAAD,CAApB;EACD;;EAEDiK,EAAAA,aAAa,GAAG;EACd,UAAMsF,QAAQ,GAAG,KAAKzF,OAAL,CAAanD,OAAb,CAAqBqG,KAArB,CAA2B,GAA3B,CAAjB;;EAEAuC,IAAAA,QAAQ,CAAC/P,OAAT,CAAiBmH,OAAO,IAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvBuE,QAAAA,6BAAY,CAACgC,EAAb,CAAgB,KAAK9B,QAArB,EAA+B,KAAK7B,WAAL,CAAiBzB,KAAjB,CAAuBM,KAAtD,EAA6D,KAAK0B,OAAL,CAAahD,QAA1E,EAAoFwD,KAAK,IAAI,KAAKD,MAAL,CAAYC,KAAZ,CAA7F;EACD,OAFD,MAEO,IAAI3D,OAAO,KAAKyC,cAAhB,EAAgC;EACrC,cAAMoG,OAAO,GAAG7I,OAAO,KAAKsC,aAAZ,GACd,KAAKM,WAAL,CAAiBzB,KAAjB,CAAuBS,UADT,GAEd,KAAKgB,WAAL,CAAiBzB,KAAjB,CAAuBO,OAFzB;EAGA,cAAMoH,QAAQ,GAAG9I,OAAO,KAAKsC,aAAZ,GACf,KAAKM,WAAL,CAAiBzB,KAAjB,CAAuBU,UADR,GAEf,KAAKe,WAAL,CAAiBzB,KAAjB,CAAuBQ,QAFzB;EAIA4C,QAAAA,6BAAY,CAACgC,EAAb,CAAgB,KAAK9B,QAArB,EAA+BoE,OAA/B,EAAwC,KAAK1F,OAAL,CAAahD,QAArD,EAA+DwD,KAAK,IAAI,KAAKK,MAAL,CAAYL,KAAZ,CAAxE;EACAY,QAAAA,6BAAY,CAACgC,EAAb,CAAgB,KAAK9B,QAArB,EAA+BqE,QAA/B,EAAyC,KAAK3F,OAAL,CAAahD,QAAtD,EAAgEwD,KAAK,IAAI,KAAKM,MAAL,CAAYN,KAAZ,CAAzE;EACD;EACF,KAdD;;EAgBA,SAAKgB,iBAAL,GAAyB,MAAM;EAC7B,UAAI,KAAKF,QAAT,EAAmB;EACjB,aAAKmC,IAAL;EACD;EACF,KAJD;;EAMArC,IAAAA,6BAAY,CAACgC,EAAb,CAAgB,KAAK9B,QAAL,CAAcC,OAAd,CAAsBtC,cAAtB,CAAhB,EAAuDC,gBAAvD,EAAyE,KAAKsC,iBAA9E;;EAEA,QAAI,KAAKxB,OAAL,CAAahD,QAAjB,EAA2B;EACzB,WAAKgD,OAAL,GAAe,EACb,GAAG,KAAKA,OADK;EAEbnD,QAAAA,OAAO,EAAE,QAFI;EAGbG,QAAAA,QAAQ,EAAE;EAHG,OAAf;EAKD,KAND,MAMO;EACL,WAAK4I,SAAL;EACD;EACF;;EAEDA,EAAAA,SAAS,GAAG;EACV,UAAMhJ,KAAK,GAAG,KAAK0E,QAAL,CAAc+C,YAAd,CAA2B,OAA3B,CAAd;;EACA,UAAMwB,iBAAiB,GAAG,OAAO,KAAKvE,QAAL,CAAc+C,YAAd,CAA2B,wBAA3B,CAAjC;;EAEA,QAAIzH,KAAK,IAAIiJ,iBAAiB,KAAK,QAAnC,EAA6C;EAC3C,WAAKvE,QAAL,CAAcgB,YAAd,CAA2B,wBAA3B,EAAqD1F,KAAK,IAAI,EAA9D;;EACA,UAAIA,KAAK,IAAI,CAAC,KAAK0E,QAAL,CAAc+C,YAAd,CAA2B,YAA3B,CAAV,IAAsD,CAAC,KAAK/C,QAAL,CAAc8C,WAAzE,EAAsF;EACpF,aAAK9C,QAAL,CAAcgB,YAAd,CAA2B,YAA3B,EAAyC1F,KAAzC;EACD;;EAED,WAAK0E,QAAL,CAAcgB,YAAd,CAA2B,OAA3B,EAAoC,EAApC;EACD;EACF;;EAEDzB,EAAAA,MAAM,CAACL,KAAD,EAAQC,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKC,4BAAL,CAAkCF,KAAlC,EAAyCC,OAAzC,CAAV;;EAEA,QAAID,KAAJ,EAAW;EACTC,MAAAA,OAAO,CAACX,cAAR,CACEU,KAAK,CAACsF,IAAN,KAAe,SAAf,GAA2B1G,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAIsB,OAAO,CAACM,aAAR,GAAwBC,SAAxB,CAAkCC,QAAlC,CAA2CpC,eAA3C,KAA+D4B,OAAO,CAACZ,WAAR,KAAwBf,gBAA3F,EAA6G;EAC3G2B,MAAAA,OAAO,CAACZ,WAAR,GAAsBf,gBAAtB;EACA;EACD;;EAEDqC,IAAAA,YAAY,CAACV,OAAO,CAACb,QAAT,CAAZ;EAEAa,IAAAA,OAAO,CAACZ,WAAR,GAAsBf,gBAAtB;;EAEA,QAAI,CAAC2B,OAAO,CAACT,OAAR,CAAgBlD,KAAjB,IAA0B,CAAC2D,OAAO,CAACT,OAAR,CAAgBlD,KAAhB,CAAsB4E,IAArD,EAA2D;EACzDjB,MAAAA,OAAO,CAACiB,IAAR;EACA;EACD;;EAEDjB,IAAAA,OAAO,CAACb,QAAR,GAAmBmG,UAAU,CAAC,MAAM;EAClC,UAAItF,OAAO,CAACZ,WAAR,KAAwBf,gBAA5B,EAA8C;EAC5C2B,QAAAA,OAAO,CAACiB,IAAR;EACD;EACF,KAJ4B,EAI1BjB,OAAO,CAACT,OAAR,CAAgBlD,KAAhB,CAAsB4E,IAJI,CAA7B;EAKD;;EAEDZ,EAAAA,MAAM,CAACN,KAAD,EAAQC,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKC,4BAAL,CAAkCF,KAAlC,EAAyCC,OAAzC,CAAV;;EAEA,QAAID,KAAJ,EAAW;EACTC,MAAAA,OAAO,CAACX,cAAR,CACEU,KAAK,CAACsF,IAAN,KAAe,UAAf,GAA4B1G,aAA5B,GAA4CD,aAD9C,IAEIsB,OAAO,CAACa,QAAR,CAAiBL,QAAjB,CAA0BT,KAAK,CAACwF,aAAhC,CAFJ;EAGD;;EAED,QAAIvF,OAAO,CAACG,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAEDO,IAAAA,YAAY,CAACV,OAAO,CAACb,QAAT,CAAZ;EAEAa,IAAAA,OAAO,CAACZ,WAAR,GAAsBd,eAAtB;;EAEA,QAAI,CAAC0B,OAAO,CAACT,OAAR,CAAgBlD,KAAjB,IAA0B,CAAC2D,OAAO,CAACT,OAAR,CAAgBlD,KAAhB,CAAsB2G,IAArD,EAA2D;EACzDhD,MAAAA,OAAO,CAACgD,IAAR;EACA;EACD;;EAEDhD,IAAAA,OAAO,CAACb,QAAR,GAAmBmG,UAAU,CAAC,MAAM;EAClC,UAAItF,OAAO,CAACZ,WAAR,KAAwBd,eAA5B,EAA6C;EAC3C0B,QAAAA,OAAO,CAACgD,IAAR;EACD;EACF,KAJ4B,EAI1BhD,OAAO,CAACT,OAAR,CAAgBlD,KAAhB,CAAsB2G,IAJI,CAA7B;EAKD;;EAED7C,EAAAA,oBAAoB,GAAG;EACrB,SAAK,MAAM/D,OAAX,IAAsB,KAAKiD,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoBjD,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;EAEDoD,EAAAA,UAAU,CAAC3K,MAAD,EAAS;EACjB,UAAM2Q,cAAc,GAAGC,4BAAW,CAACC,iBAAZ,CAA8B,KAAK7E,QAAnC,CAAvB;EAEA9L,IAAAA,MAAM,CAACC,IAAP,CAAYwQ,cAAZ,EAA4BvQ,OAA5B,CAAoC0Q,QAAQ,IAAI;EAC9C,UAAI5J,qBAAqB,CAAC1D,GAAtB,CAA0BsN,QAA1B,CAAJ,EAAyC;EACvC,eAAOH,cAAc,CAACG,QAAD,CAArB;EACD;EACF,KAJD;EAMA9Q,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKmK,WAAL,CAAiB1B,OADb;EAEP,SAAGkI,cAFI;EAGP,UAAI,OAAO3Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;EAMAA,IAAAA,MAAM,CAAC6H,SAAP,GAAmB7H,MAAM,CAAC6H,SAAP,KAAqB,KAArB,GAA6BvI,QAAQ,CAACmC,IAAtC,GAA6C9B,UAAU,CAACK,MAAM,CAAC6H,SAAR,CAA1E;;EAEA,QAAI,OAAO7H,MAAM,CAACwH,KAAd,KAAwB,QAA5B,EAAsC;EACpCxH,MAAAA,MAAM,CAACwH,KAAP,GAAe;EACb4E,QAAAA,IAAI,EAAEpM,MAAM,CAACwH,KADA;EAEb2G,QAAAA,IAAI,EAAEnO,MAAM,CAACwH;EAFA,OAAf;EAID;;EAED,QAAI,OAAOxH,MAAM,CAACsH,KAAd,KAAwB,QAA5B,EAAsC;EACpCtH,MAAAA,MAAM,CAACsH,KAAP,GAAetH,MAAM,CAACsH,KAAP,CAAazI,QAAb,EAAf;EACD;;EAED,QAAI,OAAOmB,MAAM,CAACyO,OAAd,KAA0B,QAA9B,EAAwC;EACtCzO,MAAAA,MAAM,CAACyO,OAAP,GAAiBzO,MAAM,CAACyO,OAAP,CAAe5P,QAAf,EAAjB;EACD;;EAEDiB,IAAAA,eAAe,CAACyC,IAAD,EAAOvC,MAAP,EAAe,KAAKmK,WAAL,CAAiBhD,WAAhC,CAAf;;EAEA,QAAInH,MAAM,CAACiI,QAAX,EAAqB;EACnBjI,MAAAA,MAAM,CAACqH,QAAP,GAAkBxB,YAAY,CAAC7F,MAAM,CAACqH,QAAR,EAAkBrH,MAAM,CAAC+F,SAAzB,EAAoC/F,MAAM,CAACgG,UAA3C,CAA9B;EACD;;EAED,WAAOhG,MAAP;EACD;;EAEDmP,EAAAA,kBAAkB,GAAG;EACnB,UAAMnP,MAAM,GAAG,EAAf;;EAEA,SAAK,MAAM+Q,GAAX,IAAkB,KAAKrG,OAAvB,EAAgC;EAC9B,UAAI,KAAKP,WAAL,CAAiB1B,OAAjB,CAAyBsI,GAAzB,MAAkC,KAAKrG,OAAL,CAAaqG,GAAb,CAAtC,EAAyD;EACvD/Q,QAAAA,MAAM,CAAC+Q,GAAD,CAAN,GAAc,KAAKrG,OAAL,CAAaqG,GAAb,CAAd;EACD;EACF,KAPkB;EAUnB;EACA;;;EACA,WAAO/Q,MAAP;EACD;;EAEDoO,EAAAA,cAAc,GAAG;EACf,UAAMxD,GAAG,GAAG,KAAKa,aAAL,EAAZ;EACA,UAAMuF,qBAAqB,GAAG,IAAIvQ,MAAJ,CAAY,UAAS,KAAKyP,oBAAL,EAA4B,MAAjD,EAAwD,GAAxD,CAA9B;EACA,UAAMe,QAAQ,GAAGrG,GAAG,CAACmE,YAAJ,CAAiB,OAAjB,EAA0BhQ,KAA1B,CAAgCiS,qBAAhC,CAAjB;;EACA,QAAIC,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACrR,MAAT,GAAkB,CAA3C,EAA8C;EAC5CqR,MAAAA,QAAQ,CAAC5B,GAAT,CAAa6B,KAAK,IAAIA,KAAK,CAACC,IAAN,EAAtB,EACG/Q,OADH,CACWgR,MAAM,IAAIxG,GAAG,CAACc,SAAJ,CAAcjF,MAAd,CAAqB2K,MAArB,CADrB;EAED;EACF;;EAEDlB,EAAAA,oBAAoB,GAAG;EACrB,WAAOjJ,YAAP;EACD;;EAED+I,EAAAA,4BAA4B,CAACP,UAAD,EAAa;EACvC,UAAM;EAAE4B,MAAAA;EAAF,QAAY5B,UAAlB;;EAEA,QAAI,CAAC4B,KAAL,EAAY;EACV;EACD;;EAED,SAAKzG,GAAL,GAAWyG,KAAK,CAAChL,QAAN,CAAeiL,MAA1B;;EACA,SAAKlD,cAAL;;EACA,SAAKhB,mBAAL,CAAyB,KAAKD,cAAL,CAAoBkE,KAAK,CAAC1J,SAA1B,CAAzB;EACD;;EAEDwE,EAAAA,cAAc,GAAG;EACf,QAAI,KAAK1B,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAa8G,OAAb;;EACA,WAAK9G,OAAL,GAAe,IAAf;EACD;EACF,GAjmBiC;;;EAqmBZ,SAAf/H,eAAe,CAAC1C,MAAD,EAAS;EAC7B,WAAO,KAAKwR,IAAL,CAAU,YAAY;EAC3B,YAAMzB,IAAI,GAAG9F,OAAO,CAACgF,mBAAR,CAA4B,IAA5B,EAAkCjP,MAAlC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO+P,IAAI,CAAC/P,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED+P,QAAAA,IAAI,CAAC/P,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EAjnBiC;EAonBpC;EACA;EACA;EACA;EACA;EACA;;;EAEAmC,kBAAkB,CAAC8H,OAAD,CAAlB;;;;;;;;"}