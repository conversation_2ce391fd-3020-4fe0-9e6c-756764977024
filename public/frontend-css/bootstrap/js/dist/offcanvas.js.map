{"version": 3, "file": "offcanvas.js", "sources": ["../src/util/index.js", "../src/util/scrollbar.js", "../src/util/backdrop.js", "../src/util/focustrap.js", "../src/util/component-functions.js", "../src/offcanvas.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.append(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport { typeCheckConfig } from './index'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nclass FocusTrap {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  activate() {\n    const { trapElement, autofocus } = this._config\n\n    if (this._isActive) {\n      return\n    }\n\n    if (autofocus) {\n      trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n\n  _handleFocusin(event) {\n    const { target } = event\n    const { trapElement } = this._config\n\n    if (target === document || target === trapElement || trapElement.contains(target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll) {\n        this._focustrap.activate()\n      }\n\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () =>\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => Offcanvas.getOrCreateInstance(el).show())\n)\n\nenableDismissTrigger(Offcanvas)\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n"], "names": ["MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "target", "removeEventListener", "setTimeout", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "ScrollBarHelper", "constructor", "_element", "getWidth", "documentWidth", "documentElement", "clientWidth", "Math", "abs", "innerWidth", "hide", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "style", "overflow", "styleProp", "scrollbarWidth", "manipulationCallBack", "_applyManipulationCallback", "reset", "_resetElementAttributes", "actualValue", "Manipulator", "setDataAttribute", "getDataAttribute", "removeProperty", "removeDataAttribute", "callBack", "SelectorEngine", "find", "isOverflowing", "<PERSON><PERSON><PERSON>", "className", "isAnimated", "rootElement", "clickCallback", "DefaultType", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "Backdrop", "_config", "_getConfig", "_isAppended", "show", "_append", "_getElement", "add", "_emulateAnimation", "remove", "dispose", "backdrop", "createElement", "append", "EventHandler", "on", "off", "trapElement", "autofocus", "DATA_KEY", "EVENT_KEY", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_KEY", "TAB_NAV_FORWARD", "TAB_NAV_BACKWARD", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "focus", "event", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "focusableC<PERSON><PERSON>n", "key", "shift<PERSON>ey", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "preventDefault", "closest", "instance", "getOrCreateInstance", "DATA_API_KEY", "EVENT_LOAD_DATA_API", "ESCAPE_KEY", "keyboard", "scroll", "CLASS_NAME_BACKDROP", "OPEN_SELECTOR", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DISMISS", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "BaseComponent", "_isShown", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_addEventListeners", "toggle", "relatedTarget", "showEvent", "trigger", "defaultPrevented", "visibility", "removeAttribute", "setAttribute", "completeCallBack", "_queueCallback", "hideEvent", "blur", "completeCallback", "getDataAttributes", "parentNode", "each", "data", "one", "allReadyOpen", "findOne", "getInstance", "el"], "mappings": ";;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAGA,MAAMA,uBAAuB,GAAG,IAAhC;EACA,MAAMC,cAAc,GAAG,eAAvB;;EAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,MAAMU,gCAAgC,GAAGX,OAAO,IAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAI;EAAEY,IAAAA,kBAAF;EAAsBC,IAAAA;EAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAA9C;EAEA,QAAMgB,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;EACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;EAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAdiD;;;EAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACN,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAO,EAAAA,eAAe,GAAGA,eAAe,CAACP,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACW,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EvB,uBAAtF;EACD,CArBD;;EAuBA,MAAM8B,oBAAoB,GAAGpB,OAAO,IAAI;EACtCA,EAAAA,OAAO,CAACqB,aAAR,CAAsB,IAAIC,KAAJ,CAAU/B,cAAV,CAAtB;EACD,CAFD;;EAIA,MAAMgC,SAAS,GAAG9B,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAAC+B,MAAX,KAAsB,WAA1B,EAAuC;EACrC/B,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACgC,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGjC,GAAG,IAAI;EACxB,MAAI8B,SAAS,CAAC9B,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAAC+B,MAAJ,GAAa/B,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACkC,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOlB,QAAQ,CAACC,aAAT,CAAuBjB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMmC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAId,SAAS,CAACc,KAAD,CAAlB,GAA4B,SAA5B,GAAwC7C,MAAM,CAAC6C,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAcA,MAAMO,SAAS,GAAG3C,OAAO,IAAI;EAC3B,MAAI,CAACuB,SAAS,CAACvB,OAAD,CAAV,IAAuBA,OAAO,CAAC4C,cAAR,GAAyBjB,MAAzB,KAAoC,CAA/D,EAAkE;EAChE,WAAO,KAAP;EACD;;EAED,SAAOZ,gBAAgB,CAACf,OAAD,CAAhB,CAA0B6C,gBAA1B,CAA2C,YAA3C,MAA6D,SAApE;EACD,CAND;;EAQA,MAAMC,UAAU,GAAG9C,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACyB,QAAR,KAAqBsB,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAIhD,OAAO,CAACiD,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAOlD,OAAO,CAACmD,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAOnD,OAAO,CAACmD,QAAf;EACD;;EAED,SAAOnD,OAAO,CAACoD,YAAR,CAAqB,UAArB,KAAoCpD,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;EAyCA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMmD,MAAM,GAAGrD,OAAO,IAAI;EACxB;EACAA,EAAAA,OAAO,CAACsD,YAAR;EACD,CAHD;;EAKA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAa1C,MAAnB;;EAEA,MAAI0C,MAAM,IAAI,CAAC/C,QAAQ,CAACgD,IAAT,CAAcL,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOI,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAME,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAInD,QAAQ,CAACoD,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAAC/B,MAA/B,EAAuC;EACrClB,MAAAA,QAAQ,CAACqD,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAACxB,OAA1B,CAAkC0B,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMI,kBAAkB,GAAGC,MAAM,IAAI;EACnCN,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMO,CAAC,GAAGX,SAAS,EAAnB;EACA;;EACA,QAAIW,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EAiBA,MAAMG,OAAO,GAAGd,QAAQ,IAAI;EAC1B,MAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;EAClCA,IAAAA,QAAQ;EACT;EACF,CAJD;;EAMA,MAAMe,sBAAsB,GAAG,CAACf,QAAD,EAAWgB,iBAAX,EAA8BC,iBAAiB,GAAG,IAAlD,KAA2D;EACxF,MAAI,CAACA,iBAAL,EAAwB;EACtBH,IAAAA,OAAO,CAACd,QAAD,CAAP;EACA;EACD;;EAED,QAAMkB,eAAe,GAAG,CAAxB;EACA,QAAMC,gBAAgB,GAAGpE,gCAAgC,CAACiE,iBAAD,CAAhC,GAAsDE,eAA/E;EAEA,MAAIE,MAAM,GAAG,KAAb;;EAEA,QAAMC,OAAO,GAAG,CAAC;EAAEC,IAAAA;EAAF,GAAD,KAAgB;EAC9B,QAAIA,MAAM,KAAKN,iBAAf,EAAkC;EAChC;EACD;;EAEDI,IAAAA,MAAM,GAAG,IAAT;EACAJ,IAAAA,iBAAiB,CAACO,mBAAlB,CAAsC5F,cAAtC,EAAsD0F,OAAtD;EACAP,IAAAA,OAAO,CAACd,QAAD,CAAP;EACD,GARD;;EAUAgB,EAAAA,iBAAiB,CAACd,gBAAlB,CAAmCvE,cAAnC,EAAmD0F,OAAnD;EACAG,EAAAA,UAAU,CAAC,MAAM;EACf,QAAI,CAACJ,MAAL,EAAa;EACX5D,MAAAA,oBAAoB,CAACwD,iBAAD,CAApB;EACD;EACF,GAJS,EAIPG,gBAJO,CAAV;EAKD,CA3BD;;EC9PA;EACA;EACA;EACA;EACA;EACA;EAMA,MAAMM,sBAAsB,GAAG,mDAA/B;EACA,MAAMC,uBAAuB,GAAG,aAAhC;;EAEA,MAAMC,eAAN,CAAsB;EACpBC,EAAAA,WAAW,GAAG;EACZ,SAAKC,QAAL,GAAgBhF,QAAQ,CAACgD,IAAzB;EACD;;EAEDiC,EAAAA,QAAQ,GAAG;EACT;EACA,UAAMC,aAAa,GAAGlF,QAAQ,CAACmF,eAAT,CAAyBC,WAA/C;EACA,WAAOC,IAAI,CAACC,GAAL,CAASjF,MAAM,CAACkF,UAAP,GAAoBL,aAA7B,CAAP;EACD;;EAEDM,EAAAA,IAAI,GAAG;EACL,UAAMC,KAAK,GAAG,KAAKR,QAAL,EAAd;;EACA,SAAKS,gBAAL,GAFK;;;EAIL,SAAKC,qBAAL,CAA2B,KAAKX,QAAhC,EAA0C,cAA1C,EAA0DY,eAAe,IAAIA,eAAe,GAAGH,KAA/F,EAJK;;;EAML,SAAKE,qBAAL,CAA2Bf,sBAA3B,EAAmD,cAAnD,EAAmEgB,eAAe,IAAIA,eAAe,GAAGH,KAAxG;;EACA,SAAKE,qBAAL,CAA2Bd,uBAA3B,EAAoD,aAApD,EAAmEe,eAAe,IAAIA,eAAe,GAAGH,KAAxG;EACD;;EAEDC,EAAAA,gBAAgB,GAAG;EACjB,SAAKG,qBAAL,CAA2B,KAAKb,QAAhC,EAA0C,UAA1C;;EACA,SAAKA,QAAL,CAAcc,KAAd,CAAoBC,QAApB,GAA+B,QAA/B;EACD;;EAEDJ,EAAAA,qBAAqB,CAACnG,QAAD,EAAWwG,SAAX,EAAsB7C,QAAtB,EAAgC;EACnD,UAAM8C,cAAc,GAAG,KAAKhB,QAAL,EAAvB;;EACA,UAAMiB,oBAAoB,GAAG3G,OAAO,IAAI;EACtC,UAAIA,OAAO,KAAK,KAAKyF,QAAjB,IAA6B3E,MAAM,CAACkF,UAAP,GAAoBhG,OAAO,CAAC6F,WAAR,GAAsBa,cAA3E,EAA2F;EACzF;EACD;;EAED,WAAKJ,qBAAL,CAA2BtG,OAA3B,EAAoCyG,SAApC;;EACA,YAAMJ,eAAe,GAAGvF,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiCyG,SAAjC,CAAxB;EACAzG,MAAAA,OAAO,CAACuG,KAAR,CAAcE,SAAd,IAA4B,GAAE7C,QAAQ,CAAC3C,MAAM,CAACC,UAAP,CAAkBmF,eAAlB,CAAD,CAAqC,IAA3E;EACD,KARD;;EAUA,SAAKO,0BAAL,CAAgC3G,QAAhC,EAA0C0G,oBAA1C;EACD;;EAEDE,EAAAA,KAAK,GAAG;EACN,SAAKC,uBAAL,CAA6B,KAAKrB,QAAlC,EAA4C,UAA5C;;EACA,SAAKqB,uBAAL,CAA6B,KAAKrB,QAAlC,EAA4C,cAA5C;;EACA,SAAKqB,uBAAL,CAA6BzB,sBAA7B,EAAqD,cAArD;;EACA,SAAKyB,uBAAL,CAA6BxB,uBAA7B,EAAsD,aAAtD;EACD;;EAEDgB,EAAAA,qBAAqB,CAACtG,OAAD,EAAUyG,SAAV,EAAqB;EACxC,UAAMM,WAAW,GAAG/G,OAAO,CAACuG,KAAR,CAAcE,SAAd,CAApB;;EACA,QAAIM,WAAJ,EAAiB;EACfC,MAAAA,4BAAW,CAACC,gBAAZ,CAA6BjH,OAA7B,EAAsCyG,SAAtC,EAAiDM,WAAjD;EACD;EACF;;EAEDD,EAAAA,uBAAuB,CAAC7G,QAAD,EAAWwG,SAAX,EAAsB;EAC3C,UAAME,oBAAoB,GAAG3G,OAAO,IAAI;EACtC,YAAMqC,KAAK,GAAG2E,4BAAW,CAACE,gBAAZ,CAA6BlH,OAA7B,EAAsCyG,SAAtC,CAAd;;EACA,UAAI,OAAOpE,KAAP,KAAiB,WAArB,EAAkC;EAChCrC,QAAAA,OAAO,CAACuG,KAAR,CAAcY,cAAd,CAA6BV,SAA7B;EACD,OAFD,MAEO;EACLO,QAAAA,4BAAW,CAACI,mBAAZ,CAAgCpH,OAAhC,EAAyCyG,SAAzC;EACAzG,QAAAA,OAAO,CAACuG,KAAR,CAAcE,SAAd,IAA2BpE,KAA3B;EACD;EACF,KARD;;EAUA,SAAKuE,0BAAL,CAAgC3G,QAAhC,EAA0C0G,oBAA1C;EACD;;EAEDC,EAAAA,0BAA0B,CAAC3G,QAAD,EAAWoH,QAAX,EAAqB;EAC7C,QAAI9F,SAAS,CAACtB,QAAD,CAAb,EAAyB;EACvBoH,MAAAA,QAAQ,CAACpH,QAAD,CAAR;EACD,KAFD,MAEO;EACLqH,MAAAA,+BAAc,CAACC,IAAf,CAAoBtH,QAApB,EAA8B,KAAKwF,QAAnC,EAA6CvD,OAA7C,CAAqDmF,QAArD;EACD;EACF;;EAEDG,EAAAA,aAAa,GAAG;EACd,WAAO,KAAK9B,QAAL,KAAkB,CAAzB;EACD;;EA/EmB;;ECdtB;EACA;EACA;EACA;EACA;EACA;EAKA,MAAM+B,SAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,gBADG;EAEd/E,EAAAA,SAAS,EAAE,IAFG;EAEG;EACjBgF,EAAAA,UAAU,EAAE,KAHE;EAIdC,EAAAA,WAAW,EAAE,MAJC;EAIO;EACrBC,EAAAA,aAAa,EAAE;EALD,CAAhB;EAQA,MAAMC,aAAW,GAAG;EAClBJ,EAAAA,SAAS,EAAE,QADO;EAElB/E,EAAAA,SAAS,EAAE,SAFO;EAGlBgF,EAAAA,UAAU,EAAE,SAHM;EAIlBC,EAAAA,WAAW,EAAE,kBAJK;EAKlBC,EAAAA,aAAa,EAAE;EALG,CAApB;EAOA,MAAMzD,MAAI,GAAG,UAAb;EACA,MAAM2D,eAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAMC,eAAe,GAAI,gBAAe7D,MAAK,EAA7C;;EAEA,MAAM8D,QAAN,CAAe;EACb1C,EAAAA,WAAW,CAAC1D,MAAD,EAAS;EAClB,SAAKqG,OAAL,GAAe,KAAKC,UAAL,CAAgBtG,MAAhB,CAAf;EACA,SAAKuG,WAAL,GAAmB,KAAnB;EACA,SAAK5C,QAAL,GAAgB,IAAhB;EACD;;EAED6C,EAAAA,IAAI,CAAC1E,QAAD,EAAW;EACb,QAAI,CAAC,KAAKuE,OAAL,CAAaxF,SAAlB,EAA6B;EAC3B+B,MAAAA,OAAO,CAACd,QAAD,CAAP;EACA;EACD;;EAED,SAAK2E,OAAL;;EAEA,QAAI,KAAKJ,OAAL,CAAaR,UAAjB,EAA6B;EAC3BtE,MAAAA,MAAM,CAAC,KAAKmF,WAAL,EAAD,CAAN;EACD;;EAED,SAAKA,WAAL,GAAmBvF,SAAnB,CAA6BwF,GAA7B,CAAiCT,iBAAjC;;EAEA,SAAKU,iBAAL,CAAuB,MAAM;EAC3BhE,MAAAA,OAAO,CAACd,QAAD,CAAP;EACD,KAFD;EAGD;;EAEDqC,EAAAA,IAAI,CAACrC,QAAD,EAAW;EACb,QAAI,CAAC,KAAKuE,OAAL,CAAaxF,SAAlB,EAA6B;EAC3B+B,MAAAA,OAAO,CAACd,QAAD,CAAP;EACA;EACD;;EAED,SAAK4E,WAAL,GAAmBvF,SAAnB,CAA6B0F,MAA7B,CAAoCX,iBAApC;;EAEA,SAAKU,iBAAL,CAAuB,MAAM;EAC3B,WAAKE,OAAL;EACAlE,MAAAA,OAAO,CAACd,QAAD,CAAP;EACD,KAHD;EAID,GAtCY;;;EA0Cb4E,EAAAA,WAAW,GAAG;EACZ,QAAI,CAAC,KAAK/C,QAAV,EAAoB;EAClB,YAAMoD,QAAQ,GAAGpI,QAAQ,CAACqI,aAAT,CAAuB,KAAvB,CAAjB;EACAD,MAAAA,QAAQ,CAACnB,SAAT,GAAqB,KAAKS,OAAL,CAAaT,SAAlC;;EACA,UAAI,KAAKS,OAAL,CAAaR,UAAjB,EAA6B;EAC3BkB,QAAAA,QAAQ,CAAC5F,SAAT,CAAmBwF,GAAnB,CAAuBV,eAAvB;EACD;;EAED,WAAKtC,QAAL,GAAgBoD,QAAhB;EACD;;EAED,WAAO,KAAKpD,QAAZ;EACD;;EAED2C,EAAAA,UAAU,CAACtG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG2F,SADI;EAEP,UAAI,OAAO3F,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAFO,KAAT,CADiB;;EAOjBA,IAAAA,MAAM,CAAC8F,WAAP,GAAqBlG,UAAU,CAACI,MAAM,CAAC8F,WAAR,CAA/B;EACAhG,IAAAA,eAAe,CAACwC,MAAD,EAAOtC,MAAP,EAAegG,aAAf,CAAf;EACA,WAAOhG,MAAP;EACD;;EAEDyG,EAAAA,OAAO,GAAG;EACR,QAAI,KAAKF,WAAT,EAAsB;EACpB;EACD;;EAED,SAAKF,OAAL,CAAaP,WAAb,CAAyBmB,MAAzB,CAAgC,KAAKP,WAAL,EAAhC;;EAEAQ,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKT,WAAL,EAAhB,EAAoCP,eAApC,EAAqD,MAAM;EACzDvD,MAAAA,OAAO,CAAC,KAAKyD,OAAL,CAAaN,aAAd,CAAP;EACD,KAFD;EAIA,SAAKQ,WAAL,GAAmB,IAAnB;EACD;;EAEDO,EAAAA,OAAO,GAAG;EACR,QAAI,CAAC,KAAKP,WAAV,EAAuB;EACrB;EACD;;EAEDW,IAAAA,6BAAY,CAACE,GAAb,CAAiB,KAAKzD,QAAtB,EAAgCwC,eAAhC;;EAEA,SAAKxC,QAAL,CAAckD,MAAd;;EACA,SAAKN,WAAL,GAAmB,KAAnB;EACD;;EAEDK,EAAAA,iBAAiB,CAAC9E,QAAD,EAAW;EAC1Be,IAAAA,sBAAsB,CAACf,QAAD,EAAW,KAAK4E,WAAL,EAAX,EAA+B,KAAKL,OAAL,CAAaR,UAA5C,CAAtB;EACD;;EA/FY;;EC/Bf;EACA;EACA;EACA;EACA;EACA;EAMA,MAAMF,SAAO,GAAG;EACd0B,EAAAA,WAAW,EAAE,IADC;EACK;EACnBC,EAAAA,SAAS,EAAE;EAFG,CAAhB;EAKA,MAAMtB,aAAW,GAAG;EAClBqB,EAAAA,WAAW,EAAE,SADK;EAElBC,EAAAA,SAAS,EAAE;EAFO,CAApB;EAKA,MAAMhF,MAAI,GAAG,WAAb;EACA,MAAMiF,UAAQ,GAAG,cAAjB;EACA,MAAMC,WAAS,GAAI,IAAGD,UAAS,EAA/B;EACA,MAAME,aAAa,GAAI,UAASD,WAAU,EAA1C;EACA,MAAME,iBAAiB,GAAI,cAAaF,WAAU,EAAlD;EAEA,MAAMG,OAAO,GAAG,KAAhB;EACA,MAAMC,eAAe,GAAG,SAAxB;EACA,MAAMC,gBAAgB,GAAG,UAAzB;;EAEA,MAAMC,SAAN,CAAgB;EACdpE,EAAAA,WAAW,CAAC1D,MAAD,EAAS;EAClB,SAAKqG,OAAL,GAAe,KAAKC,UAAL,CAAgBtG,MAAhB,CAAf;EACA,SAAK+H,SAAL,GAAiB,KAAjB;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACD;;EAEDC,EAAAA,QAAQ,GAAG;EACT,UAAM;EAAEZ,MAAAA,WAAF;EAAeC,MAAAA;EAAf,QAA6B,KAAKjB,OAAxC;;EAEA,QAAI,KAAK0B,SAAT,EAAoB;EAClB;EACD;;EAED,QAAIT,SAAJ,EAAe;EACbD,MAAAA,WAAW,CAACa,KAAZ;EACD;;EAEDhB,IAAAA,6BAAY,CAACE,GAAb,CAAiBzI,QAAjB,EAA2B6I,WAA3B,EAXS;;EAYTN,IAAAA,6BAAY,CAACC,EAAb,CAAgBxI,QAAhB,EAA0B8I,aAA1B,EAAyCU,KAAK,IAAI,KAAKC,cAAL,CAAoBD,KAApB,CAAlD;EACAjB,IAAAA,6BAAY,CAACC,EAAb,CAAgBxI,QAAhB,EAA0B+I,iBAA1B,EAA6CS,KAAK,IAAI,KAAKE,cAAL,CAAoBF,KAApB,CAAtD;EAEA,SAAKJ,SAAL,GAAiB,IAAjB;EACD;;EAEDO,EAAAA,UAAU,GAAG;EACX,QAAI,CAAC,KAAKP,SAAV,EAAqB;EACnB;EACD;;EAED,SAAKA,SAAL,GAAiB,KAAjB;EACAb,IAAAA,6BAAY,CAACE,GAAb,CAAiBzI,QAAjB,EAA2B6I,WAA3B;EACD,GAhCa;;;EAoCdY,EAAAA,cAAc,CAACD,KAAD,EAAQ;EACpB,UAAM;EAAE/E,MAAAA;EAAF,QAAa+E,KAAnB;EACA,UAAM;EAAEd,MAAAA;EAAF,QAAkB,KAAKhB,OAA7B;;EAEA,QAAIjD,MAAM,KAAKzE,QAAX,IAAuByE,MAAM,KAAKiE,WAAlC,IAAiDA,WAAW,CAACjG,QAAZ,CAAqBgC,MAArB,CAArD,EAAmF;EACjF;EACD;;EAED,UAAMmF,QAAQ,GAAG/C,+BAAc,CAACgD,iBAAf,CAAiCnB,WAAjC,CAAjB;;EAEA,QAAIkB,QAAQ,CAAC1I,MAAT,KAAoB,CAAxB,EAA2B;EACzBwH,MAAAA,WAAW,CAACa,KAAZ;EACD,KAFD,MAEO,IAAI,KAAKF,oBAAL,KAA8BH,gBAAlC,EAAoD;EACzDU,MAAAA,QAAQ,CAACA,QAAQ,CAAC1I,MAAT,GAAkB,CAAnB,CAAR,CAA8BqI,KAA9B;EACD,KAFM,MAEA;EACLK,MAAAA,QAAQ,CAAC,CAAD,CAAR,CAAYL,KAAZ;EACD;EACF;;EAEDG,EAAAA,cAAc,CAACF,KAAD,EAAQ;EACpB,QAAIA,KAAK,CAACM,GAAN,KAAcd,OAAlB,EAA2B;EACzB;EACD;;EAED,SAAKK,oBAAL,GAA4BG,KAAK,CAACO,QAAN,GAAiBb,gBAAjB,GAAoCD,eAAhE;EACD;;EAEDtB,EAAAA,UAAU,CAACtG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG2F,SADI;EAEP,UAAI,OAAO3F,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAFO,KAAT;EAIAF,IAAAA,eAAe,CAACwC,MAAD,EAAOtC,MAAP,EAAegG,aAAf,CAAf;EACA,WAAOhG,MAAP;EACD;;EAtEa;;EC/BhB;EACA;EACA;EACA;EACA;EACA;;EAKA,MAAM2I,oBAAoB,GAAG,CAACC,SAAD,EAAYC,MAAM,GAAG,MAArB,KAAgC;EAC3D,QAAMC,UAAU,GAAI,gBAAeF,SAAS,CAACpB,SAAU,EAAvD;EACA,QAAMnF,IAAI,GAAGuG,SAAS,CAACtG,IAAvB;EAEA4E,EAAAA,6BAAY,CAACC,EAAb,CAAgBxI,QAAhB,EAA0BmK,UAA1B,EAAuC,qBAAoBzG,IAAK,IAAhE,EAAqE,UAAU8F,KAAV,EAAiB;EACpF,QAAI,CAAC,GAAD,EAAM,MAAN,EAAc7J,QAAd,CAAuB,KAAKyK,OAA5B,CAAJ,EAA0C;EACxCZ,MAAAA,KAAK,CAACa,cAAN;EACD;;EAED,QAAIhI,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAED,UAAMoC,MAAM,GAAG1E,sBAAsB,CAAC,IAAD,CAAtB,IAAgC,KAAKuK,OAAL,CAAc,IAAG5G,IAAK,EAAtB,CAA/C;EACA,UAAM6G,QAAQ,GAAGN,SAAS,CAACO,mBAAV,CAA8B/F,MAA9B,CAAjB,CAVoF;;EAapF8F,IAAAA,QAAQ,CAACL,MAAD,CAAR;EACD,GAdD;EAeD,CAnBD;;ECVA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;EACA;EACA;;EAEA,MAAMvG,IAAI,GAAG,WAAb;EACA,MAAMiF,QAAQ,GAAG,cAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAM6B,YAAY,GAAG,WAArB;EACA,MAAMC,mBAAmB,GAAI,OAAM7B,SAAU,GAAE4B,YAAa,EAA5D;EACA,MAAME,UAAU,GAAG,QAAnB;EAEA,MAAM3D,OAAO,GAAG;EACdoB,EAAAA,QAAQ,EAAE,IADI;EAEdwC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,MAAMxD,WAAW,GAAG;EAClBe,EAAAA,QAAQ,EAAE,SADQ;EAElBwC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,MAAMtD,eAAe,GAAG,MAAxB;EACA,MAAMuD,mBAAmB,GAAG,oBAA5B;EACA,MAAMC,aAAa,GAAG,iBAAtB;EAEA,MAAMC,UAAU,GAAI,OAAMnC,SAAU,EAApC;EACA,MAAMoC,WAAW,GAAI,QAAOpC,SAAU,EAAtC;EACA,MAAMqC,UAAU,GAAI,OAAMrC,SAAU,EAApC;EACA,MAAMsC,YAAY,GAAI,SAAQtC,SAAU,EAAxC;EACA,MAAMuC,oBAAoB,GAAI,QAAOvC,SAAU,GAAE4B,YAAa,EAA9D;EACA,MAAMY,qBAAqB,GAAI,kBAAiBxC,SAAU,EAA1D;EAEA,MAAMyC,oBAAoB,GAAG,8BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,SAAN,SAAwBC,8BAAxB,CAAsC;EACpCzG,EAAAA,WAAW,CAACxF,OAAD,EAAU8B,MAAV,EAAkB;EAC3B,UAAM9B,OAAN;EAEA,SAAKmI,OAAL,GAAe,KAAKC,UAAL,CAAgBtG,MAAhB,CAAf;EACA,SAAKoK,QAAL,GAAgB,KAAhB;EACA,SAAKC,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;EACA,SAAKC,UAAL,GAAkB,KAAKC,oBAAL,EAAlB;;EACA,SAAKC,kBAAL;EACD,GATmC;;;EAarB,aAAJnI,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD;;EAEiB,aAAPqD,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD,GAnBmC;;;EAuBpC+E,EAAAA,MAAM,CAACC,aAAD,EAAgB;EACpB,WAAO,KAAKP,QAAL,GAAgB,KAAKjG,IAAL,EAAhB,GAA8B,KAAKqC,IAAL,CAAUmE,aAAV,CAArC;EACD;;EAEDnE,EAAAA,IAAI,CAACmE,aAAD,EAAgB;EAClB,QAAI,KAAKP,QAAT,EAAmB;EACjB;EACD;;EAED,UAAMQ,SAAS,GAAG1D,6BAAY,CAAC2D,OAAb,CAAqB,KAAKlH,QAA1B,EAAoCgG,UAApC,EAAgD;EAAEgB,MAAAA;EAAF,KAAhD,CAAlB;;EAEA,QAAIC,SAAS,CAACE,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKV,QAAL,GAAgB,IAAhB;EACA,SAAKzG,QAAL,CAAcc,KAAd,CAAoBsG,UAApB,GAAiC,SAAjC;;EAEA,SAAKV,SAAL,CAAe7D,IAAf;;EAEA,QAAI,CAAC,KAAKH,OAAL,CAAamD,MAAlB,EAA0B;EACxB,UAAI/F,eAAJ,GAAsBU,IAAtB;EACD;;EAED,SAAKR,QAAL,CAAcqH,eAAd,CAA8B,aAA9B;;EACA,SAAKrH,QAAL,CAAcsH,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAKtH,QAAL,CAAcsH,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAKtH,QAAL,CAAcxC,SAAd,CAAwBwF,GAAxB,CAA4BT,eAA5B;;EAEA,UAAMgF,gBAAgB,GAAG,MAAM;EAC7B,UAAI,CAAC,KAAK7E,OAAL,CAAamD,MAAlB,EAA0B;EACxB,aAAKe,UAAL,CAAgBtC,QAAhB;EACD;;EAEDf,MAAAA,6BAAY,CAAC2D,OAAb,CAAqB,KAAKlH,QAA1B,EAAoCiG,WAApC,EAAiD;EAAEe,QAAAA;EAAF,OAAjD;EACD,KAND;;EAQA,SAAKQ,cAAL,CAAoBD,gBAApB,EAAsC,KAAKvH,QAA3C,EAAqD,IAArD;EACD;;EAEDQ,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKiG,QAAV,EAAoB;EAClB;EACD;;EAED,UAAMgB,SAAS,GAAGlE,6BAAY,CAAC2D,OAAb,CAAqB,KAAKlH,QAA1B,EAAoCkG,UAApC,CAAlB;;EAEA,QAAIuB,SAAS,CAACN,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKP,UAAL,CAAgBjC,UAAhB;;EACA,SAAK3E,QAAL,CAAc0H,IAAd;;EACA,SAAKjB,QAAL,GAAgB,KAAhB;;EACA,SAAKzG,QAAL,CAAcxC,SAAd,CAAwB0F,MAAxB,CAA+BX,eAA/B;;EACA,SAAKmE,SAAL,CAAelG,IAAf;;EAEA,UAAMmH,gBAAgB,GAAG,MAAM;EAC7B,WAAK3H,QAAL,CAAcsH,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,WAAKtH,QAAL,CAAcqH,eAAd,CAA8B,YAA9B;;EACA,WAAKrH,QAAL,CAAcqH,eAAd,CAA8B,MAA9B;;EACA,WAAKrH,QAAL,CAAcc,KAAd,CAAoBsG,UAApB,GAAiC,QAAjC;;EAEA,UAAI,CAAC,KAAK1E,OAAL,CAAamD,MAAlB,EAA0B;EACxB,YAAI/F,eAAJ,GAAsBsB,KAAtB;EACD;;EAEDmC,MAAAA,6BAAY,CAAC2D,OAAb,CAAqB,KAAKlH,QAA1B,EAAoCmG,YAApC;EACD,KAXD;;EAaA,SAAKqB,cAAL,CAAoBG,gBAApB,EAAsC,KAAK3H,QAA3C,EAAqD,IAArD;EACD;;EAEDmD,EAAAA,OAAO,GAAG;EACR,SAAKuD,SAAL,CAAevD,OAAf;;EACA,SAAKyD,UAAL,CAAgBjC,UAAhB;;EACA,UAAMxB,OAAN;EACD,GApGmC;;;EAwGpCR,EAAAA,UAAU,CAACtG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG2F,OADI;EAEP,SAAGT,4BAAW,CAACqG,iBAAZ,CAA8B,KAAK5H,QAAnC,CAFI;EAGP,UAAI,OAAO3D,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAHO,KAAT;EAKAF,IAAAA,eAAe,CAACwC,IAAD,EAAOtC,MAAP,EAAegG,WAAf,CAAf;EACA,WAAOhG,MAAP;EACD;;EAEDsK,EAAAA,mBAAmB,GAAG;EACpB,WAAO,IAAIlE,QAAJ,CAAa;EAClBR,MAAAA,SAAS,EAAE6D,mBADO;EAElB5I,MAAAA,SAAS,EAAE,KAAKwF,OAAL,CAAaU,QAFN;EAGlBlB,MAAAA,UAAU,EAAE,IAHM;EAIlBC,MAAAA,WAAW,EAAE,KAAKnC,QAAL,CAAc6H,UAJT;EAKlBzF,MAAAA,aAAa,EAAE,MAAM,KAAK5B,IAAL;EALH,KAAb,CAAP;EAOD;;EAEDqG,EAAAA,oBAAoB,GAAG;EACrB,WAAO,IAAI1C,SAAJ,CAAc;EACnBT,MAAAA,WAAW,EAAE,KAAK1D;EADC,KAAd,CAAP;EAGD;;EAED8G,EAAAA,kBAAkB,GAAG;EACnBvD,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKxD,QAArB,EAA+BqG,qBAA/B,EAAsD7B,KAAK,IAAI;EAC7D,UAAI,KAAK9B,OAAL,CAAakD,QAAb,IAAyBpB,KAAK,CAACM,GAAN,KAAca,UAA3C,EAAuD;EACrD,aAAKnF,IAAL;EACD;EACF,KAJD;EAKD,GAxImC;;;EA4Id,SAAf1B,eAAe,CAACzC,MAAD,EAAS;EAC7B,WAAO,KAAKyL,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGxB,SAAS,CAACf,mBAAV,CAA8B,IAA9B,EAAoCnJ,MAApC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAI0L,IAAI,CAAC1L,MAAD,CAAJ,KAAiBpC,SAAjB,IAA8BoC,MAAM,CAACzB,UAAP,CAAkB,GAAlB,CAA9B,IAAwDyB,MAAM,KAAK,aAAvE,EAAsF;EACpF,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED0L,MAAAA,IAAI,CAAC1L,MAAD,CAAJ,CAAa,IAAb;EACD,KAZM,CAAP;EAaD;;EA1JmC;EA6JtC;EACA;EACA;EACA;EACA;;;AAEAkH,+BAAY,CAACC,EAAb,CAAgBxI,QAAhB,EAA0BoL,oBAA1B,EAAgDE,oBAAhD,EAAsE,UAAU9B,KAAV,EAAiB;EACrF,QAAM/E,MAAM,GAAG1E,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcJ,QAAd,CAAuB,KAAKyK,OAA5B,CAAJ,EAA0C;EACxCZ,IAAAA,KAAK,CAACa,cAAN;EACD;;EAED,MAAIhI,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAEDkG,EAAAA,6BAAY,CAACyE,GAAb,CAAiBvI,MAAjB,EAAyB0G,YAAzB,EAAuC,MAAM;EAC3C;EACA,QAAIjJ,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,WAAKqH,KAAL;EACD;EACF,GALD,EAXqF;;EAmBrF,QAAM0D,YAAY,GAAGpG,+BAAc,CAACqG,OAAf,CAAuBnC,aAAvB,CAArB;;EACA,MAAIkC,YAAY,IAAIA,YAAY,KAAKxI,MAArC,EAA6C;EAC3C8G,IAAAA,SAAS,CAAC4B,WAAV,CAAsBF,YAAtB,EAAoCzH,IAApC;EACD;;EAED,QAAMuH,IAAI,GAAGxB,SAAS,CAACf,mBAAV,CAA8B/F,MAA9B,CAAb;EACAsI,EAAAA,IAAI,CAAChB,MAAL,CAAY,IAAZ;EACD,CA1BD;AA4BAxD,+BAAY,CAACC,EAAb,CAAgBnI,MAAhB,EAAwBqK,mBAAxB,EAA6C,MAC3C7D,+BAAc,CAACC,IAAf,CAAoBiE,aAApB,EAAmCtJ,OAAnC,CAA2C2L,EAAE,IAAI7B,SAAS,CAACf,mBAAV,CAA8B4C,EAA9B,EAAkCvF,IAAlC,EAAjD,CADF;EAIAmC,oBAAoB,CAACuB,SAAD,CAApB;EACA;EACA;EACA;EACA;EACA;;EAEAhI,kBAAkB,CAACgI,SAAD,CAAlB;;;;;;;;"}