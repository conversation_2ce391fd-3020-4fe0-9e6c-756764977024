{"version": 3, "file": "toast.js", "sources": ["../src/util/index.js", "../src/util/component-functions.js", "../src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\nenableDismissTrigger(Toast)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "isElement", "j<PERSON>y", "nodeType", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "length", "addEventListener", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "enableDismissTrigger", "component", "method", "clickEvent", "EVENT_KEY", "EventHandler", "on", "event", "tagName", "preventDefault", "target", "closest", "instance", "getOrCreateInstance", "DATA_KEY", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "Toast", "BaseComponent", "constructor", "_config", "_getConfig", "_timeout", "_hasMouseInteraction", "_hasKeyboardInteraction", "_setListeners", "show", "showEvent", "trigger", "_element", "defaultPrevented", "_clearTimeout", "add", "complete", "remove", "_maybeScheduleHide", "_queueCallback", "hide", "hideEvent", "dispose", "Manipulator", "getDataAttributes", "setTimeout", "_onInteraction", "isInteracting", "type", "nextElement", "relatedTarget", "clearTimeout", "each", "data"], "mappings": ";;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;EAOA,MAAMA,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAiCA,MAAMU,SAAS,GAAGlB,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACmB,MAAX,KAAsB,WAA1B,EAAuC;EACrCnB,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACoB,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAwBA,MAAMC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIZ,SAAS,CAACY,KAAD,CAAlB,GAA4B,SAA5B,GAAwC/B,MAAM,CAAC+B,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAsBA,MAAMO,UAAU,GAAG7B,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACa,QAAR,KAAqBiB,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAI/B,OAAO,CAACgC,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAOjC,OAAO,CAACkC,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAOlC,OAAO,CAACkC,QAAf;EACD;;EAED,SAAOlC,OAAO,CAACmC,YAAR,CAAqB,UAArB,KAAoCnC,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;EAyCA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMkC,MAAM,GAAGpC,OAAO,IAAI;EACxB;EACAA,EAAAA,OAAO,CAACqC,YAAR;EACD,CAHD;;EAKA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAAC9B,QAAQ,CAACgC,IAAT,CAAcN,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOI,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMG,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAInC,QAAQ,CAACoC,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACI,MAA/B,EAAuC;EACrCrC,MAAAA,QAAQ,CAACsC,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDL,QAAAA,yBAAyB,CAACtB,OAA1B,CAAkCwB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACM,IAA1B,CAA+BJ,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMK,kBAAkB,GAAGC,MAAM,IAAI;EACnCP,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMQ,CAAC,GAAGb,SAAS,EAAnB;EACA;;EACA,QAAIa,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;ECvOA;EACA;EACA;EACA;EACA;EACA;;EAKA,MAAMG,oBAAoB,GAAG,CAACC,SAAD,EAAYC,MAAM,GAAG,MAArB,KAAgC;EAC3D,QAAMC,UAAU,GAAI,gBAAeF,SAAS,CAACG,SAAU,EAAvD;EACA,QAAMX,IAAI,GAAGQ,SAAS,CAACP,IAAvB;EAEAW,EAAAA,6BAAY,CAACC,EAAb,CAAgBxD,QAAhB,EAA0BqD,UAA1B,EAAuC,qBAAoBV,IAAK,IAAhE,EAAqE,UAAUc,KAAV,EAAiB;EACpF,QAAI,CAAC,GAAD,EAAM,MAAN,EAAc9D,QAAd,CAAuB,KAAK+D,OAA5B,CAAJ,EAA0C;EACxCD,MAAAA,KAAK,CAACE,cAAN;EACD;;EAED,QAAIvC,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAED,UAAMwC,MAAM,GAAG7D,sBAAsB,CAAC,IAAD,CAAtB,IAAgC,KAAK8D,OAAL,CAAc,IAAGlB,IAAK,EAAtB,CAA/C;EACA,UAAMmB,QAAQ,GAAGX,SAAS,CAACY,mBAAV,CAA8BH,MAA9B,CAAjB,CAVoF;;EAapFE,IAAAA,QAAQ,CAACV,MAAD,CAAR;EACD,GAdD;EAeD,CAnBD;;ECVA;EACA;EACA;EACA;EACA;EACA;EAYA;EACA;EACA;EACA;EACA;;EAEA,MAAMR,IAAI,GAAG,OAAb;EACA,MAAMoB,QAAQ,GAAG,UAAjB;EACA,MAAMV,SAAS,GAAI,IAAGU,QAAS,EAA/B;EAEA,MAAMC,eAAe,GAAI,YAAWX,SAAU,EAA9C;EACA,MAAMY,cAAc,GAAI,WAAUZ,SAAU,EAA5C;EACA,MAAMa,aAAa,GAAI,UAASb,SAAU,EAA1C;EACA,MAAMc,cAAc,GAAI,WAAUd,SAAU,EAA5C;EACA,MAAMe,UAAU,GAAI,OAAMf,SAAU,EAApC;EACA,MAAMgB,YAAY,GAAI,SAAQhB,SAAU,EAAxC;EACA,MAAMiB,UAAU,GAAI,OAAMjB,SAAU,EAApC;EACA,MAAMkB,WAAW,GAAI,QAAOlB,SAAU,EAAtC;EAEA,MAAMmB,eAAe,GAAG,MAAxB;EACA,MAAMC,eAAe,GAAG,MAAxB;;EACA,MAAMC,eAAe,GAAG,MAAxB;EACA,MAAMC,kBAAkB,GAAG,SAA3B;EAEA,MAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMC,OAAO,GAAG;EACdH,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA;EACA;EACA;EACA;EACA;;EAEA,MAAME,KAAN,SAAoBC,8BAApB,CAAkC;EAChCC,EAAAA,WAAW,CAAC7F,OAAD,EAAUgB,MAAV,EAAkB;EAC3B,UAAMhB,OAAN;EAEA,SAAK8F,OAAL,GAAe,KAAKC,UAAL,CAAgB/E,MAAhB,CAAf;EACA,SAAKgF,QAAL,GAAgB,IAAhB;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAKC,uBAAL,GAA+B,KAA/B;;EACA,SAAKC,aAAL;EACD,GAT+B;;;EAaV,aAAXb,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD;;EAEiB,aAAPI,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJrC,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAvB+B;;;EA2BhC+C,EAAAA,IAAI,GAAG;EACL,UAAMC,SAAS,GAAGrC,6BAAY,CAACsC,OAAb,CAAqB,KAAKC,QAA1B,EAAoCvB,UAApC,CAAlB;;EAEA,QAAIqB,SAAS,CAACG,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKC,aAAL;;EAEA,QAAI,KAAKX,OAAL,CAAaP,SAAjB,EAA4B;EAC1B,WAAKgB,QAAL,CAAcvE,SAAd,CAAwB0E,GAAxB,CAA4BxB,eAA5B;EACD;;EAED,UAAMyB,QAAQ,GAAG,MAAM;EACrB,WAAKJ,QAAL,CAAcvE,SAAd,CAAwB4E,MAAxB,CAA+BvB,kBAA/B;;EACArB,MAAAA,6BAAY,CAACsC,OAAb,CAAqB,KAAKC,QAA1B,EAAoCtB,WAApC;;EAEA,WAAK4B,kBAAL;EACD,KALD;;EAOA,SAAKN,QAAL,CAAcvE,SAAd,CAAwB4E,MAAxB,CAA+BzB,eAA/B,EApBK;;;EAqBL/C,IAAAA,MAAM,CAAC,KAAKmE,QAAN,CAAN;;EACA,SAAKA,QAAL,CAAcvE,SAAd,CAAwB0E,GAAxB,CAA4BtB,eAA5B;;EACA,SAAKmB,QAAL,CAAcvE,SAAd,CAAwB0E,GAAxB,CAA4BrB,kBAA5B;;EAEA,SAAKyB,cAAL,CAAoBH,QAApB,EAA8B,KAAKJ,QAAnC,EAA6C,KAAKT,OAAL,CAAaP,SAA1D;EACD;;EAEDwB,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKR,QAAL,CAAcvE,SAAd,CAAwBC,QAAxB,CAAiCmD,eAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,UAAM4B,SAAS,GAAGhD,6BAAY,CAACsC,OAAb,CAAqB,KAAKC,QAA1B,EAAoCzB,UAApC,CAAlB;;EAEA,QAAIkC,SAAS,CAACR,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMG,QAAQ,GAAG,MAAM;EACrB,WAAKJ,QAAL,CAAcvE,SAAd,CAAwB0E,GAAxB,CAA4BvB,eAA5B,EADqB;;;EAErB,WAAKoB,QAAL,CAAcvE,SAAd,CAAwB4E,MAAxB,CAA+BvB,kBAA/B;;EACA,WAAKkB,QAAL,CAAcvE,SAAd,CAAwB4E,MAAxB,CAA+BxB,eAA/B;;EACApB,MAAAA,6BAAY,CAACsC,OAAb,CAAqB,KAAKC,QAA1B,EAAoCxB,YAApC;EACD,KALD;;EAOA,SAAKwB,QAAL,CAAcvE,SAAd,CAAwB0E,GAAxB,CAA4BrB,kBAA5B;;EACA,SAAKyB,cAAL,CAAoBH,QAApB,EAA8B,KAAKJ,QAAnC,EAA6C,KAAKT,OAAL,CAAaP,SAA1D;EACD;;EAED0B,EAAAA,OAAO,GAAG;EACR,SAAKR,aAAL;;EAEA,QAAI,KAAKF,QAAL,CAAcvE,SAAd,CAAwBC,QAAxB,CAAiCmD,eAAjC,CAAJ,EAAuD;EACrD,WAAKmB,QAAL,CAAcvE,SAAd,CAAwB4E,MAAxB,CAA+BxB,eAA/B;EACD;;EAED,UAAM6B,OAAN;EACD,GArF+B;;;EAyFhClB,EAAAA,UAAU,CAAC/E,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG0E,OADI;EAEP,SAAGwB,4BAAW,CAACC,iBAAZ,CAA8B,KAAKZ,QAAnC,CAFI;EAGP,UAAI,OAAOvF,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;EAMAF,IAAAA,eAAe,CAACuC,IAAD,EAAOrC,MAAP,EAAe,KAAK6E,WAAL,CAAiBP,WAAhC,CAAf;EAEA,WAAOtE,MAAP;EACD;;EAED6F,EAAAA,kBAAkB,GAAG;EACnB,QAAI,CAAC,KAAKf,OAAL,CAAaN,QAAlB,EAA4B;EAC1B;EACD;;EAED,QAAI,KAAKS,oBAAL,IAA6B,KAAKC,uBAAtC,EAA+D;EAC7D;EACD;;EAED,SAAKF,QAAL,GAAgBoB,UAAU,CAAC,MAAM;EAC/B,WAAKL,IAAL;EACD,KAFyB,EAEvB,KAAKjB,OAAL,CAAaL,KAFU,CAA1B;EAGD;;EAED4B,EAAAA,cAAc,CAACnD,KAAD,EAAQoD,aAAR,EAAuB;EACnC,YAAQpD,KAAK,CAACqD,IAAd;EACE,WAAK,WAAL;EACA,WAAK,UAAL;EACE,aAAKtB,oBAAL,GAA4BqB,aAA5B;EACA;;EACF,WAAK,SAAL;EACA,WAAK,UAAL;EACE,aAAKpB,uBAAL,GAA+BoB,aAA/B;EACA;EARJ;;EAaA,QAAIA,aAAJ,EAAmB;EACjB,WAAKb,aAAL;;EACA;EACD;;EAED,UAAMe,WAAW,GAAGtD,KAAK,CAACuD,aAA1B;;EACA,QAAI,KAAKlB,QAAL,KAAkBiB,WAAlB,IAAiC,KAAKjB,QAAL,CAActE,QAAd,CAAuBuF,WAAvB,CAArC,EAA0E;EACxE;EACD;;EAED,SAAKX,kBAAL;EACD;;EAEDV,EAAAA,aAAa,GAAG;EACdnC,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B7B,eAA/B,EAAgDR,KAAK,IAAI,KAAKmD,cAAL,CAAoBnD,KAApB,EAA2B,IAA3B,CAAzD;EACAF,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B5B,cAA/B,EAA+CT,KAAK,IAAI,KAAKmD,cAAL,CAAoBnD,KAApB,EAA2B,KAA3B,CAAxD;EACAF,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B3B,aAA/B,EAA8CV,KAAK,IAAI,KAAKmD,cAAL,CAAoBnD,KAApB,EAA2B,IAA3B,CAAvD;EACAF,IAAAA,6BAAY,CAACC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B1B,cAA/B,EAA+CX,KAAK,IAAI,KAAKmD,cAAL,CAAoBnD,KAApB,EAA2B,KAA3B,CAAxD;EACD;;EAEDuC,EAAAA,aAAa,GAAG;EACdiB,IAAAA,YAAY,CAAC,KAAK1B,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;EACD,GAxJ+B;;;EA4JV,SAAfxC,eAAe,CAACxC,MAAD,EAAS;EAC7B,WAAO,KAAK2G,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGjC,KAAK,CAACnB,mBAAN,CAA0B,IAA1B,EAAgCxD,MAAhC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO4G,IAAI,CAAC5G,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED4G,QAAAA,IAAI,CAAC5G,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAVM,CAAP;EAWD;;EAxK+B;;EA2KlC2C,oBAAoB,CAACgC,KAAD,CAApB;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA1C,kBAAkB,CAAC0C,KAAD,CAAlB;;;;;;;;"}