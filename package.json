{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "rtl": "webpack --config webpack-rtl.config.js webpack-rtl-public.config", "build": "react-scripts --max_old_space_size=4096 build"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "axios": "^0.21", "css-loader": "^6.7.1", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "postcss": "^8.1.14", "react": "^17.0.2", "react-dom": "^17.0.2", "react-scripts": "^4.0.3", "sass-loader": "^13.0.0", "style-loader": "^3.3.1", "web-vitals": "^2.1.4"}, "dependencies": {"@adaaugusta/react-google-login": "^6.2.0", "@formatjs/intl-pluralrules": "^4.3.2", "@formatjs/intl-relativetimeformat": "^10.0.0", "@fortawesome/fontawesome-free": "^5.15.4", "@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@fortawesome/react-fontawesome": "^0.1.18", "@itexperts/barcode-scanner": "^1.0.1", "@stripe/react-stripe-js": "^1.16.2", "@stripe/stripe-js": "^1.46.0", "@tinymce/tinymce-react": "^4.3.0", "ag-charts-community": "^5.3.0", "ag-charts-react": "^5.3.0", "apexcharts": "^3.35.0", "awesome-react-calculator": "^1.1.0", "bitcoin-address-validation": "^2.2.1", "bootstrap": "^5.2.3", "bootstrap-icons": "^1.8.1", "chart.js": "^3.0.0", "clsx": "^1.1.1", "country-list-with-dial-code-and-flag": "^4.1.0", "country-telephone-data": "^0.6.3", "date-fns": "^2.29.3", "del": "^6.1.1", "echarts": "^5.3.3", "echarts-for-react": "^3.0.2", "email-validator": "^2.0.4", "ethereum-address": "^0.0.4", "faker": "^5.5.3", "file-saver": "^2.0.5", "formik": "^2.2.9", "fortawesome": "^0.0.1-security", "history": "^5.0.0", "i": "^0.3.7", "libphonenumber-js": "^1.10.26", "lightbox2": "^2.11.4", "line-awesome": "^1.3.0", "mini-css-extract-plugin": "^2.5.3", "moment": "^2.29.3", "node-sass": "^7.0.1", "npm": "^9.4.0", "prism-themes": "^1.9.0", "prop-types": "^15.8.1", "pure-react-carousel": "^1.28.1", "qr-code-styling": "^1.6.0-rc.1", "razorpay": "^2.8.4", "react-bootstrap": "^2.1.2", "react-bootstrap-icons": "^1.8.2", "react-bootstrap-sweetalert": "^5.2.0", "react-bootstrap-v5": "^1.4.0", "react-chartjs-2": "^4.3.1", "react-color": "^2.19.3", "react-data-table-component": "^7.4.7", "react-datepicker": "^4.7.0", "react-device-detect": "^2.2.2", "react-elastic-carousel": "^0.11.5", "react-facebook-login": "^4.1.1", "react-google-recaptcha": "^2.1.0", "react-helmet": "^6.1.0", "react-icons": "^4.3.1", "react-id-swiper": "^4.0.0", "react-image-lightbox": "^5.1.4", "react-inlinesvg": "^2.3.0", "react-intl": "^5.25.1", "react-lottie-player": "^1.5.4", "react-multi-email-input": "^0.1.3", "react-multiple-image-uploader": "^1.0.3", "react-notify-toast": "^0.5.1", "react-portal-tooltip": "^2.4.7", "react-pro-sidebar": "^0.7.1", "react-quill": "^2.0.0", "react-redux": "^7.2.6", "react-router": "^6.2.1", "react-router-dom": "^6.3.0", "react-search-autocomplete": "^7.2.2", "react-select": "^5.3.1", "react-simple-image-slider": "^2.4.1", "react-social-login-buttons": "^3.6.1", "react-to-print": "^2.14.6", "react-toastify": "^8.2.0", "react-tooltip": "^5.7.4", "react-topbar-progress-indicator": "^4.1.1", "reactcss": "^1.2.3", "reactjs-social-login": "^2.6.2", "reactstrap": "^9.0.2", "redux": "^4.1.2", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.1", "rtlcss-webpack-plugin": "^4.0.7", "sass": "^1.49.7", "slick-carousel": "^1.8.1", "socicon": "^3.0.5", "styled-components": "^5.3.3", "swiper": "^5.4.5", "toastr": "^2.1.4", "use-sound": "^4.0.1", "yup": "^0.32.11"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "eslint --fix"}}